# 批次2分析报告：库存容器查询模块

## 数据范围
- **todo.md行数**: 第12-18行
- **涉及字段**: id
- **主要接口**: `/wmd/front/inventory/container/query` (但实际代码中是不同的具体接口)

## 1. 接口匹配验证

### 1.1 接口URL不匹配问题分析
**重要发现**: todo.md中记录的接口URL与实际代码内容存在严重不匹配

#### todo.md第12行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `'/wis/front/inventory/container/task_no_detail'`
- **文件位置**: `src/lib/wis-front.js:178`
- **验证结果**: ❌ 完全不匹配

#### todo.md第13行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `'/wis/front/inventory/container/task_no_detail_new_special'`
- **文件位置**: `src/lib/wis-front.js:179`
- **验证结果**: ❌ 完全不匹配

#### todo.md第14行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `'/wis/front/inventory/container/upper_detail'`
- **文件位置**: `src/lib/wis-front.js:180`
- **验证结果**: ❌ 完全不匹配

#### todo.md第15行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `'/wis/front/inventory/container/upper_detail_new'`
- **文件位置**: `src/lib/wis-front.js:181`
- **验证结果**: ❌ 完全不匹配

#### todo.md第16-18行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: 
  - `'/wmd/front/inventory/container/query'` (第16行) ✅ 匹配
  - `'/wmd/front/inventory/container/upper_detail'` (第17行) ❌ 不匹配
  - `'/wmd/front/inventory/container/task_no_detail'` (第18行) ❌ 不匹配
- **文件位置**: `src/lib/fetch-switch.js:65-67`

### 1.2 实际接口分析

#### WIS前缀接口 (src/lib/wis-front.js)
这些是WIS服务的接口列表，用于接口前缀切换：
```javascript
// 实际使用WIS_FRONT前缀 (/wis/front)
'/wis/front/inventory/container/task_no_detail'
'/wis/front/inventory/container/task_no_detail_new_special'
'/wis/front/inventory/container/upper_detail'
'/wis/front/inventory/container/upper_detail_new'
```

#### WMD前缀接口 (src/lib/fetch-switch.js)
这些是接口切换配置，支持新旧前缀切换：
```javascript
// 支持 /wmd/front 和 /wms/front 前缀切换
'/wmd/front/inventory/container/query'
'/wmd/front/inventory/container/upper_detail'
'/wmd/front/inventory/container/task_no_detail'
```

## 2. 实际接口使用情况分析

### 2.1 upperDetailApi 和 upperQCDetailApi
**定义位置**: `src/component/query/server.js:91-98`
```javascript
export const upperDetailApi = (param) => sendPostRequest({
  url: '/inventory/container/upper_detail',
  param,
}, process.env.BASE_URI_WMD);  // /wmd/front

export const upperQCDetailApi = (param) => sendPostRequest({
  url: '/inventory/container/upper_detail',
  param,
}, process.env.WIS_FRONT);     // /wis/front
```

**调用位置**: `src/component/query/container-query/reducers.js:547`
**数据流分析**:
```javascript
const res = [1, 2, 6].includes(upperType) ? 
  yield upperQCDetailApi(params) :    // 使用WIS前缀
  yield upperDetailApi(params);       // 使用WMD前缀
```

### 2.2 taskNoDetailApi 和 taskNoQCDetailApi
**定义位置**: `src/component/query/server.js:101-108`
```javascript
export const taskNoDetailApi = (param) => sendPostRequest({
  url: '/inventory/container/task_no_detail',
  param,
}, process.env.BASE_URI_WMD);  // /wmd/front

export const taskNoQCDetailApi = (param) => sendPostRequest({
  url: '/inventory/container/task_no_detail',
  param,
}, process.env.WIS_FRONT);     // /wis/front
```

**调用位置**: `src/component/query/container-query/reducers.js:585`

### 2.3 新版本容器查询接口
**定义位置**: 
- `src/component/query/container-query-new/inbound/enter-warehouse/server.js:5`
- `src/component/query/container-query-new/inbound/enter-warehouse-special/server.js:5`

```javascript
// 入库装箱
export const queryDetailAPI = (param) => sendPostRequest({
  url: '/inventory/container/upper_detail_new',
  param,
}, process.env.WIS_FRONT);

// 特殊入库装箱
export const queryDetailAPI = (param) => sendPostRequest({
  url: '/inventory/container/task_no_detail_new_special',
  param,
}, process.env.WIS_FRONT);
```

## 3. 字段使用方式分析

### 3.1 数值比较操作 (高风险)
**位置**: `src/component/query/container-query-new/util.js:14-16`
```javascript
targetItemIndex = list.findIndex((v) => 
  `${v.skuCode}` === skuCode && 
  Number(v.reviewNum) < Number(v.num)  // 数值比较
);
```

**位置**: `src/component/query/container-query-new/util.js:66`
```javascript
result.current.reviewNum = (result.current.reviewNum || 0) + 1;  // 数值运算
```

**风险分析**: 
- 如果reviewNum或num字段从long变为string，这些数值比较可能失效
- 需要确认这些字段是否受影响

### 3.2 字符串拼接作为ID (中风险)
**位置**: 多个文件中的模式
```javascript
const id = `${goodsSn}-${size}`;  // 字符串拼接
list.findIndex((v) => `${v.goodsSn}-${v.size}` === id);  // 字符串比较
```

**风险分析**:
- 如果goodsSn或size字段受影响，需要确保字符串拼接正常
- 目前看起来是安全的字符串操作

### 3.3 容器类型判断 (低风险)
**位置**: `src/component/query/container-query/reducers.js:216-236`
```javascript
switch (containerType) {
  case 2:
    url = '/inventory/container/query_container_detail';
    break;
  case 3:
    url = '/inventory/get_batch_container_info';
    break;
  // ...
}
```

**风险分析**: 
- containerType用于数值比较，但这是枚举值，通常不会受影响
- 低风险

## 4. 数据结构分析

### 4.1 容器查询返回数据
**基于代码分析的数据结构**:
```javascript
// upperDetailApi/taskNoDetailApi 返回结构
{
  code: '0',
  info: {
    goodsLists: [
      {
        goodsSn: string,      // 商品编号
        size: string,         // 尺码
        reviewNum: number,    // 复核数量 (可能受影响)
        num: number,          // 总数量 (可能受影响)
        skuCode: string,      // SKU编码
        // ... 其他字段
      }
    ],
    // ... 其他字段
  }
}
```

### 4.2 非正品分箱数据
**基于代码分析的数据结构**:
```javascript
// diffBoxForNonGenuineAPI 返回结构
{
  code: '0',
  info: {
    containerCode: string,        // 容器号
    bizTypeStr: string,          // 业务类型
    operationStepStr: string,    // 操作步骤
    sowingNum: number,           // 已扫描数量 (可能受影响)
    totalNum: number,            // 总数量 (可能受影响)
    turnoverBoxDetails: [...]    // 周转箱详情
  }
}
```

## 5. 风险点评估

### 5.1 高风险点
1. **数值比较操作**:
   ```javascript
   Number(v.reviewNum) < Number(v.num)
   ```
   - 如果reviewNum或num字段从long变为string，需要确保Number()转换正常

2. **数值运算**:
   ```javascript
   result.current.reviewNum = (result.current.reviewNum || 0) + 1;
   ```
   - 如果reviewNum字段类型变更，需要确保运算逻辑正确

### 5.2 中风险点
1. **接口URL不匹配**: todo.md中的记录与实际代码严重不符
2. **前缀切换逻辑**: WIS和WMD前缀的切换可能影响接口调用

### 5.3 低风险点
1. **字符串拼接**: `${goodsSn}-${size}` 等操作
2. **容器类型枚举**: switch case中的数值比较

## 6. 修改建议

### 6.1 需要验证的点
1. **确认受影响字段**: 明确哪些字段从long变为string
2. **数值操作兼容性**: 确保Number()转换在字符串类型下正常工作
3. **接口URL映射**: 更正todo.md中的接口记录

### 6.2 可能需要修改的代码
如果reviewNum、num、sowingNum、totalNum等字段受影响：
```javascript
// 当前代码
Number(v.reviewNum) < Number(v.num)

// 可能需要的修改 (如果字段变为string)
parseInt(v.reviewNum, 10) < parseInt(v.num, 10)
// 或者
+v.reviewNum < +v.num
```

## 7. 测试场景建议

### 7.1 数值比较测试
- **场景1**: 验证reviewNum < num的比较逻辑
- **场景2**: 验证数值运算 reviewNum + 1 的正确性
- **场景3**: 测试边界值 (0, 负数, 大数值)

### 7.2 接口调用测试
- **场景1**: 验证WIS前缀接口的正常调用
- **场景2**: 验证WMD前缀接口的正常调用
- **场景3**: 测试接口切换逻辑

## 8. 结论

**批次2总体风险评估**: 高风险
- **高风险项**: 2个 (数值比较和运算)
- **中风险项**: 2个 (接口URL不匹配、前缀切换)
- **低风险项**: 多个 (字符串操作、枚举比较)

**关键问题**:
1. **todo.md记录错误**: 大部分接口URL记录与实际代码不符
2. **数值操作风险**: 存在多处数值比较和运算，需要重点关注
3. **接口前缀复杂**: WIS和WMD两套前缀系统，增加了复杂性

**建议优先级**:
1. **立即处理**: 验证数值字段的类型变更影响
2. **重点关注**: 更正todo.md中的接口记录
3. **持续监控**: 确保接口前缀切换逻辑正常
