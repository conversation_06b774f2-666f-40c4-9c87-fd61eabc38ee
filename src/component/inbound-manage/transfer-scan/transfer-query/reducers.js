import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { classFocus, getHeaderTitle } from 'lib/util';
import { modal, message } from 'common';
import error from 'source/audio/delete.mp3';
import {
  scanBoxAPI,
  scanPalletAPI,
  scanStorageLocationAPI,
} from './server';

const audio = new Audio(error);
audio.load();

export const defaultState = {
  // initLoading: true,
  dataLoading: 1,
  headerTitle: '',
  activeTab: 1, // 激活tab
  showDetailPage: false,
  code: '', // 扫描的条码
  // 周转箱信息
  containerInfo: {
    containerCode: '', // 周转箱
    containerNum: 0, // 交接箱数
    handoverContainerStatusName: '', // 交接状态
    handoverProcessList: [], // 交接流程列表
    handoverTypeName: '', // 交接类型名称
    processType: '', // 流程类型编码
    processTypeName: '', // 流程类型名称
    palletCode: '', // 托盘号
    subsWarehouseName: '', // 来源子仓名称
    warehouseName: '', // 所属仓库
    shelvesParkName: '', // 上架园区
    handoverType: '', // 交接类型
    planReceiveSubWarehouseName: '', // 计划卸货子仓
    subWarehouseName: '', // 发货子仓
    receiveSubWarehouseName: '', // 收货子仓
  },
  // 托盘号信息
  palletInfo: {
    containerList: [], // 周转箱号列表
    containerNum: 0, // 交接箱数
    handoverProcessList: [], // 交接流程列表
    handoverProcessTypeName: '', // 交接流程类型名称
    handoverTypeName: '', // 交接类型
    palletCode: '', // 托盘号
    subsWarehouseName: '', // 来源子仓名称
    warehouseName: '', // 所属仓库
    shelvesParkName: '', // 上架园区
    planReceiveSubWarehouseName: '', // 计划卸货子仓
    subWarehouseName: '', // 发货子仓
    receiveSubWarehouseName: '', // 收货子仓
  },
  // 暂存位信息
  storageLocationInfo: {
    bindingUser: '', // 绑定人
    bingingTime: '', // 绑定时间
    containerList: [], // 周转箱号列表
    processType: '', // 流程类型编码
    processTypeName: '', // 流程类型名称
    containerNum: 0, // 交接箱数
    handoverTypeName: '', // 交接类型
    locationTypeName: '', // 暂存位类型
    palletCode: '', // 托盘号
    storageLocation: '', // 暂存位
    warehouseName: '', // 仓库
  },
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(draft, data) {
    assign(draft, data);
  },
  * init() {
    yield this.changeData({ headerTitle: getHeaderTitle() });
    classFocus('code');
  },
  // 扫描箱子
  * scanCode() {
    const { code } = yield '';
    const params = {
      code: code?.trim().toUpperCase(),
    };
    markStatus('dataLoading');
    const { code: resCode, info, msg } = yield scanBoxAPI(params);
    if (resCode === '0') {
      yield this.changeData({
        containerInfo: {
          containerCode: info.containerCode, // 周转箱
          containerNum: info.containerNum, // 交接箱数
          handoverContainerStatusName: info.handoverContainerStatusName, // 交接状态
          handoverProcessList: info.handoverProcessList || [], // 交接流程列表
          processType: info.processType,
          processTypeName: info.processTypeName, // 流程类型名称
          handoverTypeName: info.handoverTypeName, // 交接类型
          palletCode: info.palletCode, // 托盘号
          subsWarehouseName: info.subsWarehouseName, // 来源子仓名称
          warehouseName: info.warehouseName, // 所属仓库
          shelvesParkName: info.shelvesParkName, // 上架园区
          handoverType: info.handoverType, // 交接类型
          planReceiveSubWarehouseName: info.planReceiveSubWarehouseName, // 计划卸货子仓
          subWarehouseName: info.subWarehouseName, // 发货子仓
          receiveSubWarehouseName: info.receiveSubWarehouseName, // 收货子仓
        },
        code: '',
      });
      classFocus('code');
    } else if (resCode === '400601') {
      audio.play();
      yield this.changeData({
        code: '',
      });
      message.error(msg, 3000, true);
      classFocus('code');
    } else {
      yield this.changeData({
        code: '',
      });
      modal.error({
        content: msg,
        onOk: () => classFocus('code'),
      });
    }
  },
  // 扫描托盘
  * scanPallet() {
    const { code } = yield '';
    const params = {
      code: code?.trim().toUpperCase(),
    };
    markStatus('dataLoading');
    const { code: resCode, info, msg } = yield scanPalletAPI(params);
    if (resCode === '0') {
      yield this.changeData({
        palletInfo: {
          containerList: info?.containerList || [], // 周转箱号列表
          containerNum: info.containerNum, // 交接箱数
          handoverProcessList: info?.handoverProcessList || [], // 交接流程列表
          handoverProcessTypeName: info.handoverProcessTypeName, // 交接流程类型名称
          handoverTypeName: info.handoverTypeName, // 交接类型
          palletCode: info.palletCode, // 托盘号
          subsWarehouseName: info.subsWarehouseName, // 来源子仓名称
          warehouseName: info.warehouseName, // 所属仓库
          shelvesParkName: info.shelvesParkName, // 上架园区
          planReceiveSubWarehouseName: info.planReceiveSubWarehouseName, // 计划卸货子仓
          subWarehouseName: info.subWarehouseName, // 发货子仓
          receiveSubWarehouseName: info.receiveSubWarehouseName, // 收货子仓
        },
        code: '',
      });
      classFocus('code');
    } else if (['400900', '400901', '400902'].includes(resCode)) {
      audio.play();
      yield this.changeData({
        code: '',
      });
      message.error(msg, 3000, true);
      classFocus('code');
    } else {
      yield this.changeData({
        code: '',
      });
      modal.error({
        content: msg,
        onOk: () => classFocus('code'),
      });
    }
  },
  // 扫描暂存位
  * scanStorageLocation() {
    const { code } = yield '';
    const params = {
      code: code?.trim(),
    };
    markStatus('dataLoading');
    const { code: resCode, info, msg } = yield scanStorageLocationAPI(params);
    if (resCode === '0') {
      yield this.changeData({
        storageLocationInfo: {
          bindingUser: info.bindingUser, // 绑定人
          bingingTime: info.bingingTime, // 绑定时间
          containerList: info?.containerList || [], // 周转箱号列表
          containerNum: info.containerNum, // 交接箱数
          handoverTypeName: info.handoverTypeName, // 交接类型
          locationTypeName: info.locationTypeName, // 暂存位类型
          processType: info.processType,
          processTypeName: info.processTypeName, // 流程类型名称
          palletCode: info.palletCode, // 托盘号
          storageLocation: info.storageLocation, // 暂存位
          warehouseName: info.warehouseName, // 仓库
        },
        code: '',
      });
      classFocus('code');
    } else if (['400900', '400901', '400902'].includes(resCode)) {
      audio.play();
      yield this.changeData({
        code: '',
      });
      message.error(msg, 3000, true);
      classFocus('code');
    } else {
      yield this.changeData({
        code: '',
      });
      modal.error({
        content: msg,
        onOk: () => classFocus('code'),
      });
    }
  },
};
