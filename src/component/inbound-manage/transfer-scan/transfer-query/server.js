import { sendPostRequest } from '../../../../lib/public-request';

// 扫描周转箱号
export const scanBoxAPI = (param) => sendPostRequest({
  url: '/pda/handover_scan/container_query',
  param,
}, process.env.WIS_FRONT);

// 扫描托盘号
export const scanPalletAPI = (param) => sendPostRequest({
  url: '/pda/handover_scan/pallet_query',
  param,
}, process.env.WIS_FRONT);

// 扫描暂存位
export const scanStorageLocationAPI = (param) => sendPostRequest({
  url: '/pda/handover_scan/location_query',
  param,
}, process.env.WIS_FRONT);
