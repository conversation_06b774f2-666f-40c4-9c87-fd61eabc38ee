.contentWrapper {
  padding: 6px 12px 0px 12px;
  background-color: #fff;
}

// tabButton部分
.handleButtonWrapper {
  font-size: 14px;
  border-radius: 5px;
  border: 1px solid #197AFA;
  overflow: hidden;
  width: 100%;
  display: flex;
  position: relative;
  span {
    position: relative;
    // width: 98px;
    width:33.333%;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 16px;
    padding: 6px 0;
    text-align: center;
    background-color: #fff;
    color: #197afa;
    box-sizing: border-box;

    &.active {
      background-color: #197afa;
      color: #fff;
      box-shadow: -1px 0px #197AFA;
    }
  }

  span::after {
    position: absolute;
    top: 50%;
    right: 0px;
    content: '';
    width: 0px;
    height: 14px;
    border-right: 1px solid #197AFA;
    z-index: 1;
    transform: translateY(-50%);
  }

  span:nth-child(3)::after {
    border: none;
  }
}

// 查询内容部分
.sectionWrapper {
  // box-shadow: 0px 2px 4px 0px rgba(25, 122, 250, 0.15);
  padding: 5px 0px 0px 0px;

  &>div {
    background-color: #fff;
    overflow: hidden;
    .scrollPart{
      height: 245px;
      overflow-y: scroll;
      .ptO{
        padding-top: 0px;
      }
      .mt4{
        border-top: 2px solid #f3f4fb;
        padding-top: 4px;
        margin-bottom: 4px;
      }
      .mb4{
        margin-bottom: 4px !important;
      }
    }
  }

  p {
    position: relative;
    display: flex;
    justify-content: space-between;
    font-weight: 500;
    font-size: 14px;
    padding: 0px 14px;

    &.flex_start {
      justify-content: space-between;
    }

    span:first-child {
      color: #333e59;
    }

    span:last-child {
      color: #ff8c00;
    }

    .ml{
      margin-left: 10px;
    }
    
    .lineName {
      font-size: 14px;
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: #197afa;
    }
  }

  .secondPart {
    font-size: 12px;
    padding: 0 14px;
    box-sizing: border-box;
    // max-height: 95px;
    // overflow-y: scroll;
    margin-bottom: 16px;

    div {
      margin-bottom: 5px;

      span {
        display: inline-block;
      }

      .dot {
        position: relative;
        width: 6px;
        height: 6px;
        background: #CCCFD7;
        border-radius: 50%;
        margin-right: 8px;

        &::after {
          position: absolute;
          top: 160%;
          left: 50%;
          transform: translateX(-50%);
          content: '';
          width: 1px;
          height: 12px;
          background: #E6EAF0;
        }

        &.lastDot {
          &::after {
            content: none;
          }
        }
      }

      .label {
        color: #333e59;
        margin-right: 4px;
      }

      .desc {
        display: inline;
        color: #666c7c
      }

      .status {
        height: 20px;
        line-height: 20px;
        color: #ff8c00;
        text-align: center;
        padding: 0px 4px;
        background: #FFF3E5;
        border-radius: 4px;
        margin-left: 10px;
        box-sizing: border-box;
      }
    }

  }

  .palletCode {
    position: relative;
    // 标识
    .flag {
      position: absolute;
      top: 7px;
      right: -25px;
      width: auto;
      margin-left: 5px;
      padding: 2px 5px;
      height: 20px;
      line-height: 20px;
      font-size: 12px;
      color: #fff;
      vertical-align: top;
      border-radius: 4px;
      background-color: #ff8c00;
      
  }
  }
  
}

// 详情内容部分
.detailPageWrapper {

  .detailContentWrapper,
  h3 {
    .index {
      font-size: 12px;
      font-weight: 400;
      color: #333e59;
      margin-right: 13px;
    }

    .boxCode {
      font-size: 12px;
      font-weight: 500;
      color: #141737;
      height: 20px;
      line-height: 20px;
    }

    .state {
      font-size: 12px;
      font-weight: 400;
      color: #333e59;
      height: 20px;
      line-height: 20px;
    }
  }

  h3,
  ul>li {
    display: flex;
    justify-content: flex-start;
    padding: 7px 6px 7px 6px;
    box-sizing: border-box;
  }

  ul>li {
    border-bottom: 1px solid #eeeef0;
  }

  h3 {
    position: relative;
    background: #F4F5F8;

    .demo {
      position: absolute;
      top: 4px;
      right: -4px;
      width: 40px;
      height: 20px;
      background: #0059CE;
      border-radius: 2px 2px 0px 2px;
      color: #fff;
      font-size: 14px;
      line-height: 20px;
      text-align: center;
    }

    &::before {
      position: absolute;
      top: 24px;
      right: -4px;
      content: '';
      width: 0;
      height: 0;
      border-top: 4px solid #0059CE;
      border-right: 4px solid transparent;
    }
  }

  .detailContentWrapper {
    padding: 8px 8px 0 8px;
  }

  .demoTextWrapper {
    padding: 0 8px;
    position: sticky;
    top: 40px;
  }
}

.textIcon{
  display: inline-block;
  height: 12px;
  border-radius: 10px;
  border: 1px solid red;
  padding-bottom: 2px;
  line-height: 14px;
  margin-right: 5px;
  color: red;
  font-weight: 400;
}
