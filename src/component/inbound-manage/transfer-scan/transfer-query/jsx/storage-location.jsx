import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { LabelList, RowInfo } from 'common';
import Icon from '@shein-components/Icon';
import store from '../reducers';
import style from '../style.less';

// 转运暂存位信息
function Transfer(props) {
  const {
    storageLocationInfo,
  } = props;
  return (
    <div className={style.sectionWrapper}>
      {
        storageLocationInfo.storageLocation && (
        <div>
          <RowInfo
            extraStyle={{
              borderBottom: 'none',
              justifyContent: 'flex-start',
            }}
            label={`${t('暂存位')}：`}
            content={<span>{storageLocationInfo.storageLocation}</span>}
            type="info"
          />
          <LabelList
            labelList={[t('所属仓库')]}
            valueList={[storageLocationInfo.warehouseName]}
            lessLabelItem
          />
          <LabelList
            labelList={[t('交接类型')]}
            valueList={[storageLocationInfo.handoverTypeName]}
            lessLabelItem
          />
          <LabelList
            labelList={[t('托盘号')]}
            valueList={[storageLocationInfo.palletCode]}
            lessLabelItem
          />
          <LabelList
            labelList={[t('流程类型')]}
            valueList={[storageLocationInfo.processTypeName]}
            lessLabelItem
          />
          <div
            onClick={() => store.changeData({ showDetailPage: true })}
            style={{
              display: 'flex', justifyContent: 'space-between', alignItems: 'center', fontSize: '14px',
            }}
          >
            <span style={{ color: '#666C7C', paddingLeft: 14, marginTop: 3 }}>
              {t('周转箱数')}：
              <span style={{ color: '#197AFA' }}>{storageLocationInfo.containerNum}</span>
            </span>
            <Icon style={{ color: '#197AFA' }} name="arr-right" />
          </div>
          <LabelList
            labelList={[t('绑定人')]}
            valueList={[storageLocationInfo.bindingUser]}
            lessLabelItem
          />
          <LabelList
            labelList={[t('绑定时间')]}
            valueList={[storageLocationInfo.bingingTime]}
            lessLabelItem
          />
        </div>
        )
      }
    </div>
  );
}

Transfer.propTypes = {
  storageLocationInfo: PropTypes.shape(),
};

export default Transfer;
