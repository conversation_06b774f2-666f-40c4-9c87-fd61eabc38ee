import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { LabelList, RowInfo, TextBar } from 'common';
import Icon from '@shein-components/Icon';
import classNames from 'classnames';
import store from '../reducers';
import style from '../style.less';

// 转运暂存位信息
function Transfer(props) {
  const {
    palletInfo,
  } = props;
  return (
    <div className={style.sectionWrapper}>
      {
        palletInfo.palletCode && (
          <>
            <div>
              <RowInfo
                extraStyle={{
                  borderBottom: 'none',
                  justifyContent: 'flex-start',
                }}
                label={`${t('托盘号')}：`}
                content={(
                  <div className={style.palletCode}>
                    <span>{palletInfo.palletCode}</span>
                    {
                      palletInfo?.replenishmentMode === 1
                        ? <span className={style.flag}>{palletInfo.replenishmentModeName}</span>
                        : null
                    }
                  </div>
                )}
                type="info"
              />
              <div className={style.scrollPart}>
                <LabelList
                  className={style.ptO}
                  labelList={[t('所属仓库')]}
                  valueList={[palletInfo.warehouseName]}
                  lessLabelItem
                />
                {/* <LabelList
                  className={style.ptO}
                  labelList={[t('绑托人')]}
                  valueList={[palletInfo.warehouseName]}
                  lessLabelItem
                />
                <LabelList
                  className={style.ptO}
                  labelList={[t('操作时间')]}
                  valueList={[palletInfo.warehouseName]}
                  lessLabelItem
                /> */}
                <LabelList
                  className={style.ptO}
                  labelList={[t('交接类型')]}
                  valueList={[palletInfo.handoverTypeName]}
                  lessLabelItem
                />
                <div
                  onClick={() => store.changeData({ showDetailPage: true })}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    fontSize: '14px',
                    marginTop: '0px',
                    marginBottom: 4,
                  }}
                >
                  <span style={{ color: '#666C7C', paddingLeft: 14 }}>
                    {t('周转箱数')}：
                    <span style={{ color: '#197AFA' }}>{palletInfo.containerNum}</span>
                  </span>
                  <Icon style={{ color: '#197AFA' }} name="arr-right" />
                </div>
                <LabelList
                  className={style.ptO}
                  labelList={[t('计划卸货子仓')]}
                  valueList={[palletInfo.planReceiveSubWarehouseName]}
                  lessLabelItem
                />
                <LabelList
                  className={style.ptO}
                  labelList={[t('发货子仓')]}
                  valueList={[palletInfo.subWarehouseName]}
                  lessLabelItem
                />
                <LabelList
                  className={style.ptO}
                  labelList={[t('收货子仓')]}
                  valueList={[palletInfo.receiveSubWarehouseName]}
                  lessLabelItem
                />
                <p className={classNames([style.mt4, style.flex_start])}>
                  <span>{t('交接信息')}</span>
                  <span>{palletInfo.handoverProcessType ? palletInfo.handoverProcessTypeName : ''}</span>
                </p>
                <div className={style.secondPart}>
                  {
                    palletInfo.handoverProcessList.map((i, index) => (
                      <div key={i.processName}>
                        <span className={classNames([style.dot, index === palletInfo.handoverProcessList.length - 1 ? style.lastDot : ''])} />
                        <span className={style.label}>{i.processName}  :</span>
                        <span className={style.desc}>{i.processInfo}</span>
                        {i.flagName && (<span className={style.status}>{i.flagName}</span>)}
                      </div>
                    ))
                  }
                </div>
              </div>
            </div>
            {palletInfo.storeTypeName && (<TextBar text={[palletInfo.storeTypeName]} type="red" />)}
            {palletInfo.shelvesParkName && (<TextBar text={[palletInfo.shelvesParkName]} type="red" />)}
          </>
        )
      }
    </div>
  );
}

Transfer.propTypes = {
  palletInfo: PropTypes.shape(),
};

export default Transfer;
