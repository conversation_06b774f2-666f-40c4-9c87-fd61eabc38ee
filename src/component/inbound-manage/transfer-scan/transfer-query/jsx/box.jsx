/* eslint-disable max-len */
import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { LabelList, RowInfo, TextBar } from 'common';
import classNames from 'classnames';
import { QUERY_CAR_SEARCH } from 'lib/jumpUrl';
import { queryStr } from 'lib/util';
import style from '../style.less';

// 转运暂存位信息
function Transfer(props) {
  const { containerInfo } = props;
  return (
    <div className={style.sectionWrapper}>
      {
        containerInfo.containerCode && (
          <>
            <div>
              <RowInfo
                extraStyle={{
                  borderBottom: 'none',
                  justifyContent: 'flex-start',
                }}
                label={`${t('周转箱')}：`}
                content={<span>{containerInfo.containerCode}{containerInfo.replenishmentModeName ? `(${containerInfo.replenishmentModeName})` : ''} </span>}
                type="info"
              />
              <div className={style.scrollPart}>
                <LabelList
                  className={style.ptO}
                  labelList={[t('所属仓库')]}
                  // eslint-disable-next-line no-nested-ternary
                  valueList={[`${containerInfo.warehouseName}  ${containerInfo.handoverType === 10 ? '' : containerInfo.originSubsWarehouseName ? containerInfo.originSubsWarehouseName : ''}`]}
                  lessLabelItem
                />
                {
                  containerInfo.handoverType !== 5 && (
                    <LabelList
                      className={style.ptO}
                      labelList={[t('托盘号')]}
                      valueList={[containerInfo.palletCode]}
                      lessLabelItem
                    />
                  )
                }
                <LabelList
                  className={classNames([style.ptO, style.mb4])}
                  labelList={[t('交接类型')]}
                  valueList={[containerInfo.handoverTypeName]}
                  lessLabelItem
                />
                <LabelList
                  className={classNames([style.ptO, style.mb4])}
                  labelList={[t('计划卸货子仓')]}
                  valueList={[containerInfo.planReceiveSubWarehouseName]}
                  lessLabelItem
                />
                <LabelList
                  className={classNames([style.ptO, style.mb4])}
                  labelList={[t('发货子仓')]}
                  valueList={[containerInfo.subWarehouseName]}
                  lessLabelItem
                />
                <LabelList
                  className={classNames([style.ptO, style.mb4])}
                  labelList={[t('收货子仓')]}
                  valueList={[containerInfo.receiveSubWarehouseName]}
                  lessLabelItem
                />
                <p className={classNames([style.mt4, style.flex_start])}>
                  <span>{t('交接信息')}</span>
                  <span className={style.ml}>{containerInfo.processType ? containerInfo.processTypeName : ''}</span>
                </p>
                <div className={style.secondPart}>
                  {
                    containerInfo.handoverProcessList.map((i, index) => (
                      // eslint-disable-next-line react/no-array-index-key
                      <div key={index}>
                        <span className={classNames([style.dot, index === containerInfo.handoverProcessList.length - 1 ? style.lastDot : ''])} />
                        <span className={style.label}>{i.processName}  :</span>
                        <span className={style.desc}>{i.processInfo}</span>
                        {i.flagName && (<span className={style.status}>{i.flagName}</span>)}
                      </div>
                    ))
                  }
                </div>
              </div>
            </div>
            {containerInfo.storeTypeName && (<TextBar text={[containerInfo.storeTypeName]} type="red" />)}
            {containerInfo.shelvesParkName && (<TextBar text={[containerInfo.shelvesParkName]} type="red" />)}
          </>
        )
      }
    </div>
  );
}

Transfer.propTypes = {
  containerInfo: PropTypes.shape(),
};

export default Transfer;
