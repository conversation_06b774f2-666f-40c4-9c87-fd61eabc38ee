/* eslint-disable max-len */
import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { RowInfo } from 'common';
import style from '../style.less';

// 转运暂存位信息
function Detail(props) {
  const {
    activeTab,
    palletInfo,
    storageLocationInfo,
  } = props;

  return (
    <div className={style.detailPageWrapper}>
      <RowInfo
        extraStyle={{
          borderBottom: 'none',
          justifyContent: 'flex-start',
          position: 'sticky',
          top: 0,
          backgroundColor: '#fff',
        }}
        label={`${activeTab === 2 ? t('托盘号') : t('暂存位')}：`}
        content={<span>{activeTab === 2 ? palletInfo.palletCode : storageLocationInfo.storageLocation}</span>}
        type="info"
      />
      <div className={style.demoTextWrapper}>
        <h3>
          <div className={style.index}>{t('序号')}</div>
          <div>
            <p className={style.boxCode}>{t('周转箱')}</p>
          </div>
        </h3>
      </div>
      <div className={style.detailContentWrapper}>
        <ul>
          {
          activeTab === 2 && palletInfo.containerList?.map((i, index) => (
            <li key={i.containerCode}>
              <div className={style.index}>{index + 1}</div>
              <div>
                <p className={style.boxCode}>{i.containerCode}</p>
                {/* <p className={style.state}>{i.storeTypeName}</p> */}
              </div>
            </li>
          ))
          }
          {
          activeTab === 3 && storageLocationInfo.containerList.map((i, index) => (
            <li key={i.containerCode}>
              <div className={style.index}>{index + 1}</div>
              <div>
                <p className={style.boxCode}>{i.containerCode}</p>
                {/* <p className={style.state}>{i.storeTypeName}</p> */}
              </div>
            </li>
          ))
          }
        </ul>
      </div>
    </div>
  );
}

Detail.propTypes = {
  activeTab: PropTypes.number,
  palletInfo: PropTypes.shape(),
  storageLocationInfo: PropTypes.shape(),
};

export default Detail;
