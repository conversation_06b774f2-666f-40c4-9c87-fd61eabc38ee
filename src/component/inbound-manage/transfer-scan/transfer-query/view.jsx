import React from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import classNames from 'classnames';
import { SCAN_TRANSFER_QUERY } from 'lib/jumpUrl';
import { queryStr, classFocus } from 'lib/util';
import {
  Footer, Header, FocusInput, pages,
} from 'common';
import store from './reducers';
import Container from './jsx/box';
import Pallet from './jsx/pallet';
import Transfer from './jsx/storage-location';
import style from './style.less';
import DetailPage from './jsx/detail';

const { View } = pages;

class DefaultPage extends React.Component {
  componentDidMount() {
    store.init();
    const { match: { query } } = this.props;
    const routeContainerCode = query ? query.routeContainerCode : undefined;
    if (routeContainerCode) {
      store.changeData({
        code: routeContainerCode,
      }).then(() => {
        store.scanCode();
      });
    }
  }

  render() {
    const {
      dispatch,
      headerTitle,
      dataLoading,
      code,
      activeTab,
      showDetailPage,
    } = this.props;
    // 根据type区分引入哪个详情页
    const contentPage = () => {
      switch (activeTab) {
        case 1:
          return <Container {...this.props} />;
        case 2:
          return <Pallet {...this.props} />;
        case 3:
          return <Transfer {...this.props} />;
        default:
          return null;
      }
    };
    // 根据type 修改input的label
    const inputLabel = () => {
      switch (activeTab) {
        case 1:
          return t('周转箱查询');
        case 2:
          return t('托盘号查询');
        case 3:
          return t('暂存位查询');
        default:
          return t('周转箱查询');
      }
    };
    return (
      <View>
        <Header title={headerTitle || t('交接查询')} />
        {
          !showDetailPage && (
            <>
              <div className={style.contentWrapper}>
                <div className={style.handleButtonWrapper}>
                  {
                    [t('周转箱'), t('托盘号'), t('暂存位')].map((i, index) => (
                      <span
                        key={i}
                        className={classNames([activeTab === index + 1 ? style.active : ''])}
                        onClick={() => {
                          store.changeData({
                            activeTab: index + 1,
                            code: '',
                          });
                        }}
                      >
                        {i}
                      </span>
                    ))
                  }
                </div>
              </div>
              <Form>
                <FocusInput
                  placeholder={t('请扫描')}
                  className="code"
                  value={code}
                  disabled={dataLoading === 0}
                  onChange={(e) => {
                    store.changeData({ code: e.target.value.trim() });
                  }}
                  onPressEnter={() => {
                    if (!code) {
                      return;
                    }
                    switch (activeTab) {
                      case 1:
                        store.scanCode();
                        break;
                      case 2:
                        store.scanPallet();
                        break;
                      case 3:
                        store.scanStorageLocation();
                        break;

                      default:
                        break;
                    }
                  }}
                >
                  <label>{inputLabel()}</label>
                </FocusInput>
              </Form>
            </>
          )
        }
        {
          showDetailPage ? <DetailPage {...this.props} /> : contentPage()
        }
        <Footer
          beforeBack={(back) => {
            const { match: { query } } = this.props;
            const routeContainerCode = query ? query.routeContainerCode : undefined;
            if (showDetailPage) {
              store.changeData({
                showDetailPage: false,
              });
              classFocus('code');
            } else if (routeContainerCode) {
              window.open(SCAN_TRANSFER_QUERY + queryStr({ routeContainerCode }), '_self');
            } else {
              back();
            }
          }}
          dispatch={dispatch}
        />
      </View>
    );
  }
}

DefaultPage.propTypes = {
  dispatch: PropTypes.func,
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  code: PropTypes.string,
  activeTab: PropTypes.number,
  showDetailPage: PropTypes.bool,
  routeContainerCode: PropTypes.string,
  match: PropTypes.shape(),
};

export default i18n(DefaultPage);
