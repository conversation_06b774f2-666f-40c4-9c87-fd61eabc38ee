import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import store from './reducers';
import {
  FocusInput, Footer, FooterBtn, Header, modal,
} from '../../../common';
import style from '../../../style.css';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      dispatch,
      dataLoading,
      taskCode, // 任务单号
      containerCode, // 周转箱号
      shelfContainersList,
      containerCount,
      listShow,
      upperType,
      headerTitle,
    } = this.props;
    return (
      <div className={style.flexColContainer}>
        <Header title={headerTitle || t('领取上架任务')} />
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: '0 15px',
            fontSize: 14,
            lineHeight: '40px',
          }}
        >
          <div>
            {t('已扫描箱数')}
          </div>
          <div>
            {containerCount}
          </div>
        </div>
        <Form>
          <FocusInput
            data-step="1"
            placeholder={t('请扫描周转箱')}
            autoFocus
            value={containerCode}
            className="containerCode"
            label={t('周转箱')}
            disabled={dataLoading === 0}
            onChange={(e) => {
              store.changeData({
                data: {
                  containerCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={() => {
              if (containerCode?.trim()) {
                store.scanContainer({
                  param: {
                    containerCode: containerCode.trim()
                      .toUpperCase(),
                    taskCode,
                  },
                });
              }
            }}
          />
        </Form>

        <div style={{ margin: '10px 0 10px 15px' }}>
          <span>{t('任务单号')}:</span>
          <span>
            {taskCode}
          </span>
        </div>
        {
          listShow &&
          (
            <div style={{
              flex: '1 1 auto',
              overflowY: 'auto',
              backgroundColor: '#fff',
            }}
            >
              {
                shelfContainersList.map((obj) => (
                  <p
                    key={obj.idx + obj.pickContainerCode}
                    style={{
                      color: '#141737',
                      lineHeight: '40px',
                      margin: '0 14px',
                      borderBottom: '1px solid #E8EBF0',
                    }}
                  >
                    <span style={{
                      color: '#333E59',
                      marginRight: 20,
                    }}
                    >{obj.idx}
                    </span>
                    {obj.pickContainerCode}
                    <span
                      style={{ float: 'right' }}
                    >{`${obj.handoverTypeName} ${obj.storeTypeName}`}
                    </span>
                  </p>
                ))
              }
            </div>
          )
        }
        <Footer
          style={{ position: 'absolute' }}
          dispatch={dispatch}
        >
          <FooterBtn
            data-step="2"
            data-intro={t('第二步，关单（方法1：点击【关单】手动关单；方法2：自动关单满6个箱子）')}
            // disabled={context.showIntroVal}
            onClick={() => {
              modal.confirm({
                content: t('确定关单'),
                onOk: () => {
                  store.forceClose({
                    param: {
                      taskCode,
                      upperType,
                    },
                  });
                },
              });
            }}
          >
            {t('关单')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  dataLoading: PropTypes.number,
  containerCode: PropTypes.string,
  taskCode: PropTypes.string,
  containerCount: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  shelfContainersList: PropTypes.arrayOf(PropTypes.shape()),
  listShow: PropTypes.bool,
  upperType: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  headerTitle: PropTypes.string,
};

export default i18n(Container);
