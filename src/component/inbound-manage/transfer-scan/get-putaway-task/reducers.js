import assign from 'object-assign';
import { push } from 'react-router-redux';
import { t } from '@shein-bbl/react';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import {
  scanContainerCodeAPI,
  getContainersByTaskCodeAPI,
  forceCloseAPI,
  getTaskMsgAPI,
} from './server';
import { classFocus, getHeaderTitle } from '../../../../lib/util';
import Modal from '../../../common/modal';
import { message } from '../../../common';

const defaultState = {
  taskCode: '', // 任务单号 SJ19040900001
  containerCode: '', // 当前扫描的周转箱号
  shelfContainersList: [], // 当前任务单号对应的周转箱列表
  containerCount: '', // 已成功加入上架任务的周转箱数量
  upperStatus: '', // 1-待上架 2-上架中 3-已上架 4-已取消  当前任务的状态，为上架中时不可再扫描箱号
  upperType: '', // 关单成功需要判断上架类型，并跳转
  gotoModalVisible: false,
  closeStatus: '', // 任务单号是否关单状态， 0-进行中，1- 关单, 2-强制关单
  dataLoading: 1,
  listShow: false,
  headerTitle: '', // 页面标题
  handoverWorkSubWarehouseId: '', // 进入页面选择的子仓
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  // 初始当前页面数据
  resetPageStore(draft) {
    assign(draft, defaultState, { headerTitle: draft.headerTitle });
  },
  // 初始化要获取任务，看是否存在未完成的任务
  * init(action, ctx, put) {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }
    yield ctx.changeData({
      data: {
        headerTitle: getHeaderTitle(),
        handoverWorkSubWarehouseId: preSubMenu?.subWarehouseId,
      },
    });
    yield ctx.getUserTask();
  },
  // 查询当前用户的任务信息
  * getUserTask(action, ctx) {
    const result = yield getTaskMsgAPI();
    if (result.code === '0') {
      yield ctx.changeData({
        data: {
          taskCode: result.info?.taskCode, // 任务单号
          containerCount: result.info?.shelfContainerCount, // 已成功加入上架任务的周转箱数量
          closeStatus: result.info?.closeStatus,
        },
      });
      // 有任务单号，则自动根据任务单号查询周转箱号列表
      if (result.info?.taskCode) {
        yield ctx.getContainersByTaskCode({
          param: {
            taskCode: result.info?.taskCode,
          },
        });
      }
    } else {
      yield ctx.changeData({
        data: {
          taskCode: '',
          containerCount: '',
          upperType: '',
          upperStatus: '',
          closeStatus: '',
        },
      });
      Modal.error({
        content: result.msg,
        className: 'containerCode',
      });
    }
  },

  // 扫描周转箱
  * scanContainer(action, ctx, put) {
    markStatus('dataLoading');
    const { handoverWorkSubWarehouseId } = yield '';
    const {
      code,
      info,
      msg,
    } = yield scanContainerCodeAPI({
      ...action.param,
      containerCode: (action.param.containerCode || '').toUpperCase(),
      handoverWorkSubWarehouseId,
    });

    if (code === '0') {
      const {
        shelfContainerCount, // 已扫箱数
        taskCode, // 任务单号
        upperType,
        upperStatus,
        closeStatus,
      } = info || {};
      yield ctx.changeData({
        data: {
          containerCount: shelfContainerCount,
          taskCode,
          upperType,
          upperStatus,
          closeStatus,
          containerCode: '',
        },
      });

      // 有任务单号，则自动根据任务单号查询周转箱号列表
      if (taskCode) {
        yield ctx.getContainersByTaskCode({
          param: {
            taskCode,
          },
        });
      }

      // 自动关单
      if (closeStatus === 1) {
        message.success(t('领取的周转箱已经达到上限,自动关箱'), 3000);
        yield put(push('/put-shelves/shift-up'));
      }

      classFocus('containerCode');
    } else {
      yield ctx.changeData({
        data: {
          containerCode: '',
        },
      });
      Modal.error({
        content: msg,
        className: 'containerCode',
      });
    }
  },
  // 根据任务单号查询周转箱号列表
  * getContainersByTaskCode(action, ctx) {
    markStatus('dataLoading');
    const {
      code,
      info,
      msg,
    } = yield getContainersByTaskCodeAPI(action.param);
    if (code === '0') {
      // 加上序号
      const shelfContainersList = (info.shelfContainers || []).map((item, idx) => ({
        ...item,
        idx: idx + 1,
      }));
      yield ctx.changeData({
        data: {
          shelfContainersList,
          containerCode: '',
          listShow: true,
        },
      });
      classFocus('containerCode');
    } else {
      yield ctx.changeData({
        data: {
          shelfContainersList: [],
          containerCode: '',
          listShow: false,
        },
      });
      Modal.error({
        content: msg,
        className: 'containerCode',
      });
    }
  },
  // 强制关单
  * forceClose(action, ctx, put) {
    markStatus('dataLoading');
    const { taskCode } = action.param;
    const {
      code,
      msg,
      info,
    } = yield forceCloseAPI({ taskCode });
    if (code === '0') {
      const { qualityCheckingFlag } = info || {};
      if (qualityCheckingFlag) {
        // 跳质检上架
        yield put(push('/put-shelves/quality-shelves'));
      } else {
        // 跳移位上架
        yield put(push('/put-shelves/shift-up'));
      }
      yield ctx.changeData({
        data: {
          containerCode: '',
        },
      });
      classFocus('containerCode');
    } else {
      yield ctx.changeData({
        data: {
          containerCode: '',
        },
      });
      Modal.error({
        content: msg,
        className: 'containerCode',
      });
    }
  },
};
