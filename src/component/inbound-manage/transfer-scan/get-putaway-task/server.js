import { sendPostRequest } from '../../../../lib/public-request';

// 扫描周转箱号（生成/加入上架任务）
export const scanContainerCodeAPI = (param) => sendPostRequest({
  url: '/pda/handover_get_upper_task/scan_container',
  param,
}, process.env.WIS_FRONT);

// 获取当前用户的任务信息
export const getTaskMsgAPI = (param) => sendPostRequest({
  url: '/pda/handover_get_upper_task/get_task_by_user',
  param,
}, process.env.WIS_FRONT);

// 根据任务单号查询周转箱号列表
export const getContainersByTaskCodeAPI = (param) => sendPostRequest({
  url: '/pda/handover_get_upper_task/query_container_by_task_code',
  param,
}, process.env.WIS_FRONT);

// 强制关单
export const forceCloseAPI = (param) => sendPostRequest({
  url: '/pda/handover_get_upper_task/force_close',
  param,
}, process.env.WIS_FRONT);
