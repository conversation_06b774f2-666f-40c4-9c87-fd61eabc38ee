import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import {
  FocusInput, Footer, Header, modal,
} from 'common';
import store from './reducers';
import style from '../../../style.css';

class Container extends Component {
  componentDidMount() {
    // 初始化数据
    store.init();
  }

  render() {
    const {
      headerTitle,
      dataLoading,
      location,
      palletCode,
      locationDisabled,
    } = this.props;

    return (
      <div className={style.flexColContainer}>
        <Header title={headerTitle || t('待转运暂存扫描')} />
        <Form>
          <FocusInput
            data-bind="location"
            className="location"
            autoFocus
            disabled={locationDisabled || dataLoading === 0}
            placeholder={t('请扫描待转运暂存位')}
            onChange={(e) => {
              store.changeData({
                location: e.target?.value?.trim()?.toUpperCase(),
              });
            }}
            onPressEnter={() => {
              if (location?.trim()) {
                store.scanLocation({
                  location: location.trim()?.toUpperCase(),
                });
              }
            }}
          >
            <label>{t('待转运暂存位')}</label>
          </FocusInput>
          <FocusInput
            data-bind="palletCode"
            className="palletCode"
            disabled={!locationDisabled || dataLoading === 0}
            placeholder={t('请扫描周转箱或托盘')}
            onChange={(e) => {
              store.changeData({
                palletCode: e.target?.value?.trim()?.toUpperCase(),
              });
            }}
            onPressEnter={() => {
              if (palletCode?.trim()) {
                store.scanPalletCode({
                  palletCode: palletCode.trim()?.toUpperCase(),
                });
              }
            }}
          >
            <label>{t('周转箱号')}</label>
          </FocusInput>
        </Form>

        <Footer
          beforeBack={(back) => {
            if (palletCode) {
              modal.confirm({
                content: t('是否返回?'),
                onOk: () => back(),
              });
              return;
            }
            back();
          }}
        />
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number.isRequired,
  palletCode: PropTypes.string.isRequired,
  location: PropTypes.string.isRequired,
  locationDisabled: PropTypes.bool.isRequired,
};

export default i18n(Container);
