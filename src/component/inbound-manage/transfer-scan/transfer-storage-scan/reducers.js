import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { classFocus, getHeaderTitle } from 'lib/util';
import { message, modal } from 'common';
import error from 'source/audio/delete.mp3';
import { scanLocationServerAPI, scanPalletCodeServerAPI } from './server';

// 报错异常声音
const audio = new Audio(error);
audio.load();

const defaultState = {
  headerTitle: t('待转运暂存扫描'), // 页面标题
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  location: '', // 转运暂存号
  palletCode: '', // 托盘号
  locationDisabled: false, // 转运暂存位是否已扫描成功，用于输入框禁用
};

export default {
  defaultState,
  // 改state数据
  changeData: (draft, action) => {
    assign(draft, action);
  },
  // 初始化
  * init() {
    yield this.changeData({
      ...defaultState,
      headerTitle: getHeaderTitle(),
    });
  },
  // 扫描暂存位条码
  * scanLocation(action, ctx) {
    markStatus('dataLoading'); // loading正在查询状态
    const { palletCode } = yield '';
    const params = {
      location: action.location,
      palletCode,
    };
    const { code, msg } = yield scanLocationServerAPI(params);
    if (code === '0') {
      yield ctx.changeData({
        // 库位扫描成功后禁用库位输入框
        locationDisabled: true,
      });
      classFocus('palletCode');
    } else if (code === '400901') {
      // 所有业务异常提示需要发出声音。
      audio.play();
      yield ctx.changeData({
        location: '',
      });
      message.error(msg, 3000, true);
      classFocus('location');
    } else {
      yield ctx.changeData({
        location: '',
      });
      modal.error({ content: msg, className: 'location' });
    }
  },
  // 扫描托盘号
  * scanPalletCode(action, ctx) {
    markStatus('dataLoading');
    const { location } = yield '';
    const params = {
      location,
      palletCode: action.palletCode,
    };
    const { code, msg } = yield scanPalletCodeServerAPI(params);
    // 不管成功还是失败，托盘号是一定要清空的，成功的话，还要清空转运暂存号
    yield ctx.changeData({ palletCode: '' });
    if (code === '0') {
      message.success(t('待转运暂存成功！'));
      yield ctx.changeData({
        locationDisabled: false,
        location: '',
      });
      classFocus('location');
    } else if (code === '400901') {
      // 所有业务异常提示需要发出声音。
      audio.play();
      message.error(msg, 3000, true);
      yield this.changeData({
        palletCode: '',
      });
      classFocus('palletCode');
    } else {
      modal.error({ content: msg, className: 'palletCode' });
    }
  },
};
