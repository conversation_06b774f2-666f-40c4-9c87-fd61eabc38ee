import { sendPostRequest } from '../../../../lib/public-request';

/**
 * 扫描暂存位条码
 * @param param
 * @returns {*}
 */
export const scanLocationAPI = (param) => sendPostRequest({
  url: '/pda/handover_wait_upper_storage/scan_location',
  param,
}, process.env.WIS_FRONT);

/**
 * 扫描托盘号
 * @param param
 * @returns {*}
 */
export const scanPalletCodeAPI = (param) => sendPostRequest({
  url: '/pda/handover_wait_upper_storage/scan_pallet',
  param,
}, process.env.WIS_FRONT);
