import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import {
  Header,
  FocusInput,
  Footer,
} from 'common';
import store from './reducers';
import style from '../../../style.css';

class Container extends Component {
  componentDidMount() {
    // 初始化数据
    store.init();
  }

  render() {
    const {
      dataLoading,
      location,
      palletCode,
      locationDisabled,
      palletDisabled, // 扫托盘禁用
      handoverTypeName, // 托盘类型名称
      containerNum, // 托盘周转箱数量
      headerTitle, // 页面标题
    } = this.props;

    return (
      <div className={style.flexColContainer}>
        <Header title={headerTitle || t('待上架暂存扫描')} />
        <Form>
          <FocusInput
            data-bind="location"
            className="location"
            autoFocus
            disabled={locationDisabled || dataLoading === 0}
            placeholder={t('请扫描')}
            onPressEnter={() => {
              if (location?.trim()) {
                store.scanLocation({
                  data: {
                    location: location.trim(),
                  },
                });
              }
            }}
          >
            <label>{t('待上架暂存位')}</label>
          </FocusInput>
        </Form>
        <Form>
          <FocusInput
            data-bind="palletCode"
            className="palletCode"
            disabled={palletDisabled || dataLoading === 0}
            placeholder={t('请扫描周转箱或托盘')}
            onPressEnter={() => {
              if (palletCode?.trim() && location) {
                store.scanPalletCode({
                  data: {
                    palletCode: palletCode.trim()
                      .toUpperCase(),
                    location,
                  },
                });
              }
            }}
          >
            <label>{t('周转箱号')}</label>
          </FocusInput>
        </Form>
        <div style={{ marginTop: 10 }}>
          <div style={{
            paddingLeft: 14,
            color: '#333',
          }}
          >{handoverTypeName}
          </div>
          <div style={{
            paddingLeft: 14,
            color: '#333',
          }}
          >{t('托盘周转箱总数')}:{containerNum}
          </div>
        </div>
        <Footer />
      </div>
    );
  }
}

Container.propTypes = {
  dataLoading: PropTypes.number.isRequired,
  location: PropTypes.string.isRequired,
  palletCode: PropTypes.string.isRequired,
  locationDisabled: PropTypes.bool.isRequired,
  palletDisabled: PropTypes.bool.isRequired,
  handoverTypeName: PropTypes.string,
  containerNum: PropTypes.number,
  headerTitle: PropTypes.string,
};

export default i18n(Container);
