import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import {
  classFocus,
  getHeaderTitle, trimStr,
} from 'lib/util';
import { scanLocationAPI, scanPalletCodeAPI } from './server';
import message from '../../../common/message';
import Modal from '../../../common/modal';
import error from '../../../../source/audio/delete.mp3';

// 报错异常声音
const audio = new Audio(error);
audio.load();

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  location: '', // 转运暂存号
  palletCode: '', // 托盘号
  locationDisabled: false, // 转运暂存位是否已扫描成功，用于输入框禁用
  palletDisabled: true, // 周转箱输入框禁用
  handoverTypeName: '', // 托盘类型名称
  containerNum: 0, // 托盘周转箱数量
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action);
  },
  * init(action, ctx) {
    classFocus('location');
    yield ctx.changeData({ headerTitle: getHeaderTitle() });
  },
  // 扫描暂存位条码
  * scanLocation(action, ctx) {
    markStatus('dataLoading'); // loading正在查询状态
    const {
      code,
      msg,
    } = yield scanLocationAPI(trimStr(action.data)); // trimStr用于去除obj属性前后空格
    if (code === '0') {
      yield ctx.changeData({
        // 库位扫描成功后禁用库位输入框
        locationDisabled: true,
        palletDisabled: false,
      });
      classFocus('palletCode');
    } else {
      // 所有异常提示需要发出声音。
      audio.play();
      Modal.error({
        content: msg,
        className: 'location',
      });
      yield ctx.changeData({
        location: '',
      });
    }
  },
  // 扫描托盘号
  * scanPalletCode(action, ctx) {
    markStatus('dataLoading');
    const { location } = yield '';
    const param = {
      palletCode: action.data.palletCode,
      location,
    };
    const { code, info, msg } = yield scanPalletCodeAPI(trimStr(param)); // trimStr用于去除前后空格
    yield ctx.changeData({ data: { palletCode: '' } }); // 不管成功还是失败，托盘号是一定要清空的
    if (code === '0') {
      message.success(t('待上架暂存成功！'));
      const { handoverTypeName, containerNum } = info;
      yield ctx.changeData({
        location: '',
        locationDisabled: false,
        palletCode: '',
        palletDisabled: true,
        handoverTypeName,
        containerNum, // 更新托盘周转箱数量
      });
      classFocus('location');
    } else {
      yield ctx.changeData({
        palletCode: '', // 清空周转箱或托盘号
      });
      // 所有异常提示需要发出声音
      audio.play();
      Modal.error({
        content: msg,
        className: 'palletCode',
      });
    }
  },
};
