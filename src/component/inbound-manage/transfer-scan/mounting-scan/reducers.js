import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { modal, message } from 'common';
import { classFocus, getHeaderTitle } from 'lib/util';
import error from 'source/audio/delete.mp3';
import { push } from 'react-router-redux';
import {
  scanContainerAPI, scanPalletCodeAPI, closePalletAPI, returnSubmitAPI,
} from './server';

const audio = new Audio(error);
audio.load();
// 测试一下
const defaultState = {
  loading: 1, // 正在请求中时，禁用按钮
  headerTitle: t('装托扫描'), // 页面标题
  count: 0, // 已采箱数[前端自己计算累加]
  containerCode: '', // 周转箱
  containerCodeList: [], // 周转箱列表数据
  containerDisabled: false, // 周转箱是否置灰
  palletCode: '', // 托盘号
  scanedPalletCode: '', // 已扫描的托盘号
  palletDisabled: true, // 托盘号是否置灰
  handoverType: '', // 交接类型
  handoverTypeName: '', // 交接类型
  subWarehouseId: '', // 子仓
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action);
  },
  * init(action, ctx, put) {
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }
    // eslint-disable-next-line max-len
    yield ctx.changeData({ headerTitle: getHeaderTitle(), subWarehouseId: preSubMenu.subWarehouseId });
  },
  // 扫描周转箱号
  * scanContainerCode(params) {
    const { count, subWarehouseId } = yield '';
    const { containerCode, palletCode: pagePalletCode } = params;

    markStatus('loading');
    const { code, info, msg } = yield scanContainerAPI({
      ...params,
      handoverWorkSubWarehouseId: subWarehouseId,
    });
    if (code === '0') {
      const {
        containerCodeList = [], functionTag, handoverType, handoverTypeName, palletCode,
        shelvesParkName,
      } = info;
      // * 有托盘且已装托，需要额外处理
      if (palletCode && functionTag === 'close_pallet_flag') {
        // 关托成功后需要将周转箱进行清空处理
        yield this.changeData({
          palletCode,
          scanedPalletCode: palletCode,
          handoverType,
          handoverTypeName,
        });

        const tip = pagePalletCode ? t('该周转箱已装托，是否确认关托？') : t('您有未关托的托盘，是否继续操作');
        const isContinue = yield this.closePallet({
          content: tip,
          palletCode,
          okFlag: pagePalletCode,
          cancelFlag: !pagePalletCode,
        });

        // 不继续装托 - 即关箱处理
        if (isContinue) {
          if (!pagePalletCode) {
            message.success(t('未关托的托盘号{}', palletCode), 3000, true);
          }
          // 关托成功后需要将周转箱进行清空处理
          yield this.changeData({
            palletCode: '',
            containerCode: '',
          });
        } else {
          // 继续装托
          yield this.changeData({
            palletCode,
            palletDisabled: true,
            containerCode: '',
            containerDisabled: false,
            count: pagePalletCode ? count : count + (containerCodeList?.length || 0),
          });
        }
        classFocus('containerCode');
        return;
      }

      // 当前界面已有托盘或当前周转箱有托盘展示
      if (palletCode || pagePalletCode) {
        yield this.changeData({
          count: count + 1,
        });
      }

      // 将返回的数据进行填
      yield this.changeData({
        functionTag,
        handoverType, // 交接类型 type
        handoverTypeName, // 交接类型中文名
        palletCode,
        scanedPalletCode: palletCode, // 已扫描的托盘号
        containerCode: palletCode ? '' : containerCode,
        containerCodeList, // 周转箱下的商品列表
        containerDisabled: true, // 禁用周转箱
        palletDisabled: false, // 启用托盘
        shelvesParkName, // 推荐上架园区
      });

      classFocus('palletCode');

      // 若界面托盘中已存在值，则禁用托盘，聚焦周转箱
      if (pagePalletCode) {
        yield this.changeData({
          containerDisabled: false,
          palletDisabled: true,
        });
        classFocus('containerCode');
      }
    } else if (['400900', '400901', '400902'].includes(code)) {
      // 报错情况：已操作发货扫描，无法继续装托
      yield this.changeData({
        containerCode: '',
        containerDisabled: false,
      });
      audio.play();
      message.error(msg, 3000, true);
      classFocus('containerCode');
    } else {
      yield this.changeData({
        containerCode: '',
        containerDisabled: false,
      });
      modal.error({
        content: msg,
        className: 'containerCode',
      });
    }
  },
  // 扫描托盘号
  * scanPalletCode(action) {
    // 托盘号禁用，不可扫描
    yield this.changeData({
      palletDisabled: true,
    });
    const { count, subWarehouseId } = yield '';
    markStatus('loading');
    // 检测当前扫描的是否为托盘号，是否需要打开一个新托盘号
    const res = yield scanPalletCodeAPI({
      ...action,
      handoverWorkSubWarehouseId: subWarehouseId,
    });
    if (res.code === '0') {
      const { functionTag, palletCode, shelvesParkName } = res.info;
      // 当前托盘未关托
      if (functionTag === 'close_pallet_flag') {
        const isClose = yield this.closePallet({
          content: t('您有未关托的托盘，是否确认开启新托盘？'),
          palletCode,
        });

        // 关闭弹框，聚焦周转箱
        yield this.changeData({
          containerCode: '', // 清空并聚焦周转箱号
          palletCode: '',
          containerDisabled: false,
          palletDisabled: true,
          scanedPalletCode: '',
          handoverType: '', // 交接类型
          handoverTypeName: '', // 交接类型
        });

        // 开启新托盘 - 生成新的托盘
        if (isClose) {
          yield this.scanPalletCode(action);
        }
        return;
      }
      message.success(t('装托成功'), 1000);
      yield this.changeData({
        count: count + (res.info?.containerCodeList?.length || 0),
        containerCode: '',
        containerDisabled: false,
        palletDisabled: true,
        palletCode: res.info.palletCode,
        scanedPalletCode: res.info?.palletCode,
        shelvesParkName, // 推荐上架园区
      });
      classFocus('containerCode');
    } else {
      yield this.changeData({
        palletCode: '',
        palletDisabled: false,
      });
      audio.play();
      message.error(res.msg, 3000, true);
      classFocus('palletCode');
    }
  },
  /**
   * @params {string} content- 弹框提示语
   * @params {function} palletCode - 托盘号
   */
  * closePallet(action) {
    const {
      palletCode, content, okFlag = true, cancelFlag = false, isBack, back,
    } = action;
    // 提交成功则判断：是否关托
    const isClosePallet = yield new Promise((r) => modal.confirm({
      content,
      onOk: () => r(okFlag),
      onCancel: () => r(cancelFlag),
    }));
    if (isClosePallet) {
      // 关托请求
      const innerRes = yield closePalletAPI({ palletCode });
      // 成功直接返回；失败提示错误信息并返回
      if (innerRes.code === '0') {
        message.success(t('关托成功', 500));
        yield this.changeData({
          count: 0,
          scanedPalletCode: '', // 已扫描的托盘号
          handoverType: '', // 交接类型
          handoverTypeName: '', // 交接类型名称
        });
        // 关托成功
        return new Promise((resolve) => resolve(true));
      } else {
        audio.play();
        message.error(innerRes.msg, 3000, true);
        // 托盘为占用或者为已关托 需要特殊处理进行返回
        if (isBack && innerRes.code === '400901') {
          back();
        }
        // 关托失败
        return new Promise((resolve) => resolve(false));
      }
    } else {
      // 取消关托 - 关闭弹框，聚焦周转箱
      yield this.changeData({
        containerCode: '', // 清空并聚焦周转箱号
        palletCode: '',
        containerDisabled: false,
        palletDisabled: true,
      });
      classFocus('containerCode');
      // 取消关托
      return new Promise((resolve) => resolve(false));
    }
  },
  // 返回
  * back(back, ctx) {
    const { containerCode, palletCode, containerDisabled } = yield '';
    // 同时有周转箱号和托盘号才确信确认弹窗
    if (containerCode && palletCode) {
    // 是否确定返回
      const isBack = yield new Promise((r) => modal.confirm({
        content: t('是否确认返回?'),
        onOk: () => r(true),
        onCancel: () => r(false),
      }));
      // 弹窗确认逻辑 取消不做操作
      if (isBack) {
      // 返回提交
        const { code, msg } = yield returnSubmitAPI({
          containerCode: (containerCode || '').toUpperCase(),
          palletCode: (palletCode || '').toUpperCase(),
        });
        if (code === '0') {
          const isClose = yield this.closePallet({
            content: t('是否关托'),
            palletCode,
            isBack: true,
            back,
          });
          if (isClose) {
            back();
          }
        } else {
        // 根据当前聚焦输入框，判断清空哪个
          const focusKey = containerDisabled ? 'palletCode' : 'containerCode';
          yield ctx.changeData({
            [focusKey]: '',
          });
          message.error(msg, 3000, true);
        }
      }
    } else if (palletCode) {
      const isClose = yield this.closePallet({
        content: t('确认是否关托?'),
        palletCode,
        isBack: true,
        back,
      });
      // 不关托 则保留托盘数据并置灰
      yield this.changeData({
        palletCode,
      });
      if (isClose) {
        back();
      }
    } else {
      back();
    }
  },
};
