import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { CellsTitle } from 'react-weui/build/packages';
import {
  Header,
  Footer,
  FocusInput,
  RowInfo,
  ProgressBar,
  modal,
} from 'common';
import { classFocus } from 'lib/util';
import store from './reducers';
import style from '../../../style.css';
import navStore from '../../../nav/reducers';
import styles from './style.less';

class Container extends Component {
  componentDidMount() {
    store.init();
    this.onresize = () => requestAnimationFrame(() => this.forceUpdate());
    window.addEventListener('resize', this.onresize);
    navStore.changeData({ data: { pageStore: store } });
  }

  componentDidUpdate() {
    navStore.changeData({ data: { pageStore: store } });
  }

  render() {
    const {
      containerCode,
      containerDisabled,
      palletDisabled,
      count,
      palletCode,
      scanedPalletCode,
      headerTitle,
      handoverTypeName,
      totalBoxNum,
      receivedBoxNum,
      handoverType,
      loading,
      shelvesParkName,
    } = this.props;

    const height = window.innerHeight - 66;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <Header title={headerTitle || t('装托扫描')} />
        <Form>
          <RowInfo
            extraStyle={{
              borderBottom: 'none',
            }}
            label={t('已采箱数')}
            content={count}
          />
        </Form>
        {/* // todo 确认是否需要 */}
        {
          handoverType === 12 ? (
            <div style={{ fontSize: 14, padding: '14px 0 0 14px' }}>
              <span style={{ color: '#666C7C' }}>{t('总数')}：</span>
              <ProgressBar percentage={totalBoxNum ? receivedBoxNum / totalBoxNum : 0} style={{ width: 100, display: 'inline-block', margin: '0 10px' }} />
              <span>{t('已收')}{receivedBoxNum}{t('（共{}）', totalBoxNum)}</span>
            </div>
          ) : null
        }
        <Form style={{ marginTop: '10px' }}>
          <FocusInput
            placeholder={t('请扫描周转箱/包裹')}
            autoFocus
            value={containerCode}
            disabled={containerDisabled || loading === 0}
            className="containerCode"
            onChange={(e) => {
              if (e.target.value.length > 50) {
                store.changeData({ containerDisabled: true });
                modal.error({
                  content: t('上架周转箱/包裹不能超过50位'),
                  onOk: () => {
                    store.changeData({
                      containerDisabled: false,
                      containerCode: '',
                    });
                    classFocus('containerCode');
                  },
                });
                return;
              }
              store.changeData({
                containerCode: e.target.value.trim().slice(0, 50),
              });
            }}
            onPressEnter={() => {
              if (!containerCode?.trim()) return;
              store.changeData({
                containerCode: containerCode.trim().toUpperCase(),
              });
              store.scanContainerCode({
                containerCode: containerCode.trim().toUpperCase(),
                palletCode,
              });
            }}
          >
            <label>{t('周转箱/包裹')}</label>
          </FocusInput>
          <FocusInput
            placeholder={t('请扫描托盘号')}
            value={palletCode}
            disabled={palletDisabled || loading === 0}
            className="palletCode"
            onChange={(e) => store.changeData({ palletCode: e.target.value.trim() })}
            onPressEnter={() => {
              if (!palletCode?.trim()) return;
              store.changeData({ palletCode: palletCode.trim().toUpperCase() });
              store.scanPalletCode({
                containerCode,
                palletCode: palletCode.trim().toUpperCase(),
              });
            }}
          >
            <label>{t('托盘')}</label>
          </FocusInput>
        </Form>
        {
          handoverTypeName && (
            <>
              <CellsTitle>{t('信息')}</CellsTitle>
              <div className={styles.infoContainer}>
                <div className={styles.section}>
                  {
                    scanedPalletCode && <div>{t('托盘号')}：{scanedPalletCode}</div>
                  }
                  <div className={styles.rightItem}>{handoverTypeName}</div>
                </div>
              </div>
            </>
          )
        }
        {shelvesParkName && (
          <>
            {!handoverTypeName && (<CellsTitle>{t('信息')}</CellsTitle>)}
            <div className={styles.infoContainer}>
              <div className={styles.section}>
                {shelvesParkName && <div>{t('推荐上架园区')}：{shelvesParkName}</div>}
              </div>
            </div>
          </>
        )}
        <Footer
          disabled={palletDisabled && containerDisabled}
          beforeBack={(back) => store.back(back)}
        />
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  count: PropTypes.number,
  loading: PropTypes.number, // 是否正在请求中
  containerCode: PropTypes.string, // 周转箱号
  containerDisabled: PropTypes.bool, // 是否禁用周转型号
  palletCode: PropTypes.string, // 托盘号
  scanedPalletCode: PropTypes.string, // 已扫描的托盘号
  palletDisabled: PropTypes.bool, // 是否禁用托盘号
  handoverType: PropTypes.oneOfType([PropTypes.number, PropTypes.string]), // 交接类型 - type
  handoverTypeName: PropTypes.string, // 交接类型 - 名称
  totalBoxNum: PropTypes.number,
  receivedBoxNum: PropTypes.number,
  shelvesParkName: PropTypes.string, // 交接类型 - 名称
};

export default i18n(Container);
