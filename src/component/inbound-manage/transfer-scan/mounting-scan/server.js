import { sendPostRequest } from '../../../../lib/public-request';

// 扫描周转箱
export const scanContainerAPI = (param) => sendPostRequest({
  url: '/pda/handover_loading/scan_container',
  param,
}, process.env.WIS_FRONT);

// 扫描托盘
export const scanPalletCodeAPI = (param) => sendPostRequest({
  url: '/pda/handover_loading/scan_pallet',
  param,
}, process.env.WIS_FRONT);

// 关闭托盘
export const closePalletAPI = (param) => sendPostRequest({
  url: '/pda/handover_loading/close_pallet',
  param,
}, process.env.WIS_FRONT);

// 装托扫描-返回提交
export const returnSubmitAPI = (param) => sendPostRequest({
  url: '/pda/handover_loading/return_submit',
  param,
}, process.env.WIS_FRONT);
