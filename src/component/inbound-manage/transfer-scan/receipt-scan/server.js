import { sendPostRequest } from '../../../../lib/public-request';

// 扫描仓库条码
export const scanWarehouseAPI = (param) => sendPostRequest({
  url: '/pda/handover_receive/scan_warehouse',
  param,
}, process.env.WIS_FRONT);

// 扫描托盘号
export const scanPalletCodeAPI = (param) => sendPostRequest({
  url: '/pda/handover_receive/scan_pallet',
  param,
}, process.env.WIS_FRONT);

// 收货
export const receiveConfirmAPI = (param) => sendPostRequest({
  url: '/pda/handover_receive/confirm',
  param,
}, process.env.WIS_FRONT);

// FOB收货
export const receiveConfirmFobAPI = (param) => sendPostRequest({
  url: '/pda/handover_receive/receive_report_order_num',
  param,
}, process.env.WIS_FRONT);

// 缺箱
export const shortageBoxAPI = (param) => sendPostRequest({
  url: '/pda/handover_receive/update_shortage_box',
  param,
}, process.env.WIS_FRONT);

// 按订单号收货
export const receiveConfirmPurchaseCodeAPI = (param) => sendPostRequest({
  url: '/pda/handover_receive/receive_purchase_code',
  param,
}, process.env.WIS_FRONT);

// 缺箱-稽查单
export const inspectionShortageBoxAPI = (param) => sendPostRequest({
  url: '/pda/handover_receive/update_purchase_code_shortage_box',
  param,
}, process.env.WIS_FRONT);
