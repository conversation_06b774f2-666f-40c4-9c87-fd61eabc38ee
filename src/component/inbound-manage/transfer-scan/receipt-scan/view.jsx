import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import MotRadio from 'common/mot-radio';
import {
  message,
  FocusInput, Footer, FooterBtn, Header, modal, pages, SelectInput,
} from 'common';
import {
  classFocus,
} from 'lib/util';
import ReportDetail from './jsx/report-detail';
import InspectionDetail from './jsx/inspection-detail';
import store from './reducers';

const { View } = pages;

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      dataLoading,
      subWarehouseName,
      subWarehouseCode,
      receiptDisabled,
      subWarehouseList,
      headerTitle,
      licencePlate, // 车牌号
      palletCode, // 托盘号
      palletCodeShow, // 托盘号信息展示
      handoverTypeName, // 交接类型
      subWarehouseDisabled, // 子仓条码录入是否禁用
      licencePlateInput,
      receiveGoodsSucceed,
      currentPage,
      reportOrderNum,
      waitScanBoxList,
      isContainerReceipt,
      handoverReceiveByBoxFlag,
      totalNum,
      unTakeNum,
      inspectionList,
      selectInspectionIndex,
    } = this.props;

    return (
      <div>
        {currentPage === 2 && <ReportDetail {...this.props} />}
        {currentPage === 3 && <InspectionDetail {...this.props} />}
        {currentPage === 1 && (
          <>
            <Header title={headerTitle || t('收货扫描')} />
            <View flex={false} diff={110}>
              <Form style={{ marginTop: '10px' }}>
                <SelectInput
                  value={subWarehouseName}
                  selectValue={subWarehouseCode}
                  lineBreak={false}
                  disabled={subWarehouseDisabled}
                  label={t('子仓条码')}
                  placeHolder={t('请扫描子仓')}
                  enterShow
                  className="subWarehouse"
                  selectList={subWarehouseList}
                  onSelect={(value) => {
                    const item = subWarehouseList.find((e) => e.value === value);
                    store.changeData({
                      subWarehouseCode: item.code,
                      subWarehouseName: item.name,
                    });
                    store.scanWarehouse({ subWarehouseId: item.value });
                  }}
                />
                {
                  !isContainerReceipt && (
                    <FocusInput
                      placeholder={t('请扫描车牌号')}
                      value={licencePlateInput}
                      className="licencePlate"
                      disabled={dataLoading === 0}
                      onChange={(e) => {
                        store.changeData({ licencePlateInput: e.target.value.trim() });
                        if (/^[\u4e00-\u9fa5A-Za-z0-9]{1,10}$/.test(e.target.value.trim())) {
                          store.changeData({ licencePlateInput: e.target.value.trim() });
                        } else {
                          store.changeData({ licencePlateInput: '' });
                          message.warning(t('车牌号不合法'));
                        }
                      }}
                      onPressEnter={() => {
                        classFocus('palletCode');
                      }}
                    >
                      <label>{t('车牌号')}</label>
                    </FocusInput>
                  )
                }
                <FocusInput
                  placeholder={isContainerReceipt ? t('按箱收货请扫描周转箱号') : t('请扫描周转箱或托盘')}
                  value={palletCode}
                  className="palletCode"
                  disabled={dataLoading === 0}
                  onChange={(e) => store.changeData({ palletCode: e.target.value.trim() })}
                  onPressEnter={() => store.scanPallet()}
                >
                  <label>{t('周转箱')}</label>
                </FocusInput>
              </Form>
              {licencePlate || palletCodeShow ? (
                <div style={{
                  fontSize: 14,
                  color: 'black',
                  padding: '15px',
                }}
                >
                  {t('详细信息')}
                  {!!licencePlate && <div>{t('车牌号')}:{licencePlate}</div>}
                  {!!palletCodeShow && (
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                    }}
                    >
                      <div>{t('托盘号')}:{palletCodeShow}</div>
                      <div>{handoverTypeName}</div>
                    </div>
                  )}
                </div>
              ) : null}
              {!!reportOrderNum && (
                <div style={{
                  display: 'flex',
                  fontSize: 14,
                  justifyContent: 'space-between',
                  padding: '15px',
                }}
                >
                  <div
                    style={{
                      wordBreak: ' break-all',
                      wordWrap: 'break-word',
                    }}
                  >
                    <span style={{ fontWeight: 'bold' }}>{t('报账单')}:</span>
                    <span>{reportOrderNum}</span>
                  </div>
                  <div
                    style={{ color: '#1890ff', cursor: 'pointer' }}
                    onClick={() => store.changeData({ currentPage: 2 })}
                  >
                    {`${waitScanBoxList.filter((_obj) => _obj.status === 1).length}/${waitScanBoxList.length}`}
                  </div>
                </div>
              )}
              {!!(handoverReceiveByBoxFlag) && (
                <div style={{
                  display: 'flex',
                  fontSize: 14,
                  justifyContent: 'space-between',
                  padding: '15px',
                }}
                >
                  <div
                    style={{
                      wordBreak: ' break-all',
                      wordWrap: 'break-word',
                    }}
                  >
                    {t('实收/应收')}
                  </div>
                  <div>
                    <span style={{ color: 'orange' }}>{(totalNum || 0) - (unTakeNum || 0)}</span>
                    <span>/</span>
                    <span>{totalNum || 0}</span>
                  </div>
                </div>
              )}
              {inspectionList?.map((item, index) => (
                <div style={{
                  display: 'flex',
                  fontSize: 14,
                  justifyContent: 'space-between',
                  padding: '15px',
                }}
                >
                  <MotRadio
                    checked={selectInspectionIndex === index}
                    onChange={() => {
                      store.changeData({ selectInspectionIndex: index });
                    }}
                    name={(
                      <div
                        style={{
                          wordBreak: ' break-all',
                          wordWrap: 'break-word',
                        }}
                      >
                        <span style={{ fontWeight: 'bold' }}>{t('订单号')}:</span>
                        <span>{item.originId}</span>
                      </div>
                          )}
                  />
                  <div
                    style={{ color: '#1890ff', cursor: 'pointer' }}
                    onClick={() => store.changeData({
                      currInspectionDetailIndex: index,
                      currentPage: 3,
                    })}
                  >
                    {`${item.waitScanBoxList?.filter((_obj) => _obj.status === 1).length}/${item.waitScanBoxList.length}`}
                  </div>
                </div>
              ))}
              {receiveGoodsSucceed ? (
                <h2 style={{
                  textAlign: 'center', fontWeight: 100, padding: '20px 0 ', color: '#04c75f',
                }}
                >{t('收货成功!')}
                </h2>
              ) : ''}
            </View>
            <Footer
              beforeBack={(back) => {
                if (palletCode) {
                  modal.confirm({
                    content: t('是否返回?'),
                    onOk: () => back(),
                  });
                  return;
                }
                back();
              }}
            >
              <FooterBtn
                disabled={
                  receiptDisabled
                  || dataLoading === 0
                  || (inspectionList?.length && selectInspectionIndex < 0)
                }
                onClick={() => {
                  if (reportOrderNum) {
                    store.receiptGoodsFob();
                  } else if (inspectionList?.length) {
                    store.receiptGoodsInspection();
                  } else {
                    store.receiptGoods();
                  }
                }}
              >
                {t('收货')}
              </FooterBtn>
            </Footer>
          </>
        )}
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  subWarehouseCode: PropTypes.string,
  subWarehouseName: PropTypes.string,
  palletCode: PropTypes.string,
  palletCodeShow: PropTypes.string,
  handoverTypeName: PropTypes.string,
  licencePlate: PropTypes.string,
  dataLoading: PropTypes.number,
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  receiptDisabled: PropTypes.bool,
  subWarehouseDisabled: PropTypes.bool,
  licencePlateInput: PropTypes.string,
  receiveGoodsSucceed: PropTypes.bool,
  currentPage: PropTypes.number,
  reportOrderNum: PropTypes.number,
  waitScanBoxList: PropTypes.arrayOf(PropTypes.shape()),
  isContainerReceipt: PropTypes.bool,
  handoverReceiveByBoxFlag: PropTypes.bool,
  totalNum: PropTypes.bool,
  unTakeNum: PropTypes.bool,
  inspectionList: PropTypes.arrayOf(PropTypes.shape()),
  selectInspectionIndex: PropTypes.number,
};

export default i18n(Container);
