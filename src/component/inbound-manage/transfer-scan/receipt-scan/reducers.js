import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import {
  classFocus, getHeaderTitle, getWarehouseId, getUsername,
} from 'lib/util';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { message, modal } from 'common';
import error from 'source/audio/delete.mp3';
import {
  receiveConfirmAPI,
  scanWarehouseAPI,
  scanPalletCodeAPI,
  receiveConfirmFobAPI,
  shortageBoxAPI,
  receiveConfirmPurchaseCodeAPI,
  inspectionShortageBoxAPI,
} from './server';
import { getBindSubWarehouseApi } from '../../../return/return-down/server';
import { getConfigByCodeApi } from '../../../../server/basic/common';

const audio = new Audio(error);
audio.load();
const defaultState = {
  headerTitle: t('收货扫描'),
  dataLoading: 1,
  subWarehouseCode: '', // 子仓编码
  subWarehouseName: '', // 子仓名称
  subWarehouseList: [], // 子仓列表
  subWarehouseDisabled: false, // 子仓条码录入是否禁用
  palletCode: '', // 托盘号
  // 展示数据
  palletCodeShow: '', // 托盘号
  licencePlate: '', // 车牌号
  handoverTypeName: '', // 交接类型
  licencePlateInput: '', // 录入车牌号
  receiveGoodsSucceed: false, // 是否展示收货成功提示语
  currentPage: 1, // 1-默认页，2-报账单明细
  waitScanBoxList: [], // 报账单号-应收箱明细 -箱号
  hasScanBoxList: [], // 已经扫描的
  reportOrderNum: '', // 报账单号
  waitScanBoxNum: 0, // 报账单箱子数
  receiptByBoxConfig: [],
  isContainerReceipt: false, // 是否配置了按箱收货
  handoverReceiveByBoxFlag: false, // 是否为按箱收货-后端返回标识
  totalNum: 0, // 总箱数
  unTakeNum: 0, // 待收箱数
  inspectionList: [], // 稽查订单列表
  currInspectionDetailIndex: 0, // 当前进入的稽查单明细索引
  selectInspectionIndex: -1, // 选中的收货订单
};

// eslint-disable-next-line max-len
const hasScanInspectionContainer = (containerCode, inspectionList) => inspectionList?.some((insItem) => insItem?.waitScanBoxList?.some((item) => item.containerCode === containerCode && item.status === 1));

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action);
  },
  * init(action, ctx) {
    classFocus('subWarehouse');
    yield ctx.changeData({ headerTitle: getHeaderTitle() });
    yield ctx.getSubWarehouseList(); // 获取子仓列表
    yield this.getReceiptTypeConfig();
  },
  // 重置数据
  * clearData() {
    const { subWarehouseList } = yield '';
    yield this.changeData({
      ...defaultState,
      subWarehouseList: [...subWarehouseList],
    });
    classFocus('subWarehouse');
  },
  * getSubWarehouseList() {
    const { code, info, msg } = yield getBindSubWarehouseApi({
      id: getWarehouseId(),
      userName: getUsername(),
    });
    if (code === '0') {
      const subWarehouseList = info.data.map((i) => ({
        name: i.subWarehouseName,
        value: i.subWarehouseId,
        code: i.code,
      }));
      yield this.changeData({ subWarehouseList });
    } else {
      audio.play();
      modal.error({
        content: msg,
        className: 'subWarehouse',
      });
    }
  },
  // 扫描仓库条码
  * scanWarehouse(action, ctx) {
    const {
      subWarehouseCode,
      receiptByBoxConfig,
    } = yield '';
    const {
      code,
      msg,
    } = yield scanWarehouseAPI({ subWarehouseCode });
    if (code === '0') {
      // 判断选中的子仓是否配置按箱收货
      const { subWarehouseId } = action;
      yield this.changeData({ isContainerReceipt: receiptByBoxConfig?.includes(`${subWarehouseId}`) });
      yield ctx.changeData({ subWarehouseDisabled: true });
      classFocus('palletCode');
    } else {
      audio.play();
      yield ctx.changeData({ // 清空已录入的子仓
        subWarehouseCode: '',
        subWarehouseName: '',
      });
      modal.error({
        content: msg,
        className: 'subWarehouse',
      });
    }
  },
  // 扫描托盘号
  * scanPallet(action, ctx) {
    const {
      waitScanBoxList,
      palletCode,
      reportOrderNum,
      inspectionList,
    } = yield '';
    if (
      waitScanBoxList.some((item) => item.containerCode === palletCode && item.status === 1)
      || hasScanInspectionContainer(palletCode, inspectionList)
    ) {
      audio.play();
      modal.error({
        content: t('请不要重复扫描'),
        className: 'palletCode',
      });
      yield ctx.changeData({
        palletCode: '',
      });
      return;
    }
    markStatus('dataLoading'); // 搜索loading状态
    const {
      confirmDifferentReceiptShelvesPark,
      reportOrderNumFlag = false,
      confirmDifferentReceiptSubWarehouse = false,
    } = action || {};
    const {
      subWarehouseCode,
      // palletCode,
      licencePlateInput,
    } = yield '';
    const params = {
      subWarehouseCode,
      palletCode,
      licencePlate: licencePlateInput,
      confirmDifferentReceiptShelvesPark,
      reportOrderNum,
      reportOrderNumFlag,
      confirmDifferentReceiptSubWarehouse,
      originNumber: inspectionList?.length || 0,
      originId: inspectionList?.map((item) => item.originId) || [],
    };
    const {
      code,
      info,
      msg,
    } = yield scanPalletCodeAPI(params);
    // 提示：目的子仓不在推荐园区%%内，请确认是否继续发货？
    if (['400915', '400916'].includes(code)) {
      const flag = yield new Promise((r) => {
        modal.confirm({
          content: msg,
          onOk: () => r('ok'),
          onCancel: () => r('cancel'),
        });
      });
      if (flag === 'ok') {
        yield this.scanPallet({
          ...params,
          confirmDifferentReceiptShelvesPark: true, // 确认(不校验目的子仓不在推荐园区)
        });
      } else {
        yield this.changeData({
          palletCode: '', // 清空托盘号
        });
        classFocus('palletCode');
      }
    } else if (code === '0') {
      const {
        licencePlate,
        handoverTypeName,
        orderType,
        type,
        unTakeNum,
        replenishOrderType,
        handoverType,
        waitScanBoxNum,
        handoverReceiveByBoxFlag,
        totalNum,
        tuerInspectionQualified,
      } = info;
      // 走按箱收货
      if (handoverReceiveByBoxFlag) {
        yield ctx.changeData({
          licencePlate,
          handoverReceiveByBoxFlag: true,
          unTakeNum,
          totalNum,
          receiveGoodsSucceed: true,
        });
        yield ctx.changeData({ palletCode: '' }); // 土耳其的直接清空 不用等点收货了
        classFocus('palletCode');
        return;
      }
      if (tuerInspectionQualified) {
        message.success(t('该箱号稽查结果合格，请交接到调拨区'));
      }
      // 稽查订单提示用
      if (info?.tkInspectionFlag) {
        message.error(t('稽查订单'));
      }
      // 如果不是土耳其二次收货 则弹框
      if (handoverType !== 5) {
        const status = yield new Promise((r) => modal.confirm2({
          content: `${t('待确认数量为{}？', unTakeNum)}`,
          onOk: () => r('ok'),
          onCancel: () => r('error'),
        }));
        if (status === 'ok') {
          yield ctx.changeData({
            licencePlate,
            palletCodeShow: info.palletCode,
            handoverTypeName, // 交接类型
            orderType,
            type,
            unTakeNum,
            replenishOrderType,
            reportOrderNum: info?.reportOrderNum,
            waitScanBoxNum,
            handoverReceiveByBoxFlag: !!handoverReceiveByBoxFlag,
          });
          if (!info?.reportOrderNum) {
            yield this.changeData({
              waitScanBoxList: [],
              hasScanBoxList: [],
            });
          } else {
            yield this.addScanList({ id: params.palletCode, list: info?.waitScanBoxList || [] });
          }
          // 按照稽查订单收货
          if (info?.originId) {
            yield this.addInspectionScanList({
              id: params.palletCode,
              list: info?.waitScanBoxList || [],
              originId: info.originId,
            });
          }
          classFocus('palletCode');
        } else {
          modal.info({ content: t('箱数不正确请联系异常员处理！') });
          yield ctx.changeData({ palletCode: '' });
        }
      } else {
        yield ctx.changeData({
          licencePlate,
          palletCodeShow: info.palletCode,
          handoverTypeName, // 交接类型
          orderType,
          type,
          unTakeNum,
          replenishOrderType,
          receiveGoodsSucceed: !info?.reportOrderNum && !info?.originId, // 是否展示收货成功提示语
          reportOrderNum: info?.reportOrderNum,
          waitScanBoxNum,
          handoverReceiveByBoxFlag: !!handoverReceiveByBoxFlag,
        });
        if (!info?.reportOrderNum) {
          yield this.changeData({
            waitScanBoxList: [],
            hasScanBoxList: [],
          });
        } else {
          yield this.addScanList({ id: params.palletCode, list: info?.waitScanBoxList || [] });
        }
        // 稽查订单
        if (info?.originId) {
          yield this.addInspectionScanList({
            id: params.palletCode,
            list: info?.waitScanBoxList || [],
            originId: info.originId,
          });
        }
        yield ctx.changeData({ palletCode: '' }); // 土耳其的直接清空 不用等点收货了
        classFocus('palletCode');
      }
    } else {
      audio.play();
      // 当前有操作中的报账单
      if (code === '400917') {
        const flag = yield new Promise((r) => {
          modal.confirm({
            content: msg,
            onOk: () => r(1),
            onCancel: () => r(0),
          });
        });
        if (flag) {
          // 收货逻辑
          yield this.changeData({
            waitScanBoxList: [],
            hasScanBoxList: [],
            reportOrderNum: '',
          });
          yield this.scanPallet({ ...params, reportOrderNumFlag: true });
        } else {
          yield this.changeData({ palletCode: '' });
          classFocus('palletCode');
        }
        return;
      }
      // 计划卸货子仓与当前选择收货子仓不相同，需要二次确认
      if (code === '400918') {
        const flag = yield new Promise((r) => {
          modal.confirm({
            content: msg,
            onOk: () => r(1),
            onCancel: () => r(0),
          });
        });
        if (flag) {
          yield this.scanPallet({ ...params, confirmDifferentReceiptSubWarehouse: true });
        } else {
          // 重置页面信息
          yield this.clearData();
        }
        return;
      }
      if (code === '400255') {
        message.error(t('该箱号稽查结果不合格，请交接到退供区'));
        yield ctx.changeData({
          palletCode: '',
          receiveGoodsSucceed: false,
        });
        return;
      }
      yield ctx.changeData({
        palletCode: '',
        receiveGoodsSucceed: false,
      });
      modal.error({
        content: msg,
        className: 'palletCode',
      });
    }
  },
  // 收货
  * receiptGoods(_, ctx) {
    const {
      subWarehouseCode,
      palletCodeShow,
      licencePlateInput,
    } = yield '';
    const params = {
      subWarehouseCode,
      palletCode: palletCodeShow,
      licencePlate: licencePlateInput,
    };
    const {
      code,
      msg,
    } = yield receiveConfirmAPI(params);
    if (code === '0') {
      message.success(t('收货成功'));
      yield ctx.changeData({
        palletCode: '',
        containerNum: '',
        waitScanBoxList: [],
        hasScanBoxList: [],
        reportOrderNum: '',
        waitScanBoxNum: 0,
      });
    } else {
      audio.play();
      yield ctx.changeData({
        palletCode: '',
      });
      modal.error({
        content: msg,
        className: 'palletCode',
      });
    }
  },

  // FOB收货
  * receiptGoodsFob(_, ctx) {
    const {
      subWarehouseCode,
      licencePlateInput,
      waitScanBoxList,
    } = yield '';
    const hasScanList = waitScanBoxList.filter((item) => item.status === 1);
    const containerCodeList = hasScanList.map((item) => item.containerCode);
    const params = {
      subWarehouseCode,
      licencePlate: licencePlateInput,
      containerCodeList,
    };
    markStatus('dataLoading');
    const {
      code,
      msg,
    } = yield receiveConfirmFobAPI(params);
    if (code === '0') {
      message.success(t('收货成功'));
      yield ctx.changeData({
        palletCode: '',
        containerNum: '',
        waitScanBoxList: [],
        reportOrderNum: '',
        hasScanBoxList: [],
        waitScanBoxNum: 0,
        receiveGoodsSucceed: true,
      });
    } else {
      audio.play();
      yield this.clearData();
      modal.error({
        content: msg,
        className: 'subWarehouse',
      });
    }
  },

  // 添加已扫描箱号记录
  * addScanList(action) {
    const { id, list } = action;
    const { hasScanBoxList } = yield '';
    const newHasScan = [...new Set([...hasScanBoxList, id])];
    const waitList = list.map((item) => ({
      containerCode: item,
      status: newHasScan.includes(item) ? 1 : 0,
    }));
    yield this.changeData({
      hasScanBoxList: newHasScan,
      waitScanBoxList: [...waitList],
    });
  },

  // 处理缺箱
  * handleMissBox(action) {
    const { id } = action;
    const { reportOrderNum } = yield '';
    markStatus('dataLoading');
    const res = yield shortageBoxAPI({
      containerCode: id,
      reportOrderNum,
      type: 1,
      shortageBoxType: 1,
    });
    if (res.code === '0') {
      const { waitScanBoxList } = yield '';
      const newList = [...waitScanBoxList];
      const index = newList.findIndex((item) => item.containerCode === id);
      if (index >= 0) {
        newList.splice(index, 1);
        yield this.changeData({ waitScanBoxList: [...newList] });
      }
    } else {
      modal.error({
        content: res.msg,
        className: '',
      });
    }
  },

  // 获取收货开关配置
  * getReceiptTypeConfig() {
    markStatus('dataLoading');
    const res = yield getConfigByCodeApi({ param: 'RECEIVEHANDOVER_SCAN_RECEIVE_BY_BOX_SWITCH' });
    if (res.code === '0') {
      const configValue = res?.info?.configValue?.split(',') || [];
      yield this.changeData({
        receiptByBoxConfig: configValue,
      });
    } else {
      modal.error({
        content: res.msg,
        className: '',
      });
    }
  },

  // 稽查单箱明细
  * addInspectionScanList(action) {
    const { id, list, originId } = action;
    const { inspectionList } = yield '';
    const myInspectionList = [...inspectionList];
    const currObj = myInspectionList.find((obj) => obj.originId === originId);
    const { hasScanBoxList = [] } = currObj || {};
    const newHasScan = [...new Set([...hasScanBoxList, id])];
    const waitList = list.map((item) => ({
      containerCode: item,
      status: newHasScan.includes(item) ? 1 : 0,
      originId,
    }));
    if (currObj) {
      currObj.hasScanBoxList = newHasScan;
      currObj.waitScanBoxList = [...waitList];
      yield this.changeData({
        inspectionList: [...myInspectionList],
      });
    } else {
      yield this.changeData({
        inspectionList: [
          ...myInspectionList,
          {
            originId,
            hasScanBoxList: newHasScan,
            waitScanBoxList: [...waitList],
          },
        ],
      });
    }
  },

  // 稽查单处理缺箱
  * handleInspectionMissBox(action) {
    const { id, originId } = action;
    markStatus('dataLoading');
    const res = yield inspectionShortageBoxAPI({
      containerCode: id,
      type: 1,
      shortageBoxType: 2,
      originId,
    });
    if (res.code === '0') {
      const { inspectionList, currInspectionDetailIndex } = yield '';
      const myInspectionList = [...inspectionList];
      const currObj = myInspectionList[currInspectionDetailIndex] || {};
      const { waitScanBoxList } = currObj;
      const newList = [...waitScanBoxList];
      const index = newList.findIndex((item) => item.containerCode === id);
      if (index >= 0) {
        newList.splice(index, 1);
        currObj.waitScanBoxList = [...newList];
        yield this.changeData({
          inspectionList: [...myInspectionList],
        });
      }
    } else {
      modal.error({
        content: res.msg,
        className: '',
      });
    }
  },

  // 稽查单收货
  * receiptGoodsInspection(_, ctx) {
    const {
      subWarehouseCode,
      licencePlateInput,
      inspectionList,
      selectInspectionIndex,
    } = yield '';
    const { waitScanBoxList = [] } = inspectionList[selectInspectionIndex] || {};
    const hasScanList = waitScanBoxList.filter((item) => item.status === 1);
    const containerCodeList = hasScanList.map((item) => item.containerCode);
    const params = {
      subWarehouseCode,
      licencePlate: licencePlateInput,
      containerCodeList,
    };
    markStatus('dataLoading');
    const {
      code,
      msg,
    } = yield receiveConfirmPurchaseCodeAPI(params);
    if (code === '0') {
      message.success(t('收货成功'));
      const newInspectionList = [...inspectionList];
      newInspectionList.splice(selectInspectionIndex, 1);
      yield ctx.changeData({
        palletCode: '',
        containerNum: '',
        receiveGoodsSucceed: true,
        inspectionList: [...newInspectionList],
        selectInspectionIndex: -1,
      });
    } else {
      audio.play();
      yield this.changeData({
        palletCode: '',
      });
      modal.error({
        content: msg,
        className: 'palletCode',
      });
    }
  },
};
