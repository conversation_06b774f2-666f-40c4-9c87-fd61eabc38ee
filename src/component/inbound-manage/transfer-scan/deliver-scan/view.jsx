import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { CellsTitle } from 'react-weui/build/packages';
import {
  Header,
  Footer,
  FocusInput,
  RowInfo,
  FooterBtn,
  SelectInput,
} from 'common';
import store from './reducers';
import style from '../../../style.css';
import styles from './style.less';
import navStore from '../../../nav/reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
    this.onresize = () => requestAnimationFrame(() => this.forceUpdate());
    window.addEventListener('resize', this.onresize);
    navStore.changeData({ data: { pageStore: store } });
  }

  componentDidUpdate() {
    navStore.changeData({ data: { pageStore: store } });
  }

  render() {
    const {
      handoverCode, // 交接单号
      licencePlate,
      licencePlateDisabled,
      palletDisabled,
      count,
      palletCode,
      headerTitle,
      handoverTypeName,
      containerNum,
      sendDisabled,
      scanedPalletCode,
      loading,
      subWarehouseList,
      subWarehouseName,
      subWarehouseCode,
      subWarehouseDisabled,
      shelvesParkName,
      dischargeSubWarehouseList,
    } = this.props;

    const height = window.innerHeight - 66;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <Header title={headerTitle || t('发货扫描')} />
        <Form>
          <RowInfo
            extraStyle={{
              borderBottom: 'none',
            }}
            label={t('已发货数')}
            content={count}
          />
        </Form>
        <Form style={{ marginTop: '10px' }}>
          <FocusInput
            placeholder={t('请扫描车牌')}
            autoFocus
            disabled={licencePlateDisabled || loading === 0}
            data-bind="licencePlate"
            className="licencePlate"
            onChange={(e) => {
              store.changeData({
                licencePlate: e.target.value?.trim()?.slice(0, 50),
              });
            }}
            onPressEnter={() => {
              if (!licencePlate?.trim()) return;
              store.scanLicencePlate({
                licencePlate: licencePlate?.trim(),
              });
            }}
          >
            <label>{t('车牌')}</label>
          </FocusInput>
          <SelectInput
            value={subWarehouseName}
            selectValue={subWarehouseCode}
            lineBreak={false}
            disabled={subWarehouseDisabled}
            label={t('子仓条码')}
            placeHolder={t('请扫描子仓')}
            enterShow
            className="subWarehouse"
            selectList={
            dischargeSubWarehouseList.length ? dischargeSubWarehouseList : subWarehouseList
           }
            onSelect={(value) => {
              // eslint-disable-next-line max-len
              const item = (dischargeSubWarehouseList.length ? dischargeSubWarehouseList : subWarehouseList)
                .find((e) => e.value === value);
              store.changeData({
                subWarehouseCode: item.code,
                subWarehouseName: item.name,
              });
              store.scanWarehouse();
            }}
          />
          <FocusInput
            placeholder={t('请扫描周转箱/包裹或托盘')}
            disabled={palletDisabled || loading === 0}
            data-bind="palletCode"
            className="palletCode"
            onChange={(e) => store.changeData({ palletCode: e.target.value.trim()?.toUpperCase() })}
            onPressEnter={() => {
              if (!palletCode?.trim()) return;
              store.changeData({ palletCode: palletCode.trim().toUpperCase() });
              store.scanPalletCode({
                palletCode: palletCode.trim().toUpperCase(),
                confirmDifferentDeliveryShelvesPark: false, // 校验目的子仓不在推荐园区, 不传同样false
              });
            }}
          >
            <label>{t('周转箱')}</label>
          </FocusInput>
        </Form>
        {/* 信息展示部分 */}
        <CellsTitle>{t('信息')}</CellsTitle>
        <div className={styles.infoContainer}>
          <div className={styles.section} style={{ padding: '0 15px' }}>
            <div>{t('交接单')}：{handoverCode}</div>
            <div className={styles.rightItem}>{handoverTypeName}</div>
          </div>
          {
            scanedPalletCode && (
              <div className={styles.section} style={{ padding: '0 15px' }}>
                <div>{t('托盘号')}：{scanedPalletCode}</div>
                <div className={styles.rightItem}><strong>{containerNum}{t('箱子')}</strong></div>
              </div>
            )
          }
          {
            shelvesParkName && (
              <div className={styles.section} style={{ padding: '0 15px' }}>
                <div>{t('推荐上架园区')}：{shelvesParkName}</div>
              </div>
            )
          }
        </div>
        <Footer beforeBack={(back) => store.back(back)}>
          <FooterBtn
            disabled={sendDisabled}
            onClick={() => store.sendGoods({ content: t('发货后不允许继续装车，是否确认发货?') })}
          >
            {t('发货')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  count: PropTypes.number,
  loading: PropTypes.number,
  licencePlate: PropTypes.string, // 周转箱号
  licencePlateDisabled: PropTypes.bool, // 是否禁用周转型号
  palletCode: PropTypes.string, // 托盘号
  palletDisabled: PropTypes.bool, // 是否禁用托盘号
  containerNum: PropTypes.number, // 托盘上的箱数
  handoverCode: PropTypes.string, // 交接单号
  handoverTypeName: PropTypes.string, // 交接类型 - 名称
  sendDisabled: PropTypes.bool, // 发货是否可点击
  scanedPalletCode: PropTypes.string, // 周转箱号是否已扫描
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseCode: PropTypes.string,
  subWarehouseName: PropTypes.string,
  subWarehouseDisabled: PropTypes.bool,
  shelvesParkName: PropTypes.string,
  dischargeSubWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Container);
