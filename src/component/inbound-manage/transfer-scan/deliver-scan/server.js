import { sendPostRequest } from '../../../../lib/public-request';

// 进入界面 - 获取交接单号
export const deliveryScanAPI = (param) => sendPostRequest({
  url: '/pda/handover_delivery/delivery_scan',
  param,
}, process.env.WIS_FRONT);

// 扫描托盘
export const scanPalletCodeAPI = (param) => sendPostRequest({
  url: '/pda/handover_delivery/scan_pallet',
  param,
}, process.env.WIS_FRONT);

// 发货确认
export const clickShipAPI = (param) => sendPostRequest({
  url: '/pda/handover_delivery/click_ship',
  param,
}, process.env.WIS_FRONT);

// 扫描车牌号
export const scanLicencePlateAPI = (param) => sendPostRequest({
  url: '/pda/handover_delivery/scan_license_plate',
  param,
}, process.env.WIS_FRONT);

// 扫描子仓
export const scanWarehouseAPI = (param) => sendPostRequest({
  url: '/pda/handover_receive/scan_warehouse',
  param,
}, process.env.WIS_FRONT);

/**
 * 获取绑定的子仓
 * @param param
 * @returns {*}
 */
export const getBindSubWarehouseAPI = (param) => sendPostRequest({
  url: '/sub_warehouse_user/get_bind_sub_warehouse',
  param,
}, process.env.WIS_FRONT);
