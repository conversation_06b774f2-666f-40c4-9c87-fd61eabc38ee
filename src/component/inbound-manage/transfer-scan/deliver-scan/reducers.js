import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { modal, message } from 'common';
import {
  classFocus, getHeaderTitle, getParentHref, getWarehouseId, getUsername,
} from 'lib/util';
import error from 'source/audio/delete.mp3';
import { push } from 'react-router-redux';
import {
  deliveryScanAPI, scanPalletCodeAPI, clickShipAPI, scanLicencePlateAPI,
  scanWarehouseAPI, getBindSubWarehouseAPI,
} from './server';

const audio = new Audio(error);
audio.load();

const defaultState = {
  loading: 1, // 正在请求中时，禁用按钮
  headerTitle: t('发货扫描'), // 页面标题
  count: 0, // 已发货数[前端自己计算累加]
  handoverCode: '', // 交接单号
  licencePlate: '', // 车牌号
  licencePlateDisabled: false, // 车牌号是否置灰
  palletCode: '', // 托盘号
  scanedPalletCode: '', // 已扫描的托盘号
  palletDisabled: true, // 托盘号是否置灰
  containerNum: 0, // 托盘上周转箱数量
  handoverType: 0, // 交接类型
  handoverTypeName: '', // 交接类型
  sendDisabled: true, // 进入是禁用
  subWarehouseDisabled: false, // 子仓条码录入是否禁用
  subWarehouseCode: '', // 卸货子仓编码
  subWarehouseName: '', // 子仓名称
  subWarehouseList: [], // 子仓列表
  shelvesParkName: '', // 推荐上架园区
  departureSubWarehouseId: '', // 出发仓
  transportBillNo: '', // 执行单号
  dischargeSubWarehouseList: [], // 扫车牌返回的卸货子仓
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action);
  },
  * init(action, ctx, put) {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }
    yield ctx.changeData({
      headerTitle: getHeaderTitle(),
      departureSubWarehouseId: preSubMenu?.subWarehouseId, // 填充出发子仓
    });
    yield ctx.getHandoverCode(); // 获取交接单号
    yield ctx.getSubWarehouseList(); // 获取子仓列表
  },
  * getSubWarehouseList(_, ctx) {
    const {
      code,
      info,
      msg,
    } = yield getBindSubWarehouseAPI({
      id: getWarehouseId(),
      userName: getUsername(),
    });
    if (code === '0') {
      const subWarehouseList = info.data.map((i) => ({
        name: i.subWarehouseName,
        value: i.code,
        code: i.code,
      }));
      yield ctx.changeData({ subWarehouseList });
    } else {
      audio.play();
      modal.error({
        content: msg,
        className: 'subWarehouse',
      });
    }
  },
  // 选择子仓
  * scanWarehouse(_, ctx) {
    const {
      subWarehouseCode,
    } = yield '';
    const {
      code,
      msg,
    } = yield scanWarehouseAPI({ subWarehouseCode });
    if (code === '0') {
      yield ctx.changeData({ subWarehouseDisabled: true });
      classFocus('palletCode');
    } else {
      audio.play();
      yield ctx.changeData({ // 清空已录入的子仓
        subWarehouseCode: '',
        subWarehouseName: '',
      });
      modal.error({
        content: msg,
        className: 'subWarehouse',
      });
    }
  },
  // 获取交接单号
  * getHandoverCode() {
    const { code, info, msg } = yield deliveryScanAPI();
    if (code === '0') {
      yield this.changeData({
        handoverCode: info?.handoverCode,
      });
    } else {
      modal.error({ content: msg });
    }
  },
  // 扫描车牌号
  * scanLicencePlate(action) {
    const { licencePlate } = action;
    // 是否字母或数字
    const isValid = /^[A-Za-z0-9]+$/.test(licencePlate);
    if (licencePlate.length > 10 || !isValid) {
      message.error(t('请输入正确的车牌号'), 3000, true);
      audio.play();
      yield this.changeData({
        licencePlate: '',
      });
      classFocus('licencePlate');
      return;
    }
    const { departureSubWarehouseId } = yield '';
    markStatus('loading');
    const { code, msg, info } = yield scanLicencePlateAPI({
      licencePlate,
      handoverWorkSubWarehouseId: departureSubWarehouseId,
    });
    if (code === '0') {
      // 车牌号码正确 - 禁用车牌号，可扫描托盘
      yield this.changeData({
        licencePlateDisabled: true,
        palletDisabled: false,
        transportBillNo: info?.transportBillNo,
        dischargeSubWarehouseList: (info?.dischargeWarehouseList || []).map((wi) => ({
          value: wi.subWarehouseCode,
          code: wi.subWarehouseCode,
          name: wi.subWarehouseName,
        })),
      });
      classFocus('subWarehouse');
    } else {
      yield this.changeData({
        licencePlate: '',
      });
      modal.error({ content: msg, className: 'licencePlate' });
    }
  },
  // 扫描托盘号
  * scanPalletCode(action) {
    const { palletCode, confirmDifferentDeliveryShelvesPark } = action;
    const {
      licencePlate, handoverCode, count, transportBillNo, subWarehouseCode, departureSubWarehouseId,
    } = yield '';
    const params = {
      licencePlate,
      palletCode,
      handoverCode,
      transportBillNo,
      subWarehouseCode,
      handoverWorkSubWarehouseId: departureSubWarehouseId,
      confirmDifferentDeliveryShelvesPark,
    };
    markStatus('loading');
    const { code, msg, info } = yield scanPalletCodeAPI(params);
    // 提示：目的子仓不在推荐园区%%内，请确认是否继续发货？
    if (['400915', '400916'].includes(code)) {
      const flag = yield new Promise((r) => {
        modal.confirm({
          content: msg,
          onOk: () => r('ok'),
          onCancel: () => r('cancel'),
        });
      });
      if (flag === 'ok') {
        yield this.scanPalletCode({
          ...params,
          confirmDifferentDeliveryShelvesPark: true, // 确认(不校验目的子仓不在推荐园区)
        });
      } else {
        yield this.changeData({
          palletCode: '', // 清空托盘号
        });
        classFocus('palletCode');
      }
    } else if (code === '0') {
      yield this.changeData({
        palletCode: '', // 清空托盘号
        scanedPalletCode: info?.palletCode, // 托盘号已扫描
        palletDisabled: false,
        containerNum: info?.containerNum, // 托盘上周转箱数量
        handoverType: info?.handoverType, // 交接类型
        handoverTypeName: info?.handoverTypeName, // 交接类型名称
        replenishOrderType: info?.replenishOrderType, // 补货类型
        count: count + (info?.containerNum || 0), // 已发货数量 + 1
        sendDisabled: false, // 可发货
        shelvesParkName: info?.shelvesParkName, // 上架园区name
      });
      classFocus('palletCode');
    } else if (['400900', '400901', '400902'].includes(code)) {
      message.error(msg, 3000, true);
      audio.play();
      yield this.changeData({
        palletCode: '',
      });
      classFocus('palletCode');
    } else {
      yield this.changeData({
        palletCode: '',
      });
      modal.error({ content: msg, className: 'palletCode' });
    }
  },
  // 发货
  * sendGoods(action) {
    const { content } = action;
    const status = yield new Promise((r) => {
      modal.confirm({
        content,
        onOk: () => r(true),
        onCancel: () => r(false),
      });
    });
    if (!status) {
      classFocus('palletCode');
      return;
    }

    // 确认发货逻辑
    const { handoverCode, departureSubWarehouseId } = yield '';
    const { code, msg } = yield clickShipAPI({
      handoverCode,
      handoverWorkSubWarehouseId: departureSubWarehouseId,
    });
    if (code === '0') {
      yield this.changeData({
        scanedPalletCode: '',
      });
      // 提示发货成功，并返回首页
      message.success(t('发货成功'));
      window.location.href = `#${getParentHref()}`;
    } else if (code === '400907') {
      message.error(msg);
      window.location.href = `#${getParentHref()}`;
    } else {
      modal.error({ content: msg });
    }
  },
  * back(back) {
    const { scanedPalletCode } = yield '';
    if (scanedPalletCode) {
      yield this.sendGoods({
        content: t('是否返回?'),
      });
      return;
    }
    back();
  },
};
