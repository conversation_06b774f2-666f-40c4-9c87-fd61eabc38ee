import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import { Form } from 'react-weui/build/packages/components/form';
import { Button } from 'react-weui/build/packages/components/button';
import Icon from '@shein-components/Icon';
import store from '../reducers';
import Footer from '../../../common/footer';
import FocusInput from '../../../common/focus-input';
import RowInfo from '../../../common/row-info';
import Modal from '../../../common/modal';
import style from '../../binning-by-goods/style.css';

const Container = (props) => {
  const {
    dispatch,
    boxNo,
    barCode,
    binningNum,
    boxDisabled,
    skcDisabled,
    btnDisabled,
    containerCategory,
  } = props;
  return (
    <div>
      <Form style={{
        paddingTop: '10px',
        marginBottom: '5px',
      }}
      >
        <FocusInput
          placeholder={t('请输入')}
          className="boxNo"
          data-bind="boxNo"
          autoFocus
          disabled={boxDisabled}
          onPressEnter={(e) => {
            if (e.target.value) {
              store.scanBoxNo({
                params: {
                  boxNo,
                },
              });
            }
          }}
          footer={
            (
              <Button
                style={{ marginBottom: 5 }}
                disabled={btnDisabled}
                size="small"
                onClick={() => {
                  Modal.confirm({
                    content: t('确定需要关箱吗？'),
                    onOk: () => store.closeBox({
                      params: {
                        boxNo,
                      },
                    }),
                  });
                }}
              >
                {t('关箱')}
              </Button>
            )
          }
        >
          <label style={{ paddingBottom: '10px',}}>
            <span>{t('大箱号')}</span>
            {containerCategory && <span className={style.packageTypeClass}>{containerCategory}</span>}
          </label>
        </FocusInput>
      </Form>
      <Form style={{
        marginBottom: '5px',
      }}
      >
        <div
          onClick={() => {
            if (binningNum > 0) {
              store.changeData({
                data: {
                  pageStatus: 'detail',
                },
              });
            }
          }}
        >
          <RowInfo
            label={t('已装箱商品数量')}
            content={<div style={{ color: '#337cf2' }}>{binningNum}<Icon name="arr-right" /></div>}
          />
        </div>
      </Form>
      <Form style={{
        marginBottom: '5px',
      }}
      >
        <FocusInput
          placeholder={t('请输入')}
          className="barCode"
          data-bind="barCode"
          disabled={skcDisabled === 0}
          onPressEnter={(e) => {
            if (e.target.value) {
              store.scanBarCode({
                params: {
                  boxNo,
                  barCode,
                },
              });
            }
          }}
        >
          <label>{t('商品条码')}</label>
        </FocusInput>
      </Form>
      <Footer />
    </div>
  );
};

Container.propTypes = {
  boxNo: PropTypes.string,
  barCode: PropTypes.string,
  binningNum: PropTypes.number,
  boxDisabled: PropTypes.bool,
  btnDisabled: PropTypes.bool,
  skcDisabled: PropTypes.number,
};

export default Container;
