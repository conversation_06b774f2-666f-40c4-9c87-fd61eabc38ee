import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import Icon from '@shein-components/Icon';
import Footer from '../../../common/footer';
import store from '../reducers';
import style from '../style.css';
import { classFocus } from '../../../../lib/util';
import Modal from '../../../common/modal';
import pages from '../../../common/pages';

const { View } = pages;

function List(props) {
  const {
    detailList,
  } = props;
  return (
    <div>
      <View diff={110} flex={false}>
        <Form>
          {
            detailList.map((item) => (
              <div key={item.id} className={style.listItem}>
                <div className={style.itemMain}>
                  <div>
                    <span>SKC:</span> <span>{item.skc}</span>
                  </div>
                  <div
                    onClick={() => Modal.confirm({
                      content: t('是否确定将该商品移除箱子？'),
                      onOk: () => store.deleteItem({
                        params: {
                          id: item.id,
                        },
                        list: detailList,
                      }),
                    })}
                  >
                    <Icon
                      name="delete"
                      fontSize="16px"
                      className={style.iconColor}
                    />
                  </div>
                </div>
                <div className={style.itemMain}>
                  <div>
                    <span>{t('尺码')}:</span> <span>{item.size}</span>
                  </div>
                </div>
              </div>
            ))
          }

        </Form>
        {/* TODO 加这个div是因为 View有两个子元素以上才会滚动 */}
        <div />
      </View>

      <Footer beforeBack={() => {
        store.changeData({
          data: {
            pageStatus: 'main',
          },
        });
        classFocus('barCode');
      }}
      />
    </div>
  );
}

List.propTypes = {
  dispatch: PropTypes.func,
};

export default List;
