import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import Header from '../../common/header';
import Main from './jsx/main';
import Detail from './jsx/detail';

class Container extends Component {
  componentWillMount() {
    store.init();
  }

  render() {
    const {
      pageStatus,
      goodsQuality,
      goodsQualityList,
    } = this.props;
    const secondTitle = `${t('装箱商品明细')}(${goodsQualityList.find((i) => i.id === goodsQuality).value})`;
    let children;
    switch (pageStatus) {
      case 'main':
        children = (<Main {...this.props} />);
        break;
      case 'detail':
        children = (<Detail {...this.props} />);
        break;
      default:
        break;
    }
    return (
      <div>
        <Header title={pageStatus === 'detail' ? secondTitle : t('扫商品装箱')} />
        {children}
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  headerTitle: PropTypes.string.isRequired,
  goodsQuality: PropTypes.number,
};

export default i18n(Container);
