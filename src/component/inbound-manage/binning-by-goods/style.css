.light{
    color: #f29c4f;
    font-weight: 800;
}
.listWrap{
    width: 100%;
    /* max-height: 531px; */
    overflow-y: scroll;
}
.listItem{
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 60px;
    line-height: 30px;
    padding: 0 15px;
    border-bottom: 1px solid #E5E5E5;
    overflow: hidden;
    box-sizing: border-box;
}
/*.listItem:last-child{*/
/*border-bottom: none;*/
/*}*/
.itemMain{
    /* flex: 1; */
    display: flex;
    justify-content: space-between;
}
.itemLabel{
    display: inline-flex;
    flex-direction: column;
    width: 150px;
    text-align: right;
}
.iconColor{
    color: #337cf2;
}
.color1{
    color: #ff9636;
}
.color2{
    color: #07c160;
}
.tipWrap{
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: center;
}
.tipContent{
    /* width: 130px; */
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin: 0 auto;
    border: 2px solid #ff9636;
    color: #ff9636;
    font-weight: 700;
    padding: 0 5px;
}
.tipContent2{
    /* width: 130px; */
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin: 0 auto;
    border: 2px solid #07C160;
    color: #07c160;
    font-weight: 700;
    padding: 0 5px;
}
.packageTypeClass {
    font-size: 14px;
    background: #0059CE;
    color: #ffffff;
    padding: 1px 3px;
    margin-left: 5px;
}