import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import * as servers from './server';

const defaultState = {
  headerTitle: '',
  pageStatus: 'main',
  boxNo: '', // 大箱号
  binningNum: 0, // 已装箱数量
  btnDisabled: true,
  boxDisabled: false,
  skcDisabled: 0,
  detailList: [], // 明细列表
  barCode: '', // 商品条码
  goodsQuality: 0, // 商品品质
  goodsQualityList: [
    {
      id: 0,
      value: t('未知'),
    },
    {
      id: 1,
      value: t('正品'),
    },
    {
      id: 2,
      value: t('次品'),
    },
    {
      id: 3,
      value: t('黑码'),
    },
  ], // 商品品质
  containerCategory: '', // 箱子品类
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    classFocus('boxNo');
  },
  * scanBoxNo(action, ctx) {
    yield ctx.changeData({
      data: {
        boxDisabled: true,
      },
    });
    const { warehouseId } = JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}');
    const params = assign({}, action.params, {
      warehouse: warehouseId,
    });
    const res = yield servers.scanContainer(params);
    if (res.code !== '0') {
      yield ctx.changeData({
        data: {
          boxNo: '',
          boxDisabled: false,
        },
      });
      Modal.error({
        content: res.msg,
        onOk: () => classFocus('boxNo'),
      });
    } else {
      yield ctx.changeData({
        data: {
          btnDisabled: false,
          skcDisabled: 1,
          binningNum: res.info.goodsNum,
          containerCategory: res.info.containerCategory,
          detailList: res.info.list,
          goodsQuality: res.info.goodsQuality,
        },
      });
      classFocus('barCode');
    }
  },
  * scanBarCode(action, ctx) {
    // 新增仓库传参
    const { warehouseId } = JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}');
    const params = assign({}, action.params, {
      warehouseId: Number(warehouseId),
    });
    markStatus('skcDisabled');
    const res = yield servers.scanBarCode(params);
    if (res.code !== '0') {
      yield ctx.changeData({
        data: {
          barCode: '',
        },
      });
      Modal.error({
        content: res.msg,
        onOk: () => classFocus('barCode'),
      });
    } else {
      yield ctx.changeData({
        data: {
          binningNum: res.info.goodsNum,
          detailList: res.info.list,
          barCode: '',
        },
      });
      classFocus('barCode');
    }
  },
  * deleteItem(action, ctx) {
    const res = yield servers.deleteDetailListItem(action.params);
    if (res.code !== '0') {
      Modal.error({
        content: res.msg,
      });
    } else {
      const list = action.list.filter((i) => i.id !== action.params.id);
      yield ctx.changeData({
        data: {
          detailList: list,
          binningNum: list.length,
        },
      });
      Modal.success({
        content: t('商品移除成功！'),
      });
    }
  },
  * closeBox(action, ctx) {
    yield ctx.changeData({
      data: {
        btnDisabled: true,
      },
    });
    const res = yield servers.closeBox(action.params);
    if (res.code !== '0') {
      yield ctx.changeData({
        data: {
          btnDisabled: false,
        },
      });
      Modal.error({
        content: res.msg,
      });
    } else {
      Modal.success({
        content: `${t('大箱{}已成功关箱！', action.params.boxNo)}`,
      });
      yield ctx.init();
    }
  },

};
