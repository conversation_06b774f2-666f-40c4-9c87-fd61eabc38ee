import { sendPostRequest } from '../../../lib/public-request';

/**
 * 扫描大箱号
 * @param param
 * @returns {*}
 */
export const scanContainer = (param) => sendPostRequest({
  url: '/abroad/goods_scan_box',
  param,
}, process.env.WIS_FRONT);
/**
 * 扫描商品条码
 * @param param
 * @returns {*}
 */
export const scanBarCode = (param) => sendPostRequest({
  url: '/abroad/goods_scan_goods',
  param,
}, process.env.WIS_FRONT);
/**
 * 关箱
 * @param param
 * @returns {*}
 */
export const closeBox = (param) => sendPostRequest({
  url: '/abroad/goods_close_box',
  param,
}, process.env.WIS_FRONT);
/**
 * 删除某一项明细
 * @param param
 * @returns {*}
 */
export const deleteDetailListItem = (param) => sendPostRequest({
  url: '/abroad/goods_delete_box',
  param,
}, process.env.WIS_FRONT);
