import assign from 'object-assign';
import { push } from 'react-router-redux';
import { t } from '@shein-bbl/react';
import Modal from '../../common/modal';
import { scanBigContainerServer, scanFullContainerServer } from './server';
import { classFocus, getHeaderTitle } from '../../../lib/util';

const defaultState = {
  bigContainerCode: '',
  containerCode: '',
  loading: false,
  position: 'bigContainer',
  headerTitle: '',
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  reset(draft, action) {
    assign(draft, {
      bigContainerCode: '',
      containerCode: '',
      loading: false,
      position: 'bigContainer',
    });
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  // 扫描大箱号
  * scanBigContainer(action, ctx, put) {
    yield ctx.changeData({
      data: {
        loading: true,
      },
    });
    const data = yield scanBigContainerServer(action.data);
    yield ctx.changeData({
      data: {
        loading: false,
      },
    });
    if (data.code === '0') {
      yield ctx.changeData({
        data: {
          position: 'containerCode',
        },
      });
      classFocus('containerCode');
    } else {
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
        },
      });
      Modal.error({
        title: data.msg,
        className: 'bigContainer',
      });
    }
  },
  * scanFullContainer(action, ctx, put) {
    yield ctx.changeData({
      data: {
        loading: true,
      },
    });
    const data = yield scanFullContainerServer(action.data);
    yield ctx.changeData({
      data: {
        loading: false,
        containerCode: '',
      },
    });
    if (data.code === '0') {
      Modal.success({
        title: t('换箱管理(整箱)成功!'),
        className: 'containerCode',
      });
    } else {
      Modal.error({
        title: data.msg,
        className: 'containerCode',
      });
    }
  },
};
