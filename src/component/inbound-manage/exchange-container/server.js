import { sendPostRequest } from '../../../lib/public-request';

// 扫描换箱前箱号
export const scanContainerBeforeChangeApi = (param) => sendPostRequest({
  url: '/reback/scan_container_before_change',
  param,
}, process.env.WIS_FRONT);

// 扫描商品条码
export const scanBarcodeApi = (param) => sendPostRequest({
  url: '/reback_integration/scan_barcode',
  param,
}, process.env.WIS_FRONT);

// 扫描换箱后箱号
export const scanContainerAfterChangeApi = (param) => sendPostRequest({
  url: '/reback_integration/scan_container_after_change',
  param,
}, process.env.WIS_FRONT);

// 扫描格口
export const scanBinCodeApi = (param) => sendPostRequest({
  url: '/reback_integration/scan_bin_code',
  param,
}, process.env.WIS_FRONT);
