import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { select } from 'redux-saga/effects';
import {
  scanContainerBeforeChangeApi,
  scanBarcodeApi,
  scanContainerAfterChangeApi,
  scanBinCodeApi,
} from './server';
import { message, modal } from '../../common';
import { classFocus } from '../../../lib/util';

const defaultState = {
  dataLoading: 1,
  show: false,
  afterContainerCode: '', // 换箱后箱号
  beforeContainerCode: '', // 换箱前箱号
  barcode: '', // 商品条码
  functionType: 1, // 功能类别(1.大箱换上架2.上架换大箱3.大箱换大箱)
  changeType: '', // 换箱类型(1.整箱换箱 2.散件换箱)
  position: 'beforeContainerCode',
  changeTypeLabel: '',
  skc: '',
  size: '',
  num: 0,
  showResult: false,
  beforeContainerCodeShow: '',
  binCode: '', // 格口号
  showBinCode: false, // 是否需要扫格口号
};

export default {
  defaultState,
  init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  reset(draft) {
    assign(draft, defaultState);
  },
  // 扫描换箱前箱号
  * scanContainerBeforeChange(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanContainerBeforeChangeApi(action.param);
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          position: 'afterContainerCode',
          afterContainerCode: '',
        },
      });
      classFocus('afterContainerCode');
    } else {
      yield ctx.changeData({
        data: {
          position: 'beforeContainerCode',
          beforeContainerCode: '',
        },
      });
      modal.error({ content: res.msg, className: 'beforeContainerCode' });
    }
  },
  // 扫描换箱后箱号
  * scanContainerAfterChange(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanContainerAfterChangeApi(action.param);
    if (res.code === '0') {
      const { changeType } = yield select((state) => state['inbound-manage/exchange-container']);
      if (changeType === 1) {
        yield ctx.changeData({
          data: {
            afterContainerCode: '',
            beforeContainerCode: '',
            num: res.info.num,
            showResult: true,
            position: 'beforeContainerCode',
            beforeContainerCodeShow: action.param.beforeContainerCode,
          },
        });
        classFocus('beforeContainerCode');
      } else {
        yield ctx.changeData({
          data: {
            barcode: '',
            position: 'barcode',
          },
        });
        classFocus('barcode');
      }
    } else {
      yield ctx.changeData({
        data: {
          afterContainerCode: '',
          position: 'afterContainerCode',
        },
      });
      modal.error({ content: res.msg, className: 'afterContainerCode' });
    }
  },
  // 扫描商品条码
  * scanBarcode(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanBarcodeApi(action.param);
    if (res.code === '0') {
      const { num, functionType, changeType } = yield select((state) => state['inbound-manage/exchange-container']);
      const {
        size,
        skc,
        isFinish,
        ppFlag,
      } = res.info;
      // 页面为大箱换上架周转箱&换箱类型为散货&上架周转箱为pp周转箱箱时，需要扫格口
      if (functionType === 1 && changeType === 2 && ppFlag) {
        yield ctx.changeData({
          data: {
            showBinCode: true,
            position: 'binCode',
          },
        });
        classFocus('binCode');
        return;
      }
      if (isFinish === '1') {
        // 换箱完成
        message.success(t('换箱成功'));
        yield ctx.changeData({
          data: {
            skc: '',
            size: '',
            barcode: '',
            num: 0,
            showResult: false,
            afterContainerCode: '',
            beforeContainerCode: '',
            position: 'beforeContainerCode',
          },
        });
        classFocus('beforeContainerCode');
      } else {
        yield ctx.changeData({
          data: {
            skc,
            size,
            barcode: '',
            num: num + 1,
            showResult: true,
          },
        });
        classFocus('barcode');
      }
    } else {
      yield ctx.changeData({
        data: {
          barcode: '',
        },
      });
      modal.error({ content: res.msg, className: 'barcode' });
    }
  },

  // 扫描格口
  * scanBinCode(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanBinCodeApi(action.param);
    if (res.code === '0') {
      const { num } = yield select((state) => state['inbound-manage/exchange-container']);
      const { size, skc, isFinish } = res.info;
      if (isFinish === '1') {
        // 换箱完成
        message.success(t('换箱成功'));
        yield ctx.changeData({
          data: {
            skc: '',
            size: '',
            barcode: '',
            binCode: '',
            num: 0,
            showResult: false,
            afterContainerCode: '',
            beforeContainerCode: '',
            position: 'beforeContainerCode',
            showBinCode: false,
          },
        });
        classFocus('beforeContainerCode');
      } else {
        yield ctx.changeData({
          data: {
            skc,
            size,
            barcode: '',
            binCode: '',
            position: 'barcode',
            num: num + 1,
            showResult: true,
          },
        });
        classFocus('barcode');
      }
    } else {
      yield ctx.changeData({
        data: {
          binCode: '',
        },
      });
      modal.error({ content: res.msg, className: 'binCode' });
    }
  },
};
