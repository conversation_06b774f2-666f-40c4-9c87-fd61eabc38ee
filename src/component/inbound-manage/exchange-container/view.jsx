import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
// import { push } from 'react-router-redux';
import { classFocus } from '../../../lib/util';
import store from './reducers';
import {
  Header,
  Footer,
  FocusInput,
  FooterBtn,
  pages,
  modal,
  PopSheet,
} from '../../common';
import style from './style.css';

const { View } = pages;

const changeTypeList = [
  {
    label: t('散件换箱'),
    value: 2,
  },
  {
    label: t('整箱换箱'),
    value: 1,
  },
];

const lableListMap = new Map([
  [1, [t('大箱号'), t('上架周转箱'), t('商品条码')]],
  [2, [t('上架周转箱'), t('大箱号'), t('商品条码')]],
  [3, [t('原始大箱号'), t('新大箱号'), t('商品条码')]],
]);

const titleMap = new Map([
  [1, t('大箱换上架周转箱')],
  [2, t('上架周转箱换大箱')],
  [3, t('大箱换大箱')],
]);

const msgMap = new Map([
  [1, t('上架周转箱')],
  [2, t('大箱')],
  [3, t('大箱')],
]);

class Container extends Component {
  componentDidMount() {
    store.init();
    const { match: { params: { type } } } = this.props;
    store.changeData({
      data: {
        functionType: Number(type),
      },
    });
    classFocus('beforeContainerCode');
  }

  render() {
    const {
      dispatch,
      dataLoading,
      show,
      afterContainerCode, // 换箱后箱号
      beforeContainerCode, // 换箱前箱号
      barcode, // 商品条码
      functionType, // 功能类别(1.大箱换上架2.上架换大箱3.大箱换大箱)
      changeType, // 换箱类型(1.整箱换箱 2.散件换箱)
      position,
      changeTypeLabel,
      skc,
      size,
      num,
      showResult,
      beforeContainerCodeShow,
      showBinCode,
      binCode,
    } = this.props;
    const tip = functionType === 2 ? t('上架周转箱') : t('大箱号');
    const msg = msgMap.get(functionType);
    const labelList = lableListMap.get(functionType);
    const title = titleMap.get(functionType);
    return (
      <View>
        <Header title={title} />

        <Form>
          <FocusInput
            disabled={position === 'barcode' || position === 'binCode'}
            value={changeTypeLabel}
            placeholder={t('全部类型')}
            readOnly
            className="changeBoxType"
            onClick={() => {
              store.changeData({
                data: {
                  show: true,
                },
              });
            }}
            arrow
          >
            <label>{t('换箱类型')}</label>
          </FocusInput>
        </Form>

        <Form>
          <FocusInput
            value={beforeContainerCode}
            className="beforeContainerCode"
            label={labelList[0]}
            autoFocus
            disabled={dataLoading === 0 || position !== 'beforeContainerCode'}
            onChange={(e) => {
              store.changeData({
                data: {
                  beforeContainerCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={() => {
              // containerType 箱类型(1.上架周转箱，2.大纸箱)
              store.scanContainerBeforeChange({
                param: {
                  containerCode: beforeContainerCode,
                  containerType: functionType === 2 ? 1 : 2,
                  functionType,
                },
              });
            }}
          />
        </Form>
        <Form>
          <FocusInput
            value={afterContainerCode}
            className="afterContainerCode"
            label={labelList[1]}
            disabled={dataLoading === 0 || position !== 'afterContainerCode'}
            onChange={(e) => {
              store.changeData({
                data: {
                  afterContainerCode: e.target.value.trim(),
                },
              });
            }}
            // eslint-disable-next-line consistent-return
            onPressEnter={() => {
              if (changeType === '') {
                modal.error({ content: t('请选择换箱类型'), className: 'changeBoxType' });
                store.changeData({
                  data: {
                    position: 'changeBoxType',
                  },
                });
                return false;
              }
              store.scanContainerAfterChange({
                param: {
                  beforeContainerCode,
                  afterContainerCode,
                  changeType,
                  functionType,
                },
              });
            }}
          />
        </Form>
        <Form>
          <FocusInput
            value={barcode}
            className="barcode"
            label={labelList[2]}
            disabled={dataLoading === 0 || position !== 'barcode'}
            onChange={(e) => {
              store.changeData({
                data: {
                  barcode: e.target.value,
                },
              });
            }}
            onPressEnter={() => {
              store.scanBarcode({
                param: {
                  beforeContainerCode,
                  afterContainerCode,
                  barcode,
                  functionType,
                },
              });
            }}
          />
        </Form>
        {
          showBinCode && (
            <Form>
              <FocusInput
                value={binCode}
                className="binCode"
                label={t('格口')}
                disabled={dataLoading === 0 || position !== 'binCode'}
                onChange={(e) => {
                  store.changeData({
                    data: {
                      binCode: e.target.value,
                    },
                  });
                }}
                onPressEnter={() => {
                  store.scanBinCode({
                    param: {
                      beforeContainerCode,
                      afterContainerCode,
                      barcode,
                      binCode,
                      functionType,
                    },
                  });
                }}
              />
            </Form>
          )
        }

        {
          showResult &&
          (
            <div className={style.result}>
              {
                changeType === 2 ?
                  (
                    <div className={style.col}>
                      <div>{t('SKC')}/{t('尺码')}:</div>
                      <div>{skc}/{size}</div>
                    </div>
                  ) :
                  (
                    <div className={style.col}>
                      <div>{tip}:</div>
                      <div>
                        <span className={style.red}>{beforeContainerCodeShow} </span>{t('换箱成功')}
                      </div>
                    </div>
                  )
              }
              <div className={style.col}>
                <div>{t('换箱成功')}:</div>
                <div>
                  <span className={style.red}>{num}</span>/{t('件')}
                </div>
              </div>
            </div>
          )
        }

        <PopSheet
          onClick={(v) => {
            if (position === 'beforeContainerCode' && changeType !== v.value) {
              store.changeData({
                data: {
                  changeType: v.value,
                  changeTypeLabel: v.label,
                  show: false,
                  showResult: false,
                  skc: '',
                  size: '',
                  num: 0,
                  beforeContainerCodeShow: '',
                },
              });
            } else {
              store.changeData({
                data: {
                  changeType: v.value,
                  changeTypeLabel: v.label,
                  show: false,
                },
              });
            }
            classFocus(position);
          }}
          onClose={() => {
            store.changeData({ data: { show: false } });
          }}
          cancelBtn
          menus={changeTypeList}
          show={show}
        />

        <Footer
          dispatch={dispatch}
          beforeBack={(back) => {
            store.reset();
            back();
          }}
        >
          <FooterBtn
            onClick={() => {
              modal.confirm({
                content: `${msg}${t('已装满，确定换箱')}`,
                onOk: () => {
                  store.changeData({
                    data: {
                      afterContainerCode: '',
                      barcode: '',
                      binCode: '',
                      showBinCode: false,
                      position: 'afterContainerCode',
                    },
                  });
                  classFocus('afterContainerCode');
                },
              });
            }}
          >
            {t('换箱')}
          </FooterBtn>
        </Footer>
      </View>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  dataLoading: PropTypes.number,
  show: PropTypes.bool,
  afterContainerCode: PropTypes.string,
  beforeContainerCode: PropTypes.string,
  barcode: PropTypes.string,
  functionType: PropTypes.number,
  changeType: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  match: PropTypes.shape(),
  position: PropTypes.string,
  changeTypeLabel: PropTypes.string,
  skc: PropTypes.string,
  size: PropTypes.string,
  beforeContainerCodeShow: PropTypes.string,
  num: PropTypes.number,
  showResult: PropTypes.bool,
  showBinCode: PropTypes.bool,
  binCode: PropTypes.string,
};

export default i18n(Container);
