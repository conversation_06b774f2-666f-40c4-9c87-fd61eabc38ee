import { sendPostRequest } from '../../../lib/public-request';

/**
 * 扫描大箱号
 * @param param
 * @returns {*}
 */
export const scanContainer = (param) => sendPostRequest({
  url: '/abroad/package_scan_box',
  param,
}, process.env.WIS_FRONT);
/**
 * 扫描包裹条码
 * @param param
 * @returns {*}
 */
export const scanPackageNo = (param) => sendPostRequest({
  url: '/abroad/package_scan_package',
  param,
}, process.env.WIS_FRONT);
/**
 * 关箱
 * @param param
 * @returns {*}
 */
export const closeBox = (param) => sendPostRequest({
  url: '/abroad/package_close_box',
  param,
}, process.env.WIS_FRONT);
/**
 * 删除明细
 * @param param
 * @returns {*}
 */
export const deleteDetailListItem = (param) => sendPostRequest({
  url: '/abroad/package_delete_box',
  param,
}, process.env.WIS_FRONT);
/**
 * 关箱前查询非未处理包裹
 * @param param
 * @returns {*}
 */
export const queryShiftate = (param) => sendPostRequest({
  url: '/abroad/query_shift_data',
  param,
}, process.env.WIS_FRONT);
