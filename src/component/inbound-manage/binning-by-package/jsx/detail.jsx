import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import Icon from '@shein-components/Icon';
import Footer from '../../../common/footer';
import store from '../reducers';
import style from '../style.css';
import { classFocus } from '../../../../lib/util';
import Modal from '../../../common/modal';
import pages from '../../../common/pages';

const { View } = pages;

function List(props) {
  const {
    detailList,
  } = props;
  return (
    <div>
      <View diff={110} flex={false}>
        <Form>
          {
            detailList.map((item) => (
              <div key={item.packageNo} className={style.listItem}>
                <div className={style.itemMain}>
                  <div>
                    <span>{t('包裹单号')}:</span> <span>{item.packageNo}</span>
                  </div>
                  <div
                    onClick={() => Modal.confirm({
                      content: t('是否确定将该包裹移出箱子？'),
                      onOk: () => store.deleteItem({
                        params: {
                          packageNoList: [
                            { packageNo: item.packageNo, goodsNum: item.goodsNum || 1 },
                          ],
                        },
                        list: detailList,
                      }),
                    })}
                  >
                    <Icon
                      name="delete"
                      fontSize="16px"
                      className={style.iconColor}
                    />
                  </div>
                </div>
                <div className={style.itemMain}>
                  <div>
                    <span>{t('商品数量')}:</span> <span>{item.goodsNum}</span>
                  </div>
                </div>
              </div>
            ))
          }
        </Form>
        {/* TODO 加这个div是因为 View有两个子元素以上才会滚动 */}
        <div />
      </View>

      <Footer beforeBack={() => {
        store.changeData({
          data: {
            pageStatus: 'main',
          },
        });
        classFocus('packageNo');
      }}
      />
    </div>
  );
}

List.propTypes = {
  dispatch: PropTypes.func,
};

export default List;
