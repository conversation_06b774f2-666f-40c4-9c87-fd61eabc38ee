import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { Button } from 'react-weui/build/packages/components/button';
import Icon from '@shein-components/Icon';
import store from '../reducers';
import Footer from '../../../common/footer';
import FocusInput from '../../../common/focus-input';
import RowInfo from '../../../common/row-info';
import Modal from '../../../common/modal';

const Container = (props) => {
  const {
    boxNo,
    packageNo,
    binningNum,
    boxDisabled,
    packageDisabled,
    btnDisabled,
  } = props;
  return (
    <div>
      <Form style={{
        marginBottom: '5px',
      }}
      >
        <FocusInput
          placeholder={t('请输入')}
          className="boxNo"
          data-bind="boxNo"
          autoFocus
          disabled={boxDisabled}
          onPressEnter={(e) => {
            if (e.target.value) {
              store.scanBoxNo({
                params: {
                  boxNo,
                },
              });
            }
          }}
          footer={
            (
              <Button
                style={{ marginBottom: 5 }}
                disabled={btnDisabled}
                size="small"
                onClick={() => {
                  store.queryShiftate({
                    params: {
                      boxNo,
                    },
                  });
                }}
              >
                {t('关箱')}
              </Button>
            )
          }
        >
          <label>{t('大箱号')}</label>
        </FocusInput>
      </Form>
      <Form style={{
        marginBottom: '5px',
      }}
      >
        <div
          onClick={() => {
            if (binningNum > 0) {
              store.changeData({
                data: {
                  pageStatus: 'detail',
                },
              });
            }
          }}
        >
          <RowInfo
            label={t('已装箱包裹数量')}
            content={<div style={{ color: '#337cf2' }}>{binningNum}<Icon name="arr-right" /></div>}
          />
        </div>
      </Form>
      <Form style={{
        marginBottom: '5px',
      }}
      >
        <FocusInput
          placeholder={t('请输入')}
          className="packageNo"
          data-bind="packageNo"
          disabled={packageDisabled === 0}
          onPressEnter={(e) => {
            if (e.target.value) {
              store.scanPackageNo({
                params: {
                  boxNo,
                  packageNo,
                },
              });
            }
          }}
        >
          <label>{t('包裹单号')}</label>
        </FocusInput>
      </Form>
      <Footer />
    </div>
  );
};

Container.propTypes = {
  boxNo: PropTypes.string,
  packageNo: PropTypes.string,
  binningNum: PropTypes.number,
  boxDisabled: PropTypes.bool,
  btnDisabled: PropTypes.bool,
  packageDisabled: PropTypes.number,
};

export default Container;
