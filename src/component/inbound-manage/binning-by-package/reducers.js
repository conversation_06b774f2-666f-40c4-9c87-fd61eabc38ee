import React from 'react';
import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import * as servers from './server';
import style from './style.css';

const defaultState = {
  headerTitle: '',
  pageStatus: 'main',
  boxNo: '', // 大箱号
  binningNum: 0, // 已装箱数量
  btnDisabled: true,
  boxDisabled: false,
  packageDisabled: 0,
  detailList: [], // 明细列表
  packageNo: '', // 商品条码
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    classFocus('boxNo');
  },
  * scanBoxNo(action, ctx) {
    yield ctx.changeData({
      data: {
        boxDisabled: true,
      },
    });
    const { warehouseId } = JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}');
    const params = assign({}, action.params, {
      warehouse: warehouseId,
    });
    const res = yield servers.scanContainer(params);
    if (res.code !== '0') {
      yield ctx.changeData({
        data: {
          boxNo: '',
          boxDisabled: false,
        },
      });
      Modal.error({
        content: res.msg,
        onOk: () => classFocus('boxNo'),
      });
    } else {
      yield ctx.changeData({
        data: {
          btnDisabled: false,
          packageDisabled: 1,
          binningNum: res.info.goodsNumTotal,
          detailList: res.info.list || [],
        },
      });
      classFocus('packageNo');
    }
  },
  * scanPackageNo(action, ctx) {
    markStatus('packageDisabled');
    const res = yield servers.scanPackageNo(action.params);
    if (res.code !== '0') {
      yield ctx.changeData({
        data: {
          packageNo: '',
        },
      });
      Modal.error({
        content: res.msg,
        onOk: () => classFocus('packageNo'),
      });
    } else {
      yield ctx.changeData({
        data: {
          binningNum: res.info.goodsNumTotal,
          detailList: res.info.list || [],
          packageNo: '',
        },
      });
      classFocus('packageNo');
    }
  },
  * deleteItem(action, ctx) {
    const res = yield servers.deleteDetailListItem(action.params);
    if (res.code !== '0') {
      Modal.error({
        content: res.msg,
      });
    } else {
      const list = action.list
        .filter((i) => i.packageNo !== action.params.packageNoList[0].packageNo);
      yield ctx.changeData({
        data: {
          detailList: list,
          binningNum: list.length,
        },
      });
      Modal.success({
        content: t('包裹移除成功！'),
      });
    }
  },
  // 关箱前查询非未处理包裹
  * queryShiftate(action, ctx) {
    yield ctx.changeData({
      data: {
        btnDisabled: true,
      },
    });
    const res = yield servers.queryShiftate(action.params);
    yield ctx.changeData({
      data: {
        btnDisabled: false,
      },
    });
    if (res.code === '0') {
      // 有非未处理包裹则弹窗，无直接关箱
      if (res.info && res.info.list && res.info.list.length) {
        const listArr = res.info.list;
        Modal.confirm({
          content: (
            <div className={style.modalListCont}>
              <div className={style.modalListTitle}>{t('包裹状态非“未处理”需移出箱子')}</div>
              {
                listArr.map((item) => (
                  <div>
                    <div>{t('包裹单号')}: {item.packageNo}</div>
                    {t('商品数量')}:{item.goodsNum}
                  </div>
                ))
              }
            </div>
          ),
          buttons: [{
            type: 'primary',
            label: t('关闭'),
            onClick: () => console.log(0),
          }],
        });
      } else {
        // 无非未处理包裹则弹窗确认是否关箱
        const status = yield new Promise((r) => {
          Modal.confirm({
            content: t('确定需要关箱吗？'),
            onOk: () => r('ok'),
          });
        });
        if (status === 'ok') {
          yield ctx.closeBox({
            params: action.params,
          });
        }
      }
    } else {
      Modal.error({
        content: res.msg,
      });
    }
  },
  * closeBox(action, ctx) {
    yield ctx.changeData({
      data: {
        btnDisabled: true,
      },
    });
    const res = yield servers.closeBox(action.params);
    if (res.code !== '0') {
      yield ctx.changeData({
        data: {
          btnDisabled: false,
        },
      });
      Modal.error({
        content: res.msg,
      });
      yield ctx.scanBoxNo({
        params: action.params,
      });
    } else {
      Modal.success({
        content: `${t('大箱{}已成功关箱！', action.params.boxNo)}`,
      });
      yield ctx.init();
    }
  },

};
