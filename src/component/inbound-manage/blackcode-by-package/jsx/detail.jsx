import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import Icon from '@shein-components/Icon';
import Footer from '../../../common/footer';
import store from '../reducers';
import style from '../style.css';
import { classFocus } from '../../../../lib/util';
import Modal from '../../../common/modal';

const List = (props) => {
  const {
    detailList,
  } = props;
  return (
    <div>
      <Form>
        <div className={style.listWrap}>
          {
            detailList.map(item => (
              <div key={item.packageNo} className={style.listItem}>
                <div className={style.itemMain}>
                  <div>
                    <span>{t('包裹单号')}:</span> <span>{item.packageNo}</span>
                  </div>
                  <div
                    onClick={() => Modal.confirm({
                      content: t('是否确定将该包裹移出箱子？'),
                      onOk: () => store.deleteItem({
                        params: {
                          packageNoList: [
                            {
                              packageNo: item.packageNo,
                              goodsNum: item.goodsNum || 1
                            },
                          ],
                        },
                        list: detailList,
                      }),
                    })}
                  >
                    <Icon
                      name="delete"
                      fontSize="16px"
                      className={style.iconColor}
                    />
                  </div>
                </div>
                <div className={style.itemMain}>
                  <div>
                    <span>{t('商品数量')}:</span> <span>{item.goodsNum}</span>
                  </div>
                </div>
              </div>
            ))
          }
        </div>
      </Form>
      <Footer beforeBack={() => {
        store.changeData({
          data: {
            pageStatus: 'main',
          },
        });
        classFocus('packageNo');
      }}
      />
    </div>
  );
};

List.propTypes = {
  dispatch: PropTypes.func,
};

export default List;
