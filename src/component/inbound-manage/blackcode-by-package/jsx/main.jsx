import React from 'react';
import { classFocus } from 'lib/util';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { Button } from 'react-weui/build/packages/components/button';
import { Dialog, Input } from 'react-weui/build/packages';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import style from '../style.css';
import store from '../reducers';
import Footer from '../../../common/footer';
import FocusInput from '../../../common/focus-input';
import Modal from '../../../common/modal';

const reg = /^[1-9]\d{0,3}$/; // 匹配商品件数
function Container(props) {
  const {
    loading,
    boxNo,
    binningNum,
    boxDisabled,
    btnDisabled,
    editObjVisible,
    editObj,
    show,
    inputValue,
    className,
    backNumObj,
    showCloseBoxModal,
    materialBarCode,
    disabledMaterialBarCode,
  } = props;
  const btns = [{
    label: t('取消'),
    type: 'default',
    onClick: () => {
      store.onConfirm({ type: 'cancel' });
    },
  }, {
    label: t('确定'),
    type: 'primary',
    disabled: !loading,
    onClick: () => {
      if (!loading) {
        return;
      }
      const params = {
        containerCode: editObj.containerCode,
        operationNum: inputValue,
        operationType: backNumObj.operationType,
      };
      store.onConfirm({
        type: 'ok',
        params,
        boxNo,
      });
    },
  }];
  const canSure = loading && !((!disabledMaterialBarCode || !materialBarCode) && (JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '') === '98');
  return (
    <div>
      <Dialog
        className={className}
        title={backNumObj.operationType === 1 ? t('增加黑码商品') : t('减少黑码商品')}
        show={show}
        buttons={btns}
      >
        <div style={{
          display: 'flex',
        }}
        >
          <div
            style={{
              width: '100%',
              lineHeight: '30px',
              marginBottom: '15px',
            }} className={style.tipWrap}
          >
            <label style={{ whiteSpace: 'nowrap' }}>{t('件数')}：</label>
            <Input
              maxLength={20}
              style={{
                width: '78%',
                backgroundColor: 'rgba(189, 166, 166, 0.08)',
                borderRadius: '5px',
                padding: '5px 10px',
              }}
              className="Input"
              placeholder={t('请输入件数')}
              value={inputValue}
              onChange={(e) => {
                const val = e.target.value.trim();
                if (val && !reg.test(val)) {
                  store.changeData({ data: { inputValue: '' } });
                } else {
                  store.handleChange(e);
                }
              }}
            />
          </div>
        </div>
      </Dialog>
      <Dialog
        className={className}
        title={t('是否确认关箱？')}
        show={showCloseBoxModal}
        buttons={
          [
            {
              label: t('取消'),
              type: 'default',
              onClick: () => {
                store.changeData({
                  data: {
                    showCloseBoxModal: false,
                    materialBarCode: '',
                    disabledMaterialBarCode: false,
                  },
                });
              },
            },
            {
              label: t('确定'),
              type: canSure ? 'primary' : 'default',
              disabled: !canSure,
              onClick: () => {
                if (canSure) {
                  store.closeBox({
                    params: {
                      container_code: boxNo,
                    },
                  });
                }
              },
            },
          ]
        }
      >
        <div style={{
          display: 'flex',
        }}
        >
          <div
            style={{
              width: '100%',
              lineHeight: '30px',
              marginBottom: '15px',
            }} className={style.tipWrap}
          >
            <label style={{ whiteSpace: 'nowrap' }}>{t('耗材条码')}：</label>
            <FocusInput
              placeholder={t('请扫描')}
              className="materialBarCode"
              data-bind="materialBarCode"
              style={{
                width: '78%',
                backgroundColor: 'rgba(189, 166, 166, 0.08)',
              }}
              autoFocus
              disabled={disabledMaterialBarCode}
              onPressEnter={(e) => {
                if (e.target.value) {
                  store.scanMaterialBarCode({
                    params: {
                      materialBarCode,
                      containerCode: boxNo,
                    },
                  });
                }
              }}
            />
          </div>
        </div>
      </Dialog>
      <Form style={{
        paddingTop: '10px',
        marginBottom: '5px',
      }}
      >
        <FocusInput
          placeholder={t('请输入')}
          className="boxNo"
          data-bind="boxNo"
          autoFocus
          disabled={boxDisabled}
          onPressEnter={(e) => {
            if (e.target.value) {
              store.scanBoxNo({
                params: {
                  containerCodeList: [boxNo],
                },
              });
            }
          }}
          footer={
            editObjVisible ? (
              <Button
                style={{ marginBottom: 5 }}
                disabled={btnDisabled}
                size="small"
                onClick={() => {
                  // 如果是南沙退货中心仓需要扫耗材条码
                  if ((JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '') === '98') {
                    store.changeData({
                      data: {
                        showCloseBoxModal: true,
                      },
                    });
                    classFocus('materialBarCode');
                  } else {
                    // 弹窗确认是否关箱
                    Modal.confirm({
                      content: t('是否确认关箱？'),
                      onOk: () => store.closeBox({
                        params: {
                          container_code: boxNo,
                        },
                      }),
                    });
                  }
                }}
              >
                {t('关箱')}
              </Button>
            ) : ''
          }
        >
          <label>
            <span>{t('箱号')}</span>
            { editObj.goodsQualityName && <span className={style.packageTypeClass}>{editObj.goodsQualityName}</span>}
            { editObj.containerCategory && <span className={style.packageTypeClass}>{editObj.containerCategory}</span>}
          </label>
        </FocusInput>
      </Form>
      {
        editObjVisible ? (
          <div>
            <Form>
              <div
                className={style.binningNumClass}
                onClick={() => {
                  if (binningNum > 0) {
                    store.changeData({
                      data: {
                        pageStatus: 'detail',
                      },
                    });
                  }
                }}
              >
                <span>{t('商品总数')}：</span>
                <span>{editObj.number}</span>
              </div>
            </Form>
            <div
              style={{
                paddingTop: '15px',
              }}
              className={style.binningNumClass}
              onClick={() => {
                if (binningNum > 0) {
                  store.changeData({
                    data: {
                      pageStatus: 'detail',
                    },
                  });
                }
              }}
            >
              <span>{t('非特殊入库黑码数量')}：</span>
            </div>
            <div
              className={style.dispalyCenter}
              style={{
                marginBottom: '5px',
              }}
            >
              <Button
                style={{
                  margin: 0,
                  padding: '0 12px',
                }}
                disabled={!(editObj.blackNumber > 0)}
                size="small"
                onClick={() => {
                  store.changeData({
                    data: {
                      show: true,
                    },
                  });
                  store.changeBackNumObj({
                    data: {
                      operationType: 2,
                    },
                  });
                }}
              >
                {t('－')}
              </Button>
              <span className={style.backcodeNumClass}>{editObj.blackNumber}</span>
              <Button
                style={{
                  margin: 0,
                  padding: '0 12px',
                }}
                size="small"
                onClick={() => {
                  store.changeData({
                    data: {
                      show: true,
                    },
                  });
                  store.changeBackNumObj({
                    data: {
                      operationType: 1,
                    },
                  });
                }}
              >
                {t('＋')}
              </Button>
            </div>
          </div>
        ) : ''
      }
      <Footer />
    </div>
  );
}

Container.propTypes = {
  loading: PropTypes.number,
  boxNo: PropTypes.string,
  packageNo: PropTypes.string,
  binningNum: PropTypes.number,
  boxDisabled: PropTypes.bool,
  btnDisabled: PropTypes.bool,
  packageDisabled: PropTypes.number,
  editObjVisible: PropTypes.bool,
  showCloseBoxModal: PropTypes.bool,
  materialBarCode: PropTypes.string,
  disabledMaterialBarCode: PropTypes.bool,
};

export default Container;
