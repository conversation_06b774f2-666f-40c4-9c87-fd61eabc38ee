import { sendPostRequest } from '../../../lib/public-request';

/**
 * 扫描大箱号
 * @param param
 * @returns {*}
 */
export const scanContainer = (param) => sendPostRequest({
  url: '/reback_packing/black_container',
  param,
}, process.env.WIS_FRONT);
/**
 * 关箱
 * @param param
 * @returns {*}
 */
export const closeBox = (param) => sendPostRequest({
  url: '/reback_check/close_container',
  param,
}, process.env.WIS_FRONT);
/**
 * 次品/黑码装箱明细-操作(增减/减少)
 * @param param
 * @returns {*}
 */
export const changeBlackNumber = (param) => sendPostRequest({
  url: '/reback_black_container/modify',
  param,
}, process.env.WIS_FRONT);

/**
 * 扫耗材条码
 * @param param
 * @returns {*}
 */
export const scanMaterialBarCode = (param) => sendPostRequest({
  url: '/reback_check/scan_material_bar_code',
  param,
}, process.env.WIS_FRONT);
