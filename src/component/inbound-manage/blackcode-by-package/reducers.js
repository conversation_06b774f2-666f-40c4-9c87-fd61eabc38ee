import React from 'react';
import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import Modal from '../../common/modal';
import message from '../../common/message';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import * as servers from './server';

const defaultState = {
  loading: 1,
  headerTitle: '',
  pageStatus: 'main',
  boxNo: '', // 大箱号
  binningNum: 0, // 已装箱数量
  btnDisabled: true,
  boxDisabled: false,
  packageDisabled: 0,
  packageNo: '', // 商品条码
  editObjVisible: false,
  editObj: {
    blackNumber: '',
    containerCategory: '',
    containerCode: '',
    goodsQuality: '',
    goodsQualityName: '',
    id: '',
    number: '',
  },
  backNumObj: {
    containerCode: '',
    operationNum: '',
    operationType: '',
    warehouseId: '',
  },
  show: false, // 弹窗显示隐藏
  inputValue: '',
  selectValue: '',
  name: '',
  blackContainerList: [],
  materialBarCode: '', // 耗材条码
  disabledMaterialBarCode: false,
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  changeEditObj: (draft, action) => {
    assign(draft.editObj, action.data);
  },
  changeBackNumObj: (draft, action) => {
    assign(draft.backNumObj, action.data);
  },
  * handleChange(e, ctx) {
    const value = e.target.value.trim();
    yield ctx.changeData({
      data: {
        inputValue: value,
      },
    });
  },
  * onConfirm(action, ctx) {
    markStatus('loading');

    if (action.type === 'ok') {
      markStatus('packageDisabled');
      const { warehouseId } = JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}');
      const reqObj = { warehouseId, ...action.params };
      const res = yield servers.changeBlackNumber(reqObj);
      if (res.code !== '0') {
        Modal.error({
          content: res.msg,
          onOk: () => classFocus('packageNo'),
        });
      } else {
        yield ctx.scanBoxNo({
          params: {
            containerCodeList: [action.boxNo],
          },
        });
        classFocus('packageNo');
      }
    }
    yield ctx.changeData({
      data: {
        show: false,
        inputValue: '',
      },
    });
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    classFocus('boxNo');
  },
  * scanBoxNo(action, ctx) {
    yield ctx.changeData({
      data: {
        boxDisabled: true,
      },
    });
    const { warehouseId } = JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}');
    const params = assign({}, action.params, {
      warehouseId,
    });
    const res = yield servers.scanContainer(params);
    if (res.code !== '0') {
      yield ctx.changeData({
        data: {
          boxNo: '',
          boxDisabled: false,
        },
      });
      Modal.error({
        content: res.msg,
        onOk: () => classFocus('boxNo'),
      });
    } else {
      yield ctx.changeData({
        data: {
          editObj: res.info.data[0],
          editObjVisible: true,
          btnDisabled: false,
          packageDisabled: 1,
        },
      });
      classFocus('packageNo');
    }
  },
  * closeBox(action, ctx) {
    markStatus('loading');

    yield ctx.changeData({
      data: {
        btnDisabled: true,
      },
    });
    const res = yield servers.closeBox(action.params);
    if (res.code !== '0') {
      yield ctx.changeData({
        data: {
          btnDisabled: false,
        },
      });
      Modal.error({
        content: res.msg,
      });
    } else {
      message.success(`${t('关箱成功')}`);
      yield ctx.init();
    }
  },
  * scanMaterialBarCode(action, ctx) {
    const res = yield servers.scanMaterialBarCode(action.params);
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          disabledMaterialBarCode: true,
        },
      });
    } else {
      yield ctx.changeData({
        data: {
          materialBarCode: '',
          disabledMaterialBarCode: false,
        },
      });
      classFocus('materialBarCode');
      Modal.error({
        content: res.msg,
      });
    }
  },
};
