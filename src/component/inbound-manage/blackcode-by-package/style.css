.light {
    color: #f29c4f;
    font-weight: 800;
}

.listWrap {
    width: 100%;
    min-height: 531px;
    overflow-y: scroll;
}

.listItem {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 60px;
    line-height: 30px;
    padding: 0 15px;
    border-bottom: 1px solid #E5E5E5;
    overflow: hidden;
    box-sizing: border-box;
}

/*.listItem:last-child{*/
/*border-bottom: none;*/
/*}*/
.itemMain {
    /* flex: 1; */
    display: flex;
    justify-content: space-between;
}

.itemLabel {
    display: inline-flex;
    flex-direction: column;
    width: 150px;
    text-align: right;
}

.iconColor {
    color: #337cf2;
}

.color1 {
    color: #ff9636;
}

.color2 {
    color: #07c160;
}

.tipWrap {
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.tipContent {
    /* width: 130px; */
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin: 0 auto;
    border: 2px solid #ff9636;
    color: #ff9636;
    font-weight: 700;
    padding: 0 5px;
}

.tipContent2 {
    /* width: 130px; */
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin: 0 auto;
    border: 2px solid #07C160;
    color: #07c160;
    font-weight: 700;
    padding: 0 5px;
}

.modalListCont {
    text-align: left;
    margin: 0 -10px -40px -10px;
    position: relative;
    top: -40px;
    background-color: #fff;
    max-height: 260px;
    overflow-y: auto;
}

.modalListCont > div:not(:last-of-type):not(.modalListTitle) {
    border-bottom: 1px solid #ccc;
    padding-bottom: 2px;
    line-height: 26px;
}

.modalListTitle {
    text-align: center;
    font-weight: bold;
    margin-bottom: 10px;
}

.dispalyCenter {
    display: -webkit-flex;
    -webkit-align-items: center;
    -webkit-justify-content: center;
}

.packageNumberClass {
    font-size: 18px;
    height: 30px;
    line-height: 30px;
    margin-top: 10px;
    display: -webkit-flex;
    -webkit-justify-content: center;
    justify-content: center;
}

.packageTypeClass {
    font-size: 14px;
    background: #0059CE;
    color: #ffffff;
    padding: 1px 3px;
    margin-left: 5px;
}

.backcodeNumClass {
    min-width: 100px;
    color: #337cf2;
    padding: 0;
    font-size: 20px;
    text-align: center;
}
.binningNumClass{
    padding: 10px 15px;
    display: -webkit-flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}
