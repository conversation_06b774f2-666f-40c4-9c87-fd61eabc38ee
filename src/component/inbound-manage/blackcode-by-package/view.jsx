import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import Header from '../../common/header';
import Main from './jsx/main';
import Detail from './jsx/detail';

class Container extends Component {
  componentWillMount() {
    store.init();
  }

  render() {
    const {
      pageStatus,
    } = this.props;
    let children;
    switch (pageStatus) {
      case 'main':
        children = (<Main {...this.props} />);
        break;
      case 'detail':
        children = (<Detail {...this.props} />);
        break;
    }
    return (
      <div>
        <Header title={t('非特殊入库黑码装箱')} />
        {children}
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  headerTitle: PropTypes.string.isRequired,
};

export default i18n(Container);
