import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import Modal from '../../common/modal';
import { scanBigContainerServer, scanPartContainerServer, scanGoodsBarCodeServer } from './server';
import { classFocus, getHeaderTitle } from '../../../lib/util';

const defaultState = {
  bigContainerCode: '',
  containerCode: '',
  goodsBarCode: '',
  loading: false,
  position: 'bigContainer',
  headerTitle: '',
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  reset(draft, action) {
    assign(draft, {
      bigContainerCode: '',
      containerCode: '',
      goodsBarCode: '',
      loading: false,
      position: 'bigContainer',
    });
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  // 扫描大箱号
  * scanBigContainer(action, ctx, put) {
    yield ctx.changeData({
      data: {
        loading: true,
      },
    });
    const data = yield scanBigContainerServer(action.data);
    yield ctx.changeData({
      data: {
        loading: false,
      },
    });
    if (data.code === '0') {
      yield ctx.changeData({
        data: {
          position: 'containerCode',
        },
      });
      classFocus('containerCode');
    } else {
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
        },
      });
      Modal.error({
        title: data.msg,
        className: 'bigContainer',
      });
    }
  },
  // 扫周转箱（散件）
  * scanPartContainer(action, ctx, put) {
    yield ctx.changeData({
      data: {
        loading: true,
      },
    });
    const data = yield scanPartContainerServer(action.data);
    yield ctx.changeData({
      data: {
        loading: false,
      },
    });
    if (data.code === '0') {
      yield ctx.changeData({
        data: {
          position: 'goodsBarCode',
        },
      });
      classFocus('goods_bar');
    } else {
      yield ctx.changeData({
        data: {
          containerCode: '',
        },
      });
      Modal.error({
        title: data.msg,
        className: 'containerCode',
      });
    }
  },
  // 扫商品条码
  * scanGoodsBarCode(action, ctx, put) {
    yield ctx.changeData({
      data: {
        loading: true,
      },
    });
    const data = yield scanGoodsBarCodeServer(action.data);
    yield ctx.changeData({
      data: {
        loading: false,
        goodsBarCode: '',
      },
    });
    if (data.code === '0') {
      Modal.success({
        title: t('扫描商品条码成功'),
        className: 'goods_bar',
      });
    } else {
      Modal.error({
        title: data.msg,
        className: 'goods_bar',
      });
    }
  },
};
