import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form, Article, Button } from 'react-weui/build/packages';
import { push } from 'react-router-redux';
import store from './reducers';
import Header from '../../common/header';
import Footer from '../../common/footer';
import FooterBtn from '../../common/footer-btn';
import FocusInput from '../../common/focus-input';
import Modal from '../../common/modal';
import { classFocus } from '../../../lib/util';

class Container extends Component {
  componentDidMount() {

  }

  render() {
    const {
      dispatch,
      bigContainerCode,
      loading,
      containerCode,
      goodsBarCode,
      position,
      headerTitle,
    } = this.props;
    return (
      <div style={{ marginBottom: '56px' }}>
        <Header title={headerTitle || t('换箱管理(散件)')} />
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            autoFocus
            value={bigContainerCode}
            className="bigContainer"
            disabled={loading || position !== 'bigContainer'}
            onChange={(e) => {
              store.changeData({
                data: {
                  bigContainerCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={() => {
              if (bigContainerCode) {
                store.scanBigContainer({ data: { bigContainerCode } });
              }
            }}
          >
            <label>{t('大箱号')}</label>
          </FocusInput>
        </Form>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            value={containerCode}
            className="containerCode"
            disabled={loading || position !== 'containerCode'}
            onChange={(e) => {
              store.changeData({
                data: {
                  containerCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={() => {
              if (containerCode) {
                store.scanPartContainer({ data: { bigContainerCode, containerCode } });
              }
            }}
          >
            <label>{t('上架周转箱')}</label>
          </FocusInput>
        </Form>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            value={goodsBarCode}
            className="goods_bar"
            disabled={loading || position !== 'goodsBarCode'}
            onChange={(e) => {
              store.changeData({
                data: {
                  goodsBarCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={() => {
              if (goodsBarCode) {
                store.scanGoodsBarCode({ data: { bigContainerCode, containerCode, goodsBarCode } });
              }
            }}
          >
            <label>{t('商品条码')}</label>
          </FocusInput>
        </Form>
        <Footer
          dispatch={dispatch} beforeBack={(back) => {
            store.init();
            back();
          }}
        >
          <FooterBtn
              // loading={loading}
            disabled={!bigContainerCode}
            onClick={() => {
              store.changeData({
                data: {
                  position: 'containerCode',
                  containerCode: '',
                  goodsBarCode: '',
                },
              });
              classFocus('containerCode');
            }}
          >
            {t('换箱')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

export default i18n(Container);
