import { sendPostRequest } from '../../../lib/public-request';

// 根据用户查询异常任务列表，初始进来的那个页面需要初始化数据
export const queryByUserApi = param => sendPostRequest({
  url: '/pda/location_exception/query_by_user',
  param,
});

// 根据用户及库位号查询任务列表
export const queryByUserAndLocationApi = param => sendPostRequest({
  url: '/pda/location_exception/query_by_user_and_location',
  param,
});

// 扫描库位号
export const scanLocationApi = param => sendPostRequest({
  url: '/pda/location_exception/scan_location',
  param,
});

// 查询用户处理中异常任务列表
export const queryHandlingTaskByUserApi = param => sendPostRequest({
  url: '/pda/location_exception/query_handling_task_by_user',
  param,
});

// 完成异常任务处理
export const finishHandlingApi = param => sendPostRequest({
  url: '/pda/location_exception/finish_handling',
  param,
});

// 保存异常任务的备注
export const editExceptionTaskApi = param => sendPostRequest({
  url: '/exception/edit_exception_task',
  param,
});
