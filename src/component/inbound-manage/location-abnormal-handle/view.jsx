import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import store from './reducers';
import HandlePage from './jsx/handle-page';
import DefaultPage from './jsx/default-page';

// 库位异常处理
class Container extends Component {
  componentDidMount() {
    store.init();
    const { status, locationCode } = this.props.match.params;
    // status - 1: 库位异常处理界面点击按钮跳转到盘点，并且盘点那边点返回，回到处理界面
    // status - 3: 库位异常处理界面点击按钮跳转到盘点，并且盘点那边点 完成盘点 ，回到处理界面
    if (status && (status === '1' || status === '3')) {
      store.changeData({ data: { type: '2' } });
      // 根据盘点那边的state信息进行渲染
      store.getBeforeState();
    }

    // status - 2: 库位异常任务显示界面扫码跳或点击处理中跳转到盘点，并且盘点那边点返回，回到库位这边的界面
    if (status && status === '2') {
      store.changeData({ data: { type: '1' } });
      if (locationCode) {
        store.queryByUserAndLocation({ location: locationCode });
      }
    }

    // status - 4: 库位异常处理界面扫码跳或点击处理中转到盘点，并且盘点那边点 完成盘点 ，回到处理界面（可能需要弹窗）
    if (status && status === '4') {
      store.changeData({ data: { type: '2' } });
      if (locationCode) {
        store.getTakeAccountInfo({
          param: {
            locationCode,
          },
        });
      }
    }
  }

  render() {
    const {
      type,
    } = this.props;
    return (
      <div>
        {
          type === '2' ? (
            <HandlePage {...this.props} />
          ) : (
            <DefaultPage {...this.props} />
          )
        }
      </div>
    );
  }
}

Container.propTypes = {
  type: PropTypes.string,
  status: PropTypes.string,
};

export default i18n(Container);
