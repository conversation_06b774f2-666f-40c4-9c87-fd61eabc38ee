import React from 'react';
import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { select } from 'redux-saga/effects';
import { getHeaderTitle, classFocus } from '../../../lib/util';
import {
  queryByUserApi,
  queryByUserAndLocationApi,
  scanLocationApi,
  queryHandlingTaskByUserApi,
  finishHandlingApi,
  editExceptionTaskApi,
} from './server';
import { modal, message } from '../../common';
import styles from './style.css';

const defaultState = {
  headerTitle: '', // 页面标题：统一从后端接口获取
  dataLoading: 1,
  type: '1', // '1' - 默认页面， '2' - 库位异常处理页面
  showListPage: false, // 点击默认页面的 '处理中任务数' ，则打开列表页
  pageNum: 1, // 默认页码为1
  pageSize: 5000, // 默认查5000条数据，前端暂不支持滚动列表数据再请求，只能缓存本地做滚动
  location: '', // 库位号
  count: 0, // 默认页面的总条数
  listData: [
    // {
    //   id: 1,
    //   location: '111',
    //   reason: '黑码',
    //   isRecommend: true,
    // },
  ], // 默认页面的列表数据
  handlingCount: 0, // 处理中任务数
  handlingMaxCount: 0, // 处理中最大任务数量
  subWarehouseName: '', // 所在仓库
  areaName: '', // 所在区域
  isAssign: true, // 该用户是否有指派任务
  handleLocation: '', // 库位号，为防止和其他页面的location字段冲突，处理页面用这个
  reason: '', // 处理页面的异常原因
  remark: '', // 备注
  resultCandidates: [ // 处理结果候选
    // {
    //   handleResult: '盘亏',
    //   id: 1,
    // },
  ],
  handlingListData: [ // 状态为处理中的任务列表
    // {
    //   id: 1,
    //   location: '111',
    //   reason: '黑码',
    // },
  ],
  showResultPop: false, // 是否弹出处理结果的弹窗
  showRemarkPop: false, // 是否弹出备注信息的弹窗
  id: 0, // 库位异常任务id, 最后提交处理结果的时候会用到
  resultId: 0, // 处理结果的id
  result: '', // 处理结果
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    markStatus('dataLoading');
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    // 获取初始化的一些数据
    const res = yield queryByUserApi({
      pageNum: 1,
      pageSize: 5000,
    });
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          count: res.info.meta.count,
          handlingCount: res.info.meta.totalRsp.handlingCount,
          handlingMaxCount: res.info.meta.totalRsp.handlingMaxCount,
          subWarehouseName: res.info.meta.totalRsp.subWarehouseName,
          areaName: res.info.meta.totalRsp.areaName,
          listData: res.info.data,
          isAssign: res.info.meta.totalRsp.isAssign,
        },
      });
      classFocus('location');
    } else {
      modal.error({
        content: res.msg,
        onOk: () => classFocus('location'),
      });
    }
  },
  // 根据库位号及用户继续查询，类似推荐库位的意思
  * queryByUserAndLocation(action, ctx) {
    yield ctx.changeData({
      data: {
        resultId: 0,
        resultCandidates: [],
      },
    });
    const res = yield queryByUserAndLocationApi({
      pageNum: 1,
      pageSize: 5000,
      location: action.location,
    });
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          count: res.info.meta.count,
          handlingCount: res.info.meta.totalRsp.handlingCount,
          handlingMaxCount: res.info.meta.totalRsp.handlingMaxCount,
          subWarehouseName: res.info.meta.totalRsp.subWarehouseName,
          areaName: res.info.meta.totalRsp.areaName,
          listData: res.info.data,
          isAssign: res.info.meta.totalRsp.isAssign,
        },
      });
    } else {
      modal.error({
        content: res.msg,
        onOk: () => classFocus('location'),
      });
      yield ctx.changeData({
        data: {
          type: '1',
          resultCandidates: [],
          resultId: 0,
        },
      });
    }
  },
  // 扫描库位号
  * scanLocation(action, ctx) {
    const res = yield scanLocationApi(action.param);
    yield ctx.changeData({
      data: {
        resultCandidates: [],
        resultId: 0,
      },
    });
    if (res.code === '0') {
      yield ctx.changeData({ data: { showListPage: false } });
      // 只是排除了常规报错的情况，
      // 还要继续判断是否成功
      if (res.info.code && res.info.code === '0') {
        // 成功的情况 - 如果存在多任务（多原因？）则弹窗展示并提示用户去处理
        if (res.info.successData && res.info.successData.hasMultipleReason) {
          const status = yield new Promise((r) => modal.info({
            content: (
              <div style={{ fontSize: 12, backgroundColor: '#FFFFFF' }}>
                <div className={styles.rowItemWrap}>
                  <span>{t('库位')}:</span>
                  <span>{res.info.successData.location}</span>
                </div>
                {
                  res.info.successData.reasons && res.info.successData.reasons.length > 0 ?
                    (
                      <div>
                        {
                          res.info.successData.reasons.map((item, index) => (
                            <div className={styles.rowItemWrap} key={index}>
                              <span>{t('异常原因')}{index + 1}:</span>
                              <span>{item}</span>
                            </div>
                          ))
                        }
                      </div>
                    ) : ''
                }
                <div className={styles.tip}>*{t('注意，该库位有多种异常')}</div>
              </div>
            ),
            onOk: () => r(1),
            okText: t('去处理'),
          }));
          // 点击去处理
          if (status === 1) {
            yield ctx.changeData({
              data: {
                handleLocation: res.info.successData.location,
                reason: res.info.successData.reasons[0],
                remark: res.info.successData.remark,
                resultCandidates: res.info.successData.resultCandidates || [],
                resultId: 0,
                id: res.info.successData.id,
                type: '2',
              },
            });
            // 判断是否进入盘点页面
            if (res.info.successData.isCheck) {
              window.location.hash = `/take-account/take/2/2/${res.info.successData.location}`;
            }
          }
        } else if (res.info.successData && res.info.successData.isCheck) {
          // 非多原因 且是 盘点相关的， 进入盘点页面
          window.location.hash = `/take-account/take/2/2/${res.info.successData.location}`;
        } else {
          // 直接进入处理页面
          yield ctx.changeData({
            data: {
              handleLocation: res.info.successData.location,
              reason: res.info.successData.reasons[0],
              remark: res.info.successData.remark,
              resultCandidates: res.info.successData.resultCandidates || [],
              resultId: 0,
              id: res.info.successData.id,
              type: '2',
            },
          });
        }
      } else {
        // 非常规报错, 有failData
        // 当failData里含有needConfirm===true且不存在msg，不提示，直接推荐库位
        if (res.info.failData && res.info.failData.needConfirm && !res.info.failData.msg) {
          // 根据返回的库位去推荐库位，再次查询
          yield ctx.changeData({ data: { type: '1' } });
          yield ctx.queryByUserAndLocation({ location: res.info.failData.currentLocation });
          return;
        }
        // needConfirm为true,msg为空，直接查
        // needConfirm为false,不需要查数据，要提示
        if (!res.info.failData.needConfirm) {
          // 要提示
          const status = yield new Promise((r) => modal.error({
            content: res.info.failData.msg,
            onOk: () => r(1),
          }));
          if (status === 1) {
            yield ctx.changeData({ data: { location: '' } });
            classFocus('location');
          }
        }
        // needConfirm为true 且msg不为空时，需要提示，点确定再查询
        if (res.info.failData.needConfirm && res.info.failData.msg) {
          yield ctx.changeData({ data: { type: '1' } });
          window.location.hash = '/inbound-manage/location-abnormal-handle';
          // 要提示，点确定再推荐库位
          const status = yield new Promise((r) => modal.error({
            content: res.info.failData.msg,
            onOk: () => r(1),
          }));
          if (status === 1) {
            if (res.info.failData.currentLocation) {
              // 根据返回的库位去推荐库位，再次查询
              yield ctx.queryByUserAndLocation({ location: res.info.failData.currentLocation });
            }
            yield ctx.changeData({ data: { location: '' } });
            classFocus('location');
          }
        }
      }
    } else {
      // 常规报错
      yield ctx.changeData({ data: { location: '' } });
      modal.error({
        content: res.info.failData.msg || res.msg,
        onOk: () => classFocus('location'),
      });
    }
  },
  // 查看处理中任务数
  * queryHandlingTaskByUser(action, ctx) {
    // 显示showListPage页面
    yield ctx.changeData({
      data: {
        showListPage: true,
      },
    });
    const res = yield queryHandlingTaskByUserApi({
      pageNum: 1,
      pageSize: 5000,
    });
    if (res.code === '0') {
      if (res.info.data && res.info.data.length > 0) {
        yield ctx.changeData({
          data: {
            handlingListData: res.info.data,
            handlingCount: res.info.meta.count,
            handlingMaxCount: res.info.meta.totalRsp.handlingMaxCount,
          },
        });
      } else {
        yield ctx.changeData({
          data: {
            handlingListData: [],
            handlingMaxCount: 0,
            handlingCount: 0,
          },
        });
      }
    } else {
      modal.error({
        content: res.msg,
      });
    }
  },
  // 完成异常的处理
  * finishHandling(action, ctx) {
    const res = yield finishHandlingApi(action.param);
    if (res.code === '0') {
      message.success(t('处理完成'));
      // remainingFlag - 该用户是否还领取了该库区其余任务处理中,false否true是
      if (res.info.remainingFlag) {
        const status = yield new Promise((r) => modal.confirm({
          content: res.info.msg,
          onOk: () => r(1),
          onCancel: () => r(2),
          okText: t('去处理'),
        }));
        if (status === 1) {
          if (res.info.nextTask) {
            // 继续处理下一个任务
            if (res.info.nextTask.isCheck) {
              // 去盘点页面
              window.location.hash = `/take-account/take/2/2/${res.info.nextTask.location}`;
            } else {
              // 更新当前处理页面，继续处理
              yield ctx.changeData({
                data: {
                  handleLocation: res.info.nextTask.location,
                  id: res.info.nextTask.id,
                  reason: res.info.nextTask.reason,
                  remark: res.info.nextTask.remark,
                  resultCandidates: res.info.nextTask.resultCandidates || [],
                  resultId: 0,
                  type: '2',
                },
              });
            }
          } else {
            // 推荐库位, 回到任务列表
            yield ctx.changeData({ data: { type: '1', showListPage: false } });
            yield ctx.queryByUserAndLocation({ location: res.info.currentLocation });
          }
        } else {
          // 回到任务列表页
          yield ctx.changeData({ data: { type: '1', showListPage: false, location: '' } });
          // window.location.hash = '/inbound-manage/location-abnormal-handle';
          // yield ctx.init();
          yield ctx.queryByUserAndLocation({ location: res.info.currentLocation });
        }
      } else {
        // 推荐库位, 回到任务列表
        yield ctx.changeData({ data: { type: '1', showListPage: false, location: '' } });
        yield ctx.queryByUserAndLocation({ location: res.info.currentLocation });
      }
    } else {
      modal.error({
        content: res.msg,
      });
    }
  },
  // 保存异常任务的备注
  * editExceptionTask(action, ctx) {
    const res = yield editExceptionTaskApi(action.param);
    if (res.code === '0') {
      message.success(t('保存备注成功！'), 1000);
    } else {
      message.error(res.msg, 1000);
    }
  },
  // 这个的逻辑只是为了从盘点那边回来的时候渲染用，是不用请求数据的！但是逻辑其实跟上面完成处理差不多
  * getTakeAccountInfo(action, ctx) {
    const data = yield select((state) => state['take-account/take']);
    if (data && data.locationExFinishRsp && data.locationExFinishRsp.nextTask && data.locationExFinishRsp.nextTask.id) {
      if (data.locationExFinishRsp.remainingFlag) {
        const status = yield new Promise((r) => modal.confirm({
          content: data.locationExFinishRsp.msg,
          onOk: () => r(1),
          onCancel: () => r(2),
          okText: t('去处理'),
        }));
        if (status === 1) {
          if (data.locationExFinishRsp.nextTask) {
            // 继续处理下一个任务
            if (data.locationExFinishRsp.nextTask.isCheck) {
              // 去盘点页面
              window.location.hash = `/take-account/take/2/2/${data.locationExFinishRsp.nextTask.location}`;
            } else {
              // 更新当前处理页面，继续处理
              yield ctx.changeData({
                data: {
                  handleLocation: data.locationExFinishRsp.nextTask.location,
                  id: data.locationExFinishRsp.nextTask.id,
                  reason: data.locationExFinishRsp.nextTask.reason,
                  remark: data.locationExFinishRsp.nextTask.remark,
                  resultCandidates: data.locationExFinishRsp.nextTask.resultCandidates || [],
                  resultId: 0,
                  type: '2',
                },
              });
            }
          } else {
            // 推荐库位, 回到任务列表
            yield ctx.changeData({ data: { type: '1', showListPage: false } });
            yield ctx.queryByUserAndLocation({
              location: data.locationExFinishRsp.currentLocation,
            });
          }
        } else {
          // 回到任务列表页
          yield ctx.changeData({ data: { type: '1', showListPage: false, location: '' } });
          // window.location.hash = '/inbound-manage/location-abnormal-handle';
          // yield ctx.init();
          yield ctx.queryByUserAndLocation({
            location: data.locationExFinishRsp.currentLocation,
          });
        }
      } else {
        // 推荐库位, 回到任务列表
        yield ctx.changeData({ data: { type: '1', showListPage: false, location: '' } });
        yield ctx.queryByUserAndLocation({ location: data.locationExFinishRsp.currentLocation });
      }
    } else {
      // 没拿到后端返回的数据，则跳转到首页推荐库位
      yield ctx.changeData({ data: { type: '1', showListPage: false, location: '' } });
      yield ctx.queryByUserAndLocation({ location: action.param.locationCode });
    }
  },
  * getBeforeState(action, ctx) {
    const { locationExFinishRsp } = yield select((state) => state['take-account/take']);
    if (locationExFinishRsp) {
      const {
        handleLocation,
        reason,
        id,
        resultCandidates,
      } = locationExFinishRsp;
      yield ctx.changeData({
        data: {
          handleLocation,
          reason,
          id,
          resultCandidates,
        },
      });
    }
  },
};
