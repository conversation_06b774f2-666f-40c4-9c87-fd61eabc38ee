// 默认页面中点击'处理中任务数'，则展示这个页面
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import { ScrollList } from '../../../common';
import store from '../reducers';
import styles from '../style.css';

class ShowListPage extends Component {
  render() {
    const {
      handlingCount,
      handlingMaxCount,
      handlingListData,
    } = this.props;
    const height = window.innerHeight - 44 - 58;
    const rows = [
      [
        {
          title: `${t('库位')}: `,
          // render: 'location',
          render: (v) => (
            <div>
              <span style={{ color: '#666c7c' }}>{t('库位')}: </span>
              <span style={{ color: v.isAssign ? 'red' : '' }}>{v.location}</span>
            </div>
          ),
        },
        {
          title: t('处理中'),
          render: (v) => (
            <span
              style={{ color: '#0059CE' }}
              onClick={() => {
                if (v.isCheck) {
                  window.location.hash = `/take-account/take/2/2/${v.location}`;
                } else {
                  store.changeData({
                    data: {
                      showListPage: false,
                      type: '2',
                      id: v.id || '',
                      handleLocation: v.location || '',
                      resultCandidates: v.resultCandidates || [],
                      remark: v.remark || '',
                      reason: v.reason,
                    },
                  });
                }
              }}
            >
              {t('处理中')}
              <Icon name="arr-right" style={{ color: '#0059CE' }} />
            </span>
          ),
        },
      ],
      [
        {
          title: `${t('异常原因')}: `,
          render: 'reason',
        },
      ],
    ];
    return (
      <div
        style={{
          zIndex: 80,
          height,
        }}
        className={styles.showListWrap}
      >
        <div className={styles.rowItemWrap}>
          <div>
            {t('共')}<span style={{ color: '#FF993C' }}>{handlingCount}</span>{t('条数据')}
          </div>
          <div>
            <div style={{ color: '#FF993C' }}>
              *{t('处理中任务数量最多')}{handlingMaxCount}{t('个')}
            </div>
          </div>
        </div>
        <ScrollList
          rows={rows}
          data={handlingListData}
          style={{ height: 365, overflowY: 'auto' }}
          showNum={25}
        />
      </div>
    );
  }
}

ShowListPage.propTypes = {
  handlingCount: PropTypes.number,
  handlingMaxCount: PropTypes.number,
  handlingListData: PropTypes.arrayOf(PropTypes.object),
};

export default ShowListPage;
