// 默认的页面，库位异常列表页面
import React, { Component } from 'react';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import { push } from 'react-router-redux';
import { Form } from 'react-weui/build/packages';
import {
  Footer,
  FocusInput,
  RowInfo,
  ScrollList,
} from '../../../common';
import ShowListPage from './show-list-page';
import store from '../reducers';
import styles from '../style.css';
import Header from '../../../common/header';

class DefaultPage extends Component {
  render() {
    const {
      dispatch,
      dataLoading,
      type,
      showListPage,
      listData,
      count,
      subWarehouseName,
      areaName,
      handlingCount,
      isAssign,
      location,
    } = this.props;
    // 根据是否推荐及是否指派判断库位号字体的颜色
    const getColor = (v) => {
      if (v.isRecommend) {
        return '#0059CE';
      }
      if (isAssign) {
        return 'red';
      }
      return '';
    };
    const rows = [
      [
        {
          title: `${t('库位')}: `,
          // render: 'location',
          render: (v) => (
            <div>
              <span style={{ color: '#666c7c' }}>{t('库位')}: </span>
              <span
                style={{ color: getColor(v) }}
              >
                {v.location}
              </span>
            </div>
          ),
        },
      ],
      [
        {
          title: `${t('异常原因')}: `,
          render: 'reason',
        },
      ],
    ];
    return (
      <div
        style={{ overflowY: 'hidden' }}
      >
        <Header title={t('库位异常列表')} />
        <div
          onClick={() => {
            if (handlingCount > 0) {
              store.queryHandlingTaskByUser();
            }
          }}
        >
          <RowInfo
            label={t('处理中任务数')}
            content={
              (
                <div>
                  <span style={{ color: '#FF993C' }}>{handlingCount}</span>
                  {
                    handlingCount !== 0 ? (
                      <Icon name="arr-right" style={{ color: '#E8EBF0', marginLeft: 10 }} />
                    ) : null
                  }
                </div>
              )
            }
          />
        </div>
        <Form>
          <FocusInput
            autoFocus
            data-bind="location"
            onPressEnter={(e) => {
              if (e.target.value) {
                store.scanLocation({
                  param: {
                    location,
                  },
                });
              }
            }}
            disabled={dataLoading === 0}
            className="location"
          >
            <label>{t('库位号')}</label>
          </FocusInput>
        </Form>
        <div className={styles.rowItemWrap}>
          <div>
            {t('共')}<span style={{ color: '#FF993C' }}>{count}</span>{t('条数据')}
          </div>
          <div>
            {
              isAssign ? (
                <span style={{ color: '#FF993C' }}>*{t('指派任务需优先处理')}</span>
              ) : (
                <div>
                  <span>{subWarehouseName}</span>
                  <span style={{ marginLeft: 10 }}>{areaName}</span>
                </div>
              )
            }
          </div>
        </div>
        <ScrollList
          rows={rows}
          data={listData}
          style={{ height: 270, overflowY: 'auto' }}
          showNum={25}
        />
        {
          showListPage ? (
            <ShowListPage {...this.props} />
          ) : null
        }
        <Footer
          beforeBack={(back) => {
            if (showListPage) {
              store.changeData({
                data: {
                  showListPage: false,
                },
              });
            } else {
              back();
            }
          }}
        />
      </div>
    );
  }
}

DefaultPage.propTypes = {
  dispatch: PropTypes.func.isRequired,
  type: PropTypes.string,
  showListPage: PropTypes.bool,
  dataLoading: PropTypes.number,
  listData: PropTypes.arrayOf(PropTypes.object),
  count: PropTypes.number,
  subWarehouseName: PropTypes.string,
  areaName: PropTypes.string,
  handlingCount: PropTypes.number,
  isAssign: PropTypes.bool,
  location: PropTypes.string,
};

export default DefaultPage;
