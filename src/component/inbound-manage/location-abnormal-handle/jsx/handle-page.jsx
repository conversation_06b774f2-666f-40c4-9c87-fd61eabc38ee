// 库位异常处理页面
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import { push } from 'react-router-redux';
import { classFocus } from '../../../../lib/util';
import {
  FooterBtn,
  Footer,
  Header,
  PopRadio,
  PopTextArea,
} from '../../../common';
import store from '../reducers';

class HandlePage extends Component {
  render() {
    const {
      dispatch,
      headerTitle,
      handleLocation,
      reason,
      resultCandidates,
      showResultPop,
      showRemarkPop,
      result,
      resultId,
      remark,
      id,
    } = this.props;
    return (
      <div>
        <Header title={headerTitle}>
          <div
            onClick={() => {
              dispatch(push(`/take-account/take/2/1/${handleLocation}`));
            }}
          >
            <Icon name="pandian" style={{ marginRight: 10 }} />
            {t('实时盘点')}
          </div>
        </Header>
        <div style={{ paddingLeft: 15, fontSize: '16px' }}>
          <div>
            <span style={{ marginRight: '8px' }}>{t('库位')}:</span>
            <span>{handleLocation}</span>
          </div>
          <div style={{ height: 35, lineHeight: '35px' }}>
            <span style={{ marginRight: '8px' }}>{t('异常原因')}:</span>
            <span>{reason}</span>
          </div>
        </div>
        <PopRadio
          label={t('处理结果')}
          selectValue={resultId}
          selectList={resultCandidates}
          labelName="handleResult"
          valueName="id"
          show={showResultPop}
          onClick={() => {
            store.changeData({ data: { showResultPop: true } });
          }}
          onCancel={() => {
            store.changeData({ data: { showResultPop: false } });
          }}
          onOk={(val, selectItem) => {
            let str = '';
            if (selectItem && selectItem.handleResult) {
              str = selectItem.handleResult;
            }
            store.changeData({
              data: {
                showResultPop: false,
                resultId: val,
                result: str,
              },
            });
          }}
        />
        <PopTextArea
          label={t('备注')}
          placeholder={t('请输入')}
          value={remark}
          show={showRemarkPop}
          onClick={() => {
            store.changeData({ data: { showRemarkPop: true } });
            classFocus('textAreaPop');
          }}
          onCancel={() => {
            store.changeData({ data: { showRemarkPop: false } });
          }}
          onOk={(val) => {
            store.changeData({
              data: {
                showRemarkPop: false,
                remark: val,
              },
            });
            store.editExceptionTask({
              param: {
                ids: [id],
                remark: val,
              },
            });
          }}
        />
        <Footer
          beforeBack={() => {
            store.changeData({ data: { type: '1', location: '' } });
            if (handleLocation) {
              store.queryByUserAndLocation({ location: handleLocation });
            } else {
              dispatch(push('/inbound-manage/location-abnormal-handle'));
              store.init();
            }
          }}
        >
          <FooterBtn
            disabled={!handleLocation || resultId === 0}
            onClick={() => {
              if (handleLocation && resultId && result) {
                store.finishHandling({
                  param: {
                    id,
                    resultId,
                    result,
                  },
                });
              }
            }}
          >
            {t('确认')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

HandlePage.propTypes = {
  dispatch: PropTypes.func.isRequired,
  headerTitle: PropTypes.string,
  handleLocation: PropTypes.string,
  reason: PropTypes.string,
  resultCandidates: PropTypes.arrayOf(PropTypes.object),
  showResultPop: PropTypes.bool,
  showRemarkPop: PropTypes.bool,
  result: PropTypes.string,
  resultId: PropTypes.number,
  remark: PropTypes.string,
  id: PropTypes.number,
};

export default HandlePage;
