import assign from 'object-assign';

// 库位异常上报
// 默认引入nav/uploadError.jsx 这个公共模块作为页面,方法也是使用nav/reducers下的方法
const defaultState = {
  headerTitle: '', // 页面标题：统一从后端接口获取
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  // * init(action, ctx) {
  //   yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  // },
};
