import React, { Component } from 'react';
// import PropTypes from 'prop-types';
import navStore from '../../nav/reducers';
import { classFocus } from '../../../lib/util';

// 库位异常上报
// 默认引入nav/uploadError.jsx 这个公共模块作为页面
class Container extends Component {
  componentDidMount() {
    navStore.changeData({ data: { showUploadError: true } });
    classFocus('location');
  }

  render() {
    return (
      <br />
    );
  }
}

Container.propTypes = {};

export default Container;
