import assign from 'object-assign';
import { getCurrentMenuList, getFormatBtnList } from '../../../lib/util';

const defaultState = {
  menus: [],
  btnList: [],
  headTitle: '',
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData: (draft, action) => {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    const menuItem = getCurrentMenuList();
    if (menuItem) {
      yield ctx.changeData({
        data: {
          headTitle: menuItem.title,
          btnList: getFormatBtnList(menuItem.children),
        },
      });
    }
  },
};
