import React, { Component } from 'react';
import { i18n, t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import { Header } from '../../common';
import store from './reducers';
import style from './style.css';
import ChooseContainerType from './jsx/choose-container-type';
import HasOrNoHead from './jsx/has-or-no-head';
import Detail from './jsx/detail';

class Container extends Component {
  componentDidMount() {
    // eslint-disable-next-line react/prop-types
    const { match: { params } } = this.props;
    // eslint-disable-next-line react/prop-types
    store.init(params);
  }

  render() {
    const {
      headerTitle,
      type,
      dataLoading,
    } = this.props;
    switch (type) {
      case 1:
        return (
          <section>
            <Header title={headerTitle || t('多货装箱')}>
              <Icon className={style.loader} name="loading" data-if={dataLoading === 0} />
            </Header>
            <ChooseContainerType {...this.props} />
          </section>
        );
      case 2:
        return (
          <section>
            <Header title={t('多货装箱')}>
              <Icon className={style.loader} name="loading" data-if={dataLoading === 0} />
            </Header>
            <HasOrNoHead {...this.props} />
          </section>
        );
      case 3:
        return (
          <section>
            <Header title={t('装箱商品明细')}>
              <Icon className={style.loader} name="loading" data-if={dataLoading === 0} />
            </Header>
            <Detail {...this.props} />
          </section>
        );
      default:
        return '';
    }
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  type: PropTypes.number,
  containersCount: PropTypes.number,
  containerCode: PropTypes.string,
  dataLoading: PropTypes.number,
};

export default i18n(Container);
