import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import message from 'common/message';
import Icon from '@shein-components/Icon';
import store from '../reducers';
import Footer from '../../../common/footer';
import List from '../../../common/list';
import style from '../../../style.css';

class DetailList extends Component {
  render() {
    const {
      dispatch,
      boxedList,
      focusPosition,
      isHeadType,
    } = this.props;

    const height = window.innerHeight - 44 - 56;
    const rows = [
      [
        {
          title: 'SKC',
          render: (record) => <span>SKC：{record.skc}</span>,
        },
        {
          title: t('数量'),
          render: (record) => (
            <span>
              {t('数量')}：{record.totalNum || 0}
              {
                !isHeadType && (
                  <span onClick={() => {
                    message.info(t('括号内数量为仓内理货多货已标记数量'));
                  }}
                  >[{record.multipleNum || 0}]<Icon style={{ color: '#0059ce' }} name="question" />
                  </span>
                )
              }
            </span>
          ),
        },
      ],
      [
        {
          title: t('尺码'),
          render: (record) => <span>{t('尺码')}：{record.size}</span>,
        },
        {
          title: '',
          render: (record) => <span>{record.storeTypeName}</span>,
        },
      ],
    ];

    return (
      <section className={style.flexColContainer} style={{ height }}>
        <List
          rows={rows}
          data={boxedList}
          rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0' }}
        />
        <Footer
          dispatch={dispatch}
          beforeBack={() => {
            store.changeData({
              data: {
                type: 2,
              },
            });
            store.classFocus(focusPosition);
          }}
        />
      </section>
    );
  }
}

DetailList.propTypes = {
  dispatch: PropTypes.func,
  boxedList: PropTypes.arrayOf(PropTypes.shape()),
  focusPosition: PropTypes.string,
  isHeadType: PropTypes.bool,
};

export default DetailList;
