import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import {
  Form,
  Button,
  FormCell,
  CellBody,
  CellFooter,
  Switch,
} from 'react-weui/build/packages';
import { classFocus } from 'lib/util';
import Modal from 'common/modal';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import Footer from '../../../common/footer';
import RowInfo from '../../../common/row-info';

class HasOrNoHead extends Component {
  render() {
    const {
      dataLoading,
      containerCode,
      hasPackedNum,
      relatedContainerCode,
      isRelatedContainerCodeDisabled,
      skuBarCode,
      isContainerCodeDisabled,
      focusPosition,
      isHeadType,
      multiPpMark,
      isShowMultiPpMark,
      scanContainerCodeSucceed,
    } = this.props;

    return (
      <section>
        {isHeadType && (
          <Form>
            <FocusInput
              autoFocus
              placeholder={t('请扫描')}
              disabled={dataLoading === 0 || isRelatedContainerCodeDisabled}
              className="relatedContainerCode"
              value={relatedContainerCode}
              onChange={(e) => {
                store.changeData({
                  data: {
                    relatedContainerCode: e.target.value.trim(),
                  },
                });
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanRelatedContainer({
                  params: {
                    relatedContainerCode: e.target.value.trim(),
                  },
                });
                store.changeData({
                  isRelatedContainerCodeDisabled: true, // 禁用相关箱号输入框
                });
              }}
              footer={(
                <Button
                  type="primary"
                  style={{ marginBottom: '5px', padding: '0 8px' }}
                  size="small"
                  onClick={() => {
                    Modal.confirm({
                      modalBlurInput: true,
                      content: (
                        <div>
                          <div>{t('是否换箱（相关箱号）')}</div>
                        </div>
                      ),
                      onOk: () => new Promise(() => {
                        store.changeData({
                          data: {
                            relatedContainerCode: '',
                            isRelatedContainerCodeDisabled: false, // 取消相关箱号禁用
                          },
                        });
                        classFocus('relatedContainerCode'); // 焦点聚焦相关箱号
                      }),
                      onCancel: () => {
                        classFocus(focusPosition); // 焦点聚焦到上次的焦点聚焦位置
                      },
                    });
                  }}
                >
                  {t('换箱')}
                </Button>
              )}
            >
              <label>{t('相关箱号')}</label>
            </FocusInput>
          </Form>
        )}
        {(isShowMultiPpMark && !isHeadType) && (
          <Form>
            <FormCell switch>
              <CellBody>{t('仓内理货多货标记')}</CellBody>
              <CellFooter>
                <Switch
                  checked={multiPpMark}
                  onChange={(e) => store.changeData({
                    data: {
                      multiPpMark: e.target.checked,
                    },
                  })}
                />
              </CellFooter>
            </FormCell>
          </Form>
        )}
        <Form>
          <FocusInput
            autoFocus={!isHeadType}
            placeholder={t('请扫描')}
            className="containerCode"
            disabled={
            dataLoading === 0 || isContainerCodeDisabled || (isHeadType && !relatedContainerCode)
          }
            value={containerCode}
            onChange={(e) => {
              store.changeData({
                data: {
                  containerCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.changeData({
                data: {
                  isContainerCodeDisabled: true,
                },
              });
              store.scanContainerCode({
                params: {
                  containerCode: e.target.value.trim(),
                },
              });
            }}
          >
            <label>{t('周转箱号')}</label>
          </FocusInput>
        </Form>
        <Form>
          <RowInfo
            label={t('已装箱商品数量')}
            content={hasPackedNum > 0 ? (
              <span
                style={{ color: '#0059ce' }}
                onClick={() => store.changeData({ data: { type: 3 } })}
              >
                {hasPackedNum}
                <Icon name="arr-right" />
              </span>
            ) : hasPackedNum}
          />
        </Form>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            className="skuBarCode"
            value={skuBarCode}
            disabled={
            (isHeadType && !relatedContainerCode) ||
              !scanContainerCodeSucceed
          }
            onChange={(e) => {
              store.changeData({
                data: {
                  skuBarCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.scanSkuCode({
                params: {
                  confirmClose: 0, // 是否确认关箱 0-否 1-是 当扫描的是箱号的时候使用
                  skuBarCode: e.target.value.trim(),
                },
              });
            }}
          >
            <label>{t('商品条码')}</label>
          </FocusInput>
        </Form>
        <Footer
          beforeBack={() => {
            store.changeData({
              data: {
                type: 1,
              },
            });
          }}
        />
      </section>
    );
  }
}

HasOrNoHead.propTypes = {
  containerCode: PropTypes.string,
  isContainerCodeDisabled: PropTypes.bool,
  dataLoading: PropTypes.number,
  skuBarCode: PropTypes.string,
  relatedContainerCode: PropTypes.string,
  isRelatedContainerCodeDisabled: PropTypes.bool,
  focusPosition: PropTypes.string,
  hasPackedNum: PropTypes.number,
  isHeadType: PropTypes.bool,
  multiPpMark: PropTypes.bool,
  isShowMultiPpMark: PropTypes.bool,
  scanContainerCodeSucceed: PropTypes.bool,
};

export default HasOrNoHead;
