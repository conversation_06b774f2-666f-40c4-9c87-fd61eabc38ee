import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Form,
} from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import Pickers from '../../../common/pickers';
import Footer from '../../../common/footer';

class ChooseContainerType extends Component {
  render() {
    const {
      packingLabel,
      packingOptions,
      showPicker,
    } = this.props;

    return (
      <div>
        <Form>
          <Pickers
            label={t('装箱类型')}
            placeholder={t('全部类型')}
            defaultValue={packingLabel}
            onClick={() => store.changeData({ data: { showPicker: true } })}
            onChange={(select) => {
              store.changeData({
                data: {
                  showPicker: false,
                  packing: select.value,
                  packingLabel: select.label,
                  type: 2, // 跳转有头无头多货装箱页面
                  isHeadType: Number(select.value) === 1, // 修改传给后端的参数 也是区分有头无头
                },
              });
              store.clear(); // 清空页面一些暂存值
              store.startShiftMultiple({
                params: {
                  orderType: select.value, // 单据类型 1 有头多货装箱;2 无头
                },
              });
            }}
            value={packingLabel || ''}
            show={showPicker}
            pickerData={packingOptions}
            onCancel={() => store.changeData({ data: { showPicker: false } })}
          />
        </Form>
        <Footer
          beforeBack={(back) => {
            store.init();
            back();
          }}
        />
      </div>
    );
  }
}

ChooseContainerType.propTypes = {
  packingLabel: PropTypes.string,
  packingOptions: PropTypes.arrayOf(PropTypes.shape),
  showPicker: PropTypes.bool,
};

export default ChooseContainerType;
