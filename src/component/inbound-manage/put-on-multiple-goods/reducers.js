import assign from 'object-assign';
import { markStatus } from 'rrc-loader-helper';
import { t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import { message, modal } from '../../common';
import {
  scanRelatedContainerAPI,
  scanContainerCodeAPI,
  scanSkuCodeAPI,
  startShiftMultipleAPI,
} from './server';
import aaooAudio from '../../../source/audio/aaoo.mp3';
import dingdongAudio from '../../../source/audio/dingdong.mp3';
import {
  getWarehouseId, classFocus as utilClassFocus, classFocus, getHeaderTitle,
} from '../../../lib/util';

const aaooEle = new Audio(aaooAudio);
const dingdongEle = new Audio(dingdongAudio);
aaooEle.load();
dingdongEle.load();

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '',
  type: 1, // 1 默认选择装箱页面 2 有头多货装箱 3 有头多货装箱明细页面 4 无头多货装箱 5 无头多货装箱明细页面
  showPicker: false, // 选择箱类型
  warehouseId: '', // 选择的仓库
  subWarehouseId: '', // 选择绑定的子仓
  packing: '', // 选择的装箱类型
  packingLabel: '', // 选择的装箱类型名称
  packingOptions: [
    {
      items: [
        {
          id: 1,
          label: t('有头多货装箱'),
          value: '1',
        },
        {
          id: 2,
          label: t('无头多货装箱'),
          value: '2',
        },
      ],
    },
  ], // 装箱类型选择
  relatedContainerCode: '', // 相关箱号
  containerCode: '', // 箱号
  isHeadType: false, // 是否有头多货装箱
  isRelatedContainerCodeDisabled: false, // 是否禁用相关箱号输入框
  isContainerCodeDisabled: false, // 周转箱是否禁用
  focusPosition: '', // 当前光标定位
  skuBarCode: '', // 商品条码
  hasPackedNum: 0, // 已装箱数量
  boxedList: [], // 已装箱商品明细
  shiftOrderCode: '', // 移位单号初始化接口可能返回 然后扫条码进行更新 扫下次条码再传给后端
  multiOriginOrderNo: '', // 多货来源单号 扫相关箱号返回 扫条码需要传给后端
  multiPpMark: false, // 仓内理货多货标记 0-关 1-开 传递的时候转换一下给后端
  isShowMultiPpMark: false, // 是否展示仓内理货多货标记开关
  scanContainerCodeSucceed: false, // 扫描箱子是否成功
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  reset(draft) {
    assign(draft, defaultState);
  },
  * clear(action, ctx) {
    yield ctx.changeData({
      data: {
        /* 切换的时候清空一些缓存值 */
        relatedContainerCode: '', // 清空相关箱号
        containerCode: '', // 清空周转箱号
        isRelatedContainerCodeDisabled: false,
        isContainerCodeDisabled: false,
        focusPosition: '',
        skuBarCode: '',
        hasPackedNum: 0,
        boxedList: [],
        shiftOrderCode: '',
        multiOriginOrderNo: '',
        multiPpMark: false,
        isShowMultiPpMark: false,
        scanContainerCodeSucceed: false, // 扫描箱子是否成功
      },
    });
  },
  * classFocus(className, ctx) {
    utilClassFocus(className);
    yield ctx.changeData({
      data: {
        focusPosition: className,
      },
    });
  },
  * init(params, ctx, put) {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId || !params?.status) {
      yield put(push(`/homework-subwarehouse/${encodeURIComponent('/inbound-manage/put-on-multiple-goods')}/1`));
      return;
    }
    yield ctx.changeData({
      data: {
        headerTitle: getHeaderTitle(),
        subWarehouseId: preSubMenu.subWarehouseId,
      },
    });
  },
  /**
   * 选择不同的装箱类型触发初始化接口
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * startShiftMultiple(action, ctx) {
    markStatus('dataLoading');
    const {
      subWarehouseId,
      isHeadType,
    } = yield '';
    const params = {
      ...action.params,
      subWarehouseId,
    };
    const data = yield startShiftMultipleAPI(params);
    if (data.code === '0') {
      const {
        containerCode, detailList, hasPackedNum, relatedCode, shiftOrderCode, isShowMultiPpMark,
        multiOriginOrderNo,
      } = data?.info || {};
      yield ctx.changeData({
        data: {
          hasPackedNum: hasPackedNum || 0, // 填充已装箱数量
          boxedList: detailList || [], // 填充已装箱商品明细
          shiftOrderCode,
          isShowMultiPpMark,
          multiOriginOrderNo,
        },
      });
      // 如果打开的是有头页面 初始化 如果有相关箱号和箱号 就填充
      if (isHeadType) {
        // 如果有进行中的相关箱号和周转箱号
        if (relatedCode && containerCode) {
          yield ctx.changeData({
            data: {
              relatedContainerCode: relatedCode, // 填充相关箱号
              containerCode, // 填充箱号
              isRelatedContainerCodeDisabled: true, // 禁用相关箱号输入框
              isContainerCodeDisabled: true, // 禁用周转箱号输入框
              scanContainerCodeSucceed: true, // 有相关箱号和周转箱号填充的话 则直接放开条码输入框
            },
          });
          yield ctx.classFocus('skuBarCode'); // 跳到商品条码处
        } else {
          yield ctx.classFocus('relatedContainerCode'); // 跳到相关箱号处
        }
      } else {
        // 如果是无头页面 初始化 如果有箱号就填充箱号
        if (containerCode) {
          yield ctx.changeData({
            data: {
              containerCode, // 填充箱号
              isContainerCodeDisabled: true, // 禁用周转箱号输入框
              scanContainerCodeSucceed: true, // 有周转箱号填充的话 则直接放开条码输入框
            },
          });
          yield ctx.classFocus('skuBarCode'); // 跳到商品条码处
        } else {
          yield ctx.classFocus('containerCode'); // 跳到箱号处
        }
      }
    } else {
      modal.error({ content: data.msg });
      yield ctx.changeData({
        data: {
          type: 1,
        },
      });
    }
  },
  /**
   * 扫描相关箱号
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * scanRelatedContainer(action, ctx) {
    markStatus('dataLoading');
    const { subWarehouseId, containerCode } = yield '';
    const data = yield scanRelatedContainerAPI({
      ...action.params,
      subWarehouseId,
      warehouseId: getWarehouseId(),
    });
    if (data.code === '0') {
      // 保存截取后的相关箱号
      yield ctx.changeData({
        data: {
          relatedContainerCode: data.info?.relatedContainerCode,
        },
      });
      // 如果containerCode有值 扫了相关箱号就跳到扫条码位置
      if (containerCode) {
        yield ctx.changeData({
          data: {
            multiOriginOrderNo: data.info?.multiOriginOrderNo, // 缓存来源单号
            scanContainerCodeSucceed: true, // 放开条码输入框
          },
        });
        yield ctx.classFocus('skuBarCode'); // 光标跳转到箱号
      } else {
        // 如果containerCode没值 扫了相关箱号就跳了周转箱位置
        yield ctx.changeData({
          data: {
            containerCode: '', // 清空箱号
            isContainerCodeDisabled: false, // 取消箱号禁用
            multiOriginOrderNo: data.info?.multiOriginOrderNo, // 缓存来源单号
          },
        });
        yield ctx.classFocus('containerCode'); // 光标跳转到箱号
      }
    } else {
      aaooEle.play();// 报错音提示
      yield ctx.changeData({ data: { relatedContainerCode: '', isRelatedContainerCodeDisabled: false } });
      modal.error({
        content: data.msg,
        className: 'relatedContainerCode',
      });
    }
  },
  /**
   * 扫描周转箱
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * scanContainerCode(action, ctx) {
    markStatus('dataLoading');
    const { isHeadType, subWarehouseId } = yield '';
    const data = yield scanContainerCodeAPI({
      ...action.params,
      subWarehouseId,
      isHeadType, // 是否有头多货装箱
      warehouseId: getWarehouseId(),
    });
    if (data.code === '0') {
      yield ctx.changeData({
        data: {
          skuBarCode: '', // 清空条码输入框
          containerCode: data?.info?.containerCode || '',
          scanContainerCodeSucceed: true, // 放开条码输入框
        },
      });
      yield ctx.classFocus('skuBarCode'); // 聚焦条码输入框
    } else {
      aaooEle.play();// 报错音提示
      yield ctx.changeData({
        data: {
          containerCode: '',
          isContainerCodeDisabled: false,
        },
      });
      modal.error({ content: data.msg, className: 'containerCode' });
    }
  },
  /**
   * 扫描商品条码
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * scanSkuCode(action, ctx) {
    markStatus('dataLoading');
    const {
      subWarehouseId, relatedContainerCode, containerCode, shiftOrderCode, hasPackedNum, boxedList,
      multiOriginOrderNo, isHeadType, multiPpMark,
    } = yield '';
    const params = {
      ...action.params,
      relatedCode: relatedContainerCode, // 相关箱号
      containerCode, // 周转箱号
      shiftOrderCode, // 移位单号
      multiOriginOrderNo, // 多货来源箱号
      warehouseId: getWarehouseId(),
      subWarehouseId,
      orderType: isHeadType ? 1 : 2,
    };
    // 无头多货装箱需要传仓内理货多货标记标识
    if (!isHeadType) {
      params.multiPpMark = multiPpMark ? 1 : 0; // 仓内理货多货标记 0-关 1-开
    }
    const data = yield scanSkuCodeAPI(params);
    if (data.code === '0') {
      // 扫条码关箱场景 0-扫描条码返回 1-关箱返回成功 2 触发你否确认关箱弹框
      if (data?.info?.returnFlag === 2) {
        dingdongEle.play(); // 叮咚
        const status = yield new Promise((r) => {
          modal.confirm({
            content: t('是否确认关箱'),
            onOk: () => r(true),
            onCancel: () => r(false),
          });
        });
        if (status) {
          yield ctx.scanSkuCode({
            params: {
              ...params,
              confirmClose: 1, // 确认关箱
            },
          });
        }
      } else if (data?.info?.returnFlag === 1) {
        // 有头保留相关箱号，清空页面其他信息，光标停留在周转箱号文本框
        yield ctx.clear();
        if (isHeadType) {
          yield ctx.changeData({
            data: {
              relatedContainerCode,
            },
          });
        }
        classFocus('containerCode');
        message.success(t('关箱成功'));
      } else {
        // 正常扫条码流程
        const {
          size, skc, storeTypeName, skuCode,
        } = data?.info || {};
        // 如果当前已扫描的商品条码列表里没有当前扫的， 则新增
        if (boxedList.find((bi) => (bi.skuCode === skuCode && bi.skuCode && skuCode))) {
          // 找到里 就+1
          yield ctx.changeData({
            data: {
              boxedList: boxedList.map((bi) => {
                if (bi.skuCode === skuCode) {
                  return {
                    ...bi,
                    totalNum: (bi.totalNum || 0) + 1,
                    // eslint-disable-next-line max-len
                    multipleNum: (!isHeadType && multiPpMark) ? (bi.multipleNum || 0) + 1 : (bi.multipleNum || 0),
                  };
                } else {
                  return bi;
                }
              }),
            },
          });
        } else {
          yield ctx.changeData({
            data: {
              boxedList: [{
                totalNum: 1, // 初始化
                size,
                skc,
                storeTypeName,
                skuCode,
                multipleNum: (!isHeadType && multiPpMark) ? 1 : 0, // 初始化
              }, ...boxedList],
            },
          });
        }
        // 清空当前扫的条码&更新移位单号
        yield ctx.changeData({
          data: {
            skuBarCode: '',
            shiftOrderCode: (data?.info || {}).shiftOrderCode,
            hasPackedNum: (hasPackedNum || 0) + 1, // 已装商品+1
          },
        });
        yield ctx.classFocus('skuBarCode');
      }
    } else {
      aaooEle.play();// 报错音提示
      yield ctx.changeData({
        data: {
          skuBarCode: '',
        },
      });
      modal.error({ content: data.msg, className: 'skuBarCode' });
    }
  },
};
