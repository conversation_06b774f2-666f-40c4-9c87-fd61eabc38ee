import { sendPostRequest } from '../../../lib/public-request';

/**
 * 多货装箱-扫描相关箱号
 * @param param
 * @returns {*}
 */
export const scanRelatedContainerAPI = (param) => sendPostRequest({
  url: '/pda/over_goods_boxing/scan_related_container',
  param,
}, process.env.WWS_URI);

/**
 * 扫描大箱号
 * @param param
 * @returns {*}
 */
export const scanContainerCodeAPI = (param) => sendPostRequest({
  url: '/pda/over_goods_boxing/scan_wmd_container',
  param,
}, process.env.WWS_URI);

/**
 * 扫描商品条码
 * @param param
 * @returns {*}
 */
export const scanSkuCodeAPI = (param) => sendPostRequest({
  url: '/pda/over_goods_boxing/scan_sku_code',
  param,
}, process.env.WWS_URI);

/**
 * 初始化接口 选择不同的装箱类型时触发
 * @param param
 * @returns {*}
 */
export const startShiftMultipleAPI = (param) => sendPostRequest({
  url: '/pda/over_goods_boxing/start_shift_multiple',
  param,
}, process.env.WWS_URI);
