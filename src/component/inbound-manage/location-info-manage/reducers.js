import assign from 'object-assign';
import { push } from 'react-router-redux';
import { t } from '@shein-bbl/react';
import { select, put } from 'redux-saga/effects';
import { classFocus, getHeaderTitle, getValuesFromAnotherObject } from '../../../lib/util';
import { message, modal } from '../../common';
import { selectDict } from '../../../server/basic/data-dictionary';
import {
  queryExtendApi,
  goodsLocationQueryApi,
  goodsLocationModifyApi,
  maintainQueryApi,
  gatherQueryApi,
  gatherConfirmApi,
  gatherLackApi,
  queryGoodsApi,
  getBtnPermissionApi,
} from './server';

const defaultState = {
  headerTitle: '', // 页面标题：统一从后端接口获取
  type: '1', // 页面的状态 1 - 初始默认界面，2 - 查询界面， 3 - 维护, 4 - 商品信息采集页面
  location: '',
  // 查询页面相关信息
  mainInfo: {
    id: 0, // 库位id
    location: '',
    enabledName: '', // 库位状态Table
    specification: '', // 库位规格
    locationTypeName: '', // 货位类型名称
    maxItemNum: '', // 品项混存上限
    needCollectFlag: false, // 是否需要采集标记
    maxAvailableRate: '', // 最大可用率
    goods: [
      // {
      //   goodsSn: 'DB93894993',
      //   size: 'ONE-SIZE',
      //   goodsSnAndSize: 'test111',
      //   collectFlag: 0,
      // },
    ], // 库位skc采集信息
    pickOrder: '',
    areaId: 0,
  },
  // 库存维护页面相关信息
  manageInfo: {
    id: 0, // 库位id
    areaId: 0,
    enabled: '', // 状态
    pickOrder: '', // 拣货顺序编号
    roadway: '', // 巷道
    subWarehouseId: 0, // 所属子仓id
    warehouseId: 0, // 所属仓库id
    extendId: 0, // 库位规格id
    maxItemNum: '', // 品项混存上限
    locationType: 0, // 货位类型id
  },
  specificationList: [], // 库位规格下拉
  locationTypeList: [], // 库位类型下拉
  showSpecificationPop: false, // 显示库位规格下拉
  showLocationTypePop: false, // 显示库位类型下拉
  info: {
    locationCode: '', // 库位号
    realCollectCount: 0, // 已采集数
    waitCollectCount: 0, // 待采集数
    taskCode: '', // 任务单号
  }, // 商品采集任务里的一些信息
  waitCollectList: [ // 待采集任务明细详情
    // {
    //   skc: 'DB93894008',
    //   size: 'ONE-SIZE',
    //   taskDetailId: 1,
    //   barcode: 'TEST001',
    // },
  ], // 待采集任务明细详情
  waitCollectInfo: {
    taskDetailId: 0,
    skc: '',
    size: '',
    barCode: '',
    height: '',
    length: '',
    specialAttribute: [],
    weight: '',
    width: '',
    // confirmDifference: false, 是否已经确认体积或者重量差异，后端返回resultCode = 1,3,4,5,6的时候，用户再次提交，则传true
  },
  // 维护页面 & 商品采集任务页面的按钮disabled
  backDisabled: false,
  showSelectPicker: false,
  specialAttributeList: [],
  btnPermission: true, // 库位维护按钮的权限，没有权限，按钮需要置灰
  gatherBtnPermission: true, // 商品采集按钮的权限
  pageLoading: false,
  goodsNum: 1, // 商品件数，默认 1，用户可修改，最大位数 3 位，即999
  pdcWeight: '',
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  changeMainInfo(draft, action) {
    assign(draft.mainInfo, action.data);
  },
  changeManageInfo(draft, action) {
    assign(draft.manageInfo, action.data);
  },
  changeWaitCollectInfo(draft, action) {
    assign(draft.waitCollectInfo, action.data);
  },
  changeInfo(draft, action) {
    assign(draft.info, action.data);
  },
  * initData(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    // 获取库位类型及规格的下拉
    const selectParam = {
      catCode: ['LOCATION_TYPE', 'SPECIAL_ATTRIBUTE'],
    };
    const [selectData, specificationData] = yield Promise.all([
      selectDict(selectParam),
      queryExtendApi(),
    ]);
    if (selectData.code === '0') {
      yield ctx.changeData({
        data: {
          locationTypeList: selectData.info.data.find((v) => v.catCode === 'LOCATION_TYPE').dictListRsps,
          specialAttributeList: selectData.info.data.find((v) => v.catCode === 'SPECIAL_ATTRIBUTE').dictListRsps,
        },
      });
    } else {
      yield ctx.changeData({ data: { locationTypeList: [] } });
    }
    if (specificationData.code === '0') {
      yield ctx.changeData({
        data: {
          specificationList: specificationData.info.data || [],
        },
      });
    } else {
      yield ctx.changeData({ data: { specificationList: [] } });
    }
  },
  // 获取库位维护按钮的权限以及商品采集按钮的权限
  // 这里要是后端能优化，支持传数组就更友好了
  * getPermission(action, ctx) {
    // 默认置 false
    yield ctx.changeData({
      data: {
        btnPermission: false,
      },
    });
    const res = yield getBtnPermissionApi({ url: `${process.env.BASE_URI}/pda/goods_location/maintain/modify` });
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          btnPermission: true,
        },
      });
    }
    // 默认置 false
    yield ctx.changeData({
      data: {
        gatherBtnPermission: false,
      },
    });
    const res2 = yield getBtnPermissionApi({ url: `${process.env.WWS_URI}/pda/goods_location/gather/query` });
    if (res2.code === '0') {
      yield ctx.changeData({
        data: {
          gatherBtnPermission: true,
        },
      });
    }
  },
  // 扫库位码查询
  * goodsLocationQuery(action, ctx) {
    const { status, ...data } = action.param;
    if (!action.param.requestType) {
      data.requestType = 0;
    }
    const res = yield goodsLocationQueryApi(data);
    try {
      if (res.code === '0') {
        yield ctx.changeData({
          data: {
            mainInfo: getValuesFromAnotherObject(defaultState.mainInfo, res.info),
            location: res.info.location,
          },
        });
        // manageInfo.maxItemNum === 0，前端要改为''
        if (res.info.maxItemNum === 0 || res.info.maxItemNum === '0') {
          yield ctx.changeMainInfo({ data: { maxItemNum: '' } });
        }
        if (!window.location.hash.includes('location-info-manage/2')) {
          yield put(push('/inbound-manage/location-info-manage/2'));
        }
      } else {
        // 如果是点上一个、下一个来查询库位，即使没找到，也不要清空当前库位号
        if (!action.param.requestType || action.param.requestType === 0) {
          yield ctx.changeMainInfo({
            location: '',
            id: 0,
          });
          yield ctx.changeData({ data: { location: '' } });
        }
        modal.error({
          modalBlurInput: true,
          content: res.msg,
          onOk: () => classFocus('location'),
        });
      }
    } finally {
      yield ctx.changeData({ data: { pageLoading: false } });
      if (status && (status === 3 || status === 4)) {
        yield put(push('/inbound-manage/location-info-manage/2'));
      }
      yield ctx.changeData({ data: { backDisabled: false } });
    }
  },
  // 库位维护查询
  * maintainQuery(action, ctx) {
    const res = yield maintainQueryApi(action.param);
    if (res.code === '0') {
      yield put(push('/inbound-manage/location-info-manage/3'));
      yield ctx.changeData({
        data: {
          manageInfo: getValuesFromAnotherObject(defaultState.manageInfo, res.info),
        },
      });
      // manageInfo.maxItemNum === 0，前端要改为''
      if (res.info.maxItemNum === 0 || res.info.maxItemNum === '0') {
        yield ctx.changeManageInfo({ data: { maxItemNum: '' } });
      }
    } else {
      modal.error({
        content: res.msg,
        onOk: () => classFocus('location'),
      });
    }
  },
  // 库位维护保存
  * goodsLocationModify(action, ctx) {
    const res = yield goodsLocationModifyApi(action.param);
    const { mainInfo } = yield select((state) => state['inbound-manage/location-info-manage']);
    if (res.code === '0') {
      const status = yield new Promise((r) => modal.success({
        content: t('库位信息维护成功！'),
        onOk: () => {
          r(1);
        },
      }));
      if (status === 1) {
        yield ctx.goodsLocationQuery({ param: { location: mainInfo.location } });
        yield put(push('/inbound-manage/location-info-manage/2'));
      }
    } else {
      modal.error({
        content: res.msg,
      });
    }
  },
  // 库位维护商品采集查询
  * gatherQuery(action, ctx) {
    const res = yield gatherQueryApi(action.param);
    // 清空相关信息
    yield ctx.changeWaitCollectInfo({
      data: {
        height: '',
        length: '',
        specialAttribute: [],
        weight: '',
        width: '',
      },
    });
    yield ctx.changeData({ data: { goodsNum: 1 } });
    // 获取 waitCollectList
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          waitCollectList: res.info.waitCollectList || [],
        },
      });
      yield ctx.changeWaitCollectInfo({
        data: {
          barCode: '',
        },
      });
      const {
        locationCode,
        realCollectCount,
        waitCollectCount,
        taskCode,
      } = res.info;
      yield ctx.changeInfo({
        data: {
          locationCode,
          realCollectCount,
          waitCollectCount,
          taskCode,
        },
      });
      if (res.info.waitCollectList && res.info.waitCollectList.length > 0) {
        yield put(push('/inbound-manage/location-info-manage/4'));
        // 用 waitCollectList 第一条数据去请求商品信息
        yield ctx.changeWaitCollectInfo({
          data: {
            taskDetailId: res.info.waitCollectList[0].taskDetailId,
            skc: res.info.waitCollectList[0].skc,
            size: res.info.waitCollectList[0].size,
            // barCode: res.info.waitCollectList[0].barCode,
          },
        });
      } else {
        modal.info({
          content: t('没有需要采集的商品'),
        });
      }
    } else {
      yield ctx.changeData({
        data: {
          waitCollectList: [],
        },
      });
      yield ctx.changeInfo({
        data: {
          locationCode: '',
          realCollectCount: '',
          waitCollectCount: '',
          taskCode: '',
        },
      });
      modal.error({
        content: res.msg,
      });
    }
  },
  // 库位维护商品采集确认
  * gatherConfirm(action, ctx) {
    const res = yield gatherConfirmApi(action.param);
    if (res.code === '0') {
      // 更新已采集和待采集数
      if (res.info.waitCollectCount) {
        yield ctx.changeInfo({
          data: {
            waitCollectCount: res.info.waitCollectCount,
          },
        });
      }
      if (res.info.realCollectCount) {
        yield ctx.changeInfo({
          data: {
            realCollectCount: res.info.realCollectCount,
          },
        });
      }
      // 后台返回1，让用户重新提交，增加confirmDifference=true参数
      if (res.info.resultCode === 1) {
        const status = yield new Promise((r) => modal.confirm({
          content: res.info.resultMsg || t('体积或重量的差异率超过配置值'),
          onOk: () => {
            r(1);
          },
          onCancel: () => {
            classFocus('weight');
          },
        }));
        if (status === 1) {
          const newParam = {
            ...action.param,
            confirmDifference: true,
          };
          yield ctx.gatherConfirm({ param: newParam });
        }
        return;
      }
      if (res.info.resultCode === 8) {
        modal.info({
          content: res.info.resultMsg,
          onOk: () => {
            classFocus('weight');
          },
        });
        yield ctx.changeWaitCollectInfo({
          data: {
            weight: '',
          },
        });
        yield ctx.changeData({ data: { pdcWeight: res.info.pdcWeight } });
      }
      // 后台返回3，4，5，6只是提示，啥也不做
      if (res.info.resultCode !== 0 && res.info.resultCode !== 2) {
        const { resultMsg } = res.info;
        let position;
        switch (res.info.resultCode) {
          case 3: case 7:
            position = 'weight';
            break;
          case 4:
            position = 'length';
            break;
          case 5:
            position = 'width';
            break;
          case 6:
            position = 'height';
            break;
          default:
            break;
        }
        yield ctx.changeWaitCollectInfo({
          data: {
            [position]: '',
          },
        });
        modal.error({
          content: resultMsg,
          onOk: () => {
            classFocus(position);
          },
        });
        return;
      }
      // 上面那种情况，暂不清除信息
      yield ctx.changeWaitCollectInfo({
        data: {
          height: '',
          length: '',
          specialAttribute: [],
          weight: '',
          width: '',
        },
      });
      const { waitCollectList, mainInfo } = yield select((state) => state['inbound-manage/location-info-manage']);
      waitCollectList.splice(waitCollectList.findIndex(
        (i) => i.taskDetailId === action.param.taskDetailId,
      ), 1);
      yield ctx.changeData({
        data: {
          waitCollectList,
        },
      });

      if (res.info.resultCode === 0 && waitCollectList.length > 0) {
        message.success(res.info.resultMsg || t('采集成功！'));
        yield ctx.changeWaitCollectInfo({
          data: {
            taskDetailId: waitCollectList[0].taskDetailId,
            skc: waitCollectList[0].skc,
            size: waitCollectList[0].size,
            barCode: '',
          },
        });
        classFocus('barCode');
      }
      // 如果前端这边已经轮询完,但后台resultCode返回0
      if (res.info.resultCode === 0 && waitCollectList.length === 0) {
        const status = yield new Promise((r) => modal.success({
          content: t('所有信息已经采集一遍，但采集任务未完成！'),
          onOk: () => {
            r(1);
          },
        }));
        if (status === 1) {
          window.location.hash = '/inbound-manage/location-info-manage/2';
          if (mainInfo.location) {
            yield ctx.goodsLocationQuery({ param: { location: mainInfo.location } });
          }
          return;
        }
      }
      // 只要是2，肯定完成采集了
      if (res.info.resultCode === 2) {
        const status1 = yield new Promise((r) => modal.success({
          content: res.info.resultMsg || t('采集任务已完成'),
          onOk: () => {
            r(1);
          },
        }));
        if (status1 === 1) {
          window.location.hash = '/inbound-manage/location-info-manage/2';
          yield ctx.changeData({ data: { pageLoading: true } });
          if (mainInfo.location) {
            const timer = yield new Promise((r) => setTimeout(() => {
              r(1);
            }, 2000));
            if (timer === 1) {
              yield ctx.goodsLocationQuery({ param: { location: mainInfo.location } });
            }
          }
        }
      }
    } else {
      modal.error({
        content: res.msg,
      });
    }
  },
  // 库位维护商品采集缺货
  * gatherLack(action, ctx) {
    const res = yield gatherLackApi(action.param);
    if (res.code === '0') {
      yield ctx.changeWaitCollectInfo({
        data: {
          height: '',
          length: '',
          specialAttribute: [],
          weight: '',
          width: '',
        },
      });
      // 更新已采集和待采集数
      if (res.info.waitCollectCount) {
        yield ctx.changeInfo({
          data: {
            waitCollectCount: res.info.waitCollectCount,
          },
        });
      }
      if (res.info.realCollectCount) {
        yield ctx.changeInfo({
          data: {
            realCollectCount: res.info.realCollectCount,
          },
        });
      }
      // 缺货上报成功后，跳过当前这个，到下一个skc
      message.success(t('缺货上报成功'));
      const { waitCollectList, mainInfo } = yield select((state) => state['inbound-manage/location-info-manage']);
      // 如果待采集数已经是0，则完成采集任务，跳转到query-page页面
      if (res.info.waitCollectCount && res.info.waitCollectCount === 0) {
        const status = yield new Promise((r) => modal.success({
          content: t('采集任务已完成'),
          onOk: () => {
            r(1);
          },
        }));
        if (status === 1) {
          window.location.hash = '/inbound-manage/location-info-manage/2';
          if (mainInfo.location) {
            yield ctx.goodsLocationQuery({ param: { location: mainInfo.location } });
          }
          return;
        }
      }
      waitCollectList.splice(waitCollectList.findIndex(
        (i) => i.taskDetailId === action.param.taskDetailId,
      ), 1);
      yield ctx.changeData({
        data: {
          waitCollectList,
        },
      });
      // 是最后一个则前端已经轮询完
      if (waitCollectList && waitCollectList.length === 0) {
        const status = yield new Promise((r) => modal.success({
          content: t('所有信息已经采集一遍，但采集任务未完成'),
          onOk: () => {
            r(1);
          },
        }));
        if (status === 1) {
          window.location.hash = '/inbound-manage/location-info-manage/2';
          if (mainInfo.location) {
            yield ctx.goodsLocationQuery({ param: { location: mainInfo.location } });
          }
          return;
        }
      }
      // 缺货的这个直接跳过， 不需要发起确认保存的请求，直接跳到下个skc
      yield ctx.changeWaitCollectInfo({
        data: {
          taskDetailId: waitCollectList[0].taskDetailId,
          skc: waitCollectList[0].skc,
          size: waitCollectList[0].size,
          barCode: '',
        },
      });
    } else {
      modal.error({
        content: res.msg,
      });
    }
  },
  // 根据barCode及taskDetailId查询商品信息
  * queryGoods(action, ctx) {
    const res = yield queryGoodsApi(action.param);
    yield ctx.changeData({ data: { goodsNum: 1 } });
    if (res.code === '0') {
      // 更换商品条吗 清空理论重量
      yield ctx.changeData({ data: { pdcWeight: '' } });

      yield ctx.changeWaitCollectInfo({
        data: {
          height: res.info.height || '',
          length: res.info.length || '',
          specialAttribute: res.specialAttribute || [],
          weight: res.info.weight || '',
          width: res.info.width || '',
        },
      });
      classFocus('weight');
    } else {
      modal.error({
        content: res.msg,
        onOk: () => classFocus('barCode'),
      });
      // 不正确的话，要清除信息
      yield ctx.changeWaitCollectInfo({
        data: {
          height: '',
          length: '',
          specialAttribute: [],
          weight: '',
          width: '',
        },
      });
      yield ctx.changeWaitCollectInfo({ data: { barCode: '' } });
    }
  },
};
