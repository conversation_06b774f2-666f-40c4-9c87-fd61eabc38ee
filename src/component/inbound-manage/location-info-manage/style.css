.itemBg {
  background-color: #ffffff;
}

.taskCode{
  margin-bottom: 10px;
  box-shadow: 0px 2px 4px 0px rgba(25,122,250,0.15);
}

.listItem {
  min-height: 24px;
  padding: 2px 15px;
}

.listLabel {
  text-align: left;
  color: #333e59;
  float: left;
}

.listContent {
  text-align: right;
  color: #141737;
  float: right;
  margin-left: 20px;
}

.spanLeft {
  text-align: left;
  color: #333e59;
  float: left;
}

.spanRight {
  text-align: right;
  color: #141737;
  float: right;
  font-weight: bold;
  position: absolute;
  right: 15px;
}

.lackGoodsBtn {
  padding-left: 2px;
  padding-right: 2px;
  font-size: 10px;
  width: 48px !important;
}

.locationCodeRight {
  font-size: 20px;
}

.taskScrollForm {
  overflow: scroll;
}

.footerBox {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.footerBtnItem {
  width: 30%;
}

.operateBtn {
  float: right;
  padding: 0 10px;
  margin-left: 12px;
  height: 24px;
  line-height: 24px;
}

.psc {
  border: 1px solid #ccc;
  text-align: center;
  width: 50px;
  height: 30px;
}
