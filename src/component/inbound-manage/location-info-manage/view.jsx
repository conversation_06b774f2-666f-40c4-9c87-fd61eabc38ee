import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import { Header } from '../../common';
import DefaultPage from './jsx/default-page';
import QueryPage from './jsx/query-page';
import ManagePage from './jsx/manage-page';
import GoodsCollectPage from './jsx/goods-collect-page';

class Container extends Component {
  componentDidMount() {
    const { status } = this.props.match.params;
    if (!status) {
      store.$init();
    }
    if (status === '2') {
      store.getPermission();
    }
    store.initData();
  }

  render() {
    const {
      headerTitle,
    } = this.props;

    const { status } = this.props.match.params;
    const getPage = () => {
      switch (status) {
        case '2':
          return <QueryPage {...this.props} />;
        case '3':
          return <ManagePage {...this.props} />;
        case '4':
          return <GoodsCollectPage {...this.props} />;
        default:
          return <DefaultPage {...this.props} />;
      }
    };

    return (
      <div>
        <Header
          title={
            status === '4' ?
              t('商品采集任务') :
              (headerTitle || t('库位信息查询维护'))
          }
        />
        {getPage()}
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
};

export default i18n(Container);
