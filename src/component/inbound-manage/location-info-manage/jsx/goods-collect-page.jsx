// 商品信息采集页面，通过query-page进来
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Form, Button, Input,
} from 'react-weui/build/packages';
import classnames from 'classnames';
import { push } from 'react-router-redux';
import store from '../reducers';
import {
  FocusInput,
  Footer,
  RowInfo,
  FooterBtn,
  modal,
} from '../../../common';
import PopSelect from '../../../common/pop-select';
import style from '../../../style.css';
import myStyle from '../style.css';
import { classFocus, round } from '../../../../lib/util';

const reg = /^([0-9]*)$/;
const reg3 = /^[1-9]\d{0,2}$/; // 匹配商品件数

class GoodsCollectPage extends Component {
  componentDidMount() {
    classFocus('barCode');
  }

  render() {
    const {
      info,
      waitCollectInfo,
      showSelectPicker,
      specialAttributeList,
      backDisabled,
      dispatch,
      goodsNum,
      pdcWeight,
    } = this.props;
    const height = window.innerHeight - 122;
    const inputWrapHeight = window.innerHeight - 66 - 40 - 172;
    const arr = info.locationCode.split('-');
    let leftLocation = '';
    let rightLocation = '';
    if (arr && arr.length && arr.length > 0) {
      if (arr.length === 1) {
        leftLocation = info.locationCode;
        rightLocation = '';
      } else {
        leftLocation = `${arr.shift()}-`;
        rightLocation = arr.join('-');
      }
    }
    return (
      <div className={style.flexColContainer} style={{ height }}>
        <div className="topWrap">
          <div
            className={classnames(myStyle.itemBg, myStyle.listItem, myStyle.taskCode)}
          >
            {info.taskCode}
          </div>
          <div
            className={classnames(myStyle.itemBg)}
            style={{ borderBottom: '1px solid #e9e9e9' }}
          >
            <div className={classnames(myStyle.listItem)}>
              <span className={myStyle.spanLeft}>{t('库位号')}</span>
              <span className={myStyle.spanRight}>
                <span>{leftLocation}</span>
                <span className={myStyle.locationCodeRight}>
                  {rightLocation}
                </span>
              </span>
            </div>
            <div className={classnames(myStyle.listItem)}>
              <span className={myStyle.spanLeft}>SKC</span>
              <span className={myStyle.spanRight}>
                {waitCollectInfo.skc || ''}
              </span>
            </div>
            <div className={classnames(myStyle.listItem)}>
              <span className={myStyle.spanLeft}>{t('尺码')}</span>
              <span className={myStyle.spanRight}>
                {waitCollectInfo.size || ''}
              </span>
            </div>
          </div>
          <Form
            style={{ marginBottom: '10px', boxShadow: '0px 2px 4px 0px rgba(25,122,250,0.15)' }}
          >
            <RowInfo
              data={[
                {
                  label: t('待采集'),
                  content: info.waitCollectCount,
                },
                {
                  label: t('已采集'),
                  content: info.realCollectCount,
                  type: 'warn',
                },
              ]}
            />
          </Form>
        </div>
        <div className={myStyle.taskScrollForm} style={{ height: inputWrapHeight }}>
          <Form>
            <FocusInput
              // data-bind="limit.barCode"
              className="barCode"
              value={waitCollectInfo.barCode || ''}
              footer={
                /* eslint-disable */
                <Button
                  className={myStyle.lackGoodsBtn}
                  disabled={!waitCollectInfo.taskDetailId}
                  onClick={() => {
                    modal.confirm({
                      content: t('您确定缺货吗？'),
                      onOk: () => {
                        store.gatherLack({
                          param: {
                            taskDetailId: waitCollectInfo.taskDetailId,
                          },
                        });
                      },
                    });
                  }}
                >
                  {t('缺货')}
                </Button>
                /* eslint-enable */
              }
              onChange={(e) => {
                const val = e.target.value.trim();
                store.changeWaitCollectInfo({ data: { barCode: val } });
              }}
              onPressEnter={(e) => {
                const val = e.target.value.trim();
                store.queryGoods({
                  param: {
                    barCode: val,
                    taskDetailId: waitCollectInfo.taskDetailId,
                  },
                });
              }}
            >
              <label>{t('条码')}</label>
            </FocusInput>
            <FocusInput
              // disabled={isDisabled || dataLoading === 0}
              data-bind="waitCollectInfo.weight"
              className="weight"
              footer={(
                <div style={{ display: 'flex', lineHeight: '32px' }}>
                  <span style={{ paddingRight: 5 }}>/</span>
                  <Input
                    className={classnames(myStyle.psc, 'goodsNum')}
                    value={goodsNum}
                    onChange={(e) => {
                      const val = e.target.value.trim();
                      if (val && !reg3.test(val)) {
                        store.changeData({ data: { goodsNum: 1 } });
                      } else {
                        store.changeData({ data: { goodsNum: e.target.value || '' } });
                      }
                    }}
                  />
                  <span style={{ paddingLeft: 5 }}>{t('件')}</span>
                </div>
              )}
              ftstyle={{
                fontSize: '14px', fontWeight: '700',
              }}
              onChange={(e) => {
                const val = e.target.value.trim();
                if (val) {
                  if (!reg.test(val) || val === '0') {
                    modal.error({
                      content: t('长宽高重量必须是大于0的数字'),
                      onOk: () => {
                        store.changeWaitCollectInfo({ data: { weight: '' } });
                        classFocus('weight');
                      },
                    });
                  } else {
                    store.changeWaitCollectInfo({ data: { weight: val } });
                  }
                } else {
                  store.changeWaitCollectInfo({ data: { weight: val } });
                }
              }}
              onPressEnter={() => {
                classFocus('length');
              }}
            >
              <label>{t('重量(g)')}</label>
            </FocusInput>
            <FocusInput
              // disabled={isDisabled || dataLoading === 0}
              data-bind="waitCollectInfo.length"
              className="length"
              onChange={(e) => {
                const val = e.target.value.trim();
                if (val) {
                  if (!reg.test(val) || val === '0') {
                    modal.error({
                      content: t('长宽高重量必须是大于0的数字'),
                      onOk: () => {
                        store.changeWaitCollectInfo({ data: { length: '' } });
                        classFocus('length');
                      },
                    });
                  } else {
                    store.changeWaitCollectInfo({ data: { length: val } });
                  }
                } else {
                  store.changeWaitCollectInfo({ data: { length: val } });
                }
              }}
              onPressEnter={() => {
                classFocus('width');
              }}
            >
              <label>{t('包装长(mm)')}</label>
            </FocusInput>
            <FocusInput
              // disabled={isDisabled || dataLoading === 0}
              data-bind="waitCollectInfo.width"
              className="width"
              onChange={(e) => {
                const val = e.target.value.trim();
                if (val) {
                  if (!reg.test(val) || val === '0') {
                    modal.error({
                      content: t('长宽高重量必须是大于0的数字'),
                      onOk: () => {
                        store.changeWaitCollectInfo({ data: { width: '' } });
                        classFocus('width');
                      },
                    });
                  } else {
                    store.changeWaitCollectInfo({ data: { width: val } });
                  }
                } else {
                  store.changeWaitCollectInfo({ data: { width: val } });
                }
              }}
              onPressEnter={() => {
                classFocus('height');
              }}
            >
              <label>{t('包装宽(mm)')}</label>
            </FocusInput>
            <FocusInput
              // disabled={isDisabled || dataLoading === 0}
              data-bind="waitCollectInfo.height"
              className="height"
              onChange={(e) => {
                const val = e.target.value.trim();
                if (val) {
                  if (!reg.test(val) || val === '0') {
                    modal.error({
                      content: t('长宽高重量必须是大于0的数字'),
                      onOk: () => {
                        store.changeWaitCollectInfo({ data: { height: '' } });
                        classFocus('height');
                      },
                    });
                  } else {
                    store.changeWaitCollectInfo({ data: { height: val } });
                  }
                } else {
                  store.changeWaitCollectInfo({ data: { height: val } });
                }
              }}
            >
              <label>{t('包装高(mm)')}</label>
            </FocusInput>
          </Form>
          <Form>
            <PopSelect
              className="selectPicker"
              label={t('特殊属性')}
              show={showSelectPicker}
              selectValue={waitCollectInfo.specialAttribute || []}
              selectList={specialAttributeList}
              // disabled={isDisabled}
              onClick={() => {
                store.changeData({ data: { showSelectPicker: true } });
              }}
              onCancel={() => {
                store.changeData({ data: { showSelectPicker: false } });
              }}
              onOk={(val) => {
                store.changeData({ data: { showSelectPicker: false } });
                store.changeWaitCollectInfo({
                  data: {
                    specialAttribute: val,
                  },
                });
              }}
            />
          </Form>
        </div>
        <Footer
          disabled={backDisabled}
          beforeBack={() => {
            store.changeData({ data: { backDisabled: true } });
            if (info.locationCode) {
              store.goodsLocationQuery({ param: { location: info.locationCode, status: 4 } });
            } else {
              dispatch(push('/inbound-manage/location-info-manage/2'));
            }
          }}
        >
          <FooterBtn
            disabled={!waitCollectInfo.taskDetailId}
            onClick={() => {
              if (waitCollectInfo.weight && !goodsNum) {
                modal.error({
                  content: t('请输入件数！'),
                  onOk: () => classFocus('goodsNum'),
                });
                return;
              }
              // weight要算平均重量
              const realWeight = round(waitCollectInfo.weight / (goodsNum || 1), 0);
              store.gatherConfirm({
                param: {
                  barCode: waitCollectInfo.barCode,
                  taskDetailId: waitCollectInfo.taskDetailId,
                  height: waitCollectInfo.height,
                  length: waitCollectInfo.length,
                  specialAttribute: waitCollectInfo.specialAttribute,
                  weight: realWeight || '',
                  width: waitCollectInfo.width,
                  pdcWeight,
                },
              });
            }}
          >
            {t('保存')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

GoodsCollectPage.propTypes = {
  dispatch: PropTypes.func.isRequired,
  info: PropTypes.shape(),
  waitCollectInfo: PropTypes.shape(),
  showSelectPicker: PropTypes.bool,
  specialAttributeList: PropTypes.arrayOf(PropTypes.object),
  backDisabled: PropTypes.bool,
  goodsNum: PropTypes.number,
};

export default GoodsCollectPage;
