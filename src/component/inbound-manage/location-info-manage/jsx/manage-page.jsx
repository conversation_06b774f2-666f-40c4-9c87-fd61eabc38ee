// 信息维护界面, 通过query-page进来
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Form,
} from 'react-weui/build/packages';
import {
  FocusInput, Footer, FooterBtn, message,
} from '../../../common';
import PopRadio from '../../../common/pop-radio';
import store from '../reducers';

class ManagePage extends Component {
  render() {
    const {
      specificationList,
      locationTypeList,
      showSpecificationPop,
      showLocationTypePop,
      manageInfo,
      mainInfo,
      backDisabled,
    } = this.props;
    return (
      <div>
        <Form>
          <FocusInput
            autoFocus
            disabled
            value={mainInfo.location}
          >
            <label>{t('库位号')}</label>
          </FocusInput>
          <PopRadio
            label={t('库位规格')}
            selectValue={manageInfo.extendId}
            selectList={specificationList}
            valueName="id"
            labelName="name"
            show={showSpecificationPop}
            onClick={() => {
              store.changeData({ data: { showSpecificationPop: true } });
            }}
            onCancel={() => {
              store.changeData({ data: { showSpecificationPop: false } });
            }}
            onOk={(val) => {
              store.changeData({
                data: {
                  showSpecificationPop: false,
                },
              });
              store.changeManageInfo({ data: { extendId: val } });
            }}
          />
          <PopRadio
            label={t('库位类型')}
            selectValue={manageInfo.locationType}
            selectList={locationTypeList}
            show={showLocationTypePop}
            onClick={() => {
              store.changeData({ data: { showLocationTypePop: true } });
            }}
            onCancel={() => {
              store.changeData({ data: { showLocationTypePop: false } });
            }}
            onOk={(val) => {
              store.changeData({
                data: {
                  showLocationTypePop: false,
                },
              });
              store.changeManageInfo({ data: { locationType: val } });
            }}
          />
          <FocusInput
            autoFocus
            value={manageInfo.maxItemNum}
            onChange={(e) => {
              const val = e.target.value.trim();
              // 只能输入正整数
              const reg = /^([0-9]*)$/;
              if (val === '0') {
                message.error(t('品项混存上限不能为0'), 1000);
                store.changeManageInfo({
                  data: {
                    maxItemNum: '',
                  },
                });
                return;
              }
              if ((!Number.isNaN(val) && reg.test(val)) || val === '') {
                store.changeManageInfo({
                  data: {
                    maxItemNum: val,
                  },
                });
              }
            }}
          >
            <label>{t('品项混存上限')}</label>
          </FocusInput>
        </Form>
        <Footer
          beforeBack={() => {
            // 请求接口，刷新query-page的数据
            store.changeData({ data: { backDisabled: true } });
            store.goodsLocationQuery({ param: { location: mainInfo.location, status: 3 } });
          }}
          disabled={backDisabled}
        >
          <FooterBtn
            disabled={!manageInfo.id}
            onClick={() => {
              const {
                id,
                areaId,
                enabled,
                pickOrder,
                roadway,
                subWarehouseId,
                warehouseId,
                extendId,
                maxItemNum,
                locationType,
              } = manageInfo;
              store.goodsLocationModify({
                param: {
                  id,
                  areaId,
                  enabled,
                  extendId,
                  locationType,
                  maxItemNum,
                  pickOrder,
                  roadway,
                  subWarehouseId,
                  warehouseId,
                },
              });
            }}
          >
            {t('保存')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

ManagePage.propTypes = {
  specificationList: PropTypes.arrayOf(PropTypes.object),
  locationTypeList: PropTypes.arrayOf(PropTypes.object),
  showSpecificationPop: PropTypes.bool,
  showLocationTypePop: PropTypes.bool,
  manageInfo: PropTypes.shape(),
  mainInfo: PropTypes.shape(),
  backDisabled: PropTypes.bool,
};

export default ManagePage;
