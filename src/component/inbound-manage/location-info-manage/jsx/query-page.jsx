// 查询到的信息页面
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Form, Button, Toast,
} from 'react-weui/build/packages';
import { push } from 'react-router-redux';
import store from '../reducers';
import {
  FocusInput,
  Table,
  FooterBtn,
  modal,
} from '../../../common';
import styles from '../style.css';
import { footerStyle } from '../../../common/footer';

class QueryPage extends Component {
  render() {
    const {
      dispatch,
      mainInfo,
      backDisabled,
      btnPermission,
      gatherBtnPermission,
      pageLoading,
    } = this.props;
    const getCollectSpan = () => {
      if (mainInfo.id && mainInfo.needCollectFlag) {
        return <span style={{ color: '#FF7019' }}>{t('需要采集')}</span>;
      }
      if (mainInfo.id && !mainInfo.needCollectFlag) {
        return t('不需要采集');
      }
      return '';
    };
    return (
      <div style={{ height: 405 }}>
        <Toast icon="loading" show={pageLoading}>{t('更新中')}...</Toast>
        <Form>
          <FocusInput
            autoFocus
            className="location"
            data-bind="location"
            onChange={(e) => {
              const val = e.target.value.trim();
              store.changeData({ data: { location: val } });
            }}
            allowClear
            allowClearBig
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              // 重新查询
              store.goodsLocationQuery({ param: { location: e.target.value } });
            }}

          >
            <label>{t('库位号')}</label>
          </FocusInput>
          <div className={styles.listItem}>
            <span className={styles.listLabel}>{t('库位状态')}:</span>
            <span className={styles.listContent}>
              {mainInfo.enabledName}
              <Button
                size="small"
                className={styles.operateBtn}
                disabled={!mainInfo.id || !btnPermission}
                onClick={() => {
                  if (!mainInfo.id) {
                    return;
                  }
                  store.maintainQuery({
                    param: {
                      id: mainInfo.id,
                    },
                  });
                }}
              >
                {t('库位维护')}
              </Button>
            </span>
          </div>
          <div className={styles.listItem}>
            <span className={styles.listLabel}>{t('库位规格')}:</span>
          </div>
          <div className={styles.listItem} style={{ textAlign: 'right' }}>{mainInfo.specification}</div>
          <div className={styles.listItem}>
            <span className={styles.listLabel}>{t('最大可用率')}:</span>
            <span className={styles.listContent}>{mainInfo.maxAvailableRate ? `${mainInfo.maxAvailableRate}%` : ''}</span>
          </div>
          <div className={styles.listItem}>
            <span className={styles.listLabel}>{t('品项混存上限')}:</span>
            <span className={styles.listContent}>{mainInfo.maxItemNum || ''}</span>
          </div>
          <div className={styles.listItem}>
            <span className={styles.listLabel}>{t('库位类型')}:</span>
            <span className={styles.listContent}>{mainInfo.locationTypeName}</span>
          </div>
          <div className={styles.listItem}>
            <span className={styles.listLabel}>{t('SKU信息')}:</span>
            <span className={styles.listContent}>
              {
                getCollectSpan()
              }
              <Button
                size="small"
                className={styles.operateBtn}
                disabled={!mainInfo.id || !gatherBtnPermission}
                onClick={() => {
                  if (!mainInfo.needCollectFlag || !mainInfo.goods || mainInfo.goods.length === 0) {
                    modal.error({
                      content: t('没有需要采集的商品'),
                    });
                    return;
                  }
                  store.gatherQuery({ param: { id: mainInfo.id } });
                }}
              >
                {t('商品采集')}
              </Button>
            </span>
          </div>
          <div
            style={{ maxHeight: 150, overflowY: 'scroll' }}
          >
            <Table
              columns={[
                {
                  title: 'SKC',
                  dataIndex: 'goodsSn',
                  width: 20,
                },
                {
                  title: t('尺码'),
                  dataIndex: 'size',
                  width: 10,
                },
                {
                  title: t('已采集'),
                  render: (v) => (
                    /* eslint-disable */
                    <span
                      style={{ color: v.collectFlag ? '' : 'red' }}
                    >
                      {v.collectFlag ? t('是') : t('否')}
                    </span>
                    /* eslint-enable */
                  ),
                  dataIndex: 'collectFlag',
                  width: 10,
                },
              ]}
              dataSource={mainInfo.goods || []}
            />
          </div>
        </Form>
        <div
          style={footerStyle}
          className={styles.footerBox}
        >
          <div
            className={styles.footerBtnItem}
          >
            <FooterBtn
              type="primary"
              plain
              onClick={() => {
                dispatch(push('/inbound-manage/location-info-manage'));
              }}
            >
              {t('返回')}
            </FooterBtn>
          </div>
          <div
            className={styles.footerBtnItem}
          >
            <FooterBtn
              type="primary"
              disabled={backDisabled || !mainInfo.pickOrder}
              plain
              onClick={() => {
                store.changeData({ data: { backDisabled: true } });
                store.goodsLocationQuery({
                  param: {
                    areaIds: [mainInfo.areaId],
                    pickOrder: mainInfo.pickOrder,
                    requestType: 1,
                  },
                });
              }}
            >
              {t('上一个')}
            </FooterBtn>
          </div>
          <div
            className={styles.footerBtnItem}
          >
            <FooterBtn
              type="primary"
              disabled={backDisabled || !mainInfo.pickOrder}
              plain
              onClick={() => {
                store.changeData({ data: { backDisabled: true } });
                store.goodsLocationQuery({
                  param: {
                    areaIds: [mainInfo.areaId],
                    pickOrder: mainInfo.pickOrder,
                    requestType: 2,
                  },
                });
              }}

            >
              {t('下一个')}
            </FooterBtn>
          </div>
        </div>
      </div>
    );
  }
}

QueryPage.propTypes = {
  dispatch: PropTypes.func.isRequired,
  mainInfo: PropTypes.shape(),
  backDisabled: PropTypes.bool,
  btnPermission: PropTypes.bool,
  gatherBtnPermission: PropTypes.bool,
  pageLoading: PropTypes.bool,
};

export default QueryPage;
