// 初始从二级菜单进来的默认页面
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Form,
} from 'react-weui/build/packages';
import { FocusInput, Footer } from '../../../common';
import store from '../reducers';

class DefaultPage extends Component {
  componentDidMount() {
    // classFocus('');
  }

  render() {
    const {
      location,
    } = this.props;
    return (
      <div>
        <Form>
          <FocusInput
            autoFocus
            data-bind="location"
            onChange={(e) => {
              const val = e.target.value.trim();
              store.changeData({ data: { location: val } });
            }}
            onPressEnter={() => {
              store.goodsLocationQuery({ param: { location } });
            }}
          >
            <label>{t('库位号')}</label>
          </FocusInput>
        </Form>
        <Footer />
      </div>
    );
  }
}

DefaultPage.propTypes = {
  location: PropTypes.string,
};

export default DefaultPage;
