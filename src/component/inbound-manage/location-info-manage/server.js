import { sendPostRequest } from '../../../lib/public-request';

// 库位规格下拉列表查询
export const queryExtendApi = (param) => sendPostRequest({
  url: '/goods_location/query_enable_extend',
  param,
}, process.env.BASE_URI_WMD);

// 查询库位
export const goodsLocationQueryApi = (param) => sendPostRequest({
  url: '/pda/goods_location/query',
  param,
});

// 库位维护查询
export const maintainQueryApi = (param) => sendPostRequest({
  url: '/pda/goods_location/maintain/query',
  param,
});

// 库存维护保存
export const goodsLocationModifyApi = (param) => sendPostRequest({
  url: '/pda/goods_location/maintain/modify',
  param,
});

// 库位维护商品采集查询
export const gatherQueryApi = (param) => sendPostRequest({
  url: '/pda/goods_location/gather/query',
  param,
}, process.env.WWS_URI);

// 库位维护商品采集确认
export const gatherConfirmApi = (param) => sendPostRequest({
  url: '/pda/goods_location/gather/confirm',
  param,
}, process.env.WWS_URI);

// 库位维护商品采集缺货
export const gatherLackApi = (param) => sendPostRequest({
  url: '/pda/goods_location/gather/lack',
  param,
}, process.env.WWS_URI);

// 查询商品信息
export const queryGoodsApi = (param) => sendPostRequest({
  url: '/gather_task/pda/query_goods',
  param,
}, process.env.WWS_URI);

// 库位维护按钮权限
export const getBtnPermissionApi = (param) => sendPostRequest({
  url: '/check_url_permission',
  param,
}, process.env.WAS_FRONT);
