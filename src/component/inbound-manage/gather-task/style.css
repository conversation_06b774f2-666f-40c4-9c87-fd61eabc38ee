.itemBg {
  background-color: #ffffff;
}

.taskCode{
  margin-bottom: 10px;
  box-shadow: 0px 2px 4px 0px rgba(25,122,250,0.15);
}
.taskCodeLeft {
  display: inline-block;
  width: 65%;
}
.taskCodeRight {
  position: relative;
  display: inline-block;
  width: 35%;
  color: #f59a23d8;
}

.listItem {
  height: 25px;
  padding: 5px 15px;
  position: relative;
}

.spanLeft {
  text-align: left;
  color: #333e59;
  float: left;
}

.spanRight {
  text-align: right;
  color: #141737;
  float: right;
  font-weight: bold;
  position: absolute;
  right: 15px;
}
.spanLeftStyle {
  color: #333e59;
  font-weight: normal;
}
.spanRightStyle {
  color: #141737;
  font-weight: bold;
}

.sizeAndColorItem {
  display: flex;
  justify-content: space-between;
  height: 25px;
  padding: 5px 15px;
  color: #333e59;
}
.lackGoodsBtn {
  padding-left: 2px;
  padding-right: 2px;
  font-size: 10px;
  width: 48px !important;
}

.locationCodeRight {
  font-size: 20px;
}

.gatherTaskScrollForm {
  overflow: scroll;
}

.psc {
  border: 1px solid #ccc;
  text-align: center;
  width: 50px;
  height: 30px;
}
