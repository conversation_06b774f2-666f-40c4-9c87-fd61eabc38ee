import { sendPostRequest } from '../../../lib/public-request';

// 查询商品信息 query_goods
export const queryGoods = (param) => sendPostRequest({
  url: '/gather_task/pda/query_goods',
  param,
}, process.env.WWS_URI);

// 采集任务时缺货 lack_goods
export const lackGoods = (param) => sendPostRequest({
  url: '/gather_task/pda/lack_goods',
  param,
}, process.env.WWS_URI);

// 提交采集任务 submit_task
export const submitTask = (param) => sendPostRequest({
  url: '/gather_task/pda/submit_task',
  param,
}, process.env.WWS_URI);

// 扫描库位-获取相关数据
export const scanLocationApi = (param) => sendPostRequest({
  url: '/gather_task/pda/scan_location',
  param,
}, process.env.WWS_URI);
