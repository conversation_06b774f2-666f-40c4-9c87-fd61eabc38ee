import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { select } from 'redux-saga/effects';
import { selectDict } from '../../../server/basic/data-dictionary';
import {
  queryGoods,
  lackGoods,
  submitTask,
  scanLocationApi,
} from './server';
import Modal from '../../common/modal';
import { message } from '../../common';
import {
  classFocus, getHeaderTitle, round,
} from '../../../lib/util';
import { getConfigByCodeApi } from '../../../server/basic/common';

const keyArr = ['weight', 'length', 'width', 'height'];

const defaultState = {
  dataLoading: 1,
  iptDisabled: true,
  locationCode: '', // 货位号
  locationCodeLeft: '', // 货位号的显示有大小之分，拆开
  locationCodeRight: '',
  realCollectCount: 0, // 已采集数
  waitCollectCount: 0, // 待采集数
  taskCode: '', // 任务单号
  size: '',
  skc: '',
  color: '',
  specialAttributeList: [], // 特殊属性下拉数据
  isScanLocation: false, // 请求校验过库位
  limit: {
    location: '', // 库位号
    barCode: '', // 商品条码
    height: '',
    length: '',
    weight: '',
    width: '',
    taskDetailId: '', // 任务详情id
    specialAttribute: [], // 特殊属性
    pdcWeight: '',
  },
  isDisabled: true,
  showPicker: false,
  headerTitle: '',
  goodsNum: 1, // 商品件数，默认 1，用户可修改，最大位数 3 位，即999

  confirmModalVisible: false,
  confirmModalInput: '',
  confirmModalMsg: '',
  // 根据 ITEM_COLLECTION_WAREHOUSE 参数配置：需要逐件采集的仓库，判断选中仓库的ID是否在集合中，
  // 1.如果在，则默认为1，不可修改
  // 2.如果不在，默认为1，可输入，整数，不超过3位，超过不可输入
  itemCollectionWarehouseArray: [],
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  changeLimit: (draft, action) => {
    assign(draft.limit, action.data);
  },
  resetData: (draft, action) => {
    assign(draft, defaultState, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    const selectParam = {
      catCode: ['SPECIAL_ATTRIBUTE'],
    };
    const [selectData] = yield Promise.all([
      selectDict(selectParam),
    ]);

    if (selectData.code === '0') {
      yield ctx.changeData({ data: { specialAttributeList: selectData.info.data.find((v) => v.catCode === 'SPECIAL_ATTRIBUTE').dictListRsps } });
    } else {
      Modal.error({
        className: 'barCode',
        content: selectData.msg,
      });
    }
    const resConfig = yield getConfigByCodeApi({ param: 'ITEM_COLLECTION_WAREHOUSE' });
    const showGridWarehouseConfig = resConfig.code === '0' && resConfig.info && resConfig.info.configValue ? resConfig.info.configValue.split(',') : [];
    yield this.changeData({
      data: {
        itemCollectionWarehouseArray: showGridWarehouseConfig || [],
      },
    });
  },
  * resetLimit(action, ctx) {
    yield ctx.changeLimit({
      data: {
        location: '', // 库位号
        barCode: '', // 商品条码
        height: '',
        length: '',
        weight: '',
        width: '',
        taskDetailId: '', // 任务详情id
        specialAttribute: [], // 特殊属性
      },
    });
  },
  // 扫库库位获取数据
  * scanLocation(selfScan, ctx) {
    // 请求时先重置位未扫过
    yield ctx.changeData({
      data: {
        isScanLocation: false,
      },
    });
    const { limit } = yield select((state) => state['inbound-manage/gather-task']);
    const { location } = limit;
    const { code, info, msg } = yield scanLocationApi({ location });
    if (code === '0') {
      // 获取采集任务的数据
      const {
        locationCode,
        realCollectCount,
        waitCollectCount,
        taskCode,
        size,
        skc,
        taskDetailId,
        isRecommend, // 判断是否弹窗等交互，1不弹窗，2、3其它对应弹窗
        color,
      } = info;
      yield ctx.changeData({
        data: {
          locationCode,
          realCollectCount,
          waitCollectCount,
          taskCode,
          size,
          skc,
          color,
        },
      });
      yield ctx.changeLimit({
        data: {
          taskDetailId,
          barCode: '', // 商品条码
          height: '',
          length: '',
          weight: '',
          width: '',
          specialAttribute: [], // 特殊属性
        },
      });
      // 拼接货位号
      if (locationCode) {
        const arr = locationCode.split('-');
        if (arr && arr.length === 1) {
          yield ctx.changeData({
            data: {
              locationCodeLeft: locationCode,
              locationCodeRight: '',
            },
          });
        } else if (arr && arr.length > 1) {
          yield ctx.changeData({
            data: {
              locationCodeLeft: `${arr.shift()}-`,
              locationCodeRight: arr.join('-'),
            },
          });
        }
      }
      if (isRecommend === 1) {
        yield this.changeData({
          data: {
            isScanLocation: true,
          },
        });
        setTimeout(() => classFocus('barCode'), 20);
      } else {
        let tipText;
        yield ctx.changeLimit({
          data: {
            location: '',
          },
        });
        if (isRecommend === 4) {
          tipText = t('存在指派的采集任务未处理，请优先处理');
        } else {
          tipText = isRecommend === 2 ? t('该货位号没有任务，请根据货位推荐进行作业') : t('当前100个未找到，请到{}库位扫描', locationCode);
        }
        // 缺货或提交的自动扫描且isRecommend为2，不弹窗提示
        if (selfScan === true && isRecommend === 2) {
          // 仅清空聚焦不弹窗提示
          classFocus('locationInput');
        } else {
          // 弹窗提示，并清空库位号并聚焦
          Modal.info({
            modalBlurInput: true,
            content: tipText,
            onOk: () => classFocus('locationInput'),
          });
        }
      }
    } else {
      yield ctx.resetLimit();
      Modal.error({
        modalBlurInput: true,
        content: msg,
        onOk: () => classFocus('locationInput'),
      });
    }
  },
  // 缺货
  * lackGoods(action, ctx) {
    markStatus('dataLoading');
    const {
      taskDetailId,
    } = action;
    const res = yield lackGoods({ taskDetailId });
    if (res.code === '0') {
      message.success(t('缺货成功'));
      yield ctx.scanLocation(true);
    } else {
      Modal.error({
        className: 'barCode',
        content: res.msg,
      });
    }
  },
  // 扫描商品条码
  * queryGoods(action, ctx) {
    markStatus('dataLoading');
    const {
      barCode,
      taskDetailId,
    } = action;
    const res = yield queryGoods({
      barCode,
      taskDetailId,
    });
    if (res.code === '0') {
      // 更换商品条吗 清空理论重量
      yield ctx.changeLimit({ data: { pdcWeight: '' } });
      const {
        length,
        skc,
        specialAttribute,
      } = res.info;
      // 限制返回数据为null或0的情况
      if (length && length !== 0) {
        yield ctx.changeLimit({
          data: {
            weight: '', // OFC-32408 扫描条码后，不初始化赋值重量数据，默认重量为空
          },
        });
      }
      yield ctx.changeLimit({ data: { skc } });
      if (specialAttribute) {
        yield ctx.changeLimit({ data: { specialAttribute } });
      }
      // 拼接用于input显示的文本
      const { limit, specialAttributeList } = yield select((state) => state['inbound-manage/gather-task']);
      const arr = [];
      specialAttributeList.forEach((item) => {
        if (limit.specialAttribute.find((i) => parseInt(i, 10) === parseInt(item.dictCode, 10))) {
          arr.push(item.dictNameZh);
        }
      });
      yield ctx.changeData({
        data: {
          isDisabled: false,
        },
      });
      classFocus('weight');
    } else {
      Modal.error({
        className: 'barCode',
        content: res.msg,
      });
      yield ctx.changeLimit({ data: { barCode: '' } });
    }
  },
  // 提交采集任务
  * submitTask(action, ctx) {
    markStatus('dataLoading');
    const {
      submitTaskSecond,
      limit,
      confirmDifference,
    } = action;
    const data = { ...limit, confirmDifference };
    const { goodsNum } = yield select((state) => state['inbound-manage/gather-task']);
    keyArr.forEach((item) => {
      limit[item] = Number(limit[item]);
    });
    const realWeight = round(limit.weight / (goodsNum || 1), 0);
    if (realWeight && !goodsNum) {
      Modal.error({
        content: t('请输入件数！'),
        onOk: () => classFocus('goodsNum'),
      });
      return;
    }
    const result = yield submitTask(assign(data, { weight: realWeight }));
    if (result.code === '0') {
      const { resultCode, resultMsg, pdcWeight } = result.info;
      if (resultCode === 0 || resultCode === 2) {
        message.success(t('提交成功'));
        // 成功，并返回下一个任务
        yield ctx.scanLocation(true);
      } else if (resultCode === 1 || resultCode === 7) {
        // 1是体积或重量的差异率超过配置值，7是与最新更新的SKC相比重量的差异率超过配置值
        // eslint-disable-next-line @shein-bbl/bbl/translate-i18n-byT
        if (resultCode === 1 && !resultMsg.includes('重量')) {
          Modal.confirm({
            content: resultMsg,
            onOk: () => {
              submitTaskSecond(true);
            },
            onCancel: () => {},
          });
        } else {
          yield ctx.changeData({
            data: {
              confirmModalVisible: true,
              confirmModalMsg: resultMsg,
            },
          });
          classFocus('confirmModalInput');
        }
      } else if (resultCode === 8) {
        Modal.info({
          content: resultMsg,
          onOk: () => {
            classFocus('weight');
          },
        });
        yield ctx.changeLimit({ data: { weight: '', pdcWeight } });
        yield ctx.changeData({ data: { isDisabled: false } });
      } else {
        let position;
        // 长宽高重量 超限制的处理
        switch (resultCode) {
          case 3: case 7:
            position = 'weight';
            break;
          case 4:
            position = 'length';
            break;
          case 5:
            position = 'width';
            break;
          case 6:
            position = 'height';
            break;
          default:
            break;
        }
        yield ctx.changeLimit({
          data: {
            [position]: '',
          },
        });
        Modal.error({
          content: resultMsg,
          onOk: () => {
            classFocus(position);
          },
        });
      }
    } else {
      Modal.error({
        content: result.msg,
      });
    }
  },
};
