import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import classnames from 'classnames';
import {
  Form, Button, Dialog,
} from 'react-weui/build/packages';
import {
  Header,
  FocusInput,
  FooterBtn,
  Footer,
  PopSelect,
  modal,
  // DragCircle,
  message,
} from '../../common';

import style from '../../style.css';
import store from './reducers';
import myStyle from './style.css';
import { classFocus, getWarehouseId } from '../../../lib/util';

const reg = /^[1-9]\d{0,3}$/; // 匹配长宽高
const reg2 = /^[1-9]\d{0,6}$/; // 匹配重量
const reg3 = /^[1-9]\d{0,2}$/; // 匹配商品件数

class Container extends Component {
  constructor(props) {
    super(props);
    this.submitTaskCb = this.submitTaskCb.bind(this);
    this.validateAndSubmit = this.validateAndSubmit.bind(this);
  }

  // eslint-disable-next-line react/no-deprecated
  componentWillMount() {
    store.init();
  }

  componentDidMount() {
    // 聚焦库位号
    classFocus('locationInput');
  }

  submitTaskCb(confirmDifference) {
    const { limit } = this.props;
    store.submitTask({
      limit,
      confirmDifference,
      submitTaskSecond: (bool) => this.submitTaskCb(bool),
    });
  }

  validateAndSubmit() {
    const { limit } = this.props;
    const keyMap = ['weight', 'length', 'width', 'height'];
    const result = keyMap.find((item) => {
      const r = item === 'weight' ? reg2 : reg;
      return !r.test(limit[item]);
    });
    if (result) {
      modal.error({
        content: (
          <div>
            <div>{t('1:重量、长、宽、高都是必填项')}</div>
            <div>{t('2:重量、长、宽、高都是大于0的正整数')}</div>
            <div>{t('3:重量位数不得超过7位,长、宽、高不得超过4位')}</div>
          </div>
        ),
        onOk: () => {
          store.changeLimit({
            data: {
              [result]: '',
            },
          });
          classFocus(result);
        },
      });
    } else {
      this.submitTaskCb(false);
    }
  }

  render() {
    const {
      dataLoading,
      taskCode,
      isDisabled,
      specialAttributeList,
      showPicker,
      limit,
      realCollectCount,
      size,
      color,
      skc,
      isScanLocation,
      locationCodeLeft,
      locationCodeRight,
      headerTitle,
      goodsNum,
      confirmModalVisible,
      confirmModalInput,
      confirmModalMsg,
      itemCollectionWarehouseArray,
    } = this.props;
    const warehouseId = getWarehouseId();
    const height = window.innerHeight - 66;
    // const inputWrapHeight = window.innerHeight - 66 - $('.topWrap').height();
    const inputWrapHeight = window.innerHeight - 66 - 162 - 30;

    const clearConfirmModal = () => {
      store.changeData({
        data: {
          confirmModalVisible: false,
          confirmModalInput: '',
          confirmModalMsg: '',
        },
      });
    };

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <Header title={headerTitle || t('执行采集任务')} />
        <div>
          <div className="topWrap">
            <div
              className={classnames(myStyle.itemBg, myStyle.listItem, myStyle.taskCode)}
            >
              <span className={classnames(myStyle.taskCodeLeft)}>{taskCode}</span>
              <section className={classnames(myStyle.taskCodeRight)}>
                {t('已采集')}:{realCollectCount}
              </section>
            </div>
            <div
              className={classnames(myStyle.itemBg)}
              style={{ borderBottom: '1px solid #e9e9e9' }}
            >
              <div className={classnames(myStyle.listItem)}>
                <span className={myStyle.spanLeft}>{t('库位号')}</span>
                <span className={myStyle.spanRight}>
                  <span>{locationCodeLeft}</span>
                  <span className={myStyle.locationCodeRight}>{locationCodeRight}</span>
                </span>
              </div>
              <div className={classnames(myStyle.listItem)}>
                <span className={myStyle.spanLeft}>SKC</span>
                <span className={myStyle.spanRight}>
                  {skc}
                </span>
              </div>
              <div className={classnames(myStyle.sizeAndColorItem)}>
                <span>
                  {t('尺码')}:
                  <span className={myStyle.spanRightStyle}>
                    {
                      size?.length > 8 ? (
                        <span onClick={() => {
                          modal.info({
                            content: <span>{t('尺码')}: {size}</span>,
                          });
                        }}
                        >
                          <span>{size.slice(0, 8)}</span>
                          <span style={{ color: '#509afb' }}>...</span>
                        </span>
                      ) : size
                    }
                  </span>
                </span>
                <span style={{ flexShrink: 0 }}>
                  <span className={myStyle.spanLeftStyle}>{t('颜色')}</span>:
                  {
                    color?.length > 3 ? (
                      <span onClick={() => {
                        modal.info({
                          content: <span>{t('颜色')}: {color}</span>,
                        });
                      }}
                      >
                        <span>{color.slice(0, 3)}</span>
                        <span style={{ color: '#509afb' }}>...</span>
                      </span>
                    ) : (
                      <span>{color}</span>
                    )
                  }
                </span>
              </div>
            </div>
          </div>
          <div className={myStyle.gatherTaskScrollForm} style={{ height: inputWrapHeight }}>
            <Form>
              <FocusInput
                disabled={isScanLocation || dataLoading === 0}
                data-bind="limit.location"
                className="locationInput"
                onPressEnter={(e) => {
                  if (e.target.value) {
                    store.scanLocation();
                  }
                }}
              >
                <label>{t('库位号')}</label>
              </FocusInput>
              <FocusInput
                data-bind="limit.barCode"
                className="barCode"
                disabled={dataLoading === 0 || !isScanLocation}
                footer={(
                  <Button
                    className={myStyle.lackGoodsBtn}
                    disabled={dataLoading === 0 || !isScanLocation}
                    onClick={() => {
                      modal.confirm({
                        content: t('您确定缺货吗？'),
                        onOk: () => {
                          store.lackGoods({
                            taskDetailId: limit.taskDetailId,
                          });
                        },
                      });
                    }}
                  >
                    {t('缺货')}
                  </Button>
                )}
                onPressEnter={() => {
                  store.queryGoods({
                    barCode: limit.barCode,
                    taskDetailId: limit.taskDetailId,
                  });
                }}
              >
                <label>{t('条码')}</label>
              </FocusInput>
              <FocusInput
                disabled={isDisabled || dataLoading === 0}
                data-bind="limit.weight"
                className="weight"
                footer={(
                  <div style={{ display: 'flex', lineHeight: '32px' }}>
                    <span style={{ paddingRight: 5 }}>/</span>
                    <FocusInput
                      className={classnames(myStyle.psc, 'goodsNum')}
                      value={goodsNum}
                      disabled={itemCollectionWarehouseArray.includes(`${warehouseId}`)}
                      onChange={(e) => {
                        const val = e.target.value.trim();
                        if (val && !reg3.test(val)) {
                          store.changeData({ data: { goodsNum: 1 } });
                        } else {
                          store.changeData({ data: { goodsNum: e.target.value || '' } });
                        }
                      }}
                      onPressEnter={() => {
                        classFocus('length');
                      }}
                    />
                    <span style={{ paddingLeft: 5 }}>{t('件')}</span>
                  </div>
                )}
                ftstyle={{
                  fontSize: '14px', fontWeight: '700',
                }}
                onChange={(e) => {
                  const val = e.target.value.trim();
                  if (val) {
                    if (!reg2.test(val)) {
                      modal.error({
                        modalBlurInput: true,
                        content: t('请输入字符长度不超过7位的正整数!'),
                        onOk: () => {
                          store.changeLimit({ data: { weight: '' } });
                        },
                      });
                    } else {
                      store.changeLimit({ data: { weight: val } });
                    }
                  } else {
                    store.changeLimit({ data: { weight: val } });
                  }
                }}
                onPressEnter={() => {
                  if (itemCollectionWarehouseArray.includes(`${warehouseId}`)) {
                    classFocus('length');
                  } else {
                    classFocus('goodsNum');
                  }
                }}
              >
                <label>{t('重量(g)')}</label>
              </FocusInput>
              <FocusInput
                disabled={isDisabled || dataLoading === 0}
                data-bind="limit.length"
                className="length"
                onChange={(e) => {
                  const val = e.target.value.trim();
                  if (val) {
                    if (!reg.test(val) || val.length > 4) {
                      modal.error({
                        modalBlurInput: true,
                        content: t('请输入字符长度不超过4位的正整数!'),
                        onOk: () => {
                          store.changeLimit({ data: { length: '' } });
                        },
                      });
                    } else {
                      store.changeLimit({ data: { length: val } });
                    }
                  } else {
                    store.changeLimit({ data: { length: val } });
                  }
                }}
                onPressEnter={() => {
                  classFocus('width');
                }}
              >
                <label>{t('包装长(mm)')}</label>
              </FocusInput>
              <FocusInput
                disabled={isDisabled || dataLoading === 0}
                data-bind="limit.width"
                className="width"
                onChange={(e) => {
                  const val = e.target.value.trim();
                  if (val) {
                    if (!reg.test(val) || val.length > 4) {
                      modal.error({
                        modalBlurInput: true,
                        content: t('请输入字符长度不超过4位的正整数!'),
                        onOk: () => {
                          store.changeLimit({ data: { width: '' } });
                        },
                      });
                    } else {
                      store.changeLimit({ data: { width: val } });
                    }
                  } else {
                    store.changeLimit({ data: { width: val } });
                  }
                }}
                onPressEnter={() => {
                  classFocus('height');
                }}
              >
                <label>{t('包装宽(mm)')}</label>
              </FocusInput>
              <FocusInput
                disabled={isDisabled || dataLoading === 0}
                data-bind="limit.height"
                className="height"
                onChange={(e) => {
                  const val = e.target.value.trim();
                  if (val) {
                    if (!reg.test(val) || val.length > 4) {
                      modal.error({
                        modalBlurInput: true,
                        content: t('请输入字符长度不超过4位的正整数!'),
                        onOk: () => {
                          store.changeLimit({ data: { height: '' } });
                        },
                      });
                    } else {
                      store.changeLimit({ data: { height: val } });
                    }
                  } else {
                    store.changeLimit({ data: { height: val } });
                  }
                }}
              >
                <label>{t('包装高(mm)')}</label>
              </FocusInput>
            </Form>
            <Form>
              <PopSelect
                className="gatherTaskPicker"
                label={t('特殊属性')}
                show={showPicker}
                selectValue={limit.specialAttribute || []}
                selectList={specialAttributeList}
                disabled={isDisabled}
                onClick={() => {
                  if (isDisabled) {
                    return;
                  }
                  store.changeData({ data: { showPicker: true } });
                }}
                onCancel={() => {
                  store.changeData({ data: { showPicker: false } });
                }}
                onOk={(val) => {
                  store.changeData({ data: { showPicker: false } });
                  store.changeLimit({
                    data: {
                      specialAttribute: val,
                    },
                  });
                }}
              />
            </Form>
          </div>
        </div>
        <Dialog
          title={t('请再次输入重量')}
          show={confirmModalVisible}
          buttons={[
            {
              label: t('取消'),
              onClick: clearConfirmModal,
            }, {
              label: t('确定'),
              onClick: () => {
                // 一致
                if (Number(limit.weight) === Number(confirmModalInput)) {
                  this.submitTaskCb(true);
                } else {
                  // 不一致
                  message.error(t('两次重量输入不一致，请重新填写重量'));
                  store.changeLimit({ data: { weight: '' } });
                  classFocus('weight');
                }
                clearConfirmModal();
              },
            },
          ]}
        >
          <span>{confirmModalMsg}</span>
          <div style={{ display: 'flex', marginTop: '20px' }}>
            <div style={{
              display: 'flex', justifyContent: 'center', alignItems: 'center', marginRight: '5px', width: '85px',
            }}
            >
              <span style={{ color: 'red' }}>*</span>{t('重量')}(g)
            </div>
            <FocusInput
              data-bind="confirmModalInput"
              className="confirmModalInput"
              labelShowStyleObj={{ width: 30 }}
            />
          </div>
        </Dialog>
        <Footer>
          <FooterBtn
            disabled={isDisabled || dataLoading === 0
              || !limit.length || !limit.width || !limit.height}
            onClick={() => { this.validateAndSubmit(); }}
          >
            {t('确认提交')}
          </FooterBtn>
        </Footer>
        {/* <DragCircle */}
        {/*  onClick={() => { */}
        {/*    navStore.changeData({ data: { showUploadError: true } }); */}
        {/*    navStore.changeLimit({ data: { location: locationCode } }); */}
        {/*    classFocus('location'); */}
        {/*  }} */}
        {/* /> */}
      </div>
    );
  }
}

Container.propTypes = {
  dataLoading: PropTypes.number.isRequired,
  taskCode: PropTypes.string.isRequired,
  isDisabled: PropTypes.bool.isRequired,
  isScanLocation: PropTypes.bool.isRequired,
  specialAttributeList: PropTypes.arrayOf(PropTypes.shape).isRequired,
  showPicker: PropTypes.bool.isRequired,
  limit: PropTypes.shape(),
  realCollectCount: PropTypes.number.isRequired, // 已采集数
  skc: PropTypes.string.isRequired,
  size: PropTypes.string.isRequired,
  color: PropTypes.string.isRequired,
  locationCodeLeft: PropTypes.string.isRequired,
  locationCodeRight: PropTypes.string.isRequired,
  headerTitle: PropTypes.string,
  goodsNum: PropTypes.number,
  confirmModalVisible: PropTypes.bool,
  confirmModalInput: PropTypes.string,
  confirmModalMsg: PropTypes.string,
  itemCollectionWarehouseArray: PropTypes.shape(),
};

const mapStateToProps = (state) => state['inbound-manage/gather-task'];
export default connect(mapStateToProps)(i18n(Container));
