import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import store from './reducers';
import CellsMenu from '../../common/cells-menu';
import Header from '../../common/header';
import Footer from '../../common/footer';

class Container extends Component {
  componentWillMount() {
    store.init();
  }
  render() {
    const {
      dispatch,
      btnList,
      headTitle
    } = this.props;
    return (
      <div>
        <Header title={headTitle} />
        <CellsMenu
          cells={btnList}
        />
        <Footer />
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  btnList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  headTitle: PropTypes.string.isRequired,
};

export default Container;
