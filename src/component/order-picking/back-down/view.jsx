import React, { Component } from 'react';
import { i18n } from '@shein-bbl/react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import store from './reducers';
import DefaultPage from './jsx/default-page';
import DownPage from './jsx/down-page';
import DetailPage from './jsx/detail-page';
import HistoryPage from './jsx/history';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      status,
      showDetail,
      detailList,
      dispatch,
      showHistoryPage,
    } = this.props;
    let children;
    if (showHistoryPage) {
      return <HistoryPage {...this.props} />;
    }
    if (showDetail) {
      return (
        <div>
          <DetailPage
            data={detailList}
            dispatch={dispatch}
          />
        </div>
      );
    }
    switch (status) {
      case 'downPage':
        children = (<DownPage {...this.props} />);
        break;
      default:
        return <DefaultPage {...this.props} />;
    }
    return (
      <div>
        {children}
      </div>
    );
  }
}

Container.propTypes = {
  status: PropTypes.string,
  showDetail: PropTypes.bool,
  detailList: PropTypes.arrayOf(PropTypes.array),
  dispatch: PropTypes.func,
  showHistoryPage: PropTypes.bool,
};

const mapStateToProps = (state) => state['order-picking/back-down'];
export default connect(mapStateToProps)(i18n(Container));
