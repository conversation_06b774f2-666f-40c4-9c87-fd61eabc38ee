import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form, Button } from 'react-weui/build/packages';
import { LOCAL_BACK_DOWN_PICKER } from 'lib/storage';
import store from '../reducers';
import {
  Footer,
  FooterBtn,
  Header,
  FocusInput,
  modal,
  message,
  RowInfo,
  Pickers,
  pages,
} from '../../../common';
import myStyle from '../style.css';
import { classFocus } from '../../../../lib/util';

import Modal from './modal';

const { View } = pages;

function DownPage(props) {
  const {
    dataLoading,
    containerCode,
    locationInfo,
    location,
    barCode,
    returnTaskcode,
    shiftOrderCode,
    size,
    skc,
    skuCode,
    color,
    allPickNum,
    waitePickNum,
    hasPickNum,
    isLocationDisabled,
    isBarCodeDisabled,
    isContainerCodeDisabled,
    shortPickNum,
    modalType,
    changeBoxValue,
    changeBoxNum,
    changeBoxSwitch,
    isBarCodeBoxNumDisabled,
    shortPickNumModal,
    confirmBarCode,
    waitOffNumInput,
    confirmLoading,
    containerCodeValid,
    itemHadOffNum,
    itemWaitOffNum,
    selectShow,
    pickerDataList,
    currentPicker,
    currentLabel,
  } = props;

  // 对库位进行格式化展示
  const pos = locationInfo.indexOf('-');
  const locationLeft = locationInfo.substring(0, pos + 1);
  const locationRight = locationInfo.substring(pos + 1);

  // 关箱弹窗
  const closeContainer = () => {
    if (dataLoading === 0) return;
    modal.confirm({
      modalBlurInput: true,
      content: t('确定是否关箱?'),
      onOk: () => {
        store.closeContainer({
          params: {
            containerCode,
            shiftOrderCode,
          },
        });
      },
      onCancel: () => {
        if (waitePickNum > waitOffNumInput) {
          store.changeData({
            data: {
              barCode: '',
            },
          });
          classFocus('barCode');
        }
      },
    });
  };

  return (
    <div>
      <Header title={t('回货下架')}>
        <div
          onClick={() => {
            if (!containerCode) {
              modal.error({
                content: t('周转箱号不能为空'),
                onOk: () => classFocus('containerCode'),
              });
              return;
            }
            closeContainer();
          }}
        >
          {t('关箱')}
        </div>
        <div
          onClick={() => {
            store.shortPickModal();
          }}
        >
          {t('短拣')}
        </div>
      </Header>
      <View flex={false} diff={110}>
        <div
          className={myStyle.itemBg}
          style={{
            borderBottom: '1px solid #e9e9e9',
            marginBottom: 5,
          }}
        >
          <div className={myStyle.numShow}>
            <span className={myStyle.taskCode}>{returnTaskcode}</span>
            <span className={myStyle.taskNum}>
              {t('待拣/总数')}&nbsp;&nbsp;
              <span style={{ color: 'red' }}>{allPickNum - hasPickNum - shortPickNumModal}</span>
              /<span style={{ fontWeight: 'blod' }}>{allPickNum}</span>
            </span>
          </div>
        </div>
        <div
          className={myStyle.itemBg}
          style={{
            borderBottom: '1px solid #e9e9e9',
          }}
        >
          <div className={myStyle.listItem}>
            <span className={myStyle.spanLeft}>{t('库位')}</span>
            <span className={myStyle.spanRight}>
              <span>{locationLeft}</span>
              <span className={myStyle.locationCodeRight}>{locationRight}</span>
            </span>
          </div>
          <div className={myStyle.listItem}>
            <span className={myStyle.spanLeft}>SKC</span>
            <span className={myStyle.spanRight}>
              {skc}
            </span>
          </div>
          <div className={myStyle.listItem}>
            <span className={myStyle.spanLeft}>{t('尺码')}</span>
            <span className={myStyle.spanRight}>
              {size}
            </span>
          </div>
          <div className={myStyle.listItem}>
            <span className={myStyle.spanLeft}>{t('颜色')}</span>
            <span className={myStyle.spanRight}>
              {color}
            </span>
          </div>
        </div>
        <Form>
          <RowInfo
            extraStyle={{
              borderBottom: 'none',
            }}
            label={currentLabel}
            content={(
              <div>
                <span className={myStyle.num}>{currentPicker ? `${itemHadOffNum}/${itemWaitOffNum}` : waitePickNum}</span>
                <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    store.changeData({ data: { selectShow: true } });
                  }}
                >{t('模式')}
                </Button>
              </div>
          )}
          />
          <Pickers
            only
            value={currentPicker}
            defaultValue={currentPicker}
            label={t('选择子仓')}
            placeholder={t('请选择')}
            onClick={() => store.changeData({ data: { selectShow: true } })}
            onChange={(select) => {
              store.changeData({
                data: {
                  selectShow: false,
                  currentPicker: select.value,
                  currentLabel: select.label,
                },
              });
              localStorage.setItem(LOCAL_BACK_DOWN_PICKER, JSON.stringify(select));
            }}
            show={selectShow}
            pickerData={pickerDataList}
            onCancel={() => store.changeData({ data: { selectShow: false } })}
          />
        </Form>
        <Form style={{ marginTop: 5 }}>
          <FocusInput
            label={t('周转箱')}
            placeholder={t('请扫描')}
            className="containerCode"
            data-bind="containerCode"
            disabled={isContainerCodeDisabled}
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.scanContainer({ params: { container: containerCode } });
            }}
          />
          <FocusInput
            disabled={isLocationDisabled || !containerCodeValid || dataLoading === 0}
            label={t('库位')}
            className="location"
            placeholder={t('请扫描')}
            data-bind="location"
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              // 输入框内容为当前周转箱号，则进行关箱
              if (e.target.value === containerCode) {
                closeContainer();
                return;
              }

              // 判断和推荐库位是否一致
              if (location !== locationInfo) {
                modal.error({
                  modalBlurInput: true,
                  content: t('扫描库位不是推荐库位'),
                  className: 'location',
                });
                store.changeData({
                  data: {
                    location: '',
                  },
                });
                return;
              }

              store.scanLocation({
                params: {
                  location,
                  containerCode,
                  returnTaskCode: returnTaskcode,
                  size,
                  skc,
                  skuCode,
                },
              });
            }}
          />
          <FocusInput
            label={t('数量')}
            className="waitOffNum"
            data-bind="waitOffNumInput"
            onPressEnter={() => {
            // 业务: 提交后光标跳转至商品条码文本框
              classFocus('barCode');
            }}
            onChange={(e) => {
            // 是否为正整数
              const isPositiveInteger = (s) => /^[0-9]+$/.test(s);

              let number = Number(e.target.value);

              // 业务: 当输入的数量大于待下数量/为非正整数时，自动重置为当前待下数量
              if (!isPositiveInteger(number) || number > waitePickNum) {
                number = waitePickNum;
              }

              store.changeData({ data: { waitOffNumInput: number } });
            }}
          />
          <FocusInput
            disabled={isBarCodeDisabled || !location || !containerCodeValid || dataLoading === 0}
            label={t('条码')}
            className="barCode"
            placeholder={t('请扫描')}
            data-bind="barCode"
            onChange={(e) => {
              store.changeData({ data: { barCode: e.target.value.trim() } });
            }}
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }

              // 输入框内容为当前周转箱号，则进行关箱
              if (e.target.value === containerCode) {
                closeContainer();
                return;
              }

              if (!containerCode) {
                message.error(t('请先扫描周转箱'));
                return;
              }

              const params = {
                barCode,
                location,
                containerCode,
                returnTaskCode: returnTaskcode,
                shiftOrderCode,
                size,
                skc,
                skuCode,
              };

              if (waitOffNumInput) {
                params.offNum = Number(waitOffNumInput);
              }

              store.scanBarCode({
                params,
              });
            }}
          />
        </Form>
      </View>
      <Modal
        title={t('换箱')}
        isShowModal={modalType === 'changeBox'}
        canSure={confirmLoading && changeBoxNum !== '' && changeBoxNum < waitePickNum && changeBoxNum >= 0}
        onOK={() => {
          if (!confirmLoading) {
            return;
          }
          store.scanBarCode({
            params: {
              barCode,
              location,
              containerCode,
              returnTaskCode: returnTaskcode,
              shiftOrderCode,
              size,
              skc,
              skuCode,
              proceType: 1,
              scanNum: changeBoxNum,
            },
          });
        }}
        onCancel={() => {
          store.changeData({
            data: {
              modalType: '',
              barCode: '',
              isBarCodeDisabled: false,
            },
          });
          classFocus('barCode');
        }}
      >
        <div
          className={myStyle.changeBoxWrapper}
          style={{
            margin: '0 -28px', color: '#333', fontSize: 14,
          }}
        >
          <p className={myStyle.boxText}>{t('新周转箱')}:{changeBoxValue}</p>
          {
            !changeBoxSwitch && (
              <p className={myStyle.boxText}>
                <span>{t('商品条码')}</span>
                <input
                  className={`${myStyle.inputBox} confirmBarCode`}
                  value={confirmBarCode}
                  placeholder={t('请扫描')}
                  onChange={(e) => {
                    e.stopPropagation();
                    const { value } = e.target;
                    store.changeData({
                      data: {
                        confirmBarCode: value,
                      },
                    });
                  }}
                  onKeyUp={(e) => {
                    if (e.key === 'Enter') {
                      e.stopPropagation();
                      const { value } = e.target;
                      store.changeData({
                        data: {
                          confirmBarCode: value,
                        },
                      });
                      classFocus('changeBoxNum');
                    }
                  }}
                />
              </p>
            )
          }
          <p className={myStyle.boxText}>
            <span>{t('需换箱')}</span>
            <input
              className={`${myStyle.inputBox} changeBoxNum`}
              disabled={changeBoxSwitch || (!changeBoxSwitch && !confirmBarCode)}
              value={changeBoxNum}
              onChange={(e) => {
                e.stopPropagation();
                const { value } = e.target;
                store.changeData({
                  data: {
                    changeBoxNum: value,
                  },
                });
              }}
            />
            <span>{t('件')}</span>
          </p>
          <p data-if={changeBoxSwitch} className={myStyle.boxTipText}>{t('请逐件扫描商品后点击')}</p>
          <Form
            data-if={changeBoxSwitch}
            style={{ opacity: 0, height: 1 }}
          >
            <FocusInput
              disabled={isBarCodeBoxNumDisabled}
              style={{ opacity: 0, height: 1 }}
              data-bind="changeBoxBarCode"
              className="changeBoxBarCode"
              placeholder={t('请扫描')}
              onPressEnter={(e) => {
                store.scanBarCodeChangeBox({
                  params: {
                    barCode: e.target.value,
                    scanNum: changeBoxNum,
                    size,
                    skc,
                  },
                });
              }}
            />
          </Form>
        </div>
      </Modal>
      <Modal
        title={t('短拣')}
        isShowModal={modalType === 'shortPick'}
        canSure={confirmLoading && shortPickNum !== '' && shortPickNum < waitePickNum && shortPickNum >= 0}
        onOK={() => {
          store.shortPick({
            params: {
              location: locationInfo,
              containerCode,
              returnTaskcode,
              shiftOrderCode,
              size,
              skc,
              skuCode,
              countNum: shortPickNum,
            },
          });
        }}
        onCancel={() => {
          store.changeData({
            data: {
              modalType: '',
              shortPickNum: '',
            },
          });
        }}
      >
        <div
          className={myStyle.shortPickWrapper}
          style={{
            margin: '0 -28px', color: '#333', fontSize: 14,
          }}
        >
          <p className={myStyle.boxText}>{t('输入实际拣货数量后，系统自动计算短拣数')}</p>
          <p className={myStyle.boxText}>{t('库位号')}:{locationLeft + locationRight}</p>
          <p className={myStyle.boxText}>{t('SKC')}:{skc}</p>
          <p className={myStyle.boxText}>{t('尺码')}:{size}</p>
          <p className={myStyle.boxText}>{t('待拣数量')}:{waitePickNum}</p>
          <div style={{ marginLeft: -14 }}>
            <FocusInput
              type="number"
              lineBreak={false}
              placeholder={t('输入实际拣货数量')}
              data-bind="shortPickNum"
              className="shortPickNum"
              labelShowStyleObj={{ width: 60 }}
              autoFocus
            >
              <label>{t('拣货数')}:</label>
            </FocusInput>
          </div>
        </div>
      </Modal>
      <Footer
        beforeBack={(back) => {
          store.init();
          back();
        }}
      />
      <Footer
        beforeBack={() => {
          if (containerCode) {
            modal.confirm({
              content: t('当前周转箱未关箱,请关箱后返回!'),
              okText: t('直接返回'),
              onOk: () => store.init(),
            });
          } else {
            store.init();
          }
        }}
      >
        <FooterBtn
          onClick={() => {
            store.getDetail({
              params: {
                shiftOrderCode,
                replenishmentCode: returnTaskcode,
              },
            });
          }}
        >
          {t('明细')}
        </FooterBtn>
      </Footer>
    </div>
  );
}

DownPage.propTypes = {
  dataLoading: PropTypes.number,
  containerCode: PropTypes.string,
  confirmBarCode: PropTypes.string,
  locationInfo: PropTypes.string,
  location: PropTypes.string,
  barCode: PropTypes.string,
  returnTaskcode: PropTypes.string,
  shiftOrderCode: PropTypes.string,
  size: PropTypes.string,
  skc: PropTypes.string,
  skuCode: PropTypes.string,
  color: PropTypes.string,
  allPickNum: PropTypes.number,
  hasPickNum: PropTypes.number,
  waitePickNum: PropTypes.number,
  shortPickNum: PropTypes.number,
  isLocationDisabled: PropTypes.bool,
  isBarCodeDisabled: PropTypes.bool,
  isContainerCodeDisabled: PropTypes.bool,
  modalType: PropTypes.string,
  changeBoxValue: PropTypes.string,
  changeBoxNum: PropTypes.number,
  changeBoxSwitch: PropTypes.bool,
  isBarCodeBoxNumDisabled: PropTypes.bool,
  shortPickNumModal: PropTypes.number,
  waitOffNumInput: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  confirmLoading: PropTypes.number,
  containerCodeValid: PropTypes.bool,
  selectShow: PropTypes.bool,
  pickerDataList: PropTypes.arrayOf(PropTypes.shape()),
  currentPicker: PropTypes.number,
  currentLabel: PropTypes.string,
  itemHadOffNum: PropTypes.number,
  itemWaitOffNum: PropTypes.number,
};

export default DownPage;
