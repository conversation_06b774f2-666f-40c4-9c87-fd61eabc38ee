import React from 'react';
import PropTypes from 'prop-types';
import { Dialog } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import style from '../style.css';
import { isInt, classFocus, isaNumberInRange } from '../../../../lib/util';
import { modal } from '../../../common';

const Modal = (props) => {
  const {
    title,
    canSure,
    isShowModal,
    children,
    onOK,
    onCancel,
  } = props;
  return (
    <Dialog
      title={title}
      show={isShowModal}
      buttons={[
        {
          label: t('取消'),
          type: 'default',
          onClick: () => {
            onCancel();
          },
        }, {
          label: t('确定'),
          type: canSure ? 'primary' : 'default',
          disabled: !canSure,
          onClick: () => {
            if (canSure) {
              onOK();
            }
          },
        },
      ]}
    >
      {children}
    </Dialog>
  );
};

Modal.propTypes = {
  title: PropTypes.string,
  canSure: PropTypes.bool,
  isShowModal: PropTypes.bool,
  children: PropTypes.arrayOf(PropTypes.shape()),
  onOK: PropTypes.func,
  onCancel: PropTypes.func,
};


export default Modal;
