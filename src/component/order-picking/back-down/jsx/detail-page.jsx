import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { NavDetail, pages, Footer } from '../../../common';
import store from '../reducers';

const { View } = pages;

const rows1 = [
  [
    {
      title: 'SKC',
      render: 'goodsSn',
    },
    {
      title: t('数量'),
      render: 'alreadyNum',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('尺码'),
      render: 'size',
    },
    {
      title: t('库位'),
      render: 'location',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('周转箱'),
      render: 'containerCode',
    },
  ],
];
const rows2 = [
  [
    {
      title: 'SKC',
      render: 'goodsSn',
    },
    {
      title: t('数量'),
      render: 'waitNum',
      itemRenderStyle: { color: 'red' },
    },
  ],
  [
    {
      title: t('尺码'),
      render: 'size',
    },
    {
      title: t('库位'),
      render: 'location',
      itemRenderStyle: { color: 'red' },
    },
  ],
];


const DetailPage = (props) => {
  const {
    data,
    dispatch,
  } = props;

  const rowsList = [
    rows1, rows2,
  ];
  const hasPickNum = data[0].reduce((pre, current) => pre + current.alreadyNum, 0);
  const noPickkNum = data[1].reduce((pre, current) => pre + current.waitNum, 0);
  const totalNumList = [hasPickNum, noPickkNum];
  const navList = [t('已拣货'), t('未拣货')];
  return (
    <View diff={100}>
      <NavDetail
        data={data}
        rowsList={rowsList}
        navList={navList}
        imgUrlFieldName="imageUrl"
        totalNumList={totalNumList}
      />
      <Footer
        dispatch={dispatch}
        beforeBack={() => {
          store.changeData({
            data: {
              showDetail: false,
            },
          });
        }}
      />
    </View>
  );
};

DetailPage.propTypes = {
  data: PropTypes.arrayOf(PropTypes.array),
  dispatch: PropTypes.func,
};

export default DetailPage;
