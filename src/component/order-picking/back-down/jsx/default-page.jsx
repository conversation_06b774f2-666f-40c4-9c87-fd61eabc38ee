import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import store from '../reducers';
import {
  Header,
  RowInfo,
  FocusInput,
  modal,
  View,
  Footer,
} from '../../../common';
import { classFocus } from '../../../../lib/util';

function DefaultPage(props) {
  const {
    downNum,
    rank,
    total,
    waitTaskNum,
    containerCode,
    usedContainer,
    isContainerCodeDisabled,
    showHistoryPage,
    initLoading,
  } = props;

  const rowInfoList = [{
    label: t('待领取任务数'),
    content: waitTaskNum,
    type: 'info',
  }, {
    label: t('已下架件数'),
    content: downNum,
    type: 'warn',
  }, {
    label: t('排名'),
    content: total ? `${rank}/${total}` : '',
    type: 'sky',
  }];

  return (
    <div style={{ marginBottom: 0 }}>
      <Header
        title={t('任务领取')}
      >
        {showHistoryPage || (
          <div
            onClick={() => {
              store.getHistory();
            }}
          >
            {t('历史')}
          </div>
        )}
      </Header>
      <View
        diff={46}
        initLoading={initLoading === 0}
      >
        <Form>
          {rowInfoList.map((item) => (
            <RowInfo
              key={item.label}
              extraStyle={{
                borderBottom: 'none',
              }}
              label={item.label}
              content={item.content}
              type={item.type}
            />
          ))}
        </Form>

        <Form style={{ marginTop: 5 }}>
          <FocusInput
            disabled={isContainerCodeDisabled}
            label={t('周转箱')}
            placeholder={t('请扫描')}
            className="containerCode"
            data-bind="containerCode"
            autoFocus
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }

              if (usedContainer && usedContainer !== containerCode) {
                modal.confirm({
                  content: <span>{t('有任务中的周转箱')}(<span style={{ color: 'red' }}>{usedContainer}</span>),{t('请继续操作该箱')}！</span>,
                  okText: t('继续'),
                  onOk: () => {
                    // 点击继续，操作进行中的周转箱
                    store.changeData({
                      data: {
                        containerCode: usedContainer,
                        isContainerCodeDisabled: true,
                      },
                    });
                    store.scanContainer({ params: { container: usedContainer } });
                  },
                  onCancel: () => {
                    // 清空周转箱内容
                    store.changeData({
                      data: {
                        containerCode: '',
                        containerCodeValid: false,
                      },
                    });
                    classFocus('containerCode');
                  },
                });
                return;
              }

              store.scanContainer({ params: { container: containerCode } });
            }}
          />
        </Form>
      </View>
      <Footer />
    </div>
  );
}

DefaultPage.propTypes = {
  downNum: PropTypes.number,
  rank: PropTypes.number,
  total: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  waitTaskNum: PropTypes.number,
  containerCode: PropTypes.string,
  usedContainer: PropTypes.string,
  isContainerCodeDisabled: PropTypes.bool,
  showHistoryPage: PropTypes.bool,
  initLoading: PropTypes.number,
};

export default DefaultPage;
