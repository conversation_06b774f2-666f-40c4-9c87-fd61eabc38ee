import { sendPostRequest } from '../../../lib/public-request';
import { trimStr } from '../../../lib/util';

// 查询当前用户排名
export const queryRankInfoApi = (param) => sendPostRequest({
  baseUrl: process.env.WKB,
  url: '/rank/query',
  param,
});

// 回货下架-初始化
export const returnDownInitApi = (param) => sendPostRequest({
  url: '/pda/return_down/index',
  param,
}, process.env.WWS_URI);

// 回货下架-扫描周转箱
export const scanContainerApi = (param) => sendPostRequest({
  url: '/pda/return_down/scan_container',
  param: trimStr(param),
}, process.env.WWS_URI);

// 回货下架-扫描条码
export const scanBarCodeApi = (param) => sendPostRequest({
  url: '/pda/return_down/scan_goods',
  param,
}, process.env.WWS_URI);

// 回货下架-扫描库位
export const scanLocationApi = (param) => sendPostRequest({
  url: '/pda/return_down/scan_location',
  param: trimStr(param),
}, process.env.WWS_URI);

// 回货下架-短拣
export const shortPickApi = (param) => sendPostRequest({
  url: '/pda/return_down/short_pick',
  param,
}, process.env.WWS_URI);

// 回货下架-关箱
export const closeContainerApi = (param) => sendPostRequest({
  url: '/pda/return_down/close_container',
  param: trimStr(param),
}, process.env.WWS_URI);

// 明细
export const getDetailApi = (param) => sendPostRequest({
  url: '/pda/return_down/down_info',
  param: trimStr(param),
}, process.env.WWS_URI);

// 获取配置开关
export const getChangeBoxSwitch = (param) => sendPostRequest({
  url: '/pda/return_down/scan_piece',
  param: trimStr(param),
});

// 换箱弹窗扫描商品条码
export const scanChangeBoxBarCodeApi = (param) => sendPostRequest({
  url: '/pda/return_down/scan_goods_piece',
  param,
});

/**
 * 回货下架 - 历史查询
 * @param param
 * @returns {*}
 */
export const getHistoryApi = (param) => sendPostRequest({
  url: '/scan_down/history_down_list',
  param,
}, process.env.WWS_URI);
