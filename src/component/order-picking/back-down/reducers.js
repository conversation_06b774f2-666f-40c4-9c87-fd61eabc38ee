import React from 'react';
import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import Modal from 'common/modal';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import {
  closeContainer<PERSON>pi,
  queryRankInfoApi,
  returnDownInitApi,
  scanBarCodeApi,
  scanContainerApi,
  scanLocationApi,
  shortPickApi,
  getDetailApi,
  // getChangeBoxSwitch,
  scanChangeBoxBarCodeApi,
  getHistoryApi,
} from './server';
import { classFocus } from '../../../lib/util';
import { message, modal } from '../../common';

const defaultState = {
  initLoading: 1, // 0加载中 1加载成功 2加载失败【页面初始化】
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  // 页面状态
  status: '',
  // 操作中的周转箱
  usedContainer: '',
  // 已下架件数
  downNum: 0,
  // 排名
  rank: undefined,
  // 待领取任务数
  waitTaskNum: 0,
  // 周转箱
  containerCode: '',
  // 周转箱是否回车输入
  containerCodeValid: false,
  // 库位
  location: '',
  // 接口返回的库位
  locationInfo: '',
  // 数量 input
  waitOffNumInput: 0,
  // 商品编码
  barCode: '',
  // 周转箱输入框是否禁用
  isContainerCodeDisabled: false,
  // 库位输入框是否禁用
  isLocationDisabled: false,
  // 条码输入框是否禁用
  isBarCodeDisabled: false,
  // 换箱弹窗输入框是否禁用
  isBarCodeBoxNumDisabled: false,
  // 总拣数
  allPickNum: 0,
  // 已拣数
  hasPickNum: 0,
  // 待拣数
  waitePickNum: 0,
  // 回货类型
  orderType: '',
  // 回货任务号
  returnTaskcode: '',
  // 移位单号
  shiftOrderCode: '',
  // 尺码
  size: '',
  skc: '',
  skuCode: '',
  // 颜色
  color: '',
  detailList: [[], []],
  showDetail: false,
  total: '',
  // 短拣弹窗中短拣数量
  shortPickNum: '',
  // 弹窗类型
  modalType: '',
  // 换箱箱号
  changeBoxValue: '',
  // 换箱弹窗中换箱数量
  changeBoxNum: 0,
  changeBoxBarCode: '', // 用于界面展示
  changeBoxBarCodeVal: '', // 用于接口提交
  // 换箱配置开关
  changeBoxSwitch: false,
  // 短拣接口调用成功后返回的短拣数
  shortPickNumModal: 0,
  confirmBarCode: '', // 非逐件扫描，需传条码校验
  confirmLoading: 1, // 弹框确认按钮Loading
  historyList: [], // 历史数据
  showHistoryPage: false, // 展示历史页面
  selectShow: false,
  pickerDataList: [{
    items: [
      { label: t('待拣数量'), value: 0 },
      { label: t('已拣数量/总数量'), value: 1 },
    ],
  }],
  currentPicker: 0,
  currentLabel: t('待拣数量'),
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  // 设置任务信息
  setTaskInfo(draft, action) {
    assign(draft, {
      // 总待拣数
      allPickNum: action.data.allPickNum,
      // 已拣数
      hasPickNum: action.data.hasPickNum,
      // 商品待拣数
      waitePickNum: action.data.waitePickNum,
      waitOffNumInput: action.data.waitePickNum,
      itemHadOffNum: action.data.itemHadOffNum,
      itemWaitOffNum: action.data.itemWaitOffNum,
      // 短拣接口返回的短拣数量
      shortPickNumModal: action.data.shortPickNum || 0,
      // 商品所在库位
      locationInfo: action.data.location,
      size: action.data.size,
      skc: action.data.skc,
      skuCode: action.data.skuCode,
      color: action.data.color,
      // 清空库位和条码
      location: '',
      barCode: '',
      isLocationDisabled: false,
      isBarCodeDisabled: false,
    });
  },
  * init(action, ctx) {
    // 页面初始化
    yield ctx.returnDownInit();
    // 获取排名
    yield ctx.queryRankInfo();

    // 清空历史页面状态
    yield ctx.changeData({
      data: {
        showHistoryPage: false,
      },
    });
    // // 获取换箱弹窗开关配置
    // const res = yield getChangeBoxSwitch();
    // this.state.changeBoxSwitch = res.info.configValue === '1'; // 0-关，1-开
  },
  * queryRankInfo(action, ctx) {
    // 回货下架排名code=7
    const res = yield queryRankInfoApi({ rankTypeCode: 7 });
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          rank: res.info.rank,
          total: res.info.total,
        },
      });
    } else {
      modal.error({ content: res.msg });
    }
  },
  // 页面初始化
  * returnDownInit(action, ctx) {
    markStatus('initLoading');
    const res = yield returnDownInitApi(action.params);
    if (res.code === '0') {
      const { downNum, waitTaskNum, usedContainer } = res.info;
      yield ctx.changeData({
        data: {
          downNum,
          waitTaskNum,
          usedContainer,
        },
      });
    } else {
      modal.error({ content: res.msg });
    }
  },
  // 扫描周转箱
  * scanContainer(action, ctx) {
    yield ctx.changeData({ data: { isContainerCodeDisabled: true } });
    const res = yield scanContainerApi(action.params);
    if (res.code === '0') {
      const { changeArea, floor, area } = res.info;
      if (changeArea) {
        modal.info({
          // eslint-disable-next-line react/jsx-filename-extension
          content: <span>{t('任务切换为')}{floor}{t('层')}<b style={{ color: 'red' }}>{area}</b>{t('区')}</span>,
        });
      }
      const picker = JSON.parse(localStorage.getItem('backDownPicker'));
      if (picker) {
        yield ctx.changeData({
          data: {
            currentPicker: picker.value,
            currentLabel: picker.label,
          },
        });
      }
      // 根据状态切换页面
      yield ctx.changeData({ data: { status: 'downPage' } });
      // 设置回货任务号和移位单号
      yield ctx.changeData({
        data: {
          returnTaskcode: res.info.returnTaskcode,
          shiftOrderCode: res.info.shiftOrderCode,
        },
      });
      // 根据扫描周转箱返回数据进行设置
      yield ctx.setTaskInfo({ data: res.info });
      // 设置周转箱已回车输入
      yield ctx.changeData({
        data: {
          containerCodeValid: true,
        },
      });
      // 请求成功跳转到库位
      classFocus('location');
    } else if (res.code === '503801') {
      // 重新页面跳转至领取任务页面
      const status = yield new Promise((r) => modal.error({
        content: res.msg,
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
        classFocus('containerCode');
      }
    } else {
      modal.error({
        content: res.msg,
        className: 'containerCode',
      });
      // 清空周转箱
      yield ctx.changeData({
        data: {
          containerCode: '',
          containerCodeValid: false,
          isContainerCodeDisabled: false,
        },
      });
    }
  },
  // 扫描库位
  * scanLocation(action, ctx) {
    markStatus('dataLoading'); // 搜索loading状态
    yield ctx.changeData({ data: { isLocationDisabled: true } });
    const res = yield scanLocationApi(action.params);
    if (res.code === '0') {
      const { allInventoryNum, waitInventoryNum } = res.info.info;

      // 比较汇总数和待下架数
      if (allInventoryNum > waitInventoryNum) {
        const status = yield new Promise((r) => modal.info({
          content: t('请点数下架'),
          onOk: () => r('ok'),
        }));
        if (status === 'ok') {
          classFocus('barCode');
        }
      } else {
        classFocus('barCode');
      }
    } else {
      modal.error({
        content: res.msg,
        className: 'location',
      });
      yield ctx.changeData({
        data: {
          location: '',
          isLocationDisabled: false,
        },
      });
    }
  },
  // 扫描条码
  * scanBarCode(action, ctx) {
    const {
      waitePickNum, waitOffNumInput, containerCode: inputContainerCode,
      shiftOrderCode,
    } = yield this.state;

    yield ctx.changeData({ data: { isBarCodeDisabled: true } });

    const { params } = action;
    if (this.state.modalType === 'changeBox') {
      // // 校验不能输入小数
      // if (/[\\. ]/.test(params.scanNum) || params.scanNum === 0 || params.scanNum === '0') {
      //   this.state.changeBoxNum = '';
      //   message.error(t('请输入正整数'));
      //   return;
      // }
      // // 换箱弹窗中扫描的商品条码
      // const barCodeVal = this.state.changeBoxSwitch
      //   ? this.state.changeBoxBarCodeVal : this.state.confirmBarCode;
      // if (!barCodeVal) {
      //   message.error(t('商品条码不能为空！'));
      //   return;
      // }
      // const obj = {
      //   containerCode: this.state.changeBoxValue, // 换箱的箱号
      //   barCode: barCodeVal, // 换箱弹窗中扫描的商品条码
      //   scanNum: this.state.changeBoxNum, // 扫描的数量
      //   proceType: 1, // 1换箱 0 不换箱
      //   warehouseId: JSON.parse(localStorage.getItem('warehouse') || '{}').warehouseId || '',
      //   warehouseName: JSON.parse(localStorage.getItem('warehouse') || '{}').warehouseName || '',
      // };
      // Object.assign(params, obj);
    } else {
      Object.assign(params, {
        proceType: 0,
        warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
        warehouseName: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseName || '',
      });
    }

    // 待下架-下架数>0, 弹窗确认
    if (Number(waitePickNum) > Number(waitOffNumInput)) {
      const status = yield new Promise((r) => modal.confirm({
        content: t('确定是否关箱?'),
        onOk: () => r(1),
        onCancel: () => r(2),
      }));
      if (status === 2) {
        yield ctx.changeData({
          data: {
            barCode: '',
            isBarCodeDisabled: false,
          },
        });
        classFocus('barCode');
        return;
      }
    }

    markStatus('confirmLoading');
    // const res = yield scanBarCodeApi(action.params);
    const res = yield scanBarCodeApi(params);
    const { isChangeBox, containerCode } = res.info || {};
    if (isChangeBox) { // 当有换箱弹窗时点确定 这个是false 不会进来
      // OFC-26328:【补货下架】支持操作换箱
      // 换箱 清空周转箱
      yield ctx.changeData({
        data: {
          containerCode: '',
          containerCodeValid: false,
          isContainerCodeDisabled: false,
        },
      });
    }
    if (res.code === '0') {
      if (res.info?.showPopupToTakeTheBox) {
        yield new Promise((r) => modal.info({
          content: t('库位周转箱库存为空请取走库位周转箱如有实物请进行盘盈?'),
          onOk: () => r('ok'),
        }));
      }
      if (containerCode) {
        yield new Promise((r) => modal.info({
          content: t('下架成功，请下架空周转箱：{}', containerCode),
          onOk: () => r(1),
        }));
        // 根据res.info.type走不同的逻辑
        yield ctx.doByType({
          info: res.info,
          modalType: 'changeBox',
          tips: null,
        });
      } else {
        // 根据res.info.type走不同的逻辑
        yield ctx.doByType({
          info: res.info,
          modalType: 'changeBox',
          tips: t('提交数据成功'),
        });
      }
    } else if (res.code === '500003') {
      modal.error({
        content: res.msg,
        className: 'changeBoxBarCode',
      });
      yield ctx.changeData({
        data: {
          changeBoxBarCode: '',
          changeBoxBarCodeVal: '',
          isBarCodeBoxNumDisabled: false,
        },
      });
    } else if (res.code === '503002') {
      const status = yield new Promise((r) => modal.confirm({
        content: t('当前扫描商品，在周转箱上已经存在，不允许混放，请关箱！'),
        onOk: () => r(1),
        onCancel: () => r(2),
        okText: t('关箱'),
        cancelText: t('返回'),
      }));
      if (status === 1) {
        yield ctx.closeContainer({
          params: {
            containerCode: inputContainerCode,
            shiftOrderCode,
          },
        });
      }
      // 1. 清空库位,条码
      // 2. 光标定位到库位
      if (status === 2) {
        yield this.changeData({
          data: {
            barCode: '',
            isBarCodeDisabled: false,
            location: '',
            isLocationDisabled: false,
            containerCodeValid: true,
            containerCode: inputContainerCode,
          },
        });
        classFocus('location');
      }
    } else if (res.code === '503801') {
      // 重新页面跳转至领取任务页面
      const status = yield new Promise((r) => modal.error({
        content: res.msg,
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
        classFocus('containerCode');
      }
    } else {
      modal.error({
        content: res.msg,
        className: 'barCode',
      });
      yield ctx.changeData({
        data: {
          barCode: '',
          isBarCodeDisabled: false,
          modalType: '',
        },
      });
    }
  },
  // 扫描换箱弹窗条码
  * scanBarCodeChangeBox(action) {
    yield this.changeData({ data: { isBarCodeBoxNumDisabled: true } });
    const { params } = action;
    this.state.changeBoxBarCode = params.barCode;
    const res = yield scanChangeBoxBarCodeApi(params);
    if (res.code === '0') {
      yield this.changeData({
        data: {
          changeBoxNum: res.info.scanNum, // 数量
          isBarCodeBoxNumDisabled: false,
          changeBoxBarCode: '', // 扫描成功清空
          changeBoxBarCodeVal: params.barCode, // 保留上次扫描成功的条码，用于提交
        },
      });
      classFocus('changeBoxBarCode');
    } else {
      modal.error({
        content: res.msg,
        className: 'changeBoxBarCode',
      });
      yield this.changeData({
        data: {
          changeBoxBarCode: '',
          isBarCodeBoxNumDisabled: false,
        },
      });
    }
  },
  // 关箱
  * closeContainer(action, ctx) {
    markStatus('dataLoading');
    const res = yield closeContainerApi(action.params);
    if (res.code === '0') {
      message.success(t('关箱成功'));
      // 关箱成功之后清空输入框，不需要页面跳转
      yield ctx.changeData({
        data: {
          containerCode: '',
          location: '',
          barCode: '',
          isContainerCodeDisabled: false,
          isLocationDisabled: false,
          isBarCodeDisabled: false,
          containerCodeValid: false,
        },
      });
      // 光标跳转至周转箱，重新扫描周转箱
      classFocus('containerCode');
    } else {
      modal.error({ content: res.msg });
    }
  },
  // 短拣
  * shortPick(action, ctx) {
    // 校验不能输入小数
    if (/[\\. ]/.test(action.params.countNum)) {
      this.state.shortPickNum = '';
      message.error(t('请输入正整数'));
      return;
    }
    markStatus('confirmLoading');
    const res = yield shortPickApi(action.params);
    if (res.code === '0') {
      if (res.info.containerCode) {
        yield new Promise((r) => modal.info({
          content: (<div style={{ height: '200px', display: 'flex', alignItems: 'center' }}>{t('短拣成功，请下架空周转箱：{}', res.info.containerCode)}</div>),
          onOk: () => r(1),
        }));
        // 根据res.info.type走不同的逻辑
        yield ctx.doByType({
          info: res.info,
          modalType: 'shortPick',
          tips: null,
        });
      } else {
        // 根据res.info.type走不同的逻辑
        yield ctx.doByType({
          info: res.info,
          modalType: 'shortPick',
          tips: t('短拣成功'),
        });
      }
    } else if (res.code === '503801') {
      // 页面跳转至领取任务页面
      const status = yield new Promise((r) => modal.error({
        content: res.msg,
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
        classFocus('containerCode');
      }
    } else {
      message.error(res.msg);
    }
  },
  // 根据res.info.type走不同的逻辑
  * doByType(action, ctx) {
    // 判断该任务是否完成，type=1表示任务已完成，页面返回到上一页，type=0表示任务还未完成，需要继续操作
    const {
      type,
      returnTaskGoodsInfo,
      recommendUpperParkName,
      recommendedTasksRsp,
    } = action.info;
    const {
      containerCode, location, locationInfo,
    } = yield this.state;
    if (type === 0) {
      // 任务未完成
      if (action.tips) {
        message.success(action.tips);
      }
      yield ctx.setTaskInfo({ data: returnTaskGoodsInfo });
      yield ctx.changeData({
        data: {
          modalType: '', // 关闭弹窗
          shortPickNum: '', // 清空短捡数
          // 判断明细库位是否与推荐库位一致 若一致则不清空，不一致则清空
          location: locationInfo === returnTaskGoodsInfo.location ? locationInfo : '', // 库位信息
        },
      });
      // 周转箱有值跳转至库位，无值跳转至周转箱
      classFocus(containerCode ? 'location' : 'containerCode');
      if (action.modalType === 'changeBox') {
        // // 换箱点确定后 库位有值聚焦商品条码,无则聚焦库位
        // classFocus(location ? 'barCode' : 'location');

        if (!containerCode) {
          // 周转箱无值跳转至周转箱
          classFocus('containerCode');
        } else if (!location) {
          classFocus('location');
        } else {
          classFocus('barCode');
        }
      }
    } else if (type === 1) {
      // 任务已完成
      this.state.modalType = ''; // 关闭弹窗
      this.state.shortPickNum = ''; // 清空短拣数

      let msg = t('任务已完成');

      if (recommendUpperParkName) {
        msg += `, ${t('推荐上架园区')}："${recommendUpperParkName}"`;
      }

      if (Number(recommendedTasksRsp?.isAcrossArea) === 1) {
        msg += `, ${t('任务将由上一个任务的{}库区转移到{}库区', recommendedTasksRsp.currentArea, recommendedTasksRsp.area)}`;
      }

      const status = yield new Promise((r) => modal.info({
        content: msg,
        onOk: () => r('ok'),
      }));
      if (status === 'ok') {
        yield ctx.init();
        classFocus('containerCode');
      }
    } else {
      // 设置商品数据
      yield ctx.setTaskInfo({ data: returnTaskGoodsInfo });

      // 清空数据
      yield ctx.changeData({
        data: {
          containerCode: '',
          location: '',
          barCode: '',
          isContainerCodeDisabled: false,
          isLocationDisabled: false,
          isBarCodeDisabled: false,
          containerCodeValid: false,
        },
      });
      // 光标跳转至周转箱，重新扫描周转箱
      classFocus('containerCode');
    }
  },
  * getDetail(action, ctx, put) {
    const res = yield getDetailApi(action.params);
    if (res.code === '0') {
      yield put((draft) => {
        draft.showDetail = true;
        draft.detailList = [
          res.info.alreadyUnderGoodsInfo || [],
          res.info.waitUnderGoodsInfo || [],
        ];
      });
    } else {
      modal.error({ content: res.msg });
    }
  },
  * shortPickModal() {
    yield this.state.modalType = 'shortPick';
    classFocus('shortPickNum');
  },
  // 获取历史装箱信息
  * getHistory(action, ctx, put) {
    const { code, info, msg } = yield getHistoryApi({ shiftOrderType: 5 });
    if (code === '0') {
      if (info && info.length) {
        yield put((draft) => {
          draft.historyList = info || [];
          draft.showHistoryPage = true;
        });
      } else {
        Modal.info({
          content: t('我的上架历史数据为空'),
        });
      }
    } else {
      Modal.error({ title: msg });
    }
  },
};
