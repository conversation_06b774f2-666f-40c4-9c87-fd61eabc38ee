.itemBg {
    background-color: #ffffff;
}

.listItem {
    display: flex;
    padding: 0 15px 5px;
}

.spanLeft {
    color: #333e59;
    width: 50px;
}

.spanRight {
    flex: 1;
    text-align: right;
    color: #141737;
    font-weight: bold;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.locationCodeRight {
    font-size: 20px;
}

.numShow {
    padding: 0 8px;
    line-height: 36px;
    font-size: 15px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.taskCode {
    text-align: left;
    color: #333e59;
    float: left;
}

.taskNum {
    text-align: right;
    color: #141737;
    float: right;
}

.changeBoxWrapper , .shortPickWrapper {
    box-sizing: border-box;
    text-align: left;
    padding: 0px 24px;
}

.changeBoxWrapper .boxText,.shortPickWrapper .boxText{
    margin-bottom: 10px;
}


.changeBoxWrapper .boxTipText{
    padding-left: 20px;
    margin-bottom: 10px;
    color: #ff3636;
    font-size: 12px;
}

.inputBox {
    margin: 0 5px;
    padding: 5px;
    width: 80px;
    border: 1px solid #dddddd;
    outline: none;
    border-radius: 3px;
}

.num {
    color: rgb(255, 0, 0);
    font-weight: bold;
    font-size: 20px;
    margin-right: 40px;
}
