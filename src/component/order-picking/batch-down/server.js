import { sendPostRequest } from '../../../lib/public-request';

export const queryTask = (param = {}) => sendPostRequest({
  url: '/pda/standard_pick_goods_num',
  param,
}, process.env.WOS_URI);
/**
 * 查询当前用户排名
 * @param param
 * @returns {*}
 */
export const pdaQueryRankInfo = (param) => sendPostRequest({
  baseUrl: process.env.WKB,
  url: '/rank/query',
  param,
});
/**
 * 扫描周转箱
 * @param param
 * @returns {*}
 */
export const scanPickContainer = (param) => sendPostRequest({
  url: '/pda/standard_take_pick_task',
  param,
}, process.env.WOS_URI);
/**
 * 扫描库位
 * @param param
 * @returns {*}
 */
export const scanLocation = (param) => sendPostRequest({
  url: '/pda/batch_off/scan_location',
  param,
});

/**
 * 关箱
 * @param param
 * @returns {*}
 */
export const closePickContainer = (param) => sendPostRequest({
  url: '/pda/standard_close_container',
  param,
}, process.env.WOS_URI);

/**
 * 短拣
 * @param param
 * @returns {*}
 */
export const shortPick = (param) => sendPostRequest({
  url: '/pda/standard_short_pick',
  param,
}, process.env.WOS_URI);

export const getDetailApi = (param) => sendPostRequest({
  url: '/pda/standard_pick_detail',
  param,
}, process.env.WOS_URI);
