import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import { Form } from 'react-weui/build/packages/components/form';
import Footer from '../../common/footer';
import FooterBtn from '../../common/footer-btn';
import FocusInput from '../../common/focus-input';
import Info from './Info';
import Modal from '../../common/modal';
import RowInfo from '../../common/row-info';
import store from './reducers';
import Header from '../../common/header';


const NormalPage = (props) => {
  const {
    containerCode,
    containerDisabled,
    locationDisabled,
    info,
    locationInput,
    headerTitle,
  } = props;
  return (
    <div>
      <Header title={headerTitle || t('批量下架')}>
        <div
          onClick={() => {
            Modal.confirm({
              content: t('确认是否短拣当前商品？'),
              okText: t('是'),
              cancelText: t('否'),
              onOk: () => {
                store.shortPick({
                  params: {
                    pickContainerCode: containerCode,
                    orderNo: info.orderNo,
                    taskDetailId: info.taskDetailId,
                    taskCode: info.taskCode,
                  },
                  status: 'normalPage',
                });
              },
            });
          }}
        >
          <Icon name="duanjian" style={{ marginRight: 5 }} />
          {t('短拣')}
        </div>
      </Header>
      <Form>
        <Info data={info} />
        <RowInfo
          extraStyle={{ justifyContent: 'flex-start' }}
          textExtraStyle={{
            width: '32%',
            color: '#FF0000',
            fontWeight: 'bold',
            fontSize: 20,
          }}
          label={t('待拣数量')}
          content={info.needOperateNum}
        />
      </Form>
      <Form
        style={{ marginTop: '10px' }}
      >
        <div style={{ position: 'relative' }}>
          <FocusInput
            importance
            data-bind="containerCode"
            disabled={containerDisabled}
            className="box"
            onPressEnter={(e) => {
              if (e.target.value) {
                store.scanBox(e.target.value);
              }
            }}
          >
            <label>{t('拣货周转箱')}</label>
          </FocusInput>
        </div>
        <FocusInput
          placeholder={t('请扫描')}
          data-bind="locationInput"
          onPressEnter={(e) => {
            if (e.target.value) {
              store.scanLocation({
                params: {
                  containerCode,
                  location: locationInput,
                },
                waitOffNum: info.waitOffNum,
                shiftOrderCode: info.shiftOrderCode,
                info,
              });
            }
          }}
          disabled={locationDisabled === 0}
          className="location"
        >
          <label>{t('库位号')}</label>
        </FocusInput>
      </Form>
      <Footer
        beforeBack={() => {
          store.init();
          store.getInfo();
        }}
      >
        <FooterBtn
          onClick={() => {
            store.getDetail({
              params: {
                taskCode: info.taskCode,
              },
            });
          }}
        >
          {t('明细')}
        </FooterBtn>
      </Footer>
    </div>
  );
};

NormalPage.propTypes = {
  containerDisabled: PropTypes.number,
  locationDisabled: PropTypes.number,
  info: PropTypes.shape(),
  locationInput: PropTypes.string,
  headerTitle: PropTypes.string,
  containerCode: PropTypes.string,
};

export default NormalPage;
