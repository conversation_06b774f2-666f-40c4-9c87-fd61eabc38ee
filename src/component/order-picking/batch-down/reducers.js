import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { getApolloConfigAPI } from '../../../server/basic/common';
import {
  queryTask, pdaQueryRankInfo, scanPickContainer,
  closePickContainer, scanLocation, shortPick,
  getDetailApi,
} from './server';
import Message from '../../common/message';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle, apolloFormatObj } from '../../../lib/util';

const defaultState = {
  status: 'default',
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  baseData: [
    {
      label: t('待领取任务数'),
      num: 0,
      type: 'info',
    },
    {
      label: t('已下架件数'),
      num: 0,
      type: 'warn',
    },
    {
      label: t('排名'),
      num: 0,
      type: 'sky',
    },
  ],
  containerCode: '', // 外层周转箱
  boxDisabled: 1,
  info: '',
  locationInput: '',
  containerDisabled: false,
  locationDisabled: 1,
  headerTitle: '',
  showDetail: false,
  detailList: [[], []],
  degradeScanUpgrade: false, // 降级开关
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  // 获取页面降级开关 DEGRADE_BATCH_DOWN
  * getDegradeConfig(action, ctx) {
    try {
      const res = yield getApolloConfigAPI({ params: ['DEGRADE_BATCH_DOWN'] });
      const apolloConfigFormatRes = apolloFormatObj(res.info);

      if (res.code === '0' && apolloConfigFormatRes?.DEGRADE_BATCH_DOWN === '1') {
        yield ctx.changeData({ data: { degradeScanUpgrade: apolloConfigFormatRes?.DEGRADE_BATCH_DOWN === '1' } });
      } else {
        // 获取排名。后端接口降级
        yield ctx.getRankInfo();
      }
    } catch (e) {
      console.error(e);
    }
  },
  * getInfo(action, ctx, put) {
    // 获取页面降级开关
    yield ctx.getDegradeConfig();

    markStatus('dataLoading');
    const res = yield queryTask();
    if (res.code === '0') {
      yield put((draft) => {
        draft.baseData[0].num = res.info.needOperateNum;
        draft.baseData[1].num = res.info.hadOperateNum;
        draft.boxDisabled = 1;
      });
      classFocus('box');
    } else {
      Modal.error({ content: t('请求数据出错') });
    }
  },
  // 获取排名
  * getRankInfo(action, ctx, put) {
    // 如果开了降级，就不调接口
    const { degradeScanUpgrade } = yield '';
    if (degradeScanUpgrade) { return; }
    const data = yield pdaQueryRankInfo({ rankTypeCode: 5 });
    if (data.code === '0') {
      yield put((draft) => {
        draft.baseData[2].num = data.info.rank;
      });
      classFocus('box');
    } else {
      Modal.error({ content: t('请求数据出错') });
    }
  },
  * scanBox(action, ctx, put) {
    markStatus('boxDisabled');
    const res = yield scanPickContainer({
      containerCode: action,
      isBaseAllocate: true,
    });
    if (res.code !== '0') {
      yield put((draft) => {
        draft.containerCode = '';
      });
      Modal.error({
        content: res.msg,
        onOk: () => classFocus('box'),
      });
    } else {
      yield put((draft) => {
        draft.info = res.info;
        draft.status = 'normalPage';
        draft.containerDisabled = true;
      });
      // 返回的箱号，有可能是扫描的，也有可能是之前的任务
      if (action !== res.info.containerCode) {
        const isYes = yield new Promise((r) => Modal.confirm({
          content: t('有未关箱的任务'),
          okText: t('是'),
          cancelText: t('否'),
          onOk: () => { r(true); },
          onCancel: () => { r(false); },
        }));
        if (isYes) {
          yield put((draft) => {
            draft.containerCode = res.info.containerCode;
          });
          Message.info(t('若要使用新的箱号，请先关箱！'));
        }
      } else {
        classFocus('location');
      }
    }
  },
  * scanLocation(action, ctx) {
    yield ctx.changeData({
      data: {
        locationDisabled: 0,
      },
    });
    // 扫描正确的库位号
    if (action.info.location.toUpperCase() === action.params.location.toUpperCase()) {
      const { code, info, msg } = yield scanLocation({
        location: action.params.location,
        outboundCode: action.info.orderNo,
        pickContainerCode: action.params.containerCode,
        replenishmentCode: action.info.taskCode,
        replenishmentDetailId: action.info.taskDetailId,
      });
      if (code === '0') {
        // 已完成
        if (info.finish) {
          const status = yield new Promise((r) => Modal.success({
            content: t('任务已结束，点击确定返回任务领取页面'),
            okText: t('确定'),
            onOk: () => r(1),
          }));
          if (status === 1) {
            yield ctx.init();
            yield ctx.getInfo();
          }
        } else {
          // 未完成，调用一次扫周转箱接口获取新的库位号任务
          yield ctx.changeData({
            data: {
              locationInput: '',
              locationDisabled: 1,
            },
          });
          yield ctx.scanBox(action.params.containerCode);
        }
      } else if (code === '500383') {
        // 装箱头已使用过
        const status = yield new Promise((r) => Modal.error({
          content: msg,
          onOk: () => r(1),
        }));
        if (status === 1) {
          yield ctx.init();
          yield ctx.getInfo();
        }
      } else {
        yield ctx.changeData({
          data: {
            locationInput: '',
            locationDisabled: 1,
          },
        });
        Modal.error({
          content: msg,
          onOk: () => {
            classFocus('location');
          },
        });
      }
    } else if (action.params.location.toUpperCase() === action.params.containerCode.toUpperCase()) {
      // 扫描周转箱，换箱
      const status = yield new Promise((r) => Modal.confirm({
        content: t('是否确认换箱'),
        onOk: () => r(1),
        onCancel: () => r(2),
      }));
      if (status === 1) {
        yield ctx.closeBox({
          params: {
            containerCode: action.params.containerCode,
          },
          names: {
            name: 'locationInput',
            disabled: 'locationDisabled',
            className: 'location',
          },
        });
      }
      if (status === 2) {
        yield ctx.changeData({
          data: {
            locationInput: '',
            locationDisabled: 1,
          },
        });
        classFocus('location');
      }
    } else {
      // 错误的情况处理
      yield ctx.changeData({
        data: {
          locationInput: '',
          locationDisabled: 1,
        },
      });
      Modal.error({
        content: t('库位号扫描错误'),
        onOk: () => {
          classFocus('location');
        },
      });
    }
  },
  * closeBox(action, ctx, put) {
    const res = yield closePickContainer(action.params);
    if (res.code !== '0') {
      yield put((draft) => {
        draft[action.names.name] = '';
        draft[action.names.disabled] = 1;
      });
      Modal.error({
        content: res.msg,
        onOk: classFocus(action.names.className),
      });
    } else {
      yield put((draft) => {
        draft.containerCode = '';
        draft.containerDisabled = false;
        draft.locationInput = '';
        draft.locationDisabled = 1;
      });
      classFocus('box');
    }
  },
  * shortPick(action, ctx, put) {
    const result = yield shortPick(action.params);
    if (result.code === '0') {
      // 继续下一件
      if (result.info.backFlag === 2) {
        yield put((draft) => {
          draft.locationInput = '';
          draft.locationDisabled = 1;
          draft.info = result.info.pdaStandardOperateDetailRsp;
        });
        classFocus('location'); // 光标置于货位号
      } else {
        // 跳入领取页面
        const changeStatus = yield new Promise((r) => {
          Modal.success({
            content: t('拣货完成，点击确定返回任务领取页面'),
            okText: t('确定'),
            onOk: () => r(true),
          });
        });
        if (changeStatus) {
          yield ctx.init();
          yield ctx.getInfo();
        }
      }
    } else {
      yield put((draft) => {
        draft.containerDisabled = true;
        draft.locationInput = '';
        draft.locationDisabled = 1;
      });
      Modal.error({
        content: result.msg,
        onOk: () => {
          classFocus('location');
        },
      });
    }
  },
  * getDetail(action, ctx, put) {
    markStatus('dataLoading');
    const res = yield getDetailApi(action.params);
    if (res.code === '0') {
      const { hasPickList, waitingPickList } = res.info;
      yield put((draft) => {
        draft.showDetail = true;
        draft.detailList = [hasPickList || [], waitingPickList || []];
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
};
