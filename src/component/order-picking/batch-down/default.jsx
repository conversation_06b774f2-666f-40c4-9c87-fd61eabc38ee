import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import LoadMore from 'react-weui/build/packages/components/loadmore';
import Footer from '../../common/footer';
import FocusInput from '../../common/focus-input';
import BaseData from './base-data';
import store from './reducers';
import Header from '../../common/header';

const DefaultPage = (props) => {
  const {
    dataLoading,
    baseData,
    boxDisabled,
    headerTitle,
  } = props;
  return (
    <div>
      <Header title={headerTitle || t('批量下架')} />
      {
        dataLoading === 0 ? <LoadMore loading={dataLoading === 0} /> : <BaseData data={baseData} />
      }
      <Form
        style={{ marginTop: '10px' }}
      >
        <FocusInput
          placeholder={t('请扫描')}
          data-bind="containerCode"
          onPressEnter={(e) => {
            if (e.target.value) {
              store.scanBox(e.target.value);
            }
          }}
          disabled={boxDisabled === 0}
          className="box"
        >
          <label>{t('周转箱')}</label>
        </FocusInput>
      </Form>
      <Footer beforeBack={(back) => {
        store.init();
        back();
      }}
      />
    </div>
  );
};

DefaultPage.propTypes = {
  dataLoading: PropTypes.number,
  baseData: PropTypes.arrayOf(PropTypes.shape),
  boxDisabled: PropTypes.number,
  headerTitle: PropTypes.string,
};

export default DefaultPage;
