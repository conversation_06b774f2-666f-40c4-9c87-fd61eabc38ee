import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import DefaultPage from './default';
import NormalPage from './normal-page';
import DetailPage from './detail-page';
import store from './reducers';
// import { DragCircle } from '../../common';
import navStore from '../../nav/reducers';
import { classFocus } from '../../../lib/util';

class Container extends Component {
  componentDidMount() {
    store.getInfo();
  }

  render() {
    const {
      status,
      info,
      showDetail,
      detailList,
      dispatch,
    } = this.props;
    let children;
    switch (status) {
      case 'default':
        children = (<DefaultPage {...this.props} />);
        break;
      case 'normalPage':
        children = (<NormalPage {...this.props} />);
        break;
      default:
        break;
    }
    if (showDetail) {
      children = (
        <DetailPage
          data={detailList}
          dispatch={dispatch}
        />
      );
    }
    return (
      <div style={{ marginBottom: '56px' }}>
        {children}
        {/*<DragCircle*/}
        {/*  onClick={() => {*/}
        {/*    navStore.changeData({ data: { showUploadError: true } });*/}
        {/*    navStore.changeLimit({ data: { location: info && info.location ? info.location : '' } });*/}
        {/*    classFocus('location');*/}
        {/*  }}*/}
        {/*/>*/}
      </div>
    );
  }
}

Container.propTypes = {
  status: PropTypes.string,
  dispatch: PropTypes.func.isRequired,
  info: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.string,
  ]),
  showDetail: PropTypes.bool,
  detailList: PropTypes.arrayOf(PropTypes.array),
};

export default i18n(Container);
