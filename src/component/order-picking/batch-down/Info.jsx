import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import style from './style.css';

const Info = (props) => {
  const {
    data,
  } = props;
  const location = data.location || '';
  return (
    <div>
      <Form>
        <div className={style.firstItem} style={{ padding: '0 2px' }}>
          <span>{data.orderNo}</span>
          <div>
            {t('总件数')}: 
            <span
              className={style.weight}
            >
              {data.hadOperateNum}
            </span>
          </div>
        </div>
      </Form>
      <Form style={{
        marginTop: '10px',
      }}
      >
        <div className={style.mainInfo}>
          <div className={style.boxFloat}>
            <div className={style.floatLeft}>{t('库位号')}</div>
            <div className={style.floatRight}>
              <span style={{ fontSize: 16, fontWeight: 'normal' }}>
                <span>{location.substring(0, 6)}</span>
                <span style={{ fontSize: 20, fontWeight: 'bold' }}>{location.substring(6, 14)}</span>
                <span style={{ color: '#f00' }}>{location.substring(14)}</span>
                { data.sequence ? <span style={{ color: 'red', fontWeight: 'bold' }}>--{data.sequence}</span> : '' }
              </span>
            </div>
          </div>
        </div>
      </Form>
    </div>
  );
};

Info.propTypes = {
  data: PropTypes.shape(),
};

export default Info;
