import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { t } from '@shein-bbl/react';
import { select } from 'redux-saga/effects';
import { modal, message } from '../../common';
import {
  initApi,
  scanContainerApi,
  scanLocationApi,
  sacnGoodsApi,
  closeContainerApi,
  commitApi,
  detailApi,
  commitBatchApi,
} from './server';
import { classFocus, getHeaderTitle, compareSku } from '../../../lib/util';

const listRows = [
  [
    {
      title: 'SKC',
      render: 'skc',
    },
  ],
  [
    {
      title: t('尺码'),
      render: 'size',
      width: 33.3,
    },
    {
      title: t('可移'),
      render: 'mobileNum',
      width: 33.3,
    },
    {
      title: t('不可移'),
      render: 'nonMobileNum',
      width: 33.3,
    },
  ],
];

function getTopList(list, infoList) {
  if (!infoList.length) {
    return [];
  }
  let res = [];

  const firstItem = infoList[0];

  const isExist = list.findIndex((i) => compareSku(i, firstItem)) > -1;

  if (isExist) {
    list.forEach((item) => {
      if (compareSku(item, firstItem)) {
        res.unshift(item);
      } else {
        res.push(item);
      }
    });
  } else {
    res = [...infoList, ...list].slice(0, 10);
  }
  return res;
}

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: t('冻结移位'),
  isScanContainer: true,
  containerCode: '', // 周转箱号
  shiftOrderCode: '', // 位移单号
  locationCode: '', // 库位号
  type: '', // 货位类型 1大货 2散货
  barCode: '', // 商品条码
  allGoodsNum: '', // 已下架总数
  containerCodeShow: '', // 明细页面展示的周转箱号
  containerCodeShow2: '', // 扫描周转箱页面展示的周转箱号
  detailList: [], // 明细列表
  num: '', // 移位数量
  locationsInfo: [], // 库位商品信息
  downNum: '', // 用户当天已下架数量
  rows: [...listRows],
  showDetail: false,
  currentPosition: 'containerCode', // 光标当前位置
  barCodeRes: [], // 扫描商品条码返回的信息
};

export default {
  defaultState,
  $init: () => defaultState,
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  reset(draft) {
    assign(draft, defaultState);
  },
  // 初始化
  * initData(action, ctx) {
    markStatus('dataLoading');
    const res = yield initApi(action.params);
    if (res.code === '0') {
      const { containerCode, downNum } = res.info;
      yield ctx.changeData({
        data: {
          containerCodeShow2: containerCode,
          downNum,
        },
      });
      classFocus('containerCode');
    } else {
      modal.error({ content: res.msg, className: 'containerCode' });
    }
  },
  // 扫周转箱
  * scanContainer(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanContainerApi(action.params);
    if (res.code === '0') {
      const { shiftOrder } = res.info;
      yield ctx.changeData({
        data: {
          shiftOrderCode: shiftOrder,
          isScanContainer: false,
          containerCodeShow: action.params.containerCode,
          currentPosition: 'locationCode',
        },
      });
      classFocus('locationCode');
    } else {
      yield ctx.changeData({
        data: {
          containerCode: '',
        },
      });
      modal.error({ content: res.msg, className: 'containerCode' });
    }
  },
  // 扫库位
  * scanLocation(action, ctx) {
    markStatus('dataLoading');
    const { containerCode } = yield '';
    const res = yield scanLocationApi(action.params);
    if (res.code === '0') {
      const { type, locationsInfo, isBatch } = res.info;
      // 如果是批量下架
      if (isBatch) {
        yield ctx.changeData({
          data: {
            type,
            locationsInfo,
          },
        });
        const status = yield new Promise((r) => modal.confirm({
          content: t('该库位可冻结批量下架，点击确定后请将库位上的商品移至周转箱{}', containerCode),
          onOk: () => r(1),
          onCancel: () => r(0),
        }));
        if (status) {
          // 调后端接口
          yield this.confirmBatch();
        } else {
          yield ctx.changeData({
            data: {
              type: '',
              locationsInfo: [],
              locationCode: '',
            },
          });
          classFocus('locationCode');
        }
        return;
      }
      yield ctx.changeData({
        data: {
          type,
          locationsInfo,
          currentPosition: 'barCode',
        },
      });
      classFocus('barCode');
    } else {
      yield ctx.changeData({
        data: {
          locationCode: '',
        },
      });
      modal.error({ content: res.msg, className: 'locationCode' });
    }
  },
  // 扫商品条码
  * sacnGoods(action, ctx) {
    markStatus('dataLoading');
    const res = yield sacnGoodsApi(action.params);
    if (res.code === '0') {
      const {
        type, locationsInfo, barCode, containerCode, locationCode, shiftOrderCode,
      } = yield select((state) => state['order-picking/freeze']);
      const className = type === 1 ? 'num' : 'barCode';
      yield ctx.changeData({
        data: {
          barCodeRes: res.info.locationsInfo,
          currentPosition: className,
          locationsInfo: getTopList(locationsInfo, res.info.locationsInfo),
        },
      });
      classFocus(className);
      // 散货逻辑
      if (type === 2) {
        yield this.commit({
          params: {
            barCode,
            containerCode,
            location: locationCode,
            type,
            shiftOrderCode,
          },
        });
      }
    } else if (res.code === '503002') {
      const { shiftOrderCode, containerCode } = yield select((state) => state['order-picking/freeze']);
      const status = yield new Promise((r) => modal.confirm({
        content: t('当前扫描商品，在周转箱上已经存在，不允许混放，请关箱！'),
        onOk: () => r(1),
        onCancel: () => r(2),
        okText: t('关箱'),
        cancelText: t('返回'),
      }));
      if (status === 1) {
        yield ctx.closeContainer({
          params: {
            containerCode,
            shiftOrderCode,
          },
        });
      }
      // 1. 清空库位,条码
      // 2. 光标定位到库位
      if (status === 2) {
        yield ctx.changeData({
          data: {
            locationCode: '',
            barCode: '',
          },
        });
        classFocus('locationCode');
      }
    } else {
      yield ctx.changeData({
        data: {
          barCode: '',
        },
      });
      modal.error({ content: res.msg, className: 'barCode' });
    }
  },
  // t('关箱')
  * closeContainer(action, ctx) {
    markStatus('dataLoading');
    const res = yield closeContainerApi(action.params);
    if (res.code === '0') {
      message.success(t('关箱成功'));
      yield ctx.reset();
      yield ctx.initData();
    } else {
      const { currentPosition } = yield select((state) => state['order-picking/freeze']);
      modal.error({ content: res.msg, className: currentPosition });
    }
  },
  // 明细
  * getDetailList(action, ctx) {
    markStatus('dataLoading');
    const res = yield detailApi(action.params);
    if (res.code === '0') {
      const {
        containerCode, allGoodsNum, shiftDownDetailGoods, shiftOrderCode,
      } = res.info;
      yield ctx.changeData({
        data: {
          containerCodeShow: containerCode,
          allGoodsNum,
          detailList: shiftDownDetailGoods || [],
          shiftOrderCode,
          showDetail: true,
        },
      });
    } else {
      const { currentPosition } = yield select((state) => state['order-picking/freeze']);
      modal.error({ content: res.msg, className: currentPosition });
    }
  },
  // 提交数据
  * commit(action, ctx) {
    markStatus('dataLoading');
    const { code, info, msg } = yield commitApi(action.params);
    const { type, locationsInfo, barCodeRes } = yield select((state) => state['order-picking/freeze']);
    if (code === '0') {
      if (info) {
        yield new Promise((r) => modal.info({
          content: t('下架成功，请下架空周转箱：{}', info),
          onOk: () => r(1),
        }));
      }
      const currScanGoods = barCodeRes?.[0] || {};
      // 将商品可移数量减去扫描的数量
      const newLocationsInfo = locationsInfo.map((item) => {
        if (currScanGoods.skuCode === item.skuCode) {
          return {
            ...item,
            mobileNum: Math.max(item.mobileNum - (action.params?.num || 1), 0),
          };
        }
        return { ...item };
      });
      yield ctx.changeData({
        data: {
          num: '',
          barCode: '',
          locationsInfo: [...newLocationsInfo],
        },
      });
      // 当前下架库位没有可移动的库存了
      if (newLocationsInfo.every((item) => !item.mobileNum)) {
        yield ctx.changeData({
          data: {
            locationCode: '',
            currentPosition: 'locationCode',
          },
        });
        classFocus('locationCode');
      } else {
        yield ctx.changeData({
          data: {
            currentPosition: 'barCode',
          },
        });
        classFocus('barCode');
      }
    } else {
      const className = type === 1 ? 'num' : 'barCode';
      if (type === 1) {
        yield ctx.changeData({
          data: {
            num: '',
          },
        });
      } else {
        yield ctx.changeData({
          data: {
            barCode: '',
          },
        });
      }
      modal.error({ content: msg, className });
    }
  },

  // 批量下架确认提交
  * confirmBatch() {
    markStatus('dataLoading');
    const {
      containerCode, locationCode, type, shiftOrderCode,
    } = yield '';
    const res = yield commitBatchApi({
      containerCode,
      location: locationCode,
      type,
      shiftOrderCode,
    });
    if (res.code === '0') {
      yield this.changeData({
        data: {
          type: '',
          locationsInfo: [],
          locationCode: '',
        },
      });
      classFocus('locationCode');
      message.success(t('该库位已冻结批量下架成功'));
    } else {
      yield this.changeData({
        data: {
          type: '',
          locationsInfo: [],
          locationCode: '',
        },
      });
      modal.error({ content: res.msg, className: 'locationCode' });
    }
  },
};
