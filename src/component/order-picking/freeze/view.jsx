import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import {
  Header,
  pages,
} from '../../common';

import Content from './jsx/content';
import Detail from './jsx/detail';

const { View } = pages;

class Container extends Component {
  componentDidMount() {
    store.initData();
  }

  render() {
    const {
      headerTitle,
      isScanContainer,
      containerCode,
      shiftOrderCode,
      showDetail,
    } = this.props;
    return (
      <View>
        <Header title={headerTitle} style={{ width: '100%' }}>
          {
            !isScanContainer && (
              <span
                style={{ padding: 10 }}
                onClick={() => {
                  store.getDetailList({
                    params: {
                      containerCode,
                      shiftOrderCode,
                    },
                  });
                }}
              >
                {t('明细')}
              </span>
            )
          }
        </Header>

        {
          !showDetail ? (
            <Content {...this.props} />
          ) : (
            <Detail {...this.props} />
          )
        }
      </View>
    );
  }
}

Container.propTypes = {
  isScanContainer: PropTypes.bool,
  headerTitle: PropTypes.string,
  containerCode: PropTypes.string,
  shiftOrderCode: PropTypes.string,
  showDetail: PropTypes.bool,
};

export default i18n(Container);
