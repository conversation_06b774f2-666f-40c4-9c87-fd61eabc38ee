import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import {
  Footer,
  List,
  pages,
} from '../../../common';
import { classFocus } from '../../../../lib/util';

const { View } = pages;

const rows = [
  [
    {
      title: 'SKC',
      render: 'skc',
    },
  ],
  [
    {
      title: t('尺码'),
      render: 'size',
      width: 50,
    },
    {
      title: t('下架数'),
      render: 'num',
      width: 50,
    },
  ],
];

class Content extends Component {
  render() {
    const {
      dispatch,
      detailList,
      shiftOrderCode,
      containerCodeShow,
      allGoodsNum,
      currentPosition,
    } = this.props;
    return (
      <View>
        <div style={{ padding: '10px 0 0 15px' }}>
          <div>
            {t('移位单号')}：<span style={{ color: 'red' }}>{shiftOrderCode}</span>
          </div>
          <div>
            {t('当前周转箱')}：<span>{containerCodeShow}</span>
          </div>
          <div>
            {t('已下架数量')}：<span>{allGoodsNum}</span>
          </div>
        </div>
        {
          detailList.length > 0 && (
            <List
              rows={rows}
              data={detailList}
            />
          )
        }
        <Footer
          dispatch={dispatch}
          beforeBack={() => {
            store.changeData({
              data: {
                showDetail: false,
              },
            });
            classFocus(currentPosition);
          }}
        />
      </View>
    );
  }
}

Content.propTypes = {
  dispatch: PropTypes.func,
  shiftOrderCode: PropTypes.string,
  containerCodeShow: PropTypes.string,
  currentPosition: PropTypes.string,
  allGoodsNum: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  detailList: PropTypes.arrayOf(PropTypes.object),
};

export default Content;
