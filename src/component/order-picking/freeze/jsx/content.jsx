import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form, CellsTitle, Button } from 'react-weui/build/packages';
import store from '../reducers';
import {
  Footer,
  FocusInput,
  FooterBtn,
  modal,
  RowInfo,
  List,
} from '../../../common';
import { classFocus } from '../../../../lib/util';

const reg = /^[1-9][0-9]*$/;

class Content extends Component {
  render() {
    const {
      dataLoading,
      dispatch,
      isScanContainer,
      containerCode,
      shiftOrderCode,
      locationCode,
      barCode,
      type,
      num,
      containerCodeShow,
      containerCodeShow2,
      downNum,
      rows,
      locationsInfo,
      barCodeRes,
    } = this.props;
    return (
      <>
        {
          isScanContainer ?
            (
              <div style={{ backgroundColor: '#fff' }}>
                <RowInfo
                  label={t('已下架数量')}
                  type="info"
                  content={(
                    <span style={{ fontWeight: 'bold', color: 'FF9636' }}>{downNum}</span>
                  )}
                />
                <Form>
                  <FocusInput
                    value={containerCode}
                    disabled={dataLoading === 0}
                    className="containerCode"
                    autoFocus
                    onChange={(e) => {
                      store.changeData({
                        data: {
                          containerCode: e.target.value.trim(),
                        },
                      });
                    }}
                    onPressEnter={() => {
                      if (containerCode) {
                        store.scanContainer({
                          params: {
                            containerCode,
                          },
                        });
                      }
                    }}
                  >
                    <label>{t('周转箱')}</label>
                  </FocusInput>
                </Form>

                {
                  containerCodeShow2 && (
                    <div style={{ paddingLeft: 15 }}>
                      <span>{t('你有下架中的周转箱')}：</span>
                      <span style={{ color: 'red' }}>{containerCodeShow2}</span>
                    </div>
                  )
                }
              </div>
            ) : (
              <div>
                <Form>
                  <FocusInput
                    value={locationCode}
                    className="locationCode"
                    disabled={dataLoading === 0}
                    // autoFocus
                    onChange={(e) => {
                      store.changeData({
                        data: {
                          locationCode: e.target.value.trim(),
                        },
                      });
                    }}
                    onPressEnter={() => {
                      if (locationCode) {
                        store.scanLocation({
                          params: {
                            locationCode,
                            shiftOrderCode,
                          },
                        });
                      }
                    }}
                    footer={(
                      <Button
                        type="primary"
                        style={{ marginBottom: '5px' }}
                        size="small"
                        onClick={() => {
                          store.changeData({
                            data: {
                              locationCode: '',
                              barCode: '',
                              num: '',
                              locationsInfo: [],
                              barCodeRes: [],
                              currentPosition: 'locationCode',
                            },
                          });
                          classFocus('locationCode');
                        }}
                      >
                        {t('清空')}
                      </Button>
                    )}
                  >
                    <label>{t('库位')}</label>
                  </FocusInput>
                </Form>

                <Form>
                  <FocusInput
                    value={barCode}
                    className="barCode"
                    disabled={dataLoading === 0}
                    onChange={(e) => {
                      store.changeData({
                        data: {
                          barCode: e.target.value,
                        },
                      });
                    }}
                    onPressEnter={() => {
                      if (barCode) {
                        store.sacnGoods({
                          params: {
                            locationCode,
                            type,
                            barCode,
                            containerCode,
                          },
                        });
                      }
                    }}
                  >
                    <label>{t('商品条码')}</label>
                  </FocusInput>
                </Form>

                {
                  type === 1 && (
                  <Form>
                    <FocusInput
                      value={num}
                      className="num"
                      disabled={dataLoading === 0}
                      onChange={(e) => {
                        store.changeData({
                          data: {
                            num: e.target.value.trim(),
                          },
                        });
                      }}
                      onPressEnter={() => {
                        if (num) {
                          let errTip = '';
                          if (!reg.test(num)) {
                            errTip = t('请输入正整数');
                          }
                          if (locationsInfo[0].mobileNum - num < 0) {
                            errTip = t('数量超过可移动数量');
                          }
                          if (errTip) {
                            store.changeData({
                              data: {
                                num: '',
                              },
                            });
                            modal.error({
                              content: errTip,
                              className: 'num',
                            });
                            return;
                          }
                          store.commit({
                            params: {
                              barCode,
                              containerCode,
                              location: locationCode,
                              type,
                              shiftOrderCode,
                              num,
                            },
                          });
                        }
                      }}
                    >
                      <label>{t('数量')}</label>
                    </FocusInput>
                  </Form>
                  )
                }
                <div style={{ padding: '10px 0 0 15px' }}>
                  <div>
                    {t('当前周转箱')}：<span style={{ color: 'red' }}>{containerCodeShow}</span>
                  </div>
                  <div>
                    {t('移位单号')}：<span>{shiftOrderCode}</span>
                  </div>
                </div>
              </div>
            )
        }
        {
          rows.length > 0 && (
            <List
              rows={rows}
              data={locationsInfo}
              rowStyleOrClass={(item, index) => {
                if (barCodeRes.length && index === 0) {
                  return { backgroundColor: '#ffe5e5' };
                }
                return {};
              }}
            />
          )
        }
        <Footer
          dispatch={dispatch}
          beforeBack={(back) => {
            if (isScanContainer) {
              back();
              store.reset();
            } else {
              modal.confirm({
                content: t('周转箱正在下架，是否离开当前界面?'),
                onOk: () => {
                  store.reset();
                  store.initData();
                },
                okText: t('确认离开'),
              });
            }
          }}
        >
          {
            !isScanContainer && (
              <FooterBtn
                disabled={dataLoading === 0}
                onClick={() => {
                  modal.confirm({
                    content: t('关箱后周转箱将不允许冻结移位,是否关箱?'),
                    onOk: () => {
                      store.closeContainer({
                        params: {
                          containerCode,
                          shiftOrderCode,
                        },
                      });
                    },
                    okText: t('关箱'),
                  });
                }}
              >
                {t('关箱')}
              </FooterBtn>
            )
          }
        </Footer>
      </>
    );
  }
}

Content.propTypes = {
  dispatch: PropTypes.func,
  dataLoading: PropTypes.number,
  isScanContainer: PropTypes.bool,
  type: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  num: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  downNum: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  containerCode: PropTypes.string,
  barCode: PropTypes.string,
  shiftOrderCode: PropTypes.string,
  locationCode: PropTypes.string,
  containerCodeShow: PropTypes.string,
  containerCodeShow2: PropTypes.string,
  rows: PropTypes.arrayOf(PropTypes.shape()),
  locationsInfo: PropTypes.arrayOf(PropTypes.shape()),
  barCodeRes: PropTypes.arrayOf(PropTypes.shape()),
};

export default Content;
