import { sendPostRequest } from '../../../lib/public-request';

// 初始化
export const initApi = (param) => sendPostRequest({
  url: '/freezing_down/index',
  param,
}, process.env.WWS_URI);

// 扫描周转箱
export const scanContainerApi = (param) => sendPostRequest({
  url: '/freezing_down/scan_container',
  param,
}, process.env.WWS_URI);

// 扫描库位
export const scanLocationApi = (param) => sendPostRequest({
  url: '/freezing_down/scan_location',
  param,
}, process.env.WWS_URI);

// 扫商品条码
export const sacnGoodsApi = (param) => sendPostRequest({
  url: '/freezing_down/scan_goods',
  param,
}, process.env.WWS_URI);

// 关箱
export const closeContainerApi = (param) => sendPostRequest({
  url: '/freezing_down/close_container',
  param,
}, process.env.WWS_URI);

// 下架数量提交
export const commitApi = (param) => sendPostRequest({
  url: '/freezing_down/commit',
  param,
}, process.env.WWS_URI);

// 下架移位单明细
export const detailApi = (param) => sendPostRequest({
  url: '/freezing_down/detail/list',
  param,
}, process.env.WWS_URI);

// 批量下架提交
export const commitBatchApi = (param) => sendPostRequest({
  url: '/freezing_batch_down/commit',
  param,
}, process.env.WWS_URI);
