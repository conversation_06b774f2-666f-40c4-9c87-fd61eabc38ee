import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form, Button } from 'react-weui/build/packages';
import { push } from 'react-router-redux';
import FocusInput from '../../../common/focus-input';
import { modal, PopRadio } from '../../../common';
import LocationInfo from './table';
import Footer from '../../../common/footer';
import FooterBtn from '../../../common/footer-btn';
import store, { locationTypeMap } from '../reducers';
import style from '../../../style.css';
import innerStyle from '../style.css';

class PickingPage extends Component {
  render() {
    const {
      dispatch,
      isLocationDisabled,
      isBarCodeDisabled,
      isNumDisabled,
      isSequenceDisabled,
      containerCode,
      shiftOrderCode,
      type,
      locationCode,
      barCode,
      num,
      sequence,
      locationsInfo,
      productionDateShow,
      validityList,
      selectedProductionDate,
    } = this.props;

    const baseCommitData = {
      containerCode,
      shiftOrderCode,
      location: locationCode,
      type,
      barCode,
    };

    const height = window.innerHeight - 56 - 44;

    const validityListRender = validityList.map((x, index) => ({
      ...x,
      index: index + 1,
      productionDateLabel: x.productionDate.split(' ')[0],
    }));

    const validityListRenderSelectIndex = validityListRender
      ?.find((x) => x.productionDate === selectedProductionDate)
      ?.index;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <Form style={{ marginBottom: 10 }}>
          <FocusInput
            placeholder={t('请扫描')}
            disabled={isLocationDisabled}
            className="location"
            data-bind="locationCode"
            onPressEnter={(e) => {
              const { value } = e.target;
              if (!value) {
                return;
              }
              store.changeData({
                data: {
                  isLocationDisabled: true,
                },
              });

              store.scanLocation({
                params: {
                  locationCode,
                  shiftOrderCode,
                },
              });
            }}
            footer={(
              <Button
                size="small"
                onClick={() => {
                  store.clearLocation();
                }}
              >
                {t('清空')}
              </Button>
            )}
          >
            <label>{t('下架库位')}</label>
          </FocusInput>
          <FocusInput
            placeholder={t('请扫描')}
            disabled={isBarCodeDisabled}
            className="barCode"
            data-bind="barCode"
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.changeData({
                data: {
                  isBarCodeDisabled: true,
                },
              });

              store.scanGoods({
                params: {
                  barCode,
                  locationCode,
                  type,
                  containerCode,
                },
              });
            }}
          >
            <label>{t('商品条码')}</label>
          </FocusInput>
          {
            // 生产日期
            validityList.length > 0 && (
              <PopRadio
                label={t('生产日期')}
                className="productionDate"
                show={productionDateShow}
                selectList={validityListRender}
                selectValue={validityListRenderSelectIndex}
                valueName="index"
                labelName="productionDateLabel"
                disabled={validityList.length === 1}
                onClick={() => {
                  store.changeData({ data: { productionDateShow: true } });
                }}
                onCancel={() => {
                  store.changeData({ data: { productionDateShow: false } });
                }}
                onOk={(val) => {
                  const item = validityListRender.find((x) => x.index === val);

                  store.changeData({
                    data: {
                      productionDateShow: false,
                      selectedProductionDate: item.productionDate,
                    },
                  });
                  store.selectDate();
                }}
                placeholder={t('请选择')}
              />
            )
          }
          {/* 货位类型：1大货 |2散货 */}
          {/* 大货 散货都走录入数量 */}
          <FocusInput
            placeholder={t('请输入')}
            disabled={isNumDisabled || !locationsInfo.length}
            className="num"
            data-bind="num"
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.changeData({
                data: {
                  isNumDisabled: true,
                },
              });
              const numVal = num * 1;
              // 输入数值范围是1-2000【跟戴雄辉确认的】
              // eslint-disable-next-line no-restricted-globals
              if (isNaN(numVal) || numVal < 1 || numVal > 2000 || numVal % 1 !== 0) {
                modal.error({
                  content: t('请输入1-2000的整数'),
                  className: 'num',
                  onOk: () => store.changeData({ data: { num: '', isNumDisabled: false } }),
                });
                return;
              }

              let locationsInfoIndex = 0;

              if (validityList.length > 1) {
                // eslint-disable-next-line max-len
                locationsInfoIndex = locationsInfo.findIndex((x) => x.productionDate === selectedProductionDate);
              } else {
                const findMobileIndex = locationsInfo.findIndex((x) => x.mobileNum > 0);

                if (findMobileIndex > 0) {
                  locationsInfoIndex = findMobileIndex;
                }
              }

              if (locationsInfo[locationsInfoIndex].mobileNum - numVal < 0) {
                modal.error({
                  content: t('数量超过可移动数量'),
                  className: 'num',
                  onOk: () => store.changeData({ data: { num: '', isNumDisabled: false } }),
                });
                return;
              }

              store.commitDatas({
                params: {
                  ...baseCommitData,
                  num: numVal,
                },
              });
            }}
          >
            <label>{t('数量')}</label>
          </FocusInput>
          {/* {type === locationTypeMap.get(t('散货')) && ( */}
          {/*  <FocusInput */}
          {/*    placeholder={t('请输入')} */}
          {/*    disabled={isSequenceDisabled || !locationsInfo.length} */}
          {/*    className="sequence" */}
          {/*    data-bind="sequence" */}
          {/*    onPressEnter={(e) => { */}
          {/*      if (!e.target.value) { */}
          {/*        return; */}
          {/*      } */}

          {/*      store.changeData({ */}
          {/*        data: { */}
          {/*          isSequenceDisabled: true, */}
          {/*        }, */}
          {/*      }); */}

          {/*      store.commitDatas({ */}
          {/*        params: { */}
          {/*          ...baseCommitData, */}
          {/*          sequence, */}
          {/*        }, */}
          {/*      }); */}
          {/*    }} */}
          {/*  > */}
          {/*    <label>{t('序列号')}</label> */}
          {/*  </FocusInput> */}
          {/* )} */}
        </Form>

        <div className={innerStyle.box_mes}>
          <div>
            <span className={innerStyle.title}>{t('当前周转箱')}</span>&nbsp;:&nbsp;
            <span className={style.c_red}>{containerCode}</span>
          </div>
          <div>
            <span className={innerStyle.title}>{t('移位单号')}</span>&nbsp;:&nbsp;
            <span>{shiftOrderCode}</span>
          </div>
        </div>

        {/* <div style={highLight}>{recommendLocation}</div> */}

        {/* eslint-disable react/jsx-props-no-spreading */}
        {!!locationsInfo.length && <LocationInfo {...this.props} />}

        <Footer
          beforeBack={() => {
            modal.confirm({
              title: t('周转箱正在下架，是否离开当前页面？'),
              onOk: () => {
                store.init();
                dispatch(push('/order-picking/move-warehouse-shift-down'));
              },
            });
          }}
        >
          <FooterBtn
            onClick={() => {
              modal.confirm({
                title: t('关箱后，该周转箱将不允许移位下架，是否关箱？'),
                onOk: () => {
                  store.closeContainer({
                    params: {
                      containerCode,
                      shiftOrderCode,
                    },
                  });
                },
              });
            }}
          >
            {t('关箱')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

PickingPage.propTypes = {
  dispatch: PropTypes.func,
  isLocationDisabled: PropTypes.bool,
  isBarCodeDisabled: PropTypes.bool,
  isNumDisabled: PropTypes.bool,
  isSequenceDisabled: PropTypes.bool,
  containerCode: PropTypes.string,
  shiftOrderCode: PropTypes.string,
  type: PropTypes.string,
  locationCode: PropTypes.string,
  barCode: PropTypes.string,
  num: PropTypes.string,
  sequence: PropTypes.string,
  locationsInfo: PropTypes.arrayOf(PropTypes.shape()),
  recommendLocation: PropTypes.string,
  productionDateShow: PropTypes.bool,
  validityList: PropTypes.arrayOf(PropTypes.shape()),
  selectedProductionDate: PropTypes.string,
};

export default PickingPage;
