import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages/components/form';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import Footer from '../../../common/footer';
import RowInfo from '../../../common/row-info';
import { modal } from '../../../common';
import innerStyle from '../style.css';

class ScanContainer extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      isContainerCodeDisabled,
      downNum,
      initContainerCode,
      containerCode,
    } = this.props;

    return (
      <div>

        <Form>
          <RowInfo
            extraStyle={{
              borderBottom: 'none',
            }}
            label={t('已下架数量')}
            content={downNum}
          />
        </Form>

        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            disabled={isContainerCodeDisabled}
            autoFocus
            className="containerCode"
            data-bind="containerCode"
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }

              if (initContainerCode && initContainerCode !== containerCode) {
                store.changeData({
                  containerCode: '',
                });

                modal.error({
                  content: t('你有未完结的周转箱,不可使用其他周转箱!'),
                });

                return;
              }

              store.changeData({
                data: {
                  isContainerCodeDisabled: true,
                },
              });

              store.scanContainer({
                params: {
                  containerCode,
                },
              });
            }}
          >
            <label>{t('周转箱')}</label>
          </FocusInput>

        </Form>

        {initContainerCode && (
          <div style={{ marginTop: 10 }}>
            <div className={innerStyle.box_mes}>
              {t('你有下架中的周转箱')}:
              <span
                style={{
                  color: 'red',
                  fontsize: 15,
                }}
              >
                {initContainerCode}
              </span>
            </div>
          </div>
        )}

        <Footer
          beforeBack={(back) => {
            store.init();
            back();
          }}
        />
      </div>
    );
  }
}

ScanContainer.propTypes = {
  isContainerCodeDisabled: PropTypes.bool,
  downNum: PropTypes.number,
  initContainerCode: PropTypes.string,
  containerCode: PropTypes.string,
};

export default ScanContainer;
