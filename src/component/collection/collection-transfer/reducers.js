import assign from 'object-assign';
import { modal, message } from 'common';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { t } from '@shein-bbl/react';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import {
  scanCollection, scanContainer as scanContainerApi, closePallet, confirmReceive,
  queryVirtualPalletEnable,
} from './server';

const defaultState = {
  headerTitle: '',
  resInfo: {
    palletStatus: '',
    containerNum: '', // 周转箱数量
    carNo: '', // 转运车辆
    transferAdvance: { // 转运进度
      finishNum: '0', // 完成
      installedNum: '0', // 转运中
      loadNum: '0', // 已装托
      waitNum: '0', // 待装托
      progressName: '', //
    },
    nationalLine: '', // 国家线
    pickContainerCode: '', // 箱子信息
    palletCode: '', // 托盘号
    warehouseName: '', // 仓库
    wellenCode: '', // 波次号
    subWarehouseName: '', // 子仓
    statusName: '', //
  },
  palletCode: '',
  containerCode: '',
  palletCodeValid: false,
  containerCodeValid: false,
  closeLoading: 1,
  confirmLoading: 1,
  closeModalVisible: false, // 关托转运弹窗
  licenseNumber: '', // 车号
  steps: [],
  isVirtualPalletEnable: false,
};

export default {
  defaultState,
  // 初始当前页面数据
  resetPageStore(draft) {
    assign(draft, defaultState, {
      headerTitle: draft.headerTitle,
      isVirtualPalletEnable: draft.isVirtualPalletEnable,
      palletCodeValid: draft.palletCodeValid,
    });
  },
  $init: () => defaultState,
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    // 新增一个菜单：集货装托（二步集货），点击进入是带上二步集货的参数，不请求虚拟托盘
    if (!action?.isSecondStep) {
      const res = yield queryVirtualPalletEnable({
        warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
      }); // 查询是虚拟托盘开关是否开启
      if (res.info && res.info.isVirtualPalletEnable) {
        yield ctx.changeData({
          data: {
            palletCodeValid: true,
            isVirtualPalletEnable: res.info.isVirtualPalletEnable,
          },
        });
        classFocus('collectionTransferContainerCode');
      }
    }
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  $scanPallet(draft) {
    assign(draft, {
      palletCodeValid: true,
    });
  },
  $scanContainer(draft) {
    assign(draft, {
      containerCodeValid: true,
    });
  },
  // 清空托盘
  * cleanPallet(__, ctx) {
    yield ctx.changeData({
      data: {
        palletCode: '',
        palletCodeValid: false,
      },
    });
  },
  // 清空周转箱
  * cleanContainer(__, ctx) {
    yield ctx.changeData({
      data: {
        containerCode: '',
        containerCodeValid: false,
      },
    });
  },
  // 扫描托盘
  * scanPallet(action, ctx) {
    yield ctx.changeData({ data: { PalletValid: true } });
    // 是否需要校验合并波次拣货周转箱,初次扫描默认为true
    const res = yield scanCollection({ isNeedCheckMergeWellen: true, ...action.param });
    if (res.code === '0') {
      if (+res.info.resultType !== 6) {
        // 接口响应成功，数据回填，清空周转箱
        yield ctx.changeData({
          data: {
            resInfo: res.info,
          },
        });
      }
      // 如果resultType为0 则表示托盘查询成功。
      if (+res.info.resultType === 0) {
        yield ctx.cleanContainer();
        classFocus('collectionTransferContainerCode');
        // 如果resultType为3  则表示装托成功
      } else if (+res.info.resultType === 3) {
        yield ctx.cleanContainer();
        message.success(t('周转箱集货转运绑托成功'));
        classFocus('collectionTransferContainerCode');
      } else if (+res.info.resultType === 4) {
        // 如果resultType为4  则弹出：当前已绑箱XXX个，是否关托转运？ 关托
        const status = yield new Promise((r) => modal.confirm({
          content: res.info.resultMsg,
          onOk: () => r(true),
          onCancel: () => r(false),
        }));
        if (status) {
          yield ctx.postClosePallet({
            param: {
              palletCode: action.param.palletCode,
            },
          });
        } else {
          yield ctx.cleanContainer();
          message.success(t('周转箱集货转运绑托成功'));
          classFocus('collectionTransferContainerCode');
        }
      } else if (+res.info.resultType === 5) {
        // 如果resultType为5 弹出提示信息
        const status = yield new Promise((r) => modal.info({
          content: res.info.resultMsg,
          onOk: () => r('ok'),
        }));
        if (status === 'ok') {
          yield ctx.cleanContainer();
          // 聚焦到周转箱
          classFocus('collectionTransferContainerCode');
        }
      } else if (+res.info.resultType === 6) {
        // 该周转箱是合并波次类型
        const status = yield new Promise((r) => modal.confirm({
          content: res.info.resultMsg,
          onOk: () => r(true),
          onCancel: () => r(false),
        }));
        if (status) {
          yield ctx.scanPallet({
            param: {
              ...action.param,
              isNeedCheckMergeWellen: false,
            },
          });
        } else {
          yield ctx.cleanContainer();
          classFocus('collectionTransferContainerCode');
        }
      } else if (+res.info.resultType === 7) {
        // 已装托的周转箱和拣货周转箱非同一类型
        const status = yield new Promise((r) => modal.info({
          content: res.info.resultMsg,
          onOk: () => r('ok'),
        }));
        if (status === 'ok') {
          yield ctx.changeData({
            data: {
              resInfo: defaultState.resInfo,
            },
          });
          yield ctx.cleanContainer();
          // 聚焦到周转箱
          classFocus('collectionTransferContainerCode');
        }
      }
    } else if (res.code === '500383') {
      const status = yield new Promise((r) => modal.info({
        content: res.msg,
        onOk: () => r('ok'),
      }));
      if (status === 'ok') {
        yield ctx.cleanContainer();
        // 聚焦到托盘
        classFocus('collectionTransferContainerCode');
      }
    } else {
      //  接口报错的话，清空托盘号，聚焦到托盘号。弹框报错信息
      yield new Promise((r) => (
        modal.error({
          audioErr: true,
          content: res.msg,
          onOk: () => r(0),
          onCancel: () => r(0),
        })
      ));
      if (action.param.containerCode) {
        // 周转箱存在则只去清空周转箱
        yield ctx.cleanContainer();
        classFocus('collectionTransferContainerCode');
      } else {
        // 清空所有数据
        yield ctx.changeData({
          data: assign({}, defaultState),
        });
        classFocus('collectionTransferPalletCode');
      }
    }
  },
  // 扫描周转箱
  * scanContainer(action, ctx) {
    const res = yield scanContainerApi({ isNeedCheckMergeWellen: true, ...action.param });
    if (res.code === '0') {
      // 接口成功数据回填，清空周转箱
      if (+res.info.resultType !== 6) {
        yield ctx.changeData({
          data: {
            resInfo: { ...res.info, transferAdvance: res.info.transferAdvance || {} },
          },
        });
      }
      if (action.param.isVirtualPalletEnable) {
        // 虚拟托盘开关打开-清空周转箱，保留托盘信息，焦点置于扫描周转箱
        yield ctx.cleanContainer();
        // 避免后续代码弹窗modalBlurInput不生效
        if (![5, 6, 7].includes(+res.info.resultType)) {
          classFocus('collectionTransferContainerCode');
        }
      } else {
        // 避免后续代码弹窗modalBlurInput不生效
        if (![6, 7].includes(+res.info.resultType)) {
          // 聚焦到托盘
          classFocus('collectionTransferPalletCode');
        }
      }
      if (+res.info.resultType === 3) {
        // 如果resultType为3  则弹出提示信息 该周转箱所在托盘已装车
        message.success(res.info.resultMsg, 2000);
        yield ctx.cleanContainer();
        classFocus('collectionTransferContainerCode');
      } else if (+res.info.resultType === 4) {
        // 如果resultType为4  则弹出提示信息
        const status = yield new Promise((r) => modal.confirm({
          content: res.info.resultMsg,
          onOk: () => r('ok'),
          onCancel: () => r('onCancel'),
        }));
        if (status === 'ok') {
          yield ctx.postClosePallet({
            param: {
              palletCode: res.info.palletCode,
              isVirtualPalletEnable: action.param.isVirtualPalletEnable,
            },
          });
        }
        if (status === 'onCancel') {
          yield ctx.cleanContainer();
          classFocus('collectionTransferContainerCode');
        }
      } else if (+res.info.resultType === 5) {
        // 如果resultType为5 弹出提示信息
        const status = yield new Promise((r) => modal.info({
          content: res.info.resultMsg,
          onOk: () => r('ok'),
        }));
        if (status === 'ok') {
          yield ctx.cleanContainer();
          // 聚焦到托盘
          classFocus('collectionTransferContainerCode');
        }
      } else if (+res.info.resultType === 6) {
        // 该周转箱是合并波次类型
        const status = yield new Promise((r) => modal.confirm({
          modalBlurInput: true,
          content: res.info.resultMsg,
          onOk: () => r(true),
          onCancel: () => r(false),
        }));
        if (status) {
          yield ctx.scanContainer({
            param: {
              ...action.param,
              isNeedCheckMergeWellen: false,
            },
          });
        } else {
          yield ctx.cleanContainer();
          classFocus('collectionTransferContainerCode');
        }
      } else if (+res.info.resultType === 7) {
        // 已装托的周转箱和拣货周转箱非同一类型
        const status = yield new Promise((r) => modal.info({
          content: res.info.resultMsg,
          onOk: () => r('ok'),
          modalBlurInput: true,
        }));
        if (status === 'ok') {
          yield ctx.changeData({
            data: {
              resInfo: defaultState.resInfo,
            },
          });
          yield ctx.cleanContainer();
          // 聚焦到周转箱
          classFocus('collectionTransferContainerCode');
        }
      }
    } else if (res.code === '500383') {
      const status = yield new Promise((r) => modal.info({
        content: res.msg,
        onOk: () => r('ok'),
      }));
      if (status === 'ok') {
        yield ctx.cleanContainer();
        // 聚焦到托盘
        classFocus('collectionTransferContainerCode');
      }
    } else {
      // 报错的话清空周转箱，并且聚焦在周转箱。弹框
      yield new Promise((r) => (
        modal.error({
          audioErr: true,
          content: res.msg,
          onOk: () => r(0),
          onCancel: () => r(0),
        })
      ));
      classFocus('collectionTransferContainerCode');
      yield ctx.cleanContainer();
    }
  },
  // 提交关托转运
  * postClosePallet(action, ctx) {
    markStatus('closeLoading');
    const res = yield closePallet(action.param);
    if (res.code === '0') {
      yield ctx.changeData({
        data: assign({}, defaultState),
      });
      yield ctx.init();
      if (action.param.isVirtualPalletEnable) {
        message.success(t('托盘已成功关托'), 2000);
      } else {
        classFocus('collectionTransferPalletCode');
        message.success(t('托盘已成功关托转运'), 2000);
      }
    } else {
      // 错误的话弹框。
      modal.error({
        audioErr: true,
        content: res.msg,
      });
    }
  },
  // 提交确认接收
  * postReceivePallet(action, ctx) {
    markStatus('confirmLoading');
    const res = yield confirmReceive(action.param);
    if (res.code === '0') {
      // resultMsg存在则提示
      if (res.info && res.info.resultMsg) {
        message.success(res.info.resultMsg, 2000);
      } else {
        message.success(t('托盘已成功确认接收，周转箱可进行下一流程作业'), 2000);
      }
      yield ctx.changeData({
        data: assign({}, defaultState),
      });
      yield ctx.init();
      if (!action.param.isVirtualPalletEnable) {
        classFocus('collectionTransferPalletCode');
      }
    } else {
      // 错误的话弹框
      modal.error({
        audioErr: true,
        content: res.msg,
      });
    }
  },
};
