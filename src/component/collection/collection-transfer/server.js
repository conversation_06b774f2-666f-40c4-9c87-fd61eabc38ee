import { sendPostRequest } from 'lib/public-request';

// 扫描周转箱(只有周转箱有值时，调用的接口)查询的
export const scanContainer = (param) => sendPostRequest({
  url: '/sub_collection/scan_transfer_container',
  param,
}, process.env.WOS_URI);

// 扫描托盘(只有托盘有值时 查询，或者托盘、周转箱都有值时 绑托)
export const scanCollection = (param) => sendPostRequest({
  url: '/sub_collection/scan_transfer_pallet',
  param,
}, process.env.WOS_URI);

// 提交关托盘
export const closePallet = (param) => sendPostRequest({
  url: '/sub_collection/close_transfer_pallet',
  param,
}, process.env.WOS_URI);

// 提交确认接收
export const confirmReceive = (param) => sendPostRequest({
  url: '/sub_collection/confirm_receive',
  param,
}, process.env.WOS_URI);

// 查询是虚拟托盘开关是否开启
export const queryVirtualPalletEnable = (param) => sendPostRequest({
  url: '/sub_collection/turn_on_scan_pallet',
  param,
}, process.env.WOS_URI);
