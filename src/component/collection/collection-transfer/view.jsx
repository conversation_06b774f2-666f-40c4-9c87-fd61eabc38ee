import React, { Component } from 'react';
import PropTypes from 'prop-types';
import assign from 'object-assign';
import { connect } from 'react-redux';
import { Form, CellsTitle, Button } from 'react-weui/build/packages';
import { i18n, t } from '@shein-bbl/react';
import {
  Header, Footer, FooterBtn,
  FocusInput, modal,
} from 'common';
import { classFocus } from 'lib/util';
import UserIntro from 'common/user-intro';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import store from './reducers';
import styles from './style.css';
import IntroContext from '../../user-intro-page/IntroContext';

function BlueSpan({ text, style }) {
  return <span style={assign({ color: '#0059CE' }, style)}>{text}</span>;
}

BlueSpan.propTypes = {
  text: PropTypes.string.isRequired,
  style: PropTypes.shape(),
};

function RedText({ text }) {
  return (
    <div
      style={{
        position: 'absolute', right: '25px', top: '0px', color: 'red',
      }}
    >
      {text}
    </div>
  );
}

RedText.propTypes = {
  text: PropTypes.string.isRequired,
};

class View extends Component {
  componentDidMount() {
    const { params: { isSecondStep } } = this.props;
    store.init({ isSecondStep });
  }

  render() {
    const {
      containerCode,
      palletCode,
      palletCodeValid,
      containerCodeValid,
      closeLoading,
      confirmLoading,
      resInfo,
      resInfo: {
        palletStatus,
        containerNum,
        nationalLine,
        pickContainerCode,
        palletCode: palletCodeText,
        wellenCode,
        warehouseName,
        subWarehouseName,
        statusName,
        carNo,
        transferAdvance: {
          finishNum,
          installedNum,
          loadNum,
          waitNum,
          progressName,
        },
        displayClosePallet,
      },
      steps,
      headerTitle,
      isVirtualPalletEnable,
      params: { isSecondStep },
    } = this.props;
    return (
      <IntroContext.Consumer>
        {(context) => {
          context.changePageStore(store);
          return (
            <div>
              <Header title={headerTitle || t('集货装托')} />
              <UserIntro
                showIntro={context.showIntroVal}
                steps={steps}
                finishHandle={() => classFocus(isVirtualPalletEnable ? 'collectionTransferContainerCode' : 'collectionTransferPalletCode')}
              />
              <Form>
                <FocusInput
                  disabled={palletCodeValid || context.showIntroVal}
                  label={t('托盘')}
                  value={palletCode}
                  data-step="1"
                  data-intro={t('第一步，扫描托盘')}
                  className="collectionTransferPalletCode"
                  onChange={(e) => {
                    store.changeData({
                      data: {
                        palletCode: e.target.value,
                      },
                    });
                  }}
                  placeholder={t('请扫描托盘')}
                  onPressEnter={() => {
                    if (palletCode) {
                      store.scanPallet({
                        param: {
                          containerCode,
                          palletCode,
                        },
                      });
                    }
                  }}
                  autoFocus
                  footer={(
                    <Button
                      type="primary"
                      style={{ marginBottom: 5 }}
                      size="small"
                      onClick={() => {
                        store.init();
                        if (palletCode) {
                          classFocus('collectionTransferPalletCode');
                        } else {
                          store.changeData({ data: { palletCodeValid: true } });
                        }
                      }}
                    >
                      {t('清空')}
                    </Button>
                    )}
                />
                <FocusInput
                  disabled={containerCodeValid || context.showIntroVal}
                  label={t('周转箱')}
                  data-step="2"
                  data-intro={t('第二步，扫描拣货周转箱')}
                  className="collectionTransferContainerCode"
                  value={containerCode}
                  onChange={(e) => {
                    store.changeData({
                      data: {
                        containerCode: e.target.value,
                      },
                    });
                  }}
                  placeholder={t('请扫描周转箱')}
                  onPressEnter={() => {
                    if (containerCode && palletCode) {
                      store.scanPallet({
                        param: {
                          containerCode,
                          palletCode,
                        },
                      });
                    } else if (containerCode) {
                      store.scanContainer({
                        param: {
                          containerCode,
                          palletCode,
                          warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
                          isVirtualPalletEnable,
                        },
                      });
                    }
                  }}
                  footer={(
                    <Button
                      type="primary"
                      style={{ marginBottom: 5 }}
                      size="small"
                      onClick={() => {
                        store.changeData({ data: { containerCode: '' } });
                        if (containerCode) {
                          classFocus('collectionTransferContainerCode');
                        } else {
                          if (isVirtualPalletEnable) {
                            classFocus('collectionTransferContainerCode');
                          } else {
                            store.changeData({ data: { containerCodeValid: true } });
                          }
                        }
                      }}
                    >
                      {t('清空')}
                    </Button>
                    )}
                />
              </Form>
              <Form style={{ marginTop: '10px' }}>
                <CellsTitle>
                  {t('装托信息')}：
                  <BlueSpan text={palletCodeText} />
                  <RedText text={statusName} />
                </CellsTitle>
                <CellsTitle>
                  {t('箱数')}：
                  <BlueSpan text={containerNum} style={{ color: 'red', fontSize: '24px', fontWeight: 900 }} />
                </CellsTitle>
                <CellsTitle>
                  {t('转运车辆')}：
                  <BlueSpan text={carNo} />
                </CellsTitle>
              </Form>
              <Form style={{ marginTop: '10px' }}>
                <CellsTitle>
                  {t('箱信息')}：
                  <BlueSpan text={pickContainerCode} />
                  <RedText text={progressName} />
                </CellsTitle>
                <div className={styles.transferRowInfoBox}>
                  <div>
                    <span>{t('波次')}：</span>
                    <BlueSpan text={wellenCode} />
                  </div>
                  <div>
                    <span>{t('仓库')}：</span>
                    <BlueSpan text={warehouseName} />
                  </div>
                </div>
                <div className={styles.transferRowInfoBox}>
                  <div>
                    <span>{t('子仓')}：</span>
                    <BlueSpan text={subWarehouseName} />
                  </div>
                  <div>
                    <span>{t('国家线')}：</span>
                    <BlueSpan text={nationalLine} />
                  </div>
                </div>
                <div className={styles.transferRowInfoBox}>
                  {t('波次子仓转运进度(待装托/已装托/转运中/完成)')}：
                </div>
                <div className={styles.transferRowInfoBox}>
                  <BlueSpan text={`${waitNum}/${loadNum}/${installedNum}/${finishNum}`} />
                </div>
              </Form>
              <Footer style={{ position: 'absolute' }}>
                {
                  // eslint-disable-next-line no-nested-ternary
                    palletStatus !== 3
                    // 集货装托（二步集货）不展示关托按钮
                      ? (
                        isSecondStep !== '2' ? (
                          <FooterBtn
                            data-step="3"
                            data-intro={t('第三步，点击按钮')}
                            // data-position="top-right-aligned"
                            // data-hintPosition="top-middle"
                            className="confirmBtn"
                            disabled={
                              palletStatus !== 2 || closeLoading === 0 || context.showIntroVal
                            }
                            onClick={() => {
                              modal.confirm({
                                content: t('是否确认关托？'),
                                onOk: () => {
                                  if (isVirtualPalletEnable) {
                                    store.postClosePallet({
                                      param: {
                                        palletCode: resInfo.palletCode,
                                        isVirtualPalletEnable,
                                      },
                                    });
                                  } else {
                                    store.postClosePallet({
                                      param: {
                                        palletCode,
                                        isVirtualPalletEnable,
                                      },
                                    });
                                  }
                                },
                                onCancel: () => {},
                              });
                            }}
                          >
                            {t('关托')}
                          </FooterBtn>
                        ) : (displayClosePallet && (
                          <FooterBtn
                            data-step="3"
                            data-intro={t('第三步，点击按钮')}
                            // data-position="top-right-aligned"
                            // data-hintPosition="top-middle"
                            className="confirmBtn"
                            disabled={
                              palletStatus !== 2 || closeLoading === 0 || context.showIntroVal
                            }
                            onClick={() => {
                              modal.confirm({
                                content: t('是否确认关托？'),
                                onOk: () => {
                                  if (isVirtualPalletEnable) {
                                    store.postClosePallet({
                                      param: {
                                        palletCode: resInfo.palletCode,
                                        isVirtualPalletEnable,
                                      },
                                    });
                                  } else {
                                    store.postClosePallet({
                                      param: {
                                        palletCode,
                                        isVirtualPalletEnable,
                                      },
                                    });
                                  }
                                },
                                onCancel: () => {},
                              });
                            }}
                          >
                            {t('关托')}
                          </FooterBtn>
                        ))
                      )
                      : (
                        <FooterBtn
                          className="confirmBtn"
                          disabled={palletStatus !== 3 || confirmLoading === 0}
                          onClick={() => {
                            modal.confirm({
                              content: t('是否确定接收'),
                              onOk: () => {
                                store.postReceivePallet({
                                  param: { palletCode: palletCodeText, isVirtualPalletEnable },
                                });
                              },
                              onCancel: () => {},
                            });
                          }}
                        >
                          {t('确认接收')}
                        </FooterBtn>
                      )
                  }
              </Footer>
            </div>
          );
        }}
      </IntroContext.Consumer>
    );
  }
}

View.propTypes = {
  params: PropTypes.shape(),
  containerCode: PropTypes.string.isRequired,
  palletCode: PropTypes.string.isRequired,
  resInfo: PropTypes.shape().isRequired,
  palletCodeValid: PropTypes.bool.isRequired,
  containerCodeValid: PropTypes.bool.isRequired,
  closeLoading: PropTypes.number.isRequired,
  confirmLoading: PropTypes.number.isRequired,
  headerTitle: PropTypes.string.isRequired,
  isVirtualPalletEnable: PropTypes.bool.isRequired,
  steps: PropTypes.number,
};

const mapStateToProps = (state) => state['collection/collection-transfer'];
export default connect(mapStateToProps)(i18n(View));
