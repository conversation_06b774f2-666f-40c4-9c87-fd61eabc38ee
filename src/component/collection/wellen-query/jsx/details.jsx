import React from 'react';
import PropTypes from 'prop-types';
import { Form, CellsTitle } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import Footer from '../../../common/footer';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import style from '../style.css';
import RowInfo from '../../../common/row-info';

const Details = (props) => {
  const {
    info,
  } = props;
  const {
    palletCode,
    wellenCode,
    collectFloor,
  } = info;
  return (
    <div>
      <CellsTitle
        style={{ fontSize: 14 }}
      >
        {t('信息')}
      </CellsTitle>
      <div className={style.listWrap}>
        <div className={style.listItem}>
          <span className={style.itemSpan}>{t('托盘号')}:</span>
          <span className={style.itemRight}>{palletCode}</span>
        </div>
      </div>
      <div className={style.listWrap}>
        <div className={style.listItem}>
          <span className={style.itemSpan}>{t('波次号后4位')}:</span>
          <span className={style.itemRight}>{wellenCode.slice(-4)}</span>
        </div>
      </div>
      <div className={style.listWrap}>
        <div className={style.listItem}>
          <span className={style.itemSpan}>{t('集货楼层')}:</span>
          <span className={style.itemRight}>{collectFloor}</span>
        </div>
      </div>
      <Footer
        beforeBack={(back) => {
          store.init();
          back();
        }}
      />
    </div>
  );
};

Details.propTypes = {
  palletCode: PropTypes.string,
  info: PropTypes.shape(),
  scanLocationDisabled: PropTypes.bool,
};

export default Details;
