import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import Footer from '../../../common/footer';
import FocusInput from '../../../common/focus-input';

function Entry(props) {
  const {
    dataLoading,
    palletCode,
  } = props;

  return (
    <div>
      <div>
        <Form>
          <FocusInput
            disabled={!dataLoading}
            placeholder={t('请扫描')}
            autoFocus
            value={palletCode}
            className="palletCode"
            onChange={(e) => {
              store.changeData({
                data: {
                  palletCode: e.target.value,
                },
              });
            }}
            onPressEnter={() => {
              if (palletCode) {
                store.scanPallet({
                  props: {
                    palletCode,
                  },
                });
              }
            }}
          >
            <label>{t('托盘号')}</label>
          </FocusInput>
        </Form>
      </div>
      <Footer />
    </div>
  );
}

Entry.propTypes = {
  palletCode: PropTypes.string,
  palletCodeDisabled: PropTypes.bool,
  info: PropTypes.shape(),
  resultType: PropTypes.number,
  dataLoading: PropTypes.number,
};

export default Entry;
