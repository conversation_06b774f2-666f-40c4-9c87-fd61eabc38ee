import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import Header from '../../common/header';
import Entry from './jsx/entry';
import Details from './jsx/details';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      resultType,
      headerTitle,
      showDetail,
    } = this.props;
    return (
      <div>
        <Header title={headerTitle || t('波次查询')} />
        <Entry {...this.props} />
        {showDetail ? (
          <Details {...this.props} />
        ) : ''}
      </div>
    );
  }
}

Container.propTypes = {
  resultType: PropTypes.number,
  headerTitle: PropTypes.string,
};

export default i18n(Container);
