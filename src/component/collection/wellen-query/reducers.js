import assign from 'object-assign';
import React from 'react';
import { markStatus } from 'rrc-loader-helper';
import {
  scanPalletServer,
} from './server';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle } from '../../../lib/util';

const defaultState = {
  dataLoading: 1,
  palletCode: '',
  info: {
    palletCode: '',
    wellenCode: '',
    collectFloor: '',
  },
  showDetail: false,
  headerTitle: '',
};

export default {
  defaultState,

  $init: () => defaultState,

  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },

  changeData(draft, action) {
    assign(draft, action.data);
  },

  changeInfo(draft, action) {
    assign(draft, {
      info: assign({}, draft.info, action.data),
    });
  },

  * scanPallet(action, ctx, put) {
    markStatus('dataLoading');
    const result = yield scanPalletServer(action.props);
    // const result = {
    //   code: '0',
    //   info: {
    //     wellenCode: '111',
    //     collectFloor: '222'
    //   }
    // };
    if (result.code === '0') {
      yield put((draft) => {
        assign(draft, {
          palletCode: '',
          info: {
            palletCode: result.info.palletCode,
            wellenCode: result.info.wellenCode,
            collectFloor: result.info.collectFloor,
          },
          showDetail: true,
        });
      });
    } else {
      yield put((draft) => {
        draft.palletCode = '';
        draft.info = {
          palletCode: '',
          wellenCode: '',
          collectFloor: '',
        };
        draft.showDetail = false;
      });
      Modal.info({
        title: result.msg,
        onOk: () => {
          classFocus('palletCode');
        },
      });
    }
  },
};
