import { sendPostRequest } from 'lib/public-request';

// 扫描周转箱(只有周转箱有值时，调用的接口)查询的
export const scanContainer = param => sendPostRequest({
  url: '/collection/task/receive/scan_container_code',
  param,
}, process.env.WOS_URI);

// 集货任务领取-领取任务
export const receiveTaskApi = param => sendPostRequest({
  url: '/collection/task/receive/get_task',
  param,
}, process.env.WOS_URI);

// 查询参数配置
export const queryPackageNumber = param => sendPostRequest({
  url: '/config/getConfig',
  param,
}, process.env.BASE_URI_WMD);
