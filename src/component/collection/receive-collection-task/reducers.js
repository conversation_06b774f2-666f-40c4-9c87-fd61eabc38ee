import React from 'react';
import assign from 'object-assign';
import { modal, message } from 'common';
import { classFocus } from 'lib/util';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { put } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import error from 'source/audio/delete.mp3';
import {
  scanContainer as scanContainerApi,
  receiveTaskApi,
  queryPackageNumber,
} from './server';

// 报错异常声音
const audio = new Audio(error);
audio.load();

const defaultState = {
  palletCode: '', // 托盘号
  palletCodeValid: false, // 托盘号
  btnShowNumber: 1,
  footerText: '',
  containerCodes: [],
  showPalletCode: '',
  containerCode: '',
  containerCodeValid: false,
  dataLoading: 1,
  confirmLoading: 1,
  receivePalletBool: false,
  scannedInfoList: [], // 扫描过的周转箱号与波次号
  configValue: 0,
};

export default {
  state: defaultState,
  $init: () => defaultState,
  * init(action, ctx) {
    const res = yield queryPackageNumber({ param: 'COLLECTION_TASK_AUTO_CLOSE_BOX_NUM' });
    const { code, info, msg } = res;
    if (code === '0') {
      if (info && info.configValue) {
        yield ctx.changeData({
          data: {
            configValue: Number(info.configValue),
          },
        });
      }
    } else {
      modal.error({ title: msg });
    }
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  // 禁用托盘
  $scanPallet(draft) {
    assign(draft, {
      palletCodeValid: true,
    });
  },
  // 禁用周转箱
  $scanContainer(draft) {
    assign(draft, {
      containerCodeValid: true,
    });
  },
  // 清空周转箱
  * cleanContainer(__, ctx) {
    yield ctx.changeData({
      data: {
        containerCode: '',
        containerCodeValid: false,
      },
    });
  },
  // 扫描周转箱
  * scanContainer(action, ctx) {
    markStatus('dataLoading');
    const { scannedInfoList } = this.state;
    if (scannedInfoList.map(item => item.containerCode).includes(action.param.containerCode)) {
      yield new Promise(r => modal.info({
        content: t('该周转箱已经领取，请勿重复扫描'),
        onOk: () => r('ok'),
      }));
      yield ctx.cleanContainer();
      classFocus('containerCode');
      return;
    }
    const param = {
      ...action.param,
      scannedInfoList,
    };
    const {
      code,
      info,
      msg,
    } = yield scanContainerApi(param);
    if (code === '0' && info) {
      // 跳转code 0 - 正常 1 - 重置页面并焦点置于周转箱 2 - 清空页面并焦点置于周转箱 3 - 确认后清空页面，焦点置于周转箱 4 - 任务领取成功
      if (info.containerCode) {
        const scannedInfoListNew = [{
          containerCode: info.containerCode || '',
          wellenCode: info.wellenCode || '',
          locationCode: info.locationCode || '',
        }];
        yield ctx.changeData({
          data: {
            scannedInfoList: [...scannedInfoListNew, ...scannedInfoList],
          },
        });
        yield ctx.cleanContainer();
        classFocus('containerCode');
      }
      if (info.code === 1) {
        // 扫周装箱报错需要发出声音。
        audio.play();
        yield ctx.cleanContainer();
        modal.info({
          content: (
            <div>
              <p>{ info.message }</p>
              { info.wellenTypeName && <p style={{ fontSize: '20px', color: 'red' }}>{ info.wellenTypeName }</p> }
            </div>
          ),
          onOk: () => {
            classFocus('containerCode');
          },
        });
      } else if (info.code === 2) {
        // 扫周装箱报错需要发出声音。
        audio.play();
        message.warning(info.message);
        // 清空所有数据
        yield ctx.changeData({
          data: assign({}, defaultState),
        });
        yield ctx.cleanContainer();
        classFocus('containerCode');
      } else if (info.code === 3) {
        yield new Promise(r => modal.info({
          content: info.message,
          onOk: () => r('ok'),
        }));
        yield ctx.cleanContainer();
        classFocus('containerCode');
      } else if (info.code === 4) {
        const status = yield new Promise(r => modal.confirm2({
          title: t('任务领取成功'),
          content: <div style={{ maxHeight: 100, overflowY: 'scroll' }}>{(info.taskReceiveInfo || []).map(i => (
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}><div>{i.pickContainerCode}</div><div style={{ display: 'inline-flex', justifyContent: 'space-between', alignItems: 'center' }}>{i.locationCode ? (<><b style={{ fontWeight: '500' }}>{(`${i.locationCode}`).slice(0, -2)}</b><b style={{ fontSize: '18px', color: 'red' }}>{(`${i.locationCode}`).slice(-2)}</b></>) : '-'}</div></div>
          ))}
          </div>,
          onOk: () => r('ok'),
          onCancel: () => r('cancel'),
          cancelText: t('返回'),
          okText: t('去集货'),
        }));
        if (status === 'ok') {
          // 弹窗关闭，页面跳转至【子仓集货】页面
          yield put(push('/collection/sub-collection'));
        }
        if (status === 'cancel') {
          // 弹窗关闭，返回至领取任务初始化页，清空页面内容，焦点置于周转箱
          yield ctx.changeData({
            data: assign({}, defaultState),
          });
          classFocus('containerCode');
        }
      }
    } else {
      // 扫周装箱报错需要发出声音。
      audio.play();
      // 报错的话清空周转箱，并且聚焦在周转箱。弹框
      yield ctx.cleanContainer();
      modal.error({
        audioErr: true,
        content: msg,
        onOk: () => {
          classFocus('containerCode');
        },
      });
    }
  },
  // 集货任务领取-领取任务
  * receiveTask(action, ctx) {
    markStatus('confirmLoading');
    const {
      code,
      msg,
      info,
    } = yield receiveTaskApi({ containerList: action.param.containerList });
    if (code === '0') {
      const status = yield new Promise(r => modal.confirm2({
        title: t('任务领取成功'),
        content: <div style={{ maxHeight: 100, overflowY: 'scroll' }}>{(info || []).map(i => (
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}><div>{i.pickContainerCode}</div><div style={{ display: 'inline-flex', justifyContent: 'space-between', alignItems: 'center' }}><b style={{ fontWeight: '500' }}>{(`${i.locationCode}`).slice(0, -2)}</b><b style={{ fontSize: '18px', color: 'red' }}>{(`${i.locationCode}`).slice(-2)}</b></div></div>
        ))}
        </div>,
        onOk: () => r('ok'),
        onCancel: () => r('cancel'),
        cancelText: t('返回'),
        okText: t('去集货'),
      }));
      if (status === 'ok') {
        // 弹窗关闭，页面跳转至【子仓集货】页面
        yield put(push('/collection/sub-collection'));
      }
      if (status === 'cancel') {
        // 弹窗关闭，返回至领取任务初始化页，清空页面内容，焦点置于周转箱
        yield ctx.changeData({
          data: assign({}, defaultState),
        });
        classFocus('containerCode');
      }
    } else if (code === '602902') {
      yield ctx.changeData({
        data: assign({}, defaultState),
      });
      modal.info({
        content: msg,
        onOk: () => {
          classFocus('containerCode');
        },
      });
    } else {
      // 错误的话弹框
      modal.error({
        audioErr: true,
        content: msg,
      });
    }
  },
};
