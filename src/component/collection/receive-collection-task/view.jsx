import React, { Component } from 'react';
import PropTypes from 'prop-types';
import assign from 'object-assign';
import { CellsTitle, Form } from 'react-weui/build/packages';
import { i18n, t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import { classFocus } from 'lib/util';
import store from './reducers';
import Footer from '../../common/footer';
import message from '../../common/message';
import {
  Header,
  FocusInput,
  FooterBtn,
  pages,
  modal,
} from '../../common';
import styles from './style.css';

const { View } = pages;

function BlueSpan({
  text,
  style,
}) {
  return (
    <span
      style={assign({
        color: '#0059CE',
        fontSize: '14px',
      }, style)}
    >
      {text}
    </span>
  );
}

BlueSpan.propTypes = {
  text: PropTypes.string,
  style: PropTypes.shape(),
};

class Container extends Component {
  componentDidMount() {
    store.$init();
    store.init();
  }

  render() {
    const {
      dispatch,
      containerCodes,
      containerCode,
      dataLoading,
      confirmLoading,
      scannedInfoList,
      configValue,
    } = this.props;
    return (
      <View>
        <Header title={t('领取集货任务')}>
          <div
            onClick={() => {
              dispatch(push('/collection/close-collection-task'));
            }}
          >
            {t('取消任务')}
          </div>
        </Header>
        <Form
          style={{
            marginBottom: 12,
          }}
        >
          <FocusInput
            autoFocus
            disabled={dataLoading === 0}
            placeholder={t('请扫描周转箱')}
            className="containerCode"
            label={t('周转箱')}
            value={containerCode}
            onChange={(e) => {
              store.changeData({
                data: {
                  containerCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={(e) => {
              if (containerCode) {
                if (containerCodes && containerCodes.length > 0) {
                  const targetValue = e.target.value.trim();
                  const f = containerCodes.find((ci) => (ci.no === targetValue));
                  if (f) {
                    if (f.isChecked) {
                      message.warning(t('重复扫箱'), 1000);
                      store.changeData({
                        data: {
                          containerCode: '',
                        },
                      });
                      return;
                    } else {
                      let containerCodesIndex = '';
                      const containerCodesNew = containerCodes.map((v, index) => {
                        if (v.no === targetValue) {
                          containerCodesIndex = index;
                          return { ...v, isChecked: true };
                        }
                        return v;
                      });
                      const containerCodesList1 = [];
                      const containerCodesList2 = [];
                      containerCodesNew.forEach((item, index) => {
                        if (containerCodesIndex !== index) {
                          if (item.isChecked) {
                            containerCodesList2.push(item);
                          } else {
                            containerCodesList1.push(item);
                          }
                        }
                      });
                      store.changeData({
                        data: {
                          containerCodes: [containerCodesNew[containerCodesIndex], ...containerCodesList1, ...containerCodesList2],
                        },
                      });
                    }
                  } else {
                    modal.error({
                      content: t('该周转箱{}不在当前托盘中', e.target.value),
                      className: 'containerCode',
                    });
                  }
                  store.changeData({
                    data: {
                      containerCode: '',
                    },
                  });
                  return;
                }
                store.scanContainer({
                  param: {
                    containerCode,
                  },
                });
              } else {
                modal.error({
                  content: t('请扫描周转箱'),
                  className: 'containerCode',
                });
              }
            }}
          />
        </Form>
        <Form className="receiveClassName">
          <div className={styles.divboxShadow}>
            <CellsTitle style={{ fontSize: '14px' }}>
              {t('已领 / 任务上限箱数')}：
              <span
                style={{
                  fontSize: '20px',
                  color: '#d9001bfe',
                }}
              >
                <span dangerouslySetInnerHTML={{ __html: scannedInfoList.length }} />
                /{configValue}
              </span>
            </CellsTitle>
          </div>
          <div
            style={{
              height: 'calc(100vh - 180px)',
              overflowY: 'auto',
            }}
          >
            {scannedInfoList && scannedInfoList.length > 0 && scannedInfoList
              .map((item) => (
                <div key={item.containerCode} className={styles.trayBox}>
                  <p className={styles.trayLeft}>{item.containerCode}</p>
                  <p>
                    <p style={{ marginRight: '30px' }}>{t('波次号')}: {item.wellenCode}</p>
                    <p>{t('整波集货库位')}: {item.locationCode}</p>
                  </p>
                </div>
              ))}
          </div>
        </Form>
        <Footer
          beforeBack={() => {
            dispatch(push('/sub-menu'));
          }}
        >
          {scannedInfoList.length !== 0 && (
            <FooterBtn
              disabled={scannedInfoList.length === 0 || confirmLoading === 0}
              onClick={() => {
                modal.confirm({
                  title: t('任务箱数{}，是否确认领取集货任务？', scannedInfoList.length),
                  onOk: () => {
                    store.receiveTask({
                      param: {
                        containerList: scannedInfoList.map((item) => ({
                          containerCode: item.containerCode || '',
                          wellenCode: item.wellenCode || '',
                        })),
                      },
                    });
                  },
                  onCancel: () => {
                    store.changeData({
                      data: {
                        containerCode: '',
                      },
                    });
                    classFocus('containerCode');
                  },
                });
              }}
            >
              {t('领取任务')}
            </FooterBtn>
          )}
        </Footer>
      </View>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  containerCodes: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  scannedInfoList: PropTypes.arrayOf(PropTypes.shape()),
  containerCode: PropTypes.string,
  dataLoading: PropTypes.number,
  confirmLoading: PropTypes.number,
  configValue: PropTypes.number,
};

export default i18n(Container);
