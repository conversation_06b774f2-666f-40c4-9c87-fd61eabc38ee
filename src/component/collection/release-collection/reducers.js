import assign from 'object-assign';
import { releaseCollection, updateReleaseRecord } from './server';
import { modal } from '../../common';
import { getHeaderTitle } from '../../../lib/util';
import errorAudio from '../../../source/audio/delete.mp3';

const audioError = new Audio(errorAudio);
audioError.load();

const defaultState = {
  collectLocation: '',
  collectionType: 0,
  containers: [],
  subList: [],
  info: {
    collectLocation: '',
    pickContainerCodeList: [],
  },
  isLocationDisabled: false,
  headerTitle: '',
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  * release(action, ctx) {
    const result = yield releaseCollection(action.params);
    if (+result.code === 0) {
      yield ctx.changeData({
        data: assign({}, result.info, {
          isLocationDisabled: false,
        }),
      });
    } else {
      audioError.play();
      yield ctx.changeData({
        data: {
          collectionType: 0,
          collectLocation: '',
          isLocationDisabled: false,
        },
      });
      modal.error({
        content: result.msg,
        className: 'collectLocation',
      });
    }
  },
  * releaseSub(action, ctx) {
    const result = yield updateReleaseRecord(action.params);
    if (+result.code === 0) {
      yield ctx.changeData({
        data: assign({}, {
          collectionType: 8,
          info: result.info,
          collectLocation: '',
        }),
      });
    } else {
      audioError.play();
      yield ctx.changeData({
        data: {
          collectionType: 0,
          collectLocation: '',
          isLocationDisabled: false,
        },
      });
      modal.error({ content: result.msg });
    }
  },
};
