import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import FooterBtn from '../../../common/footer-btn';
import Footer from '../../../common/footer';
import List from '../../../common/list';
import Header from '../../../common/header';
import style from '../../../style.css';
import innerStyle from '../style.css';

class ReleaseMain extends React.Component {
  render() {
    const {
      collectLocation,
      collectionType,
      subList,
      headerTitle,
    } = this.props;

    const rows = [
      [
        {
          title: t('子仓'),
          render: 'subWarehouseName',
        },
        {
          title: t('子仓集货位'),
          render: 'subCollectLocation',
        },
      ],
      [
        {
          title: t('托盘'),
          render: 'palletCode',
        },
        {
          title: t('周转箱数'),
          render: 'containerNum',
          default: 0,
        },
      ],
    ];

    let title = '';
    const palletCodes = subList.map((v) => v.palletCode).join('、');
    if (+collectionType === 4) {
      title = (
        <div>
          {t('托盘号{}对应周转箱状态为子仓集货完成,是否释放集货库位?', palletCodes)}
        </div>
      );
    } else if (+collectionType === 5) {
      title = (
        <div>
          {t('托盘号{}对应周转箱状态为子仓集货完成且任务存在全部短拣,是否释放集货库位?', palletCodes)}
        </div>
      );
    }

    const height = window.innerHeight - 56;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <Header title={headerTitle || t('释放集货库位')} />
        <div className={innerStyle.tips}>
          {title}
        </div>

        <List
          rows={rows}
          data={subList}
        />

        <Footer
          beforeBack={() => {
            store.init();
          }}
        >
          <FooterBtn
            onClick={() => {
              store.releaseSub({
                params: { collectLocation },
              });
            }}
          >
            {t('确认')}
          </FooterBtn>
        </Footer>
      </div>

    );
  }
}

ReleaseMain.propTypes = {
  collectLocation: PropTypes.string.isRequired,
  subList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  collectionType: PropTypes.number.isRequired,
  headerTitle: PropTypes.string.isRequired,
};

export default ReleaseMain;
