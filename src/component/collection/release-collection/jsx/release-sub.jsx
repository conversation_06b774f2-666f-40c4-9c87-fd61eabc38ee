import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import Footer from '../../../common/footer';
import FooterBtn from '../../../common/footer-btn';
import Header from '../../../common/header';
import innerStyle from '../style.css';

class RealeaseSub extends React.Component {
  render() {
    const {
      containers,
      collectLocation,
      collectionType,
      headerTitle,
    } = this.props;

    let title = '';
    if (+collectionType === 1) {
      title = (
        <div>
          {t('周转箱{}状态为拣货完成,是否释放集货库位?', containers.join('、'))}
        </div>
      );
    } else if (+collectionType === 2) {
      title = (
        <div>
          {t('周转箱{}状态为拣货完成且存在全部短拣任务,是否释放集货库位?', containers.join('、'))}
        </div>
      );
    } else if (+collectionType === 7) {
      title = (
        <div>
          {t('是否释放集货库位?')}
        </div>
      );
    } else {
      title = (
        <div>
          {t('存在全部短拣任务,是否释放集货库位?')}
        </div>
      );
    }

    return (
      <div>
        <Header title={headerTitle || t('释放集货库位')} />
        <div className={innerStyle.tips}>
          {title}
        </div>

        <Footer
          beforeBack={() => {
            store.init();
          }}
        >
          <FooterBtn
            onClick={() => {
              store.releaseSub({
                params: { collectLocation },
              });
            }}
          >
            {t('确认')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

RealeaseSub.propTypes = {
  containers: PropTypes.arrayOf(PropTypes.string).isRequired,
  collectionType: PropTypes.string.isRequired,
  collectLocation: PropTypes.string.isRequired,
  headerTitle: PropTypes.string.isRequired,
};

export default RealeaseSub;
