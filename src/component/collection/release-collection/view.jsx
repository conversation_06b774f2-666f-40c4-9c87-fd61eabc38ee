import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages/components/form';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import Header from '../../common/header';
import FocusInput from '../../common/focus-input';
import Footer from '../../common/footer';
import style from '../../style.css';
import ReleaseMain from './jsx/release-main';
import ReleaseSub from './jsx/release-sub';


class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      collectionType,
      info,
      isLocationDisabled,
      headerTitle,
    } = this.props;

    const height = window.innerHeight - 56;

    const containerList = info.pickContainerCodeList;
    const hasContainer = !!containerList && containerList.length > 0;

    switch (+collectionType) {
      case 1:
        return (<ReleaseSub {...this.props} />);
      case 2:
        return (<ReleaseSub {...this.props} />);
      case 3:
        return (<ReleaseSub {...this.props} />);
      case 4:
        return (<ReleaseMain {...this.props} />);
      case 5:
        return (<ReleaseMain {...this.props} />);
      case 6:
        return (<ReleaseSub {...this.props} />);
      case 7:
        return (<ReleaseSub {...this.props} />);
      default:
        return (
          <div className={style.flexColContainer} style={{ height }}>
            <Header title={headerTitle || t('释放集货库位')} />
            <Form>
              <FocusInput
                placeholder={t('请扫描')}
                disabled={isLocationDisabled}
                autoFocus
                className="collectLocation"
                data-bind="collectLocation"
                onPressEnter={(e) => {
                  if (!e.target.value) {
                    return;
                  }

                  store.changeData({
                    data: {
                      isLocationDisabled: true,
                    },
                  });

                  store.release({
                    params: {
                      collectLocation: e.target.value,
                    },
                  });
                }}
              >
                <label>{t('集货库位')}</label>
              </FocusInput>
            </Form>
            <div
              style={{
                display: collectionType === 8 ? '' : 'none',
                textAlign: 'center',
                marginTop: 10,
              }}
            >
              <span style={{ color: 'red' }}>{info.collectLocation}</span>&nbsp;
              {t('集货库位已释放')}
              {hasContainer && (
                <span>
                  ,{t('周转箱')}&nbsp;<span style={{ color: 'red' }}>{containerList.join('、')}</span>&nbsp;{t('集货异常')}
                </span>
              )}
            </div>
            <Footer
              beforeBack={(back) => {
                store.init();
                back();
              }}
            />
          </div>
        );
    }
  }
}

Container.propTypes = {
  info: PropTypes.shape().isRequired,
  collectionType: PropTypes.number.isRequired,
  isLocationDisabled: PropTypes.bool.isRequired,
  headerTitle: PropTypes.string.isRequired,
};

const mapStateToProps = state => state['collection/release-collection'];
export default connect(mapStateToProps)(i18n(Container));
