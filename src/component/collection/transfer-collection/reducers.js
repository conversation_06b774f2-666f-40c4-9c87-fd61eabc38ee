import assign from 'object-assign';
import { push } from 'react-router-redux';
import { t } from '@shein-bbl/react';
import { locationCollectionFinishApi, scanLocationApi, scanContainerApi } from './server';
import { modal, message } from '../../common';
import { classFocus, getParentHref } from '../../../lib/util';

const defaultState = {
  loading: false,
  unCollectionNum: undefined,
  collectionNum: undefined,
  containerCodes: [],
  // 出库单号
  outboundCode: '',
  billTypeText: '',
  // 拣货周转箱
  containerCode: '',
  // 集货库位
  location: '',
  // 推荐的集货库位
  recommendLocation: '',
  // 扫描成功的周转箱
  scannedContainerCode: '',

  headerTitle: '',

  isContainerCodeDisabled: false,
  isLocationDisabled: false,
  containerCodeType: '', // 周转箱所属任务类型
};

// 将扫描成功的周转箱置顶
function getContainerTopList(list, containerCode) {
  const res = [];

  list.forEach((item) => {
    if (item.containerCode === containerCode) {
      res.unshift(item);
    } else {
      res.push(item);
    }
  });

  return res;
}

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: t('特殊出库集货') } });
  },
  * locationCollectionFinish(action, ctx, put) {
    const { params } = action;
    const data = yield locationCollectionFinishApi(params);
    if (data.code === '0') {
      message.success(t('集货完成'));
      yield put(push(getParentHref()));
    } else {
      modal.error({
        content: data.msg,
        className: 'location',
      });
      yield ctx.changeData({
        data: {
          location: '',
        },
      });
    }
  },

  * scanContainer(action, ctx) {
    const { params } = action;
    const data = yield scanContainerApi(params);
    const { code, info, msg } = data;
    yield ctx.changeData({ data: { isContainerCodeDisabled: false } });
    if (code === '0') {
      const baseObj = {
        outboundCode: info.outboundCode,
        billTypeText: info.billTypeText,
        unCollectionNum: info.unCollectionNum,
        collectionNum: info.collectionNum,
        recommendLocation: info.location,
        scannedContainerCode: params.containerCode,
        containerCodeType: info.containerCodeType,
      };

      if (params.containerCode && params.location) {
        // 将扫描成功的周转箱置顶
        const list = getContainerTopList(info.containerCodes, params.containerCode);
        yield ctx.changeData({
          data: {
            containerCodes: list,
            containerCode: '',
            location: '',
            ...baseObj,
          },
        });
        classFocus('containerCode');
      } else {
        yield ctx.changeData({
          data: {
            containerCodes: info.containerCodes || [],
            ...baseObj,
          },
        });
        classFocus('location');
      }
    } else {
      yield ctx.changeData({
        data: {
          containerCode: '',
        },
      });
      modal.error({
        content: msg,
        className: 'containerCode',
      });
    }
  },

  * scanLocation(action, ctx) {
    const { params } = action;
    const data = yield scanLocationApi(params);
    const { code, info, msg } = data;
    yield ctx.changeData({ data: { isLocationDisabled: false } });
    if (code === '0') {
      if (info.collectionMsg) {
        message.info(info.collectionMsg);
      }
      // 集货完成，页面清空 ，初始化
      // 特殊场景：特殊出库集货，波次下，一个任务未开始拣货，其它任务的箱子集货完成，此时这个任务操作了一件未拣短捡，
      //         波次下待集货数量为0，但是波次状态没有变为拣货完成。业务在扫描集货货位时，前端通过后端返回的待集货
      //         箱数判断波次是否集货完成，导致没有跳转到正确页面，业务无法手动集货完成，导致无法一分。
      // 需要加上箱号判断的原因：待集货箱数为0，需要清空页面的场景，是必须存在箱号才走清空逻辑。
      if (params.containerCode && info.unCollectionNum === 0) {
        yield ctx.changeData({
          data: {
            ...defaultState,
            headerTitle: t('特殊出库集货'),
          },
        });
        classFocus('containerCode');
        return;
      }
      const baseObj = {
        outboundCode: info.outboundCode,
        billTypeText: info.billTypeText,
        unCollectionNum: info.unCollectionNum,
        collectionNum: info.collectionNum,
        recommendLocation: info.location,
      };
      if (params.containerCode && params.location) {
        yield ctx.changeData({ data: { isContainerCodeDisabled: false } });
        // 将扫描成功的周转箱置顶
        const list = getContainerTopList(info.containerCodes, params.containerCode);
        yield ctx.changeData({
          data: {
            containerCodes: list,
            scannedContainerCode: params.containerCode,
            containerCode: '',
            location: '',
            ...baseObj,
          },
        });
        classFocus('containerCode');
      } else {
        yield ctx.changeData({
          data: {
            containerCodes: info.containerCodes, ...baseObj,
          },
        });
        classFocus('containerCode');
      }
    } else if (code === '500334') {
      // 没有对应的拣货头数据，给出一个提示框
      const status = yield new Promise((r) => {
        modal.confirm({
          content: data.msg,
          onOk: () => r('ok'),
          onCancel: () => r('cancel'),
          okText: t('强制释放'),
          cancelText: t('我知道了'),
        });
      });
      if (status === 'ok') {
        yield ctx.locationCollectionFinish({ params: { location: params.location } });
      }
      if (status === 'cancel') {
        yield ctx.changeData({
          data: {
            location: '',
          },
        });
        classFocus('location');
      }
    } else {
      yield ctx.changeData({
        data: {
          location: '',
        },
      });
      modal.error({
        content: msg,
        className: 'location',
      });
    }
  },

};
