import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import store from './reducers';
import {
  Header, Footer, FooterBtn, FocusInput, RowInfo, Table, modal, pages,
} from '../../common';
import style from '../../style.css';
import innerStyle from './style.css';

const { View } = pages;
class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      isContainerCodeDisabled,
      isLocationDisabled,
      headerTitle,
      recommendLocation,
      outboundCode,
      billTypeText,
      containerCodes,
      location,
      unCollectionNum,
      collectionNum,
      containerCode,
      scannedContainerCode,
      containerCodeType,
    } = this.props;

    const height = window.innerHeight - 56 - 44;

    // 周转箱一个都没收集，不给集货完成
    const noCollection = (containerCodes || []).every((i) => !i.isCollection);
    const overflowStyle = {
      height,
      overflow: 'auto',
    };

    return (
      <div>
        <Header title={headerTitle} />
        <View flex={false} diff={110}>

          <Form>
            <FocusInput
              disabled={isContainerCodeDisabled}
              placeholder={t('请扫描')}
              autoFocus
              className="containerCode"
              data-bind="containerCode"
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.changeData({
                  data: {
                    isContainerCodeDisabled: true,
                  },
                });
                if (containerCode) {
                  if (location) {
                    store.scanLocation({ params: { containerCode, location } });
                  } else {
                    store.scanContainer({ params: { containerCode } });
                  }
                }
              }}
            >
              <label>{t('拣货周转箱')}</label>
            </FocusInput>
            <FocusInput
              disabled={isLocationDisabled}
              placeholder={t('请扫描')}
              className="location"
              data-bind="location"
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.changeData({
                  data: {
                    isLocationDisabled: true,
                  },
                });
                if (location) {
                  store.scanLocation({ params: { containerCode, location } });
                }
              }}
            >
              <label>{t('集货位')}</label>
            </FocusInput>
          </Form>
          {/* <div style={overflowStyle}> */}
          {recommendLocation && (
            <div className={innerStyle.box_recommendlocation}>
              <span>{t('推荐货位')}</span>
              <span className={innerStyle.recommendlocation}>{recommendLocation}</span>
            </div>
          )}
          <div
            className={innerStyle.wrapper}
            style={{ display: containerCodes.length ? '' : 'none' }}
          >
            <div className={innerStyle.box_mes}>
              <span>{outboundCode}</span>
              <span style={{ color: 'red', fontSize: '18px', fontWeight: 'bold' }}>{containerCodeType}</span>
              <span>{billTypeText}</span>
            </div>
            <RowInfo
              textExtraStyle={{
                fontSize: 14,
              }}
              extraStyle={{
                borderBottom: 'none',
                fontSize: 13,
              }}
              data={[
                {
                  label: t('待集货数'),
                  content: unCollectionNum,
                },
                {
                  label: t('已集货数'),
                  content: collectionNum,
                  type: 'warn',
                },
              ]}
            />
            <Table
              columns={[
                {
                  title: t('拣货周转箱'),
                  dataIndex: 'containerCode',
                  width: 20,
                  render: (data) => {
                    const color = data.isCollection ? '#0059CE' : '';
                    return <span style={{ color, fontSize: 14 }}>{data.containerCode}</span>;
                  },
                },
                {
                  title: t('是否集货'),
                  dataIndex: 'isCollection',
                  width: 20,
                  render: (data) => {
                    const color = data.isCollection ? '#0059CE' : '#B2B2B2';
                    return <Icon style={{ color, fontSize: 15 }} name="check" />;
                  },
                },
              ]}
              dataSource={containerCodes}
              rowClassName={(data) => {
                if (data.containerCode === scannedContainerCode) {
                  return innerStyle.high_light;
                }
                return '';
              }}
            />
          </div>
          {/* </div> */}
        </View>
        <Footer
          beforeBack={(back) => {
            modal.confirm({
              content: t('是否回到上级页面'),
              onOk: () => {
                store.init();
                back();
              },
            });
          }}
        >
          <FooterBtn
            disabled={noCollection || !recommendLocation}
            onClick={() => {
              if (location && location !== recommendLocation) {
                modal.error({
                  content: t('填写的集货库位和推荐集货库位不一致'),
                });
                return;
              }

              let content = t('是否强制释放集货货位?');

              if (unCollectionNum === 0) {
                content = t('是否释放集货货位?');
              }

              modal.confirm({
                content,
                onOk: () => {
                  store.locationCollectionFinish({ params: { location: recommendLocation } });
                },
              });
            }}
          >
            {t('集货完成')}
          </FooterBtn>
        </Footer>

      </div>
    );
  }
}

Container.propTypes = {
  isContainerCodeDisabled: PropTypes.bool,
  isLocationDisabled: PropTypes.bool,
  headerTitle: PropTypes.string,
  recommendLocation: PropTypes.string,
  outboundCode: PropTypes.string,
  billTypeText: PropTypes.string,
  containerCodes: PropTypes.arrayOf(PropTypes.shape),
  location: PropTypes.string.isRequired,
  unCollectionNum: PropTypes.number,
  collectionNum: PropTypes.number,
  containerCode: PropTypes.string,
  scannedContainerCode: PropTypes.string,
};

const mapStateToProps = (state) => state['collection/transfer-collection'];
export default connect(mapStateToProps)(i18n(Container));
