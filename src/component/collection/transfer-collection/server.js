import { sendPostRequest } from '../../../lib/public-request';

// 扫描周转箱
export const scanContainerApi = (param) => sendPostRequest({
  url: '/pda/scanContainerCode',
  param,
}, process.env.WOS_URI);

// 扫描库位
export const scanLocationApi = (param) => sendPostRequest({
  url: '/pda/scanLocationCode',
  param,
}, process.env.WOS_URI);

// 集货完成
export const locationCollectionFinishApi = (param) => sendPostRequest({
  url: '/pda/scanLocationCollectionFinish',
  param,
}, process.env.WOS_URI);
