import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import modal from '../../common/modal';
import {
  scanContainerServer, scanLocationServer, handoverCancelServer, queryRankInfoApi,
} from './server';
import message from '../../common/message';
import { classFocus, getHeaderTitle } from '../../../lib/util';

const defaultState = {
  pickContainerCode: '', // 拣货周转箱
  scanContainerRsp: {
    completeBoxNum: '', // 今日完成箱数
    handoverBoxNum: '', // 交接箱数
    id: '', // 主仓集货id
    isHandover: false, // 是否存在交接中的任务：true 是，false 否
    wellenCode: '', // 波次号
  },
  type: 0, // 0：扫拣货周转箱页面，1：扫描待一分暂存位页面
  temporaryLocation: '', // 待一分暂存位
  headerTitle: '',
  rank: 0, // 用户排名
  total: 0,
  siteTips: '',
  nationalLineTypeName: '',
  steps: [],
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  // 初始当前页面数据
  resetPageStore(draft) {
    assign(draft, defaultState, { headerTitle: draft.headerTitle });
  },
  reset(draft) {
    assign(draft, defaultState);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    // 获取排名
    yield ctx.queryRankInfo();
  },
  * queryRankInfo(action, ctx) {
    // 集货拉货交接排名code=12
    const res = yield queryRankInfoApi({ rankTypeCode: 12 });
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          rank: res.info.rank || 0,
          total: res.info.total || 0,
          siteTips: res.info.siteTips,
        },
      });
    } else {
      modal.error({ content: res.msg });
    }
  },
  * getData(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanContainerServer(action.param);
    if (res.code === '0') {
      if (res.info.isHandover) {
        if (JSON.stringify(action.param) === '{}') {
          const status = yield new Promise((r) => {
            modal.info({
              content: t('存在进行中任务，前往完成'),
              onOk: () => r('ok'),
            });
          });
          if (status === 'ok') {
            yield ctx.changeData({
              data: {
                type: 1,
              },
            });
            classFocus('temporaryLocation');
          }
        } else {
          message.success(t('领取任务成功'), 2000);
          yield ctx.changeData({
            data: {
              type: 1,
            },
          });
          classFocus('temporaryLocation');
        }
      }
      yield ctx.changeData({
        data: {
          scanContainerRsp: res.info,
          type: res.info.isHandover ? 1 : 0,
          siteTips: res.info.siteTips,
          nationalLineTypeName: res.info.nationalLineTypeName,
          wellenTypeName: res.info.wellenTypeName,
        },
      });
      classFocus('pickContainerCode');
    } else {
      yield ctx.changeData({
        data: {
          pickContainerCode: '',
        },
      });
      modal.error({ content: res.msg, className: 'pickContainerCode' });
    }
  },
  * scanLocation(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanLocationServer(action.param);
    if (res.code === '0') {
      message.success(t('任务完成'), 2000);
      yield ctx.init();
      yield ctx.getData({
        param: {},
      });
    } else {
      yield ctx.changeData({
        data: {
          temporaryLocation: '',
        },
      });
      modal.error({ content: res.msg, className: 'temporaryLocation' });
    }
  },
  * handoverCancel(action, ctx) {
    markStatus('dataLoading');
    const res = yield handoverCancelServer(action.param);
    if (res.code === '0') {
      message.success(t('取消交接成功'), 2000);
      yield ctx.init();
      yield ctx.getData({
        param: {},
      });
      setTimeout(() => {
        classFocus('pickContainerCode');
      });
    } else {
      modal.error({ content: res.msg });
    }
  },
};
