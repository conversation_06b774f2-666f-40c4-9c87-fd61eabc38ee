.col {
  display: flex;
  justify-content: space-between;
}
.arrow {
  position: relative;
  top: 2px;
  width: 15px;
  height: 15px;
  margin-left: 5px;
}
.label {
  font-size: 15px;
  color: #666c7c;
  font-weight: 600;
}
.value {
  font-size: 15px;
  margin-left: 5px;
  font-weight: 600;
}
.content {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  background-color: #fff;
  /* padding: 0 15px; */
}
.title {
  line-height: 32px;
  background-color: #f4f5f8;
  padding: 0 15px;
}
.num {
  color: #197afa;
  font-size: 16px;
}
.disabledNum {
  font-size: 16px;
  color:#bcbdc7;
}
.statusName {
  /* width: 50px; */
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #333e59;
}
.statusName .complete {
  background-color:#65db97;
}
.statusName .uncomplete {
  background-color:#f13120;
}
.statusName .icon {
  width: 4px;
  height: 14px;
  border-radius: 2px;
  margin: 5px 5px 0 0;
  flex: 0 0 auto;
}
.boxNum {
  margin-right: 5px;
  font-size: 12px;
  color: #171a3a;
}
.scroll {
  height: 0;
  padding: 0 15px;
  flex: 1 1 auto;
  overflow-y: auto;
}
 .redTag {
  padding: 3px;
  margin-left: 5px;
  margin-top: 5px;
  display: inline-block;
  background-color: #fee8e9;
  color: #f85555;
  border-radius: 12px;
  font-size: 13px;
}