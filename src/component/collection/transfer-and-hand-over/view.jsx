import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { i18n, t } from '@shein-bbl/react';
import UserIntro from 'common/user-intro';
import IntroContext from '../../user-intro-page/IntroContext';
import store from './reducers';
import navStore from '../../nav/reducers';
import RowInfo from '../../common/row-info';
import Modal from '../../common/modal';
import Footer from '../../common/footer';
import {
  Header,
  FocusInput,
  FooterBtn,
  pages,
} from '../../common';
import styles from './style.css';

const { View } = pages;

class Container extends Component {
  componentDidMount() {
    store.init();
    store.getData({
      param: {},
    });
  }

  render() {
    const {
      dispatch,
      dataLoading,
      type, // 0：周转箱，1：托盘，2：集货库位
      headerTitle,
      pickContainerCode,
      scanContainerRsp,
      temporaryLocation,
      rank,
      total,
      siteTips,
      nationalLineTypeName,
      steps,
      wellenTypeName,
    } = this.props;
    return (
      <IntroContext.Consumer>
        {(context) => {
          context.changePageStore(store);
          return (
            <View>
              <Header title={headerTitle || t('集货拉货交接')} />
              {
                type === 0 &&
                (
                  <div>
                    <UserIntro
                      showIntro={context.showIntroVal}
                      steps={steps}
                      endHandle={() => {
                        navStore.changeData({ data: { showIntro: false } });
                        store.changeData({ data: { type: 1 } });
                        setTimeout(() => navStore.changeData({ data: { showIntro: true } }), 10);
                      }}
                    />
                    <Form>
                      <RowInfo
                        type="info"
                        label={t('今日完成箱数')}
                        content={scanContainerRsp.completeBoxNum}
                        extraStyle={{
                          height: '40px',
                          fontSize: '14px',
                          borderBottom: 'none',
                        }}
                        textExtraStyle={{
                          fontWeight: '700',
                          color: 'orange',
                        }}
                      />
                      <RowInfo
                        type="warn"
                        label={t('排名')}
                        content={`${rank}/${total}`}
                        extraStyle={{
                          height: '40px',
                          fontSize: '14px',
                          borderBottom: 'none',
                        }}
                        textExtraStyle={{
                          fontWeight: '700',
                          color: 'orange',
                        }}
                      />
                      <FocusInput
                        data-step="1"
                        data-intro={t('第一步，扫描拣货周转箱')}
                        placeholder={context.showIntroVal ? t('请扫描拣货周转箱') : ''}
                        value={pickContainerCode}
                        className="pickContainerCode"
                        label={t('拣货周转箱')}
                        autoFocus
                        disabled={dataLoading === 0 || context.showIntroVal}
                        onChange={(e) => {
                          store.changeData({
                            data: {
                              pickContainerCode: e.target.value,
                            },
                          });
                        }}
                        onPressEnter={() => {
                          if (pickContainerCode) {
                            store.getData({
                              param: {
                                pickContainerCode,
                              },
                            });
                          }
                        }}
                      />
                    </Form>
                    {
                      wellenTypeName && (
                        <div className={styles.redTag}>{wellenTypeName}</div>
                      )
                    }
                    {
                      siteTips && (
                        <>
                          {
                            siteTips.split('/').map((item) => <div className={styles.redTag} key={item}>{item}</div>)
                          }
                        </>
                      )
                    }
                    {
                      !!nationalLineTypeName && (
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'center',
                            marginTop: 10,
                          }}
                        >
                          <div style={{ color: 'rgb(209, 136, 58)', border: '1px solid rgb(209, 136, 58)', padding: '5px 10px' }}>
                            {nationalLineTypeName}
                          </div>
                        </div>
                      )
                    }
                  </div>
                )
              }
              {
                type === 1 &&
                (
                  <div>
                    <UserIntro
                      showIntro={context.showIntroVal}
                      steps={steps}
                      finishHandle={() => {
                        navStore.changeData({ data: { showIntro: false } });
                        store.init();
                        store.getData({
                          param: {},
                        });
                      }}
                    />
                    <Form style={{ backgroundColor: '#fff', paddingBottom: '20px' }}>
                      <RowInfo
                        type="info"
                        label={t('波次号')}
                        content={scanContainerRsp.wellenCode}
                        extraStyle={{
                          height: '40px',
                          fontSize: '14px',
                          borderBottom: 'none',
                        }}
                        textExtraStyle={{
                          fontWeight: '700',
                        }}
                      />
                      <RowInfo
                        type="info"
                        label={t('箱数')}
                        content={scanContainerRsp.handoverBoxNum}
                        extraStyle={{
                          height: '40px',
                          fontSize: '14px',
                          borderBottom: 'none',

                        }}
                        textExtraStyle={{
                          fontWeight: '700',
                          color: 'orange',
                        }}
                      />
                    </Form>
                    <Form>
                      <FocusInput
                        data-step="2"
                        data-intro={t('第二步，扫描一分储位号')}
                        placeholder={context.showIntroVal ? t('请扫描待一分暂存位') : ''}
                        value={temporaryLocation}
                        autoFocus
                        className="temporaryLocation"
                        label={t('待一分暂存位')}
                        disabled={dataLoading === 0 || context.showIntroVal}
                        onChange={(e) => {
                          store.changeData({
                            data: {
                              temporaryLocation: e.target.value,
                            },
                          });
                        }}
                        onPressEnter={() => {
                          if (temporaryLocation) {
                            store.scanLocation({
                              param: {
                                id: scanContainerRsp.id,
                                temporaryLocation,
                              },
                            });
                          }
                        }}
                      />
                    </Form>
                    {
                      wellenTypeName && (
                        <div className={styles.redTag}>{wellenTypeName}</div>
                      )
                    }
                    {
                      siteTips && (
                        <>
                          {
                            siteTips.split('/').map((item) => <div className={styles.redTag} key={item}>{item}</div>)
                          }
                        </>
                      )
                    }
                    {
                      !!nationalLineTypeName && (
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'center',
                            marginTop: 10,
                          }}
                        >
                          <div style={{ color: 'rgb(209, 136, 58)', border: '1px solid rgb(209, 136, 58)', padding: '5px 10px' }}>
                            {nationalLineTypeName}
                          </div>
                        </div>
                      )
                    }
                  </div>
                )
              }
              <Footer
                dispatch={dispatch}
                beforeBack={(back) => {
                  store.reset();
                  back();
                }}
              >
                {
                  type === 1 && (
                    <FooterBtn
                      onClick={() => {
                        Modal.confirm({
                          content: (
                            <div>
                              {t('确认取消领取该任务吗？')}
                            </div>
                          ),
                          onOk: () => {
                            store.handoverCancel({
                              param: {
                                id: scanContainerRsp.id,
                              },
                            });
                          },
                          autoFocusButton: null,
                        });
                      }}
                    >
                      {t('取消交接')}
                    </FooterBtn>
                  )
                }
              </Footer>
            </View>
          );
        }}
      </IntroContext.Consumer>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  rank: PropTypes.number,
  total: PropTypes.number,
  dataLoading: PropTypes.number,
  type: PropTypes.number,
  headerTitle: PropTypes.string,
  pickContainerCode: PropTypes.string,
  scanContainerRsp: PropTypes.shape(),
  temporaryLocation: PropTypes.string,
  siteTips: PropTypes.string,
  nationalLine: PropTypes.string,
  wellenTypeName: PropTypes.string,
  steps: PropTypes.arrayOf(PropTypes.object),
};

export default i18n(Container);
