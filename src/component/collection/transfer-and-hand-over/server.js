import { sendPostRequest } from '../../../lib/public-request';

export const scanContainerServer = (param) => sendPostRequest({
  url: '/main_collect/handover_scan_container',
  param,
}, process.env.WOS_URI);
export const scanLocationServer = (param) => sendPostRequest({
  url: '/main_collect/handover_scan_location',
  param,
}, process.env.WOS_URI);
export const handoverCancelServer = (param) => sendPostRequest({
  url: '/main_collect/handover_cancel',
  param,
}, process.env.WOS_URI);

/**
 * 查询当前用户排名
 * @param param
 * @returns {*}
 */
export const queryRankInfoApi = (param) => sendPostRequest({
  baseUrl: process.env.WKB,
  url: '/rank/query',
  param,
});
