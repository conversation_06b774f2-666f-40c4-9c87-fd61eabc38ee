import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { CellsMenu, Header, Footer } from '../../common';
import store from './reducers';

class Container extends Component {
  componentWillMount() {
    store.init();
  }

  render() {
    const {
      btnList,
      headTitle,
    } = this.props;
    return (
      <div>
        <Header title={headTitle} />
        <CellsMenu
          cells={btnList}
        />
        <Footer />
      </div>
    );
  }
}

Container.propTypes = {
  btnList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  headTitle: PropTypes.string.isRequired,
};

export default Container;
