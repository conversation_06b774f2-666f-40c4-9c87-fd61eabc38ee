import assign from 'object-assign';
import React from 'react';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { select } from 'redux-saga/effects';
import {
  subScanContainer,
  subScanPallet,
  closePalletServer,
  getSubCollectBoxNumApi,
  getTaskInfoApi,
  closePalletAPI,
} from './server';
import Modal from '../../common/modal';
import message from '../../common/message';
import { classFocus, getHeaderTitle, apolloFormatObj } from '../../../lib/util';
import { getRankInfoApi, getApolloConfigAPI } from '../../../server/basic/common';
// import CollectionFinish, { PartFinish } from './jsx/showModal';
import errorAudio from '../../../source/audio/delete.mp3';

const audioError = new Audio(errorAudio);
audioError.load();

// resultType  1：未绑定托盘界面 2：已绑定托盘界面 3：周转箱子仓集货完成界面 4：子仓全部集货完成界面
const defaultState = {
  pickContainerCode: '',
  pickContainerCodeDisabled: false,
  palletCodeDisabled: false,
  scanPalletCodeDisabled: false,
  resultType: 0,
  closeLoading: 1,
  info: {
    pickContainerCode: '',
    palletCode: '',
    subCollectLocation: '',
    wellenCode: '',
    palletCount: '',
    containerCount: '',
    scanPalletCode: '',
    combineOrderMsg: '', // 合包订单提示
  },
  // 0 进行中 1 成功 2 失败
  collectTips: '', // 集货提示
  submitLoading: 1,
  isShowCollectionFinish: false,
  showCollectionFinishInfo: {},
  isShowPartFinish: false,
  showPartFinishInfo: {},
  specErrTip: '',
  rank: '-', // 排名
  total: '-',
  subCollectionBoxCount: undefined, // 已集货箱数
  isAutoPad: false,
  pickContainerCodeDisplay: '',
  nationalLine: '',
  isSubWholeCollect: false, // 整波集货是否展示
  siteTips: '', // 订单来源
  subCollectLocationDisabled: false,
  recommendMode: 0, // 集货库位推荐模式: 0-默认空; 1-推荐; 2-强制推荐; 3-不推荐
  bindPalletControlMode: 0, // // 绑定托盘控制模式: 0-默认空; 1-绑定; 2-不绑定
  firstAckMode: 0, // 库位首次确认模式: 0-默认空; 1-填充可修改; 2-自动确认; 3-不填充
  nonFirstAckMode: 0, // 库位非首次确认模式: 0-默认空; 1-扫描确认; 2-自动确认
  waitingSubCollectionContainer: '',
  isOldProcess: true, // 是否走老流程
  wellenMarkList: [], // 波次特殊信息提示
  steps: [],
  taskInfo: {}, // 当前用户对应子仓集货任务及明细信息
  degradeScanUpgrade: false, // 降级开关
  showDialog: false,
  inputPalletCode: '',
};

const msgTypeMap = {
  2: 'error',
  3: 'warning',
};

export default {
  defaultState,

  $init: () => defaultState,

  changeData(draft, action) {
    assign(draft, action.data);
  },

  changeInfo(draft, action) {
    assign(draft, {
      info: assign({}, draft.info, action.data),
    });
  },
  // 初始当前页面数据
  resetPageStore(draft) {
    assign(draft, defaultState, { headerTitle: draft.headerTitle });
  },

  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    // 获取已集箱数
    yield ctx.getSubCollectBoxNum();
    // 获取页面降级开关
    yield ctx.getDegradeConfig();
  },

  // 获取页面降级开关 DEGRADE_SUB_COLLECTION
  * getDegradeConfig(action, ctx) {
    try {
      const res = yield getApolloConfigAPI({ params: ['DEGRADE_SUB_COLLECTION'] });
      const apolloConfigFormatRes = apolloFormatObj(res.info);

      if (res.code === '0' && apolloConfigFormatRes?.DEGRADE_SUB_COLLECTION === '1') {
        yield ctx.changeData({ data: { degradeScanUpgrade: apolloConfigFormatRes?.DEGRADE_SUB_COLLECTION === '1' } });
      } else {
        // 获取排名。后端接口降级
        yield ctx.getRankInfo();
      }
    } catch (e) {
      console.error(e);
    }
  },

  * getRankInfo(action, ctx) {
    // 如果开了降级，就不调接口
    const { degradeScanUpgrade } = yield '';
    if (degradeScanUpgrade) { return; }
    const res = yield getRankInfoApi({ rankTypeCode: 8 });
    if (res.code === '0') {
      yield ctx.changeData({ data: { rank: res.info.rank, total: res.info.total } });
    } else {
      Modal.error({ content: res.msg });
    }
  },

  * getSubCollectBoxNum(action, ctx) {
    const res = yield getSubCollectBoxNumApi();
    if (res.code === '0') {
      yield ctx.changeData({ data: { subCollectionBoxCount: res.info.subCollectionBoxCount } });
    } else {
      Modal.error({ content: res.msg });
    }
  },

  * getTaskInfo(action, ctx) {
    const res = yield getTaskInfoApi();
    if (res.code === '0') {
      yield ctx.changeData({ data: { taskInfo: res.info || {} } });
    } else {
      // Modal.error({ content: res.msg });
      console.info(res.msg);
    }
  },

  * scanContainer(action, ctx) {
    yield ctx.changeData({
      data: {
        pickContainerCodeDisabled: true,
      },
    });
    const result = yield subScanContainer(action.props);
    yield ctx.changeData({
      data: {
        isShowCollectionFinish: false,
        showCollectionFinishInfo: {},
        isShowPartFinish: false,
        showPartFinishInfo: {},
        // specErrTip: '',
        nationalLine: '',
      },
    });
    if (result.code === '0') {
      const {
        bindPalletControlMode,
        recommendMode,
        firstAckMode,
        nonFirstAckMode,
        msgType,
        palletCode,
        subCollectLocation,
        waitingSubCollectionContainer,
        isSubCollectFirstTip,
        isAutoPad,
        resultType,
        siteTips,
        wellenTypeName,
        collectTips,
        packType,
        isSubWholeCollect,
        nationalLine,
        combineOrderMsg,
        isEdit,
        wellenMarkList,
      } = result.info;
      // 是否走老流程, 为了兼容新老流程，当这4个值都等于0 || null时，
      // 表示走老流程，否则新流程，这4个字段的意思在defaultState中有注释
      const isOldProcess =
        (!bindPalletControlMode && !recommendMode && !firstAckMode && !nonFirstAckMode) || false;

      yield ctx.changeData({
        data: {
          nationalLine,
          bindPalletControlMode,
          recommendMode,
          firstAckMode,
          nonFirstAckMode,
          recommendPallet: palletCode,
          recommendLocation: subCollectLocation,
          waitingSubCollectionContainer,
          isOldProcess,
          isSubCollectFirstTip,
          isAutoPad,
          resultType,
          siteTips,
          wellenTypeName,
          collectTips,
          packType,
          isSubWholeCollect,
          combineOrderMsg,
          wellenMarkList,
        },
      });

      let isCancel = false;

      // 新流程
      if (!isOldProcess) {
        /*
        （1）msgType: 2：失败提示 3：警告提示 4：弹窗提示 5：波次分流提示（二次确认)
        （2）2和3是message提示，4和5是Modal提示.
        （3）msgType = 2 || 3 || 4, 清空周转箱，光标集中
          msgType = 5时点取消时，清空周转箱，光标定位。点确认时，继续往下走
        */
        if (msgType && [2, 3, 4, 5].includes(msgType)) {
          const {
            msgTip,
          } = result.info;
          // message提示：2：失败提示 3：警告提示
          if (msgType === 2 || msgType === 3) {
            yield ctx.changeData({
              data: { pickContainerCode: '', pickContainerCodeDisabled: false },
            });
            classFocus('pickContainerCode');
            message[msgTypeMap[msgType]](msgTip, 3000);
          }
          // 4：弹窗提示
          if (msgType === 4) {
            // 清空周转箱，光标集中到周转箱
            yield ctx.changeData({
              data: { pickContainerCode: '', pickContainerCodeDisabled: false },
            });
            Modal.error({
              content: msgTip,
              className: 'pickContainerCode',
            });
          }
          // 5：波次分流提示（二次确认)，点取消时，清空周转箱，光标定位。点确认时，继续往下走
          if (msgType === 5) {
            isCancel = yield new Promise((r) => {
              Modal.confirm({
                content: msgTip,
                onOk: () => r(false),
                onCancel: () => r(true),
                className: 'pickContainerCode',
              });
            });
            // 取消
            if (isCancel) {
              // 清空周转箱，光标集中到周转箱
              yield ctx.changeData({
                data: { pickContainerCode: '', pickContainerCodeDisabled: false },
              });
            }
          }
        }
        // 不是取消 且 msgType不等于2，3，4的情况下继续走的新流程,取消或者msgType = 2,3,4表示流程中断，所以不需要走下面的逻辑
        if (!isCancel && ![2, 3, 4].includes(msgType)) {
          // 判断是否需要自动扫托盘。
          if (
            // eslint-disable-next-line max-len
            // isSubCollectFirstTip = true:首次提交 && bindPalletControlMode === 2:不绑定托盘情况 && firstAckMode = 2:自动提
            ((isSubCollectFirstTip && bindPalletControlMode === 2 && firstAckMode === 2) ||
            // eslint-disable-next-line max-len
            // isSubCollectFirstTip = false:非首次提交 && bindPalletControlMode === 2:不绑定托盘情况 && nonFirstAckMode = 2:非首次自动提交
            (!isSubCollectFirstTip && bindPalletControlMode === 2 && nonFirstAckMode === 2)) &&
            // resultType === 3和resultType === 4分别表示周转箱子仓集货完和子仓全部集货完成，这2种情况下，不需要自动扫托盘
            resultType !== 3 && resultType !== 4
          ) {
            // 自动调用扫描托盘接口
            yield ctx.scanPallet({
              props: {
                palletCode,
                pickContainerCode: action.props.pickContainerCode,
                subCollectLocation,
              },
              isAutoScanPallte: true, // 自动扫托盘
            });
          } else {
            // 不自动扫描托盘的情况下走下面的逻辑，因为扫完托盘就完成了，完成情况下不需要走该逻辑
            if (resultType === 1 || resultType === 2) {
              yield ctx.changeInfo({
                data: { palletCode: '' },
              });
              // isSubCollectFirstTip = true，表示首箱
              if (isSubCollectFirstTip) {
                // firstAckMode：0-默认空;
                switch (firstAckMode) {
                  // 1-填充可修改：库位输入框写入推荐值，焦点在库位;
                  case 1:
                    yield ctx.changeInfo({
                      data: { subCollectLocation },
                    });
                    yield ctx.changeData({
                      data: { subCollectLocationDisabled: false },
                    });
                    classFocus('subCollectLocation');
                    break;
                  // 2-自动确认：库位输入框写入推荐值，焦点在托盘，锁定不可输入;
                  case 2:
                    yield ctx.changeInfo({
                      data: { subCollectLocation },
                    });
                    // 不管配置的是填充可修改，还是自动确认，都是先判断库位号有没
                    // 有返回库位就按以前的，库位返回为空，就直接定位在集货库位输入框
                    if (subCollectLocation) {
                      yield ctx.changeData({
                        data: { subCollectLocationDisabled: true, palletCodeDisabled: false },
                      });
                      classFocus('palletCode');
                    } else {
                      yield ctx.changeData({
                        data: { subCollectLocationDisabled: false },
                      });
                      classFocus('subCollectLocation');
                    }
                    break;
                  // 3-不填充：焦点在库位
                  case 3:
                    yield ctx.changeInfo({
                      data: { subCollectLocation: '' },
                    });
                    yield ctx.changeData({
                      data: { subCollectLocationDisabled: false },
                    });
                    classFocus('subCollectLocation');
                    break;
                  default:
                    break;
                }
              } else { // 非首箱
                // nonFirstAckMode： 0-默认空;
                switch (nonFirstAckMode) {
                  // 1-扫描确认: 清空库位，焦点在库位;
                  case 1:
                    yield ctx.changeInfo({
                      data: { subCollectLocation: '' },
                    });
                    yield ctx.changeData({
                      data: { subCollectLocationDisabled: false },
                    });
                    classFocus('subCollectLocation');
                    break;
                  // 2-自动确认：库位输入框写入推荐值，焦点在托盘，锁定不可输入
                  case 2:
                    yield ctx.changeInfo({
                      data: { subCollectLocation },
                    });
                    // 不管配置的是填充可修改，还是自动确认，都是先判断库位号有没
                    // 有返回库位就按以前的，库位返回为空，就直接定位在集货库位输入框
                    if (subCollectLocation) {
                      yield ctx.changeData({
                        data: { subCollectLocationDisabled: true, palletCodeDisabled: false },
                      });
                      classFocus('palletCode');
                    } else {
                      yield ctx.changeData({
                        data: { subCollectLocationDisabled: false },
                      });
                      classFocus('subCollectLocation');
                    }
                    break;
                  default:
                    break;
                }
              }
            }
          }
        }
      } else {
        if (resultType === 1 || resultType === 2) {
          const datas = {
            info: { subCollectLocation, isEdit, palletCode: '' }, // 不需要填充托盘号
            // pickContainerCodeDisplay: '',
          };
          yield ctx.changeData({
            data: datas,
          });
          if (result.info.isEdit !== 0) {
            classFocus('subCollectLocation');
          } else if (result.info.isEdit === 0) {
            classFocus('palletCode');
          }
        }
      }

      // 新流程前提下，msgType === 5并且点取消 或 msgType === 2 或  msgType === 4；清空周转箱，焦点集中
      if (!isOldProcess && ((msgType === 5 && isCancel) || msgType === 2 || msgType === 4)) {
        yield ctx.changeData({
          data: {
            resultType: 0,
            pickContainerCode: '',
            pickContainerCodeDisabled: false,
            isShowCollectionFinish: false,
            isShowPartFinish: false,
            showCollectionFinishInfo: result.info,
          },
        });
        classFocus('pickContainerCode');
      } else {
        // 老流程走下面的逻辑，新流程也可能会走

        // resultType === 3表示周转箱子仓集货完
        if (result.info.resultType === 3) {
          yield ctx.init();
          yield ctx.changeData({
            data: {
              isShowPartFinish: true,
              showPartFinishInfo: {
                pickContainerCode: action.props.pickContainerCode,
                subCollectLocation: result.info.subCollectLocation,
                palletCode: result.info.palletCode,
              },
              showCollectionFinishInfo: result.info,
              nationalLine,
              waitingSubCollectionContainer,
              pickContainerCode: '',
              pickContainerCodeDisabled: false,
              isSubCollectFirstTip,
              isAutoPad,
              resultType,
              siteTips,
              wellenTypeName,
              collectTips,
              packType,
              isSubWholeCollect,
              combineOrderMsg,
              wellenMarkList,
            },
          });
          classFocus('pickContainerCode');
        } else if (result.info.resultType === 4) {
          // resultType === 4表示子仓全部集货完成
          yield ctx.init();
          yield ctx.changeData({
            data: {
              isShowCollectionFinish: true,
              showCollectionFinishInfo: result.info,
              pickContainerCodeDisplay: action.props.pickContainerCode,
              nationalLine,
              pickContainerCode: '',
              pickContainerCodeDisabled: false,
              isAutoPad,
              resultType,
              siteTips,
              wellenTypeName,
              collectTips,
              packType,
              isSubWholeCollect,
              isSubCollectFirstTip,
              combineOrderMsg,
              wellenMarkList,
            },
          });
          classFocus('pickContainerCode');
        }
      }
    } else {
      // 报错提示
      audioError.play();
      // 特殊处理的报错提示
      if (['502177', '502102', '502107', '502108', '502179', '502109', '502151', '502178'].includes(result.code)) {
        Modal.error({
          title: result.msg,
          className: 'pickContainerCode',
        });
        yield ctx.refreshData({
          props: assign({}, action.props, { isBaseInfo: 1 }),
        });
      } else if (['502169', '502176', '502161', '502162', '500383'].includes(result.code)) {
        // 重置数据，焦点置于周转箱
        message.error(result.msg, 3000);
        yield ctx.refreshData({
          props: assign({}, action.props, { isBaseInfo: 1 }),
        });
        classFocus('pickContainerCode');
      } else {
        Modal.info({
          title: result.msg,
          onOk: () => {
            classFocus('pickContainerCode');
          },
        });
      }
      yield ctx.changeData({
        data: {
          pickContainerCodeDisplay: '',
          isAutoPad: false,
          pickContainerCode: '',
          pickContainerCodeDisabled: false,
          resultType: 0,
        },
      });
    }
    yield ctx.getTaskInfo();
  },

  * scanPallet(action, ctx) {
    yield ctx.changeData({
      data: {
        palletCodeDisabled: true,
        scanPalletCodeDisabled: true,
      },
    });
    const result = yield subScanPallet(action.props);
    yield ctx.changeData({
      data: {
        isShowCollectionFinish: false,
        showCollectionFinishInfo: {},
        isShowPartFinish: false,
        showPartFinishInfo: {},
      },
    });
    if (result.code === '0') {
      const {
        isSubWholeCollect,
        packType,
        siteTips,
        wellenTypeName,
        collectTips,
        isSubCollectFirstTip,
        isAutoPad,
      } = yield select((state) => state['collection/sub-collection']);
      const {
        nationalLine,
        waitingSubCollectionContainer,
        isSubCollectEndTip,
        subCollectEndTip,
        containerCount,
        palletCode,
        palletCount,
        pickContainerCode,
        subCollectLocation,
        wellenCode,
        combineOrderMsg,
        wellenMarkList,
      } = result.info || {};
      if (isSubCollectEndTip) {
        Modal.info({
          content: subCollectEndTip,
          className: 'pickContainerCode',
        });
      }
      const datas = {
        info: {
          containerCount,
          palletCode,
          palletCount,
          pickContainerCode,
          subCollectLocation,
          wellenCode,
          combineOrderMsg,
        },
        nationalLine,
        waitingSubCollectionContainer,
        combineOrderMsg,
        isSubWholeCollect,
        packType,
        siteTips,
        wellenTypeName,
        showCollectionFinishInfo: result.info,
        collectTips,
        isSubCollectFirstTip,
        isAutoPad,
        wellenMarkList,
      };
      yield ctx.init();
      if (result.info.isSubFinish) {
        datas.resultType = 4;
        yield ctx.changeData({
          data: {
            isShowCollectionFinish: true,
          },
        });
        if (result.info.collectTips) {
          Modal.info({
            title: (
              <div
                style={{
                  color: 'red', textAlign: 'center', padding: 10, fontWeight: 'bold', fontSize: 20,
                }}
              >
                {result.info.collectTips}
              </div>
            ),
            onOk: () => {
              classFocus('pickContainerCode');
            },
          });
        }
      } else {
        datas.resultType = 3;
        yield ctx.changeData({
          data: {
            isShowPartFinish: true,
            showPartFinishInfo: result.info,
          },
        });
      }
      yield ctx.changeData({
        data: datas,
      });
      classFocus('pickContainerCode');
      // 已自动关托，则跳回初始页
      if (result.info.isAutoClosePallet) {
        Modal.info({
          content: t('托盘已关闭'),
          className: 'pickContainerCode',
        });
        yield ctx.changeData({
          data: assign({}, datas, {
            isShowPartFinish: true,
            isShowCollectionFinish: false,
            showPartFinishInfo: result.info,
            resultType: 3,
          }),
        });
      }
    } else {
      audioError.play();
      const { isAutoScanPallte } = action;
      if (isAutoScanPallte) {
        yield ctx.changeData({
          data: {
            subCollectLocationDisabled: false,
          },
        });
        yield ctx.changeInfo({
          data: { subCollectLocation: '' },
        });
      } else {
        yield ctx.changeData({
          data: {
            palletCodeDisabled: false,
          },
        });
        yield ctx.changeInfo({
          data: { palletCode: '' },
        });
      }
      if (['502177', '502102', '502107', '502108', '502179', '502109', '502151', '502178'].includes(result.code)) {
        Modal.error({
          title: result.msg,
          className: isAutoScanPallte ? 'subCollectLocation' : 'palletCode',
        });
        yield ctx.refreshData({
          props: { isBaseInfo: 1, pickContainerCode: action.props.pickContainerCode },
        });
      } else if (['502169', '502176'].includes(result.code)) {
        message.error(result.msg, 3000);
        yield ctx.refreshData({
          props: { isBaseInfo: 1, pickContainerCode: action.props.pickContainerCode },
        });
        classFocus(isAutoScanPallte ? 'subCollectLocation' : 'palletCode');
      } else if (['2180'].includes(result.code)) {
        message.error(result.msg);
        yield ctx.changeInfo({
          data: { palletCode: '' },
        });
        classFocus('palletCode');
      } else if (['602125', '602126', '602110', '602124', '602289', '600750', '602108', '600122', '602179'].includes(result.code)) {
        // 602108:扫描库位必须为启用、空闲的子仓集货库位/整波集货库位
        // 600122:货位所属的仓库与用户绑定的仓库不一致
        // 602179  扫描库位对子仓为嚯嚯波兰子仓01与拣货周转箱对应出库作业子仓PO_SZC不一致
        // 这里面的code，都清空库位号，光标定在库位号
        message.error(result.msg);
        yield this.changeData({
          data: { subCollectLocationDisabled: false },
        });
        yield this.changeInfo({
          data: {
            palletCode: '',
            subCollectLocation: '',
          },
        });
        classFocus('subCollectLocation');
      } else {
        Modal.error({
          title: result.msg,
          className: isAutoScanPallte ? 'subCollectLocation' : 'palletCode',
        });
      }
    }
    yield ctx.getTaskInfo();
  },

  * closePallet(action, ctx) {
    markStatus('submitLoading');
    const result = yield closePalletServer(action.props);
    if (result.code === '0') {
      message.success(`${t('{}托盘已关闭', action.props.palletCode)}`);
      yield ctx.changeData({
        data: {
          resultType: 1,
          palletCode: '',
          palletCodeDisabled: false,
        },
      });
      classFocus('palletCode');
    } else {
      audioError.play();
      yield ctx.changeInfo({
        data: {
          palletCode: '',
        },
      });
      yield ctx.changeData({
        data: {
          palletCodeDisabled: false,
        },
      });
      Modal.error({
        title: result.msg,
        onOk: () => {
          // classFocus('scanPalletCode');
          classFocus('palletCode');
        },
      });
    }
  },
  // 重新调扫描周转箱的接口，入参加上isBaseInfo = 1，调这个接口为了刷新数据，后台(龙杜源)说，在报错的情况下他无法返回这些数据
  * refreshData(action, ctx) {
    const res = yield subScanContainer(assign({}, action.props, { isBaseInfo: 1 }));
    if (res.code === '0') {
      const {
        isSubWholeCollect,
        wellenTypeName,
        packType,
        combineOrderMsg,
        siteTips,
        collectTips,
        nationalLine,
        wellenMarkList,
      } = res.info;
      yield ctx.changeData({
        data: {
          isSubWholeCollect,
          wellenTypeName,
          packType,
          combineOrderMsg,
          siteTips,
          collectTips,
          nationalLine,
          showCollectionFinishInfo: res.info,
          wellenMarkList,
        },
      });
    } else {
      Modal.error({
        content: res.msg,
      });
    }
  },
  // 提交关托转运
  * postClosePallet(action, ctx) {
    markStatus('closeLoading');
    const res = yield closePalletAPI(action.param);
    if (res.code === '0') {
      // 页面初始化
      yield ctx.changeData({
        data: assign({}, defaultState),
      });
      yield ctx.init();
      yield this.getTaskInfo();
      Modal.success({
        content: t('托盘已关托'),
        onOk: () => {
          classFocus('pickContainerCode');
        },
      });
    } else {
      yield this.changeData({
        data: {
          inputPalletCode: '',
        },
      });
      // 错误的话弹框。
      Modal.error({
        content: res.msg,
        onOk: () => {
          classFocus('inputPalletCode');
        },
      });
    }
  },
};
