import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import UserIntro from 'common/user-intro';
import IntroContext from '../../../user-intro-page/IntroContext';
import Footer from '../../../common/footer';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import { classFocus } from '../../../../lib/util';
import error from '../../../../source/audio/delete.mp3';

const audio = new Audio(error);
audio.load();

function Details(props) {
  const {
    pickContainerCode,
    info,
    resultType,
    palletCodeDisabled,
    recommendMode,
    recommendLocation,
    recommendPallet,
    subCollectLocationDisabled,
    bindPalletControlMode,
    isOldProcess,
    isSubCollectFirstTip,
    steps,
  } = props;
  const {
    subCollectLocation,
    isEdit,
    palletCode,
  } = info;
  return (
    <IntroContext.Consumer>
      {(context) => {
        context.changePageStore(store);
        return (
          <div>
            <UserIntro
              showIntro={context.showIntroVal}
              steps={steps}
              finishHandle={() => {
                store.init();
              }}
            />
            <div>
              <Form>
                <FocusInput
                  placeholder={t('请扫描')}
                  autoFocus
                  value={pickContainerCode}
                  disabled
                  className="pickContainerCode"
                >
                  <label>{t('拣货周转箱')}</label>
                </FocusInput>

                <FocusInput
                  data-step="2"
                  data-intro={t('第二步，扫描储位号')}
                  placeholder={t('请扫描储位号')}
                  value={subCollectLocation}
                  disabled={(isOldProcess && (resultType === 2 || isEdit === 0)) ||
                    subCollectLocationDisabled || context.showIntroVal}
                  className="subCollectLocation"
                  importance
                  onChange={(e) => {
                    store.changeInfo({
                      data: {
                        subCollectLocation: e.target.value,
                      },
                    });
                  }}
                  onFocus={(e) => {
                    const obj = e.target;
                    obj.focus();
                    const len = obj.value.length;
                    if (typeof obj.selectionStart === 'number' && typeof obj.selectionEnd === 'number') {
                      obj.selectionStart = len;
                      obj.selectionEnd = len;
                    }
                  }}
                  onPressEnter={() => {
                    // 托盘输入框存在这光标跳过去，否则，扫托盘
                    if (bindPalletControlMode === 1 || bindPalletControlMode === 0) {
                      store.changeData({ data: { palletCode: '' } });
                      classFocus('palletCode');
                      return;
                    }
                    if (subCollectLocation) {
                      store.scanPallet({
                        props: {
                          pickContainerCode,
                          subCollectLocation,
                        },
                        isAutoScanPallte: true, // 自动扫托盘
                      });
                    }
                  }}
                  inputStyle={(resultType === 2 || (isOldProcess && isEdit === 0)) ? { color: '#0059CE', fontSize: '17px', fontWeight: 700 } : {}}
                >
                  <label>
                    {t('集货库位')}
                    {
                      recommendMode === 1 && (
                        <span style={{ color: 'red' }}>
                          <span>：{isSubCollectFirstTip ? t('推荐') : t('绑定')}</span>
                          {recommendLocation}
                        </span>
                      )
                    }
                  </label>
                </FocusInput>

                {
                  (isOldProcess || bindPalletControlMode === 1 || bindPalletControlMode === 0) && (
                    <FocusInput
                      data-step="3"
                      data-intro={t('第三步，扫描托盘号')}
                      placeholder={t('请扫描托盘号')}
                      value={palletCode || ''}
                      autoFocus={isOldProcess && isEdit === 0}
                      importance
                      disabled={palletCodeDisabled || context.showIntroVal}
                      className="palletCode"
                      onChange={(e) => {
                        store.changeInfo({
                          data: {
                            palletCode: e.target.value,
                          },
                        });
                      }}
                      onPressEnter={() => {
                        if (palletCode) {
                          store.scanPallet({
                            props: {
                              palletCode,
                              pickContainerCode,
                              // type: false,
                              subCollectLocation,
                            },
                            // type: 1,
                          });
                        }
                      }}
                      inputStyle={resultType === 2 ? { color: '#0059CE', fontSize: '17px', fontWeight: 700 } : {}}
                    >
                      <label>
                        {t('托盘')}
                        <span>
                          {
                      resultType === 1 ? (
                        <span style={{ color: 'red' }}>：{t('新托盘')}</span>
                      ) : ''
                    }
                          {
                            (resultType === 2 && recommendPallet) ? (
                              <span style={{ color: 'red' }}>：{t('绑定')}{recommendPallet}</span>
                            ) : ''
                          }
                        </span>
                      </label>
                    </FocusInput>
                  )
                }
              </Form>
            </div>

            <Footer
              beforeBack={(back) => {
                back();
              }}
            />
          </div>
        );
      }}
    </IntroContext.Consumer>
  );
}

Details.propTypes = {
  pickContainerCode: PropTypes.string,
  info: PropTypes.shape(),
  resultType: PropTypes.number,
  palletCodeDisabled: PropTypes.bool,
  bindPalletControlMode: PropTypes.number,
  recommendMode: PropTypes.number,
  recommendLocation: PropTypes.string,
  recommendPallet: PropTypes.string,
  subCollectLocationDisabled: PropTypes.bool,
  isOldProcess: PropTypes.bool,
  isSubCollectFirstTip: PropTypes.bool,
  steps: PropTypes.arrayOf(PropTypes.shape()),
};

export default Details;
