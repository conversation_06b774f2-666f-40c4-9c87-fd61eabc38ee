import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';

const CollectionFinish = (props) => {
  const {
    wellenCode,
    subCollectLocation,
    palletCount,
    containerCount,
    combineOrderMsg,
  } = props;
  return (
    <div>
      <div>
        {t('波次')}
        <span>{wellenCode}</span>
        {t('在库位')}
        <span>{subCollectLocation}</span>
        {t('已全部集货完成,托盘数(')}
        <span style={{ color: 'red', fontWeight: 700 }}>{palletCount}</span>
        {t('托),周转箱数(')}
        <span style={{ color: 'red', fontWeight: 700 }}>{containerCount}</span>
        {t('箱)')}
      </div>
      {combineOrderMsg ?
        <h1 style={{ fontSize: '18px', color: 'red' }}>{combineOrderMsg}</h1>
        : null
      }
    </div>
  );
};

export const PartFinish = (props) => {
  const {
    pickContainerCode,
    subCollectLocation,
    palletCode,
  } = props;
  return (
    <div style={{ textAlign: 'center', fontSize: '15px' }}>
      {t('周转箱')}
      <span style={{ color: 'red' }}>{pickContainerCode}</span>
      {t('在库位')}
      <span style={{ color: 'red' }}>{subCollectLocation}</span>
      {t('托盘')}
      <span style={{ color: 'red' }}>{palletCode}</span>
      {t('集货完成')}
    </div>
  );
};

PartFinish.propTypes = {
  pickContainerCode: PropTypes.string,
  subCollectLocation: PropTypes.string,
  palletCode: PropTypes.string,
};

CollectionFinish.propTypes = {
  wellenCode: PropTypes.string,
  subCollectLocation: PropTypes.string,
  palletCount: PropTypes.string,
  containerCount: PropTypes.string,
  combineOrderMsg: PropTypes.string,
};

export default CollectionFinish;
