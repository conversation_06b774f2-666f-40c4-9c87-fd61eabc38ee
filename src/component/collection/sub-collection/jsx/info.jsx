import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages/components/form';
import { t } from '@shein-bbl/react';
import RowInfo from '../../../common/row-info';

const Info = (props) => {
  const {
    rank,
    total,
    subCollectionBoxCount,
  } = props;

  const data = [{
    label: t('已集货箱数'),
    content: subCollectionBoxCount,
    type: 'info',
  }, {
    label: t('排名'),
    content: `${rank}/${total}`,
    type: 'warn',
  }];

  return (
    <Form>
      {data.map(item => (
        <RowInfo
          key={item.label}
          extraStyle={{
            borderBottom: 'none',
          }}
          label={item.label}
          content={item.content}
          type={item.type}
        />
      ))}
    </Form>
  );
};

Info.propTypes = {
  rank: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  total: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  subCollectionBoxCount: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
};

export default Info;
