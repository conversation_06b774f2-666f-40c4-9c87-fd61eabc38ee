import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import UserIntro from 'common/user-intro';
import IntroContext from '../../../user-intro-page/IntroContext';
import store from '../reducers';
import Footer from '../../../common/footer';
import FocusInput from '../../../common/focus-input';

import Info from './info';

function Entry(props) {
  const {
    pickContainerCode,
    pickContainerCodeDisabled,
    steps,
  } = props;

  return (
    <IntroContext.Consumer>
      {(context) => {
        context.changePageStore(store);
        return (
          <div>
            <div>
              <UserIntro
                showIntro={context.showIntroVal}
                steps={steps}
                endHandle={() => store.changeData({ data: { resultType: 1 } })}
              />
              <Info {...props} />
              <Form>
                <FocusInput
                  data-step="1"
                  data-intro={t('第一步，扫描集货周转箱')}
                  placeholder={t('请扫描拣货周转箱')}
                  autoFocus
                  value={pickContainerCode}
                  disabled={pickContainerCodeDisabled || context.showIntroVal}
                  className="pickContainerCode"
                  onChange={(e) => {
                    store.changeData({
                      data: {
                        pickContainerCode: e.target.value,
                      },
                    });
                  }}
                  onPressEnter={() => {
                    if (pickContainerCode) {
                      store.scanContainer({
                        props: {
                          pickContainerCode,
                        },
                      });
                    }
                  }}
                >
                  <label>{t('拣货周转箱')}</label>
                </FocusInput>
              </Form>
            </div>
            <Footer />
          </div>
        );
      }}
    </IntroContext.Consumer>
  );
}

Entry.propTypes = {
  pickContainerCode: PropTypes.string,
  pickContainerCodeDisabled: PropTypes.bool,
  steps: PropTypes.arrayOf(PropTypes.shape()),
};

export default Entry;
