import { sendPostRequest } from '../../../lib/public-request';

export const subScanContainer = (param) => sendPostRequest({
  url: '/sub_collection/sub_scan_container',
  param,
}, process.env.WOS_URI);

export const subScanPallet = (param) => sendPostRequest({
  url: '/sub_collection/sub_scan_pallet',
  param,
}, process.env.WOS_URI);

export const closePalletServer = (param) => sendPostRequest({
  url: '/sub_collection/close_pallet',
  param,
}, process.env.WOS_URI);

/**
 * pda子仓集货获取用户当日子仓集货箱数
 * @param param
 */
export const getSubCollectBoxNumApi = (param) => sendPostRequest({
  url: '/sub_collection/sub_collection_box_num',
  param,
}, process.env.WOS_URI);

/**
 * 当前用户对应子仓集货任务及明细信息
 * @param param
 */
export const getTaskInfoApi = (param) => sendPostRequest({
  url: '/sub_collection/show/task_info',
  param,
}, process.env.WOS_URI);

// 提交关托盘
export const closePalletAPI = (param) => sendPostRequest({
  url: '/sub_collection/two_step_collect_manual_close_pallet',
  param,
}, process.env.WOS_URI);
