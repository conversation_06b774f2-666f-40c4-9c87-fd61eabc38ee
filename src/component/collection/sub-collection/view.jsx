import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import { Dialog } from 'react-weui/build/packages';
import {
  FocusInput,
} from 'common';
import { classFocus } from 'lib/util';
import store from './reducers';
import Header from '../../common/header';
import Entry from './jsx/entry';
import Details from './jsx/details';
import style from './style.css';
import FooterBtn from '../../common/footer-btn';
import Footer from '../../common/footer';
import Modal from '../../common/modal';

class Container extends Component {
  componentDidMount() {
    store.init();
    store.getTaskInfo();
  }

  render() {
    const {
      resultType,
      isShowCollectionFinish,
      showCollectionFinishInfo,
      isShowPartFinish,
      showPartFinishInfo,
      collectTips,
      headerTitle,
      // specErrTip,
      isAutoPad,
      pickContainerCodeDisplay,
      wellenTypeName,
      siteTips,
      packType,
      nationalLine,
      isSubWholeCollect,
      waitingSubCollectionContainer,
      isSubCollectFirstTip,
      combineOrderMsg,
      wellenMarkList,
      taskInfo,
      dispatch,
      showDialog,
      inputPalletCode,
    } = this.props;
    const {
      wellenCode,
      palletCount,
      containerCount,
      // combineOrderMsg,
      subCollectLocation,
    } = showCollectionFinishInfo;
    const {
      pickContainerCode,
      palletCode,
    } = showPartFinishInfo;
    const subCollectLocationText = showPartFinishInfo.subCollectLocation;
    return (
      <div>
        <Header title={headerTitle || t('子仓集货')}>
          <div
            onClick={() => {
              store.changeData({
                data: {
                  showDialog: true,
                  inputPalletCode: '',
                },
              });
              classFocus('inputPalletCode');
            }}
          >{t('关托')}
          </div>
        </Header>
        <Dialog
          title={t('扫描关托')}
          show={showDialog}
          buttons={[{
            label: t('取消'),
            type: 'primary',
            onClick: () => {
              store.changeData({
                data: {
                  showDialog: false,
                },
              });
              classFocus('pickContainerCode');
            },
          }]}
        >
          <div style={{ display: 'flex', lineHeight: 0 }}>
            <div>
              <FocusInput
                style={{
                  backgroundColor: 'rgba(189, 166, 166, 0.08)',
                  borderRadius: '5px',
                  padding: '5px 10px',
                }}
                className="inputPalletCode"
                placeholder={t('请输入托盘号')}
                value={inputPalletCode}
                onChange={(e) => {
                  store.changeData({
                    data: {
                      inputPalletCode: e.target.value,
                    },
                  });
                }}
                onPressEnter={() => {
                  if (inputPalletCode) {
                    store.postClosePallet({
                      param: {
                        palletCode: inputPalletCode,
                        warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
                      },
                    });
                  } else {
                    // 错误的话弹框。
                    Modal.error({
                      content: t('请扫描正确的托盘号'),
                      onOk: () => {
                        classFocus('inputPalletCode');
                      },
                    });
                  }
                }}
              />
            </div>
          </div>
        </Dialog>
        {
          (resultType === 1 || resultType === 2) ?
            <Details {...this.props} />
            :
            <Entry {...this.props} />
        }
        {
          !isShowCollectionFinish ? null
            : (
              <div style={{ padding: 10 }}>
                <div>
                  {t('波次')}
                  <span style={{ color: 'red' }}>{wellenCode}</span>
                  {t('在库位')}
                  <span style={{ color: 'red' }}>{subCollectLocation}</span>
                  {t('已全部集货完成,托盘数(')}
                  <span style={{ color: 'red', fontWeight: 700 }}>{palletCount}</span>
                  {t('托),周转箱数(')}
                  {isAutoPad ? (
                    <span style={{ color: 'red', fontWeight: 700, fontSize: '24px' }}>{containerCount}</span>
                  ) : (
                    <span style={{ color: 'red', fontWeight: 700 }}>{containerCount}</span>
                  )}
                  {t('箱)')} <span style={{ color: 'red', fontSize: '18px' }}>{pickContainerCodeDisplay}</span>
                </div>
              </div>
            )
        }
        {
          !isShowPartFinish ? null
            : (
              <div style={{ padding: 10 }}>
                {t('周转箱')}
                <span style={{ color: 'red' }}>{pickContainerCode}</span>
                {t('在库位')}
                <span style={{ color: 'red' }}>{subCollectLocationText}</span>
                <span>
                  {
                    palletCode && (
                      <span>{t('托盘')}<span style={{ color: 'red' }}>{palletCode}</span></span>
                    )
                  }
                </span>
                {t('集货完成')}，{t('波次')}
                <span style={{ color: 'red' }}>{wellenCode}</span>
                {
                  Number(waitingSubCollectionContainer) === 0 ? t('还有未完成的拣货箱') : (
                    <span>
                      {t('还有')}
                      <span style={{ color: 'red' }}>{waitingSubCollectionContainer}</span>
                      {t('箱待集货')}
                    </span>
                  )
                }
              </div>
            )
        }
        {
          isSubCollectFirstTip && (
            <div className={style.redTag}>{t('首箱')}</div>
          )
        }
        {
          wellenMarkList && wellenMarkList.length > 0 && (
            <>
              {
                wellenMarkList.map((item) => <div key={item} className={style.redTag}>{item}</div>)
              }
            </>
          )
        }
        {
          isSubWholeCollect && (
            <div className={style.redTag}>{t('整波集货')}</div>
          )
        }
        {
          wellenTypeName && (
            <div className={style.redTag}>{wellenTypeName}</div>
          )
        }
        {
          packType && (
            <div className={style.redTag}>{t('打包机打包')}</div>
          )
        }
        {
          combineOrderMsg && (
            <div className={style.redTag}>{combineOrderMsg}</div>
          )
        }
        {
          collectTips && (
            <div style={{ marginTop: 10, color: 'red', paddingLeft: 15 }}>
              {collectTips}
            </div>
          )
        }
        {
          siteTips === 'Amazon' && (
            <div className={style.redTag}>{t('Amazon订单')}</div>
          )
        }
        {
          (isAutoPad && resultType === 4) ? (
            <div style={{
              padding: 10, color: 'red', fontWeight: 'bold', fontSize: '36px', textAlign: 'center',
            }}
            >
              <span style={{ fontSize: '16px', fontWeight: 'normal' }}>
                {
                `(${t('波次完成')})`
                }
              </span>
            </div>
          ) : ''
        }
        {
          !!nationalLine && (
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                marginTop: 10,
              }}
            >
              <div style={{ color: 'rgb(209, 136, 58)', border: '1px solid rgb(209, 136, 58)', padding: '5px 10px' }}>
                {nationalLine}
              </div>
            </div>
          )
        }
        {isShowCollectionFinish && subCollectLocation && (
          <div className={style.locationTips}>{subCollectLocation.slice(-3)}</div>
        )}
        {isShowPartFinish && subCollectLocationText && (
          <div className={style.locationTips}>{subCollectLocationText.slice(-3)}</div>
        )}
        {(!isShowCollectionFinish && taskInfo.collectionTaskCode) ? (
          <div style={{ padding: '0px 15px' }}>
            <div className={style.taskInfoHeader}>
              <div style={{ paddingLeft: '2px' }}>{t('集货任务号')}:{taskInfo.collectionTaskCode}</div>
              <div style={{ color: '#02a7f0' }}>{taskInfo.collectionTaskStatusName}</div>
            </div>
            <div className={style.taskInfoContentWrap}>
              {(taskInfo.collectionTaskInfo || []).sort().map((ti) => (
                <div className={style.taskInfoContent}>
                  <div style={{
                    paddingLeft: '2px', color: ti.taskDetailStatus === 2 ? '#65CC2F' : '',
                  }}
                  >{ti.pickContainerCode}
                  </div>
                  <div>{ti.locationCode ? (<span><b style={{ fontWeight: 'bold' }}>{t('集货位')}{(`${ti.locationCode}`).slice(0, -2)}</b><b style={{ fontSize: '16px', color: 'red' }}>{(`${ti.locationCode}`).slice(-2)}</b></span>) : '-'}</div>
                </div>
              ))}
            </div>
          </div>
        ) : null}
        <Footer
          dispatch={dispatch}
          beforeBack={(back) => {
            store.init();
            back();
          }}
        >
          {JSON.stringify(taskInfo) === '{}' && (
            <FooterBtn
              onClick={() => {
                dispatch(push('/collection/receive-collection-task'));
              }}
            >
              {t('去领取任务')}
            </FooterBtn>
          )}

        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  resultType: PropTypes.number,
  waitingSubCollectionContainer: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  headerTitle: PropTypes.string,
  isShowCollectionFinish: PropTypes.bool,
  showCollectionFinishInfo: PropTypes.shape(),
  isShowPartFinish: PropTypes.bool,
  collectTips: PropTypes.string,
  showPartFinishInfo: PropTypes.shape(),
  // specErrTip: PropTypes.string,
  wellenTypeName: PropTypes.string,
  siteTips: PropTypes.string,
  packType: PropTypes.bool,
  nationalLine: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.bool,
  ]),
  isSubWholeCollect: PropTypes.bool,
  isSubCollectFirstTip: PropTypes.bool,
  pickContainerCodeDisplay: PropTypes.string,
  combineOrderMsg: PropTypes.bool,
  isAutoPad: PropTypes.bool,
  wellenMarkList: PropTypes.arrayOf(PropTypes.string),
  taskInfo: PropTypes.shape(),
  dispatch: PropTypes.func,
  info: PropTypes.shape(),
  showDialog: PropTypes.bool,
  inputPalletCode: PropTypes.string,
};

export default i18n(Container);
