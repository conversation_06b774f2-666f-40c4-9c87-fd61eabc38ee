import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { i18n, t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import { classFocus } from 'lib/util';
import store from './reducers';
import Footer from '../../common/footer';
import {
  Header,
  FocusInput,
  FooterBtn,
  pages,
  modal,
} from '../../common';
import styles from './style.css';

const { View } = pages;

class Container extends Component {
  componentDidMount() {
    store.$init();
  }

  render() {
    const {
      dispatch,
      containerCode,
      dataLoading,
      confirmLoading,
      containerInfo,
    } = this.props;
    return (
      <View>
        <Header title={t('取消集货任务')}/>
        <Form
          style={{
            marginBottom: 12,
          }}
        >
          <FocusInput
            autoFocus
            disabled={dataLoading === 0}
            placeholder={t('请扫描周转箱')}
            className="containerCode"
            label={t('周转箱')}
            value={containerCode}
            onChange={(e) => {
              store.changeData({
                data: {
                  containerCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={() => {
              if (containerCode) {
                store.scanContainer({
                  param: {
                    containerCode,
                  },
                });
              } else {
                modal.error({
                  content: t('请扫描周转箱'),
                  className: 'containerCode',
                });
              }
            }}
          />
        </Form>
        <Form className="receiveClassName">
          {containerInfo && containerInfo.id && (
            <div
              style={{
                height: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
            >
              <div className={styles.trayBox}>
                <p className={styles.traySpace}>
                  <span>{t('周转箱')}</span>
                  <span style={{ fontWeight: 'bold' }}>{containerInfo.containerCode}</span>
                </p>
                <p className={styles.traySpace}>
                  <span>{t('集货任务号')}</span>
                  <span style={{ fontWeight: 'bold' }}>{containerInfo.collectionTaskCode}</span>
                </p>
                <p className={styles.traySpace}>
                  <span>{t('已集货 / 任务总箱数')}</span>
                  <span
                    style={{ fontWeight: 'bold' }}>{containerInfo.collectNum}/{containerInfo.boxNum}</span>
                </p>
              </div>
            </div>
          )}
        </Form>
        <Footer
          beforeBack={() => {
            dispatch(push('/collection/receive-collection-task'));
          }}
        >
          {containerInfo && containerInfo.id && (
            <FooterBtn
              disabled={confirmLoading === 0}
              onClick={() => {
                modal.confirm({
                  title: t('是否取消集货任务?'),
                  onOk: () => {
                    store.closeTask({
                      param: {
                        id: containerInfo.id,
                      },
                    });
                  },
                  onCancel: () => {
                    store.changeData({
                      data: {
                        containerCode: '',
                      },
                    });
                    classFocus('containerCode');
                  },
                });
              }}
            >
              {t('取消任务')}
            </FooterBtn>
          )}
        </Footer>
      </View>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  showPalletCode: PropTypes.string,
  containerCode: PropTypes.string,
  dataLoading: PropTypes.number,
  confirmLoading: PropTypes.number,
  containerInfo: PropTypes.shape(),
};

export default i18n(Container);
