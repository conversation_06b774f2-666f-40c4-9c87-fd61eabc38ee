.transferRowInfoBox {
    display: flex;
    justify-content: space-between;
    padding: 0 10px 0 10px;
}

.inputBox {
    margin: 0 5px;
    padding: 5px;
    width: 120px;
    border: 1px solid #dddddd;
    outline: none;
    font-size: 14px;
    border-radius: 3px;
}

.msgBox {
    color: #616161;
    padding: 10px 0 0 14px;
    background-color: #fff;
}

.trayBox {
    box-sizing: border-box;
    width: 95%;
    line-height: 46px;
    margin: 10px 2.5%;
    padding: 0 20px;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 4px;
    box-shadow: 0px 2px 4px rgba(25, 122, 250, .15);
}

.traySpace {
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}

.trayRight {
    font-size: 14px;
    font-weight: bold;
}

.toicon{
    color: #1a7afa;
    font-weight: normal;
    font-size: 16px;
}

.trayNum{
    color: #197afa;
}

.green{
    color: #46ab17;
}

.red{
    color: #d9001b;
}

.divboxShadow{
    margin-bottom: 12px;
    box-shadow: 0px 2px 4px rgba(25, 122, 250, .15);
}

