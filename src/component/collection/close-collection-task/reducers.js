import assign from 'object-assign';
import { modal, message } from 'common';
import { classFocus } from 'lib/util';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { put } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import error from 'source/audio/delete.mp3';
import {
  scanContainer as scanContainerApi,
  confirmReceive,
} from './server';

// 报错异常声音
const audio = new Audio(error);
audio.load();

const defaultState = {
  showPalletCode: '',
  containerCode: '',
  containerCodeValid: false,
  dataLoading: 1,
  confirmLoading: 1,
  receivePalletBool: false,
  containerInfo: {
    id: '',
    containerCode: '',
    pickContainerCode: '',
  },
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  // 禁用周转箱
  $scanContainer(draft) {
    assign(draft, {
      containerCodeValid: true,
    });
  },
  // 清空周转箱
  * cleanContainer(__, ctx) {
    yield ctx.changeData({
      data: {
        containerCode: '',
        containerCodeValid: false,
      },
    });
  },
  // 扫描周转箱
  * scanContainer(action, ctx) {
    markStatus('dataLoading');
    const param = {
      ...action.param,
    };
    const {
      code,
      info,
      msg,
    } = yield scanContainerApi(param);
    if (code === '0' && info) {
      // 跳转code 0 - 正常 1 - 清空周转箱输入内容并焦点置于周转箱
      // 2 - 顶部警告提示、清空周转箱输入内容并焦点置于周转箱
      // 3 - 确认后周转箱输入内容并焦点置于周转箱 4 - 任务领取成功
      if (info.code === 0) {
        yield ctx.changeData({
          data: {
            containerInfo: info,
          },
        });
        yield ctx.cleanContainer();
        classFocus('containerCode');
      } else if (info.code === 1) {
        // 扫周装箱报错需要发出声音。
        audio.play();
        yield ctx.cleanContainer();
        modal.info({
          content: info.message,
          onOk: () => {
            classFocus('containerCode');
          },
        });
      } else if (info.code === 2) {
        // 扫周装箱报错需要发出声音。
        audio.play();
        message.warning(info.message);
        // 清空所有数据
        yield ctx.changeData({
          data: assign({}, defaultState),
        });
        yield ctx.cleanContainer();
        classFocus('containerCode');
      } else if (info.code === 3) {
        yield new Promise(r => modal.info({
          content: info.message,
          onOk: () => r('ok'),
        }));
        yield ctx.cleanContainer();
        classFocus('containerCode');
      } else if (info.code === 4) {
        message.success(t('任务领取成功'));
        yield put(push('/collection/sub-collection'));
      }
    } else {
      // 扫周装箱报错需要发出声音。
      audio.play();
      // 报错的话清空周转箱，并且聚焦在周转箱。弹框
      yield ctx.cleanContainer();
      modal.error({
        audioErr: true,
        content: msg,
        onOk: () => {
          classFocus('containerCode');
        },
      });
    }
  },
  // 取消任务
  * closeTask(action, ctx) {
    // markStatus('confirmLoading');
    const {
      code,
      msg,
    } = yield confirmReceive(action.param);
    if (code === '0') {
      message.success(t('任务取消成功'));
      yield ctx.changeData({
        data: assign({}, defaultState),
      });
      classFocus('containerCode');
    } else if (code === '2901') {
      message.error(t('仅待集货状态的任务可取消，请确认后操作'));
      yield ctx.changeData({
        data: assign({}, defaultState),
      });
      classFocus('containerCode');
    } else {
      // 错误的话弹框
      modal.error({
        audioErr: true,
        content: msg,
      });
    }
  },
};
