import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, Footer, View,
} from 'common';
import { Dialog } from 'react-weui';
import styles from './style.less';
import store from './reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      initLoading,
      dataLoading,
      isDialogVisibled,
      containerCode,
      containerCodeDisabled,
      scanContainerCode,
      scanInfo,
      tipsInfo,
      isDialogVisibledComplete,
      locationCode,
      locationCodeDisabled,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle}>
          <span onClick={() => {
            window.location.href = '#/collection/container-consolidation';
          }}
          >{t('按箱集货')}
          </span>
        </Header>
        <View
          diff={100} // 默认56; 页面内容高度：window.innerHeight - diff 中的 diff 值【必填: 100是头部标题44+底部按钮56之和】
          flex={false} // flex布局，默认为true，当需要固定单个输入框时，不启用【选填: 根据实际情况决定】
          initLoading={initLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面【选填: 根据实际情况决定】
        >
          {!scanContainerCode && (
          <Form>
            <FocusInput
              autoFocus
              value={containerCode}
              className="containerCode"
              placeholder={t('请扫描')}
              disabled={dataLoading === 0 || containerCodeDisabled}
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  containerCode: value,
                });
              }}
              onPressEnter={(e) => {
                // 空值不触发请求【是否在FocusInput统一处理】
                if (!e.target.value) {
                  return;
                }
                store.scanContainer({
                  containerCode: e.target.value,
                });
              }}
            >
              <label>{t('托盘/周转箱')}</label>
            </FocusInput>
          </Form>
          )}
          {scanContainerCode && (
          <div>
            <Form>
              <FocusInput
                autoFocus
                value={scanContainerCode}
                className="scanContainerCode"
                placeholder={t('')}
                disabled
              >
                <label>{t('托盘号')}</label>
              </FocusInput>
            </Form>
            <Form>
              <FocusInput
                value={locationCode}
                className="locationCode"
                placeholder={t('请扫描')}
                disabled={dataLoading === 0 || locationCodeDisabled}
                onChange={(e) => {
                  const { value } = e.target;
                  store.changeData({
                    locationCode: value,
                  });
                }}
                onPressEnter={(e) => {
                  // 空值不触发请求
                  if (!e.target.value) {
                    return;
                  }
                  store.scanLocation({
                    ...scanInfo,
                    locationCode: e.target.value,
                  });
                }}
              >
                <label>{t('集货库位')}</label>
              </FocusInput>
            </Form>
            <div className={styles.contentDiv}>
              <p className={styles.contentDivP}>
                <span>{t('推荐库位')}</span>
                <span className={styles.contentTitleBold}>{scanInfo?.recommendLocationCode}</span>
              </p>
              <p className={styles.contentDivP}>
                <span>{t('周转箱数')}</span>
                <span className={styles.contentTitle}>{scanInfo?.pickContainerNum}</span>
              </p>
            </div>
            <p className={styles.contentWellen}>{t('波次号')}: {scanInfo?.wellenCode}</p>
            <div className={styles.contentLineDiv}>
              <span className={styles.contentLine}>{scanInfo?.nationalLineTypeName}</span>
            </div>
          </div>
          )}
        </View>
        <Footer
          beforeBack={(back) => {
            if (scanContainerCode) {
              store.changeData({
                containerCode: '', // 扫描的托盘号/周转箱号
                scanContainerCode: '', // 扫描成功的托盘号/周转箱号
                scanInfo: {}, // 扫描成功，展示的信息
              });
            } else {
              back();
            }
          }}
        />
        <Dialog
          title={t('提示')}
          show={isDialogVisibled}
          buttons={[{
            type: 'primary',
            label: t('确认'),
            onClick: () => {
              store.clearData();
            },
          }]}
        >
          <p className={styles.dialogP2}>{t('托盘号{}在主仓集货库位', <span style={{ color: '#FF3C45' }}>{tipsInfo?.palletCode}</span>)}</p>
          <p className={styles.dialogP2}>{t('{}已集货完成', <span style={{ color: '#FF3C45' }}>{tipsInfo?.locationCode}</span>)}</p>
          <div className={styles.contentLineDiv} style={{ marginTop: '0' }}>
            <span className={styles.contentLine}>{scanInfo?.nationalLineTypeName}</span>
          </div>
        </Dialog>
        <Dialog
          title={t('提示')}
          show={isDialogVisibledComplete}
          buttons={[{
            type: 'primary',
            label: t('确认'),
            onClick: () => {
              store.clearData();
            },
          }]}
        >
          <p className={styles.dialogP2}>{
            t(
              '波次{}在主仓集货库位{}已全部集货完成，托盘数({}托)，周转箱（{}箱）',
              <span style={{ color: '#FF3C45' }}>{tipsInfo?.wellenCode}</span>,
              <span style={{ color: '#FF3C45' }}>{tipsInfo?.locationCode}</span>,
              <span style={{ color: '#FF3C45' }}>{tipsInfo?.palletNum}</span>,
              <span style={{ color: '#FF3C45' }}>{tipsInfo?.pickContainerNum}</span>,
            )
          }
          </p>
          <div className={styles.contentLineDiv} style={{ marginTop: '0' }}>
            <span className={styles.contentLine}>{scanInfo?.nationalLineTypeName}</span>
          </div>
        </Dialog>
      </div>
    );
  }
}

Container.propTypes = {
  initLoading: PropTypes.bool,
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  isDialogVisibled: PropTypes.bool,
  isDialogVisibledComplete: PropTypes.bool,
  containerCode: PropTypes.string,
  containerCodeDisabled: PropTypes.bool,
  scanContainerCode: PropTypes.string,
  scanInfo: PropTypes.shape(),
  tipsInfo: PropTypes.shape(),
  locationCode: PropTypes.string,
  locationCodeDisabled: PropTypes.bool,
};

export default i18n(Container);
