import { t } from '@shein-bbl/react';
import { classFocus, getHeaderTitle } from 'lib/util';
import assign from 'object-assign';
import { scanContainerAPI, scanLocationAPI } from './server';
import Modal from '../../common/modal';

const defaultState = {
  initLoading: false, // 初始化loading
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  location: '', // 库位
  barCode: '', // 条码
  isDialogVisibled: false, // 是否弹窗
  containerCode: '', // 扫描的托盘号/周转箱号
  containerCodeDisabled: false, // 托盘号/周转箱号是否可以输入
  scanContainerCode: '', // 扫描成功的托盘号/周转箱号
  locationCode: '', // 扫描库位号
  scanInfo: {}, // 扫描托盘号成功，展示的信息
  tipsInfo: {}, // 扫描库位号成功，弹窗展示的信息
  isDialogVisibledComplete: false, // 波次主仓集货完成 是否弹窗
  locationCodeDisabled: false, // 库位号是否可以输入
};

export default {
  state: defaultState,
  $init: () => defaultState,
  * init() {
    yield this.changeData({ headerTitle: getHeaderTitle() || t('主仓集货') });
    // 部分页面进页面会请求获取数据
    yield this.changeData({ initLoading: true });
    const status = yield new Promise((r) => {
      setTimeout(() => r(1), 800);
    });
    if (status === 1) {
      yield this.changeData({ initLoading: false });
    }
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  clearData(draft) {
    assign(draft, {
      location: '', // 库位
      barCode: '', // 条码
      isDialogVisibled: false, // 是否弹窗
      containerCode: '', // 扫描的托盘号/周转箱号
      scanContainerCode: '', // 扫描成功的托盘号/周转箱号
      scanInfo: {}, // 扫描托盘号成功，展示的信息
      tipsInfo: {}, // 扫描库位号成功，弹窗展示的信息
      isDialogVisibledComplete: false, // 波次主仓集货完成 是否弹窗
      containerCodeDisabled: false, // 托盘号/周转箱号是否可以输入
      locationCodeDisabled: false, // 库位号是否可以输入
    });
    classFocus('containerCode');
  },
  // 扫描托盘号
  * scanContainer(action, ctx) {
    yield this.changeData({ containerCodeDisabled: true });
    const res = yield scanContainerAPI({ containerCode: action.containerCode });
    if (res.code === '0') {
      yield ctx.changeData({
        scanInfo: res.info || {},
        scanContainerCode: res?.info?.palletCode,
        containerCodeDisabled: false,
      });
      classFocus('locationCode');
    } else {
      yield this.changeData({
        containerCode: '',
        containerCodeDisabled: false,
      });
      Modal.error({
        content: res.msg,
        className: 'containerCode',
      });
    }
  },
  // 扫描库位
  * scanLocation(action, ctx) {
    const params = {
      locationCode: action.locationCode,
      palletCode: action.palletCode,
      pickContainerCodeList: action.pickContainerCodeList,
      recommendLocationCode: action.recommendLocationCode,
      wellenCode: action.wellenCode,
    };
    yield this.changeData({
      locationCodeDisabled: true,
    });
    const res = yield scanLocationAPI(params);
    if (res.code === '0') {
      yield this.changeData({
        locationCodeDisabled: false,
      });
      if (res.info.isWellenMainCollectionComplete) {
        yield ctx.changeData({
          isDialogVisibledComplete: true,
          tipsInfo: res.info || {},
        });
      } else {
        yield ctx.changeData({
          isDialogVisibled: true,
          tipsInfo: res.info || {},
        });
      }
    } else {
      yield this.changeData({
        locationCode: '',
        locationCodeDisabled: false,
      });
      Modal.error({
        content: res.msg,
        className: 'locationCode',
      });
    }
  },
};
