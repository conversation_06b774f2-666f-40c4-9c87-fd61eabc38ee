import React from 'react';
import { t } from '@shein-bbl/react';
import { getHeaderTitle, classFocus } from 'lib/util';
import { markStatus } from 'rrc-loader-helper';
import { modal } from 'common';
import styles from './style.css';
import {
  scanContainerAPI,
  confirmAPI,
  scanLocationAPI,
} from './server';

const defaultState = {
  initLoading: false, // 初始化loading
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  pickContainerCode: '', // 周转箱号
  locationCode: '', // 集货库位
  infoData: {}, // 扫描周转箱返回的info
  showScanLocationInput: false, // 展示扫描集货库位输入框
  recommendLocationCode: '', // 推荐库位号
  detailList: [], // 每次扫描周转箱的数据集合
};

export default {
  state: defaultState,
  $init: () => defaultState,
  * init() {
    yield this.changeData({ initLoading: false, headerTitle: getHeaderTitle() || t('按箱集货') });
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 扫描周转箱号
  * scanContainer(pickContainerCode) {
    const { detailList, infoData } = yield '';
    if (detailList.some((i) => i.pickContainerCode === pickContainerCode)) {
      yield this.changeData({
        pickContainerCode: '', // 清空周转箱
      });
      modal.error({ content: `${t('请不要重复扫描相同的周转箱号')}${pickContainerCode}`, className: 'pickContainerCode' });
      return;
    }
    markStatus('dataLoading');
    const {
      bindPickContainerTotal, // 绑定托盘的拣货箱总数量
      palletCode, // 托盘号
      palletId,
      scanPickContainerNum, // 扫描箱数
      wellenCode, // 波次号
    } = infoData;
    const { code, msg, info } = yield scanContainerAPI({
      pickContainerCode,
      bindPickContainerTotal,
      palletCode,
      palletId,
      scanPickContainerNum,
      wellenCode,
    });
    if (code === '0') {
      yield this.changeData({
        infoData: info,
        detailList: [info, ...detailList],
        pickContainerCode: '', // 清空周转箱
      });
      classFocus('pickContainerCode');
    } else {
      yield this.changeData({
        pickContainerCode: '', // 清空周转箱
      });
      modal.error({ content: msg, className: 'pickContainerCode' });
    }
  },

  // 扫描周转箱号后点击确认
  * getRecommendLocationCode(isConfirm) {
    markStatus('dataLoading');
    const { infoData } = yield '';
    const {
      bindPickContainerTotal, // 绑定托盘的拣货箱总数量
      scanPickContainerNum, // 扫描箱数
      wellenCode, // 波次号
    } = infoData;
    const { code, msg, info } = yield confirmAPI({
      wellenCode,
      isConfirm,
      bindPickContainerTotal,
      scanPickContainerNum,
    });
    if (code === '0') {
      if (info.isNeedConfirm) {
        const confirmFlag = yield new Promise((r) => {
          modal.confirm({
            content: t('你扫描的箱数和系统数据不符，你确认要执行主仓集货吗'),
            onOk: () => { r(1); },
            onCancel: () => { r(0); },
          });
        });
        if (confirmFlag) {
          // 点击确认
          yield this.getRecommendLocationCode(true);
        } else {
          // 点击取消 聚焦到周转箱
          classFocus('pickContainerCode');
        }
      } else {
        yield this.changeData({
          recommendLocationCode: info.recommendLocationCode, // 获取推荐库位号
          showScanLocationInput: true,
          locationCode: '',
        });
        // 聚焦库位号
        classFocus('locationCode');
      }
    } else {
      modal.error({ content: msg, className: 'pickContainerCode' });
    }
  },
  // 扫描集货库位
  * scanLocation(locationCode) {
    markStatus('dataLoading');
    const { infoData, recommendLocationCode, detailList } = yield '';
    const { code, msg, info } = yield scanLocationAPI({
      locationCode,
      palletCode: infoData.palletCode,
      wellenCode: infoData.wellenCode,
      recommendLocationCode,
      pickContainerCodeList: detailList.map((e) => e.pickContainerCode),
    });
    if (code === '0') {
      const {
        wellenCode, locationCode: infoLocationCode, palletNum,
        pickContainerNum, palletCode, nationalLineTypeName,
      } = info;
      // 是否波次主仓集货完成
      const title = info.isWellenMainCollectionComplete ?
        `${t('波次')}${wellenCode}${t('在主仓集货库位')}${infoLocationCode}${t('已全部集货完成，托盘数(')}${palletNum}${t(')，周转箱（')}${pickContainerNum}${t('箱）')}`
        : `${t('托盘号')}${palletCode}${t('在主仓集货库位')}${infoLocationCode}${t('已集货完成')}`;
      const confirmFlag = yield new Promise((r) => {
        modal.info({
          content: (
            <div>
              <div>{title}</div>
              {nationalLineTypeName && (
              <span className={styles.nationalLine}>
                {nationalLineTypeName}
              </span>
              )}
            </div>
          ),
          onOk: () => { r(1); },
        });
      });
      if (confirmFlag) {
        yield this.init();
      }
    } else {
      yield this.changeData({
        locationCode: '',
      });
      modal.error({ content: msg, className: 'locationCode' });
    }
  },
};
