import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, Footer, FooterBtn, View,
} from 'common';
import style from 'common/common.css';
import store from './reducers';
import styles from './style.css';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      initLoading,
      dataLoading,
      pickContainerCode, // 周转箱号
      locationCode, //  集货库位
      showScanLocationInput, // 展示扫描集货库位输入框
      infoData, //  扫描周转箱返回的info
      detailList, // 每次扫描周转箱的数据集合
      recommendLocationCode, // 推荐库位号
    } = this.props;

    return (
      <div>
        <Header title={headerTitle} />
        <View
          diff={110} // 默认56; 页面内容高度：window.innerHeight - diff 中的 diff 值【必填: 100是头部标题44+底部按钮56之和】
          flex={false} // flex布局，默认为true，当需要固定单个输入框时，不启用【选填: 根据实际情况决定】
          initLoading={initLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面【选填: 根据实际情况决定】
        >
          <Form className={style.pageStikyChild}>
            {!showScanLocationInput && (
            <FocusInput
              autoFocus
              value={pickContainerCode}
              className="pickContainerCode"
              placeholder={t('请扫描周转箱号')}
              disabled={dataLoading === 0}
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  pickContainerCode: value,
                });
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanContainer(pickContainerCode);
              }}
            >
              <label>{t('周转箱号')}</label>
            </FocusInput>
            )}
            {showScanLocationInput && (
            <FocusInput
              value={locationCode}
              className="locationCode"
              placeholder={t('请扫描集货库位')}
              disabled={dataLoading === 0}
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  locationCode: value,
                });
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanLocation(locationCode);
              }}
            >
              <label>{t('集货库位')}</label>
            </FocusInput>
            )}
            { Object.keys(infoData)?.length > 0 && (
            <div>
              <div className={styles.topInfoStyle}>
                {showScanLocationInput && <div>{t('推荐库位')}: {recommendLocationCode || t('无')}</div>}
                <div>{t('波次号')}: {infoData.wellenCode}</div>
                <div>{t('绑定托盘')}: {infoData.palletCode}</div>
              </div>
              <div className={styles.totalStyle}>
                <span>{t('扫描箱数')}/{t('绑定箱数')}:</span>
                <span className={styles.numStyle}>
                  {infoData.scanPickContainerNum || 0}/{infoData.bindPickContainerTotal || 0}
                </span>
              </div>
            </div>
            )}
          </Form>
          { Object.keys(infoData)?.length > 0 && (
            <div className={styles.infoStyle}>
              {
              detailList.map((item) => (
                <div className={styles.itemStyle} key={item.palletId}>
                  <div>
                    {item.pickContainerCode}
                  </div>
                  <div>{t('托盘号')}: {item.palletCode}</div>
                </div>
              ))
              }
            </div>
          )}
        </View>
        <Footer
          beforeBack={() => {
            window.location.href = '#/collection/main-collection';
          }}
        >
          {/* 扫描周转箱后展示该按钮 */}
          {Object.keys(infoData)?.length > 0 && !showScanLocationInput && (
          <FooterBtn
            onClick={() => {
              store.getRecommendLocationCode(false);
            }}
          >{t('确定')}
          </FooterBtn>
          )}
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  initLoading: PropTypes.bool,
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  pickContainerCode: PropTypes.string,
  locationCode: PropTypes.string,
  showScanLocationInput: PropTypes.bool,
  infoData: PropTypes.shape(),
  detailList: PropTypes.arrayOf(PropTypes.shape()),
  recommendLocationCode: PropTypes.string,
};

export default i18n(Container);
