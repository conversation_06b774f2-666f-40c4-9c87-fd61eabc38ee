import { sendPostRequest } from '../../../lib/public-request';

// 海外二步集货项目--按箱集货--扫描周转箱
export const scanContainerAPI = (param) => sendPostRequest({
  url: '/box_collection/box_scan_pick_container',
  param,
}, process.env.WOS_URI);
// export const scanContainerAPI = (param) => sendPostRequest({
//   url: 'https://soapi-sdk-web01.dotfashion.cn/mock/5768/wos/front/box_collection/box_scan_pick_container',
//   param,
// }, '');

// 海外二步集货项目--按箱集货--扫描周转箱确认提交
export const confirmAPI = (param) => sendPostRequest({
  url: '/box_collection/box_scan_pick_container_confirm',
  param,
}, process.env.WOS_URI);
// export const confirmAPI = (param) => sendPostRequest({
//   url: 'https://soapi-sdk-web01.dotfashion.cn/mock/5768/wos/front/box_collection/box_scan_pick_container_confirm',
//   param,
// }, '');

// 海外二步集货项目--按箱集货--扫描库位号
export const scanLocationAPI = (param) => sendPostRequest({
  url: '/main_collection/main_or_box_scan_collect_location',
  param,
}, process.env.WOS_URI);
// export const scanLocationAPI = (param) => sendPostRequest({
//   url: 'https://soapi-sdk-web01.dotfashion.cn/mock/5768/wos/front/main_collection/main_or_box_scan_collect_location',
//   param,
// }, '');
