import { sendPostRequest } from 'lib/public-request';

// 集货转运-扫描托盘
export const scanPalletAPI = (param) => sendPostRequest({
  url: '/collect_transport/scan_pallet',
  param,
}, process.env.WOS_URI);

// 集货转运-扫描车牌
export const scanLicenseAPI = (param) => sendPostRequest({
  url: '/collect_transport/scan_license',
  param,
}, process.env.WOS_URI);

// 集货转运-扫描车牌
export const transferAPI = (param) => sendPostRequest({
  url: '/collect_transport/click_transfer',
  param,
}, process.env.WOS_URI);

// 通知Lvms接口变更车辆信息
export const pushLvmsUpdateAPI = (param) => sendPostRequest({
  url: '/vehicle_task/push_lvms_update',
  param,
}, process.env.WOS_URI);

// 初始化查询是否需要显示发货子仓
export const getIsShowDeliverySubWarehouseAPI = (param) => sendPostRequest({
  url: '/collect_transport/check_open_delivery_sub_warehouse',
  param,
}, process.env.WOS_URI);
