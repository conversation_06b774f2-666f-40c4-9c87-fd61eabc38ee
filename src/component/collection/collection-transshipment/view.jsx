import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form, Button } from 'react-weui/build/packages';
import { i18n, t } from '@shein-bbl/react';
import {
  Header, Footer, FooterBtn,
  FocusInput, modal, SelectInput,
} from 'common';
import { classFocus } from 'lib/util';
import { push } from 'react-router-redux';
import store from './reducers';
import styles from './style.css';
import Detail from './jsx/detail';

class View extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      loading,
      palletCode,
      licenseNumber,
      palletCodeDisabled,
      filterPalletList,
      licenseNumberBool,
      subWarehouseList,
      deliverySubWarehouseName,
      deliverySubWarehouseId,
      pageType,
      dispatch,
      isShowDeliverySubWarehouse,
      execNumber,
      operateSubWarehouseIdName,
      focusPosition,
      clearLicenseNumberDisabled,
    } = this.props;
    if (pageType === 2) {
      return <Detail {...this.props} />;
    } else {
      return (
        <div>
          <Header title={t('集货转运')} />
          <Form>
            {isShowDeliverySubWarehouse && (
              <SelectInput
                value={deliverySubWarehouseName}
                selectValue={deliverySubWarehouseId}
                lineBreak
                autoFocus
                headerTitle={t('选择发货子仓')}
                label={t('发货子仓')}
                placeHolder={t('请扫描子仓')}
                enterShow
                className="deliverySubWarehouseId"
                selectList={subWarehouseList}
                onSelect={(value) => {
                  const item = subWarehouseList.find((e) => e.value === value);
                  if (licenseNumber || palletCode) {
                    modal.confirm({
                      content: t('修改发货子仓会清空当前转运内容，是否操作？'),
                      onOk: () => {
                        store.init({
                          focusPosition: 'licenseNumber',
                        });
                        store.changeData({
                          data: {
                            deliverySubWarehouseId: item.value,
                            deliverySubWarehouseName: item.name,
                            focusPosition: 'deliverySubWarehouseId',
                          },
                        });
                      },
                      onCancel: () => {
                        classFocus(focusPosition);
                      },
                    });
                  } else {
                    store.changeData({
                      data: {
                        deliverySubWarehouseId: item.value,
                        deliverySubWarehouseName: item.name,
                        focusPosition: 'deliverySubWarehouseId',
                      },
                    });
                    classFocus('licenseNumber'); // 聚集车牌号
                  }
                }}
              />
            )}
            <FocusInput
              label={t('车牌号')}
              className="licenseNumber"
              value={licenseNumber}
              disabled={licenseNumberBool ||
                (isShowDeliverySubWarehouse && !deliverySubWarehouseId)}
              onChange={(e) => {
                store.changeData({
                  data: {
                    licenseNumber: e.target.value,
                    focusPosition: 'licenseNumber',
                  },
                });
              }}
              autoFocus
              placeholder={t('请扫描或输入')}
              onPressEnter={() => {
                if (licenseNumber) {
                  store.scanlicenseNumber({
                    licenseNumber,
                    deliverySubWarehouseId,
                  });
                }
              }}
              footer={(
                <Button
                  type="primary"
                  style={{ marginBottom: 5 }}
                  size="small"
                  disabled={isShowDeliverySubWarehouse && clearLicenseNumberDisabled}
                  onClick={() => {
                    store.changeData({
                      data: {
                        palletCodeDisabled: true,
                        licenseNumberBool: false,
                        licenseNumber: '',
                      },
                    });
                    classFocus('licenseNumber');
                  }}
                >
                  {t('清空')}
                </Button>
              )}
            />
            <FocusInput
              label={t('托盘/周转箱')}
              disabled={palletCodeDisabled || !loading}
              value={palletCode}
              className="palletCode"
              onChange={(e) => {
                store.changeData({
                  data: {
                    palletCode: e.target.value,
                    focusPosition: 'palletCode',
                  },
                });
              }}
              placeholder={t('请扫描托盘/周转箱')}
              onPressEnter={() => {
                if (palletCode) {
                  store.scanPallet({
                    palletCode,
                    licenseNumber,
                  });
                }
              }}
              footer={(
                <Button
                  type="primary"
                  style={{ marginBottom: 5 }}
                  size="small"
                  onClick={() => {
                    store.changeData({
                      data: {
                        palletCode: '',
                      },
                    });
                    classFocus('palletCode');
                  }}
                >
                  {t('清空')}
                </Button>
              )}
            />
          </Form>
          {
            (Array.isArray(filterPalletList) && !!filterPalletList.length) && (
              <div>
                {isShowDeliverySubWarehouse ? (
                  <div style={{ padding: '10px 15px 5px 15px', display: 'flex', justifyContent: 'space-between' }}>
                    <span>{t('已装车托盘数')}:</span>
                    <span
                      onClick={() => {
                        store.changeData({
                          data: {
                            pageType: 2, // 跳转到明细页面
                          },
                        });
                      }}
                      style={{ color: '#197AFA', textDecoration: 'underline', cursor: 'pointer' }}
                    >
                      {filterPalletList.length}
                    </span>
                  </div>
                ) : (
                  <div style={{ padding: '10px 15px 5px 15px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>{t('已装车托盘数')}:</span>
                      <span>
                        {filterPalletList.length}
                      </span>
                    </div>
                    <div
                      style={{ color: '#197AFA', textDecoration: 'underline' }}
                      onClick={() => {
                        store.changeData({
                          data: {
                            pageType: 2, // 跳转到明细页面
                          },
                        });
                      }}
                    >
                      {t('车牌号')}: {licenseNumber}
                    </div>
                  </div>
                )}
              </div>
            )
          }
          {!!execNumber && isShowDeliverySubWarehouse && <div className={styles.tipMsg}>{t('执行单号')}: {execNumber}</div>}
          {!!operateSubWarehouseIdName && isShowDeliverySubWarehouse && <div className={styles.tipMsg}>{t('出库作业子仓')}: {operateSubWarehouseIdName}</div>}
          <Footer
            beforeBack={() => {
              // 若页面有输入内容，返回时提示“返回会清空当前页面内容，是否确认操作？”，确认后再返回上一菜单页面
              if ((isShowDeliverySubWarehouse && deliverySubWarehouseId)
                || palletCode || licenseNumber) {
                modal.confirm({
                  modalBlurInput: true,
                  content: t('返回会清空当前页面内容，是否确认操作？'),
                  onOk: () => {
                    dispatch(push('/sub-menu'));
                  },
                });
              } else {
                dispatch(push('/sub-menu'));
              }
            }}
            style={{ position: 'absolute' }}
          >
            <FooterBtn
              className="confirmBtn"
              disabled={!loading || !licenseNumber || !filterPalletList.length ||
                (isShowDeliverySubWarehouse && !deliverySubWarehouseId)}
              onClick={() => {
                modal.confirm({
                  content: t('发货后不允许继续装车，是否确认转运？'),
                  onOk: () => {
                    store.clickTransfer({
                      licenseNumber,
                      filterPalletList,
                    });
                  },
                  onCancel: () => {},
                });
              }}
            >
              {t('转运')}
            </FooterBtn>
          </Footer>
        </div>
      );
    }
  }
}

View.propTypes = {
  loading: PropTypes.number,
  palletCode: PropTypes.string,
  licenseNumber: PropTypes.string,
  palletCodeDisabled: PropTypes.bool,
  licenseNumberBool: PropTypes.bool,
  filterPalletList: PropTypes.arrayOf(PropTypes.shape),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape),
  deliverySubWarehouseName: PropTypes.string,
  deliverySubWarehouseId: PropTypes.string,
  pageType: PropTypes.number,
  dispatch: PropTypes.func,
  isShowDeliverySubWarehouse: PropTypes.bool,
  execNumber: PropTypes.string,
  operateSubWarehouseIdName: PropTypes.string,
  focusPosition: PropTypes.string,
  clearLicenseNumberDisabled: PropTypes.bool,
};

export default i18n(View);
