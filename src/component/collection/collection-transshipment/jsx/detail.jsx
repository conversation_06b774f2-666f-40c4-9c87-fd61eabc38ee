import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { classFocus } from 'lib/util';
import { t } from '@shein-bbl/react';
import {
  Header, Footer, List,
} from 'common';
import store from '../reducers';
import style from '../../../style.css';

const rows = [
  [
    {
      title: t('托盘号'),
      render: (record) => <span>{record.palletNo}</span>,
    },
    {
      title: t('箱数'),
      render: (record) => <span>{record.pickContainerCount}{t('箱')}</span>,
    },
  ],
];

class DetailList extends Component {
  render() {
    const {
      dispatch,
      filterPalletList,
      focusPosition,
    } = this.props;

    const height = window.innerHeight - 56;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <Header title={t('已装托盘明细')} />
        <List
          header={(
            <div style={{
              fontWeight: 'bold',
              color: '#000',
              display: 'flex',
              paddingRight: '15px',
              justifyContent: 'space-between',
            }}
            >
              <div>{t('托盘号')}</div>
              <div>{t('箱数')}</div>
            </div>
          )}
          rows={rows}
          data={filterPalletList}
          rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0' }}
        />
        <Footer
          dispatch={dispatch}
          beforeBack={() => {
            store.changeData({
              data: {
                pageType: 1,
              },
            });
            classFocus(focusPosition);
          }}
        />
      </div>
    );
  }
}

DetailList.propTypes = {
  dispatch: PropTypes.func,
  filterPalletList: PropTypes.arrayOf(PropTypes.shape()),
  focusPosition: PropTypes.string,
};

export default DetailList;
