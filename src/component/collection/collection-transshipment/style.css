.transferRowInfoBox {
    display: flex;
    justify-content: space-between;
    padding: 0 10px 0 10px;
}

.inputBox {
    margin: 0 5px;
    padding: 5px;
    width: 120px;
    border: 1px solid #dddddd;
    outline: none;
    font-size: 14px;
    border-radius: 3px;
}

.msgBox {
    color: #616161;
    margin: 5px 8px;
    padding: 4px 0 0 14px;
    background-color: #fff;
    box-shadow: 0px 2px 4px 0px rgba(25,122,250,0.15);
}

.palletBox {
  color: #616161;
  margin: 5px 8px;
  padding: 0px 14px 4px 14px;
  background-color: #fff;
  max-height: 260px;
  overflow: scroll;
  box-shadow: 0px 2px 4px 0px rgba(25,122,250,0.15);
}

.itemBox {
  color: #616161;
  padding: 4px 0;
  border-bottom: 1px solid #1E1E1E;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.tipMsg {
    display: flex;
    align-content: center;
    margin: 10px 0;
    padding: 0 15px;
}
