import assign from 'object-assign';
import { modal, message } from 'common';
import { classFocus, getWarehouseId, getUsername } from 'lib/util';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import {
  scanPalletAPI, scanLicenseAPI, transferAPI, pushLvmsUpdateAPI,
  getIsShowDeliverySubWarehouseAPI,
} from './server';
import { getBindSubWarehouseApi } from '../../../server/basic/data-dictionary';

const defaultState = {
  loading: 1,
  palletCode: '', // 托盘号
  licenseNumber: '', // 车牌号
  palletCodeDisabled: true, // 托盘号是否可输入
  palletList: [], // 缓存起来的托盘号总数，{palletNo: '托盘号', pickContainerCode: '周转箱'}
  filterPalletList: [], // 过滤后的托盘号-托盘号为单位
  licenseNumberBool: false, // 车牌号是否可以输入
  isShowDeliverySubWarehouse: false, // 是否显示发货子仓选择框
  deliverySubWarehouseId: '', // 选择的发货子仓
  deliverySubWarehouseName: '', //  选择的发货子仓名称
  subWarehouseList: [], // 子仓下拉枚举
  pageType: 1, // 1 默认页面 2 明细页面
  execNumber: '', // 执行单号 - 扫车牌返回
  unloadSubWarehouseIdList: [], // 卸货子仓id集合 - 扫车牌返回
  operateSubWarehouseIdName: '', // 出库作业子仓 - 扫托盘返回
  focusPosition: '', // 焦点聚焦位置
  clearLicenseNumberDisabled: false, // 清空扫车牌按钮是否禁用
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  // 初始当前页面数据
  resetData(draft) {
    assign(draft, defaultState);
  },
  * init(action, ctx) {
    yield ctx.getIsShowDeliverySubWarehouse(action); // 获取是否显示发货子仓标识
  },
  // 获取是否显示发货子仓标识
  * getIsShowDeliverySubWarehouse(action, ctx) {
    const { info, code } = yield getIsShowDeliverySubWarehouseAPI({
      warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
    });
    if (code === '0') {
      yield ctx.changeData({
        data: {
          isShowDeliverySubWarehouse: info?.isOpen, // 是否显示发货子仓选择框
        },
      });
      // 如果开启发货子仓才请求绑定的子仓下拉
      if (info?.isOpen) {
        yield ctx.getSubWarehouseList(); // 获取子仓列表
      }
      // 初始化设置光标定位
      if (action.focusPosition) {
        classFocus(action.focusPosition);
      }
    }
  },
  // 获取子仓下拉列表
  * getSubWarehouseList(_, ctx) {
    const {
      code,
      info,
      msg,
    } = yield getBindSubWarehouseApi({
      id: getWarehouseId(),
      userName: getUsername(),
    });
    if (code === '0') {
      const subWarehouseList = info.data.map((i) => ({
        name: i.subWarehouseName,
        value: i.subWarehouseId,
        code: i.code,
      }));
      yield ctx.changeData({
        data: {
          subWarehouseList,
        },
      });
    } else {
      modal.error({
        content: msg,
      });
    }
  },
  // 扫描托盘
  * scanPallet(action, ctx) {
    markStatus('loading');
    const {
      palletList,
      filterPalletList,
      execNumber,
      unloadSubWarehouseIdList,
      deliverySubWarehouseId,
    } = yield '';
    const { palletCode, licenseNumber } = action;
    //  如果已经存在，就不再调接口
    let tips = '';
    const canFind = palletList.some(({ palletNo, pickContainerCode }) => {
      if (palletNo === palletCode) {
        tips = t('该托盘已装车');
        return true;
      }
      if (pickContainerCode === palletCode) {
        tips = t('该周转箱所在托盘已装车');
        return true;
      }
      return false;
    });
    if (canFind) {
      message.success(tips);
      yield ctx.changeData({
        data: {
          palletCode: '',
        },
      });
      return;
    }
    const { code, msg, info } = yield scanPalletAPI({
      palletCode,
      licenseNumber,
      execNumber,
      deliverySubWarehouseId,
      unloadSubWarehouseIdList,
      warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
      originPalletCodeList: filterPalletList.map((fi) => (fi.palletNo)),
    });
    if (code === '0') {
      const {
        isSame, promptDes, carNumber, departureWarehouseId, businessType, destinationWarehouseId,
      } = info.verifyVehicleAndPickContainerRsp;
      // isSame = fasle 弹出弹框点击取消则不增加数据，点击确定则走原逻辑增加数据
      if (!isSame) {
        const isUpdate = yield new Promise((r) => {
          modal.confirm({
            content: promptDes,
            onOk: () => { r(1); },
            onCancel: () => { r(0); },
          });
        });
        // 点击取消，直接退出函数
        if (!isUpdate) {
          return;
        }
        // 调用更改车牌的信息接口;
        yield ctx.clickLvmsUpdate({
          params: {
            carNumber,
            departureWarehouseId,
            businessType,
            destinationWarehouseId,
          },
        });
      }

      const newFilterPalletList = [...filterPalletList];
      const isExits = newFilterPalletList.find((ele) => ele.palletNo === info.palletNo);
      if (isExits) {
        message.success(t('该周转箱所在托盘已装车'));
      } else {
        newFilterPalletList.unshift(info);
      }
      yield ctx.changeData({
        data: {
          palletList: [
            info,
            ...palletList,
          ],
          filterPalletList: newFilterPalletList,
          palletCode: '',
          operateSubWarehouseIdName: info.operateSubWarehouseIdName, // 出库作业子仓
          clearLicenseNumberDisabled: true,
        },
      });
    } else {
      yield new Promise((r) => (
        modal.error({
          audioErr: true,
          content: msg,
          onOk: () => r(0),
          onCancel: () => r(0),
        })
      ));
      yield ctx.changeData({ data: { palletCode: '' } });
    }
    classFocus('palletCode');
  },
  // 扫描车牌号
  * scanlicenseNumber(action, ctx) {
    markStatus('loading');
    const { licenseNumber, deliverySubWarehouseId } = action;
    // 校验车牌号
    if (licenseNumber.length > 15) {
      yield new Promise((r) => (
        modal.error({
          audioErr: true,
          content: t('车牌号不能大于15位！'),
          onOk: () => r(0),
          onCancel: () => r(0),
        })
      ));
      yield ctx.changeData({ data: { licenseNumber: '' } });
      classFocus('licenseNumber');
      return;
    }
    const { code, msg, info } = yield scanLicenseAPI({
      licenseNumber,
      deliverySubWarehouseId,
      warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
    });
    if (code === '0') {
      yield ctx.changeData({
        data: {
          palletCode: '',
          licenseNumberBool: true,
          palletCodeDisabled: false,
          execNumber: info?.execNumber, // 执行单号
          unloadSubWarehouseIdList: info?.unloadSubWarehouseIdList || [], // 卸货子仓id集合
        },
      });
      classFocus('palletCode');
    } else {
      yield new Promise((r) => (
        modal.error({
          audioErr: true,
          content: msg,
          onOk: () => r(0),
          onCancel: () => r(0),
        })
      ));
      yield ctx.changeData({ data: { licenseNumber: '' } });
      classFocus('licenseNumber');
    }
  },
  // 点击弹框确认按钮，通知LVMS变更车辆信息
  * clickLvmsUpdate(action) {
    markStatus('loading');
    const { params } = action;
    const { code, msg } = yield pushLvmsUpdateAPI(params);
    if (code === '0') {
      message.success(t('更改信息成功'));
    } else {
      message.error(msg);
    }
  },
  // 点击转运
  * clickTransfer(action, ctx) {
    markStatus('loading');
    const { licenseNumber, filterPalletList } = action;
    const {
      execNumber,
      unloadSubWarehouseIdList,
    } = yield '';
    const { code, msg } = yield transferAPI({
      licenseNumber,
      palletCodeList: filterPalletList.map((ele) => ele.palletNo),
      execNumber,
      unloadSubWarehouseIdList,
      warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
    });
    if (code === '0') {
      yield ctx.resetData();
      yield ctx.getIsShowDeliverySubWarehouse(); // 获取是否显示发货子仓标识
      classFocus('licenseNumber');
      message.success(t('转运成功'));
    } else {
      yield new Promise((r) => (
        modal.error({
          audioErr: true,
          content: msg,
          onOk: () => r(0),
          onCancel: () => r(0),
        })
      ));
      yield ctx.changeData({ data: { palletCode: '' } });
      classFocus('palletCode');
    }
  },
};
