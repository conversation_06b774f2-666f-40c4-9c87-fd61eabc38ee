import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import store, { columns } from '../reducers';
import { Table } from '../../../common';
import style from '../style.css';

class SubWarehouseItem extends Component {
  render() {
    const {
      subLocationInfos,
      showNotFinished,
      showFinished,
      palletCodeNum,
      unFinishedPalletCodeNum,
      isFinishedList,
      notFinishList,
      currentSubWarehouseId,
    } = this.props;

    return (
      <React.Fragment>
        {
          subLocationInfos.map((item, index) => (
            <div
              key={index}
              style={{ display: currentSubWarehouseId === item.subWarehouseId ? null : 'none' }}
            >
              <div>
                <div>
                  <span className={style.label}>{t('子仓集货位')}:</span>
                  <span className={style.value}>{item.subCollectLocation}</span>
                </div>
              </div>
              <div className={style.col} style={{ marginTop: 15, lineHeight: '25px' }}>
                <div className={style.statusName}>
                  <div className={`${style.icon} ${style.uncomplete}`} />
                  <div>{t('未主仓集货')}</div>
                </div>
                <div>
                  <span className={style.boxNum}>{t('托数')}:</span>
                  <span className={style.num}>{unFinishedPalletCodeNum}</span>
                </div>
                <div
                  onClick={() => {
                    if (notFinishList.length > 0) {
                      store.changeData({
                        data: {
                          showNotFinished: !showNotFinished,
                        },
                      });
                    }
                  }}
                >
                  <span className={style.boxNum}>{t('箱数')}:</span>
                  <span
                    className={notFinishList.length === 0 ? style.disabledNum :
                      style.num}
                  >
                    {notFinishList.length}
                  </span>
                  <Icon
                    name="arr-down"
                    style={{ color: notFinishList.length > 0 ? '#197AFA' : '#BCBDC7' }}
                  />
                </div>
              </div>
              {
                showNotFinished &&
                (
                  <Table
                    dataSource={notFinishList}
                    columns={columns}
                    maxHeight={200}
                  />
                )
              }
              <div className={style.col} style={{ marginTop: 15, lineHeight: '25px' }}>
                <div className={style.statusName}>
                  <div className={`${style.icon} ${style.complete}`} />
                  <div>{t('已主仓集货')}</div>
                </div>
                <div>
                  <span className={style.boxNum}>{t('托数')}:</span>
                  <span className={style.num}>{palletCodeNum}</span>
                </div>
                <div
                  onClick={() => {
                    if (isFinishedList.length > 0) {
                      store.changeData({
                        data: {
                          showFinished: !showFinished,
                        },
                      });
                    }
                  }}
                >
                  <span className={style.boxNum}>{t('箱数')}:</span>
                  <span className={isFinishedList.length === 0 ? style.disabledNum : style.num}>{isFinishedList.length}</span>
                  <Icon
                    name="arr-down"
                    style={{ color: isFinishedList.length > 0 ? '#197AFA' : '#BCBDC7' }}
                  />
                </div>
              </div>
              {
                showFinished &&
                (
                  <Table
                    dataSource={isFinishedList}
                    columns={columns}
                    maxHeight={200}
                  />
                )
              }
            </div>
          ))
        }
      </React.Fragment>
    );
  }
}

SubWarehouseItem.propTypes = {
  subLocationInfos: PropTypes.arrayOf(PropTypes.object),
  showNotFinished: PropTypes.bool,
  showFinished: PropTypes.bool,
  palletCodeNum: PropTypes.number,
  unFinishedPalletCodeNum: PropTypes.number,
  isFinishedList: PropTypes.arrayOf(PropTypes.object),
  notFinishList: PropTypes.arrayOf(PropTypes.object),
  currentSubWarehouseId: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
};

export default SubWarehouseItem;
