import React, { Component } from 'react';
import {
  Tab, TabBody, NavBar, NavBarItem,
} from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import store, { columns } from '../reducers';
import {
  Table,
} from '../../../common';
import Tips from './tips';
import style from '../style.css';

class ContainerPalletInfo extends Component {
  render() {
    const {
      data,
      showFinished,
      showNotFinished,
      typeShow,
      palletSubWarehouseIndex,
      showSearchString,
      type,
    } = this.props;

    let list = [];
    let subWarehouseList = [];
    if (typeShow === 0) {
      list.push(data);
      subWarehouseList.push(data.subWarehouseName);
    } else if (typeShow === 1) {
      const { subLocationInfos } = data;
      list = subLocationInfos[palletSubWarehouseIndex].containerInfos;
      subWarehouseList = subLocationInfos.map(i => i.subWarehouseName);
    }
    const status = data.statusName === t('主仓集货完成') ? t('已主仓集货') : t('未主仓集货');
    let isFinishedList;
    let notFinishList;
    if (status === t('已主仓集货')) {
      isFinishedList = [...list];
      notFinishList = [];
    } else {
      isFinishedList = [];
      notFinishList = [...list];
    }
    const palletCodeList = isFinishedList.map(item => item.palletCode).filter(i => i);
    const palletCodeNum = [...new Set(palletCodeList)].length;
    const unFinishedPalletCodeNum = [...new Set(notFinishList.map(i => i.palletCode).filter(i => i))].length;
    return (
      <React.Fragment>
        <div className={style.title}>
          <div style={{ position: 'relative' }}>
            <span className={style.label} style={{ color: '#D9001B' }}>
              {t('当前扫描')}: {showSearchString}
            </span>
            { type === 0 && (
              <span
                className={style.lineName}
                onClick={() => {
                  window.location.hash = `/query/car-search/searchCollection/${showSearchString}`;
                }}
              >
                {t('车辆调度查询')}
              </span>
            )}
          </div>
        </div>
        <div className={style.title}>
          <div>
            <span className={style.label}>
              {t('主仓集货位')}:
            </span>
            <span
              className={style.value}
              style={{ color: '#FF993C' }}
            >
              {data.mainCollectLocation}
            </span>
          </div>
        </div>
        <div className={style.title}>
          <div>
            <span className={style.label}>
              {t('波次')}:
            </span>
            <span className={style.value}>{data.wellenCode}</span>
          </div>
        </div>
        <div className={style.title}>
          <div>
            <span className={style.label}>
              {t('一分暂存位')}:
            </span>
            <span className={style.value}>{data.firstSowingTmpLocation}</span>
          </div>
        </div>
        <div className={style.navWrap}>
          <NavBar>
            {
              subWarehouseList.map((item, index) => (
                <NavBarItem
                  key={item}
                  style={{ fontSize: 13 }}
                  onClick={() => {
                    if (palletSubWarehouseIndex !== index) {
                      store.changeData({
                        data: {
                          palletSubWarehouseIndex: index,
                        },
                      });
                    }
                  }}
                  active={palletSubWarehouseIndex === index}
                >
                  {item}
                </NavBarItem>
              ))
            }
          </NavBar>
        </div>
        <div className={style.scroll}>
          <div>
            <div>
              <span className={style.label}>{t('子仓集货位')}:</span>
              <span className={style.value}>{data.subCollectLocation}</span>
            </div>
          </div>
          <div
            className={style.col}
            style={{
              marginTop: 15,
              lineHeight: '25px',
            }}
          >
            <div className={style.statusName}>
              <div className={`${style.icon} ${style.uncomplete}`} />
              <div>{t('未主仓集货')}</div>
            </div>
            <div>
              <span className={style.boxNum}>{t('托数')}:</span>
              <span className={style.num}>{unFinishedPalletCodeNum}</span>
            </div>
            <div
              onClick={() => {
                if (notFinishList.length > 0) {
                  store.changeData({
                    data: {
                      showNotFinished: !showNotFinished,
                    },
                  });
                }
              }}
            >
              <span className={style.boxNum}>{t('箱数')}:</span>
              <span className={status === t('未主仓集货') ? style.num : style.uncompleteNum}>
                {notFinishList.length}
              </span>
              <Icon
                name="arr-down"
                style={{ color: notFinishList.length > 0 ? '#197AFA' : '#BCBDC7' }}
              />
            </div>
          </div>
          {
            showNotFinished &&
            (
              <Table
                columns={columns}
                dataSource={notFinishList}
                maxHeight={200}
              />
            )
          }
          <div className={style.col} style={{ marginTop: 15 }}>
            <div className={style.statusName}>
              <div className={`${style.icon} ${style.complete}`} />
              <div>{t('已主仓集货')}</div>
            </div>
            <div>
              <span className={style.boxNum}>{t('托数')}:</span>
              <span className={style.num}>{palletCodeNum}</span>
            </div>
            <div
              onClick={() => {
                if (isFinishedList.length > 0) {
                  store.changeData({
                    data: {
                      showFinished: !showFinished,
                    },
                  });
                }
              }}
            >
              <span className={style.boxNum}>{t('箱数')}:</span>
              <span className={status === t('已主仓集货') ? style.num : style.uncompleteNum}>
                {isFinishedList.length}
              </span>
              <Icon
                name="arr-down"
                style={{ color: isFinishedList.length > 0 ? '#197AFA' : '#BCBDC7' }}
              />
            </div>
          </div>
          {
            showFinished &&
            (
              <Table
                columns={columns}
                dataSource={isFinishedList}
                maxHeight={200}
              />
            )
          }
          <Tips {...this.props} />
        </div>
      </React.Fragment>
    );
  }
}

ContainerPalletInfo.propTypes = {
  data: PropTypes.shape(),
  typeShow: PropTypes.number,
  showFinished: PropTypes.bool,
  showNotFinished: PropTypes.bool,
  palletSubWarehouseIndex: PropTypes.number,
  showSearchString: PropTypes.string,
  type: PropTypes.number,
};

export default ContainerPalletInfo;
