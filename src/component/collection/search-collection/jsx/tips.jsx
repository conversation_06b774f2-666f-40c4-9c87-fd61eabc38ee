import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import style from '../style.css';

const Tips = (props) => {
  const {
    wellenTypeName,
    siteTips,
    collectTips,
    packType,
    nationalLine,
    isSubWholeCollect,
    pickingTip,
    subCollectFirstTip,
    subCollectEndTip,
    isRedOrder,
    isCombineOrder,
    isUrgentOrder,
    wellenMarkList,
  } = props;
  // 有值则展示标签，无值则返回null
  const showText = (text) => {
    if (text) {
      return <div className={style.redTag}>{text}</div>;
    }
    return null;
  };
  return (
    <div>
      {
        (wellenTypeName || siteTips || collectTips ||
          packType || nationalLine || isSubWholeCollect || isRedOrder) && (
          <div style={{ padding: '10px 0' }}>
            {
              wellenMarkList && wellenMarkList.length > 0 && (
                <React.Fragment>
                  {
                    wellenMarkList.map(item => <div key={item} className={style.redTag}>{item}</div>)
                  }
                </React.Fragment>
              )
            }
            {
              isSubWholeCollect && (
                <div className={style.redTag}>{t('整波集货')}</div>
              )
            }
            {
              wellenTypeName && (
                <div className={style.redTag}>{wellenTypeName}</div>
              )
            }
            {
              packType && (
                <div className={style.redTag}>{t('打包机打包')}</div>
              )
            }
            {
              isCombineOrder && (
                <div className={style.redTag}>{t('合包订单')}</div>
              )
            }
            {
              isRedOrder && (
                <div className={style.redTag}>{t('红人订单')}</div>
              )
            }
            {
              isUrgentOrder && (
                <div className={style.redTag}>{t('紧急订单')}</div>
              )
            }
            {
              siteTips === 'Amazon' ? (
                <div className={style.redTag}>{t('Amazon订单')}</div>
              ) : showText(siteTips)
            }
            {
              pickingTip && (
                <div>
                  <span className={style.tipTitle}>{t('拣货分流')}：</span>
                  <span className={style.fBold}>{pickingTip}</span>
                </div>
              )
            }
            {
              subCollectFirstTip && (
                <div>
                  <span className={style.tipTitle}>{t('子仓集货首箱分流')}：</span>
                  <span className={style.fBold}>{subCollectFirstTip}</span>
                </div>
              )
            }
            {
              subCollectEndTip && (
                <div>
                  <span className={style.tipTitle}>{t('子仓集货完成分流')}：</span>
                  <span className={style.fBold}>{subCollectEndTip}</span>
                </div>
              )
            }
            {
              collectTips && (
                <div style={{ color: 'red', fontSize: 25 }}>{collectTips}</div>
              )
            }

            {
              !!nationalLine && (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    marginTop: 15,
                  }}
                >
                  <div
                    style={{
                      color: 'rgb(209, 136, 58)',
                      border: '1px solid rgb(209, 136, 58)',
                      padding: '5px 10px',
                    }}
                  >
                    {nationalLine}
                  </div>
                </div>
              )
            }
          </div>
        )
      }
    </div>
  );
};

Tips.propTypes = {
  collectTips: PropTypes.string,
  siteTips: PropTypes.string,
  wellenTypeName: PropTypes.string,
  packType: PropTypes.bool,
  nationalLine: PropTypes.string,
  isSubWholeCollect: PropTypes.bool,
  pickingTip: PropTypes.string,
  subCollectFirstTip: PropTypes.string,
  subCollectEndTip: PropTypes.string,
  isRedOrder: PropTypes.bool,
  isCombineOrder: PropTypes.bool,
  isUrgentOrder: PropTypes.bool,
  wellenMarkList: PropTypes.arrayOf(),
};

export default Tips;
