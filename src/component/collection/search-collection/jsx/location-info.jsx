import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Tab, TabBody, NavBar, NavBarItem,
} from 'react-weui/build/packages';
import store from '../reducers';
import SubWarehouseItem from './subWarehouse-item';
import Tips from './tips';
import style from '../style.css';

class LocationInfo extends Component {
  render() {
    const {
      data,
      currentSubWarehouseId,
      showSearchString,
      type,
    } = this.props;
    const { subLocationInfos } = data;
    const len = subLocationInfos.length;
    const currentSubLocation = subLocationInfos.find(item => item.subWarehouseId
      === currentSubWarehouseId) || subLocationInfos[0];
    if (!currentSubWarehouseId) {
      store.changeData({
        data: {
          currentSubWarehouseId: currentSubLocation.subWarehouseId,
        },
      });
    }
    const notFinishList = currentSubLocation.containerInfos.filter(item => item.statusName !== t('主仓集货完成'));
    const isFinishedList = currentSubLocation.containerInfos.filter(item => item.statusName === t('主仓集货完成'));
    const palletCodeList = isFinishedList.map(item => item.palletCode).filter(i => i);
    const palletCodeNum = [...new Set(palletCodeList)].length;
    const unFinishedPalletCodeNum = [...new Set(notFinishList.filter(i => i.palletCode).map(i => i.palletCode))].length;
    return (
      <React.Fragment>
        { type === 0 && (
          <div className={`${style.col} ${style.title}`} style={{ flexWrap: 'wrap' }}>
            <div>
              <span className={style.label} style={{ color: '#D9001B' }}>{t('当前扫描')}:</span>
              <span className={style.value} style={{ color: '#D9001B' }}>{showSearchString}</span>
            </div>
          </div>
        )}
        <div className={`${style.col} ${style.title}`} style={{ flexWrap: 'wrap' }}>
          <div>
            <span className={style.label}>{t('主仓集货位')}:</span>
            <span className={style.value} style={{ color: '#FF993C' }}>{data.mainCollectLocation}</span>
          </div>
        </div>
        <div className={`${style.col} ${style.title}`} style={{ flexWrap: 'wrap' }}>
          <div>
            <span className={style.label}>{t('波次')}:</span>
            <span className={style.value}>{data.wellenCode}</span>
          </div>
        </div>
        <div className={`${style.col} ${style.title}`} style={{ flexWrap: 'wrap' }}>
          <div>
            <span className={style.label}>{t('一分暂存位')}:</span>
            <span className={style.value}>{data.firstSowingTmpLocation}</span>
          </div>
        </div>
        {
          len === 1 &&
          (
            <div className={style.scroll}>
              <div style={{ textAlign: 'center' }}>{subLocationInfos[0].subWarehouseName}</div>
              <SubWarehouseItem
                subLocationInfos={subLocationInfos}
                notFinishList={notFinishList}
                isFinishedList={isFinishedList}
                palletCodeNum={palletCodeNum}
                unFinishedPalletCodeNum={unFinishedPalletCodeNum}
                {...this.props}
              />
              <Tips {...this.props} />
            </div>
          )
        }
        {
          len >= 2 &&
          (
            <div style={{ flex: '1 1 auto', height: 0 }}>
              <Tab>
                <NavBar>
                  {
                    subLocationInfos.map((item, index) => (
                      <NavBarItem
                        key={index}
                        style={{ fontSize: len === 2 ? 15 : 12 }}
                        onClick={() => {
                          if (currentSubWarehouseId !== item.subWarehouseId) {
                            store.changeData({
                              data: {
                                currentSubWarehouseId: item.subWarehouseId,
                                showFinished: false,
                                showNotFinished: false,
                              },
                            });
                          }
                        }}
                        active={currentSubWarehouseId === item.subWarehouseId}
                      >
                        {item.subWarehouseName}
                      </NavBarItem>
                    ))
                  }
                </NavBar>
                <TabBody>
                  <div style={{ padding: '0 15px' }}>
                    <SubWarehouseItem
                      subLocationInfos={subLocationInfos}
                      notFinishList={notFinishList}
                      isFinishedList={isFinishedList}
                      palletCodeNum={palletCodeNum}
                      unFinishedPalletCodeNum={unFinishedPalletCodeNum}
                      {...this.props}
                    />
                    <Tips {...this.props} />
                  </div>
                </TabBody>
              </Tab>
            </div>
          )
        }
      </React.Fragment>
    );
  }
}

LocationInfo.propTypes = {
  data: PropTypes.shape(),
  currentSubWarehouseId: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  showSearchString: PropTypes.string,
  type: PropTypes.number,
};

export default LocationInfo;
