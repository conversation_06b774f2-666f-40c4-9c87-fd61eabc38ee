import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import UserIntro from 'common/user-intro';
import IntroContext from '../../user-intro-page/IntroContext';
import { classFocus } from '../../../lib/util';
import store, { classNameMap } from './reducers';
import {
  Header,
  Footer,
  FocusInput,
  FooterBtn,
  pages,
} from '../../common';
import ContainerPalletInfo from './jsx/container-pallet-info';
import LocationInfo from './jsx/location-info';
import style from './style.css';

const { View } = pages;

let content;
let data;

const labelMap = new Map([
  [0, t('周转箱')],
  [1, t('托盘')],
  [2, t('集货货位')],
]);

class Container extends Component {
  componentDidMount() {
    store.init();
    const { type } = this.props;
    const className = classNameMap.get(type);
    classFocus(className);
  }

  render() {
    const {
      dispatch,
      dataLoading,
      containerCode,
      palletCode,
      collectLocation,
      containerResultRsp,
      palletResultRsp,
      locationRspList,
      showContent,
      type, // 0：周转箱，1：托盘，2：集货库位
      typeShow,
      headerTitle,
      // collectTips,
      // siteTips,
      // wellenTypeName,
      // packType,
      steps,
    } = this.props;

    // const arr = [collectTips, siteTips, wellenTypeName, packType];
    // let height = 0;
    // for (let i = 0; i < 3; i++) {
    //   if (arr[i]) height += 10;
    // }

    const label = labelMap.get(type);

    switch (typeShow) {
      case 0:
        data = containerResultRsp;
        break;
      case 1:
        data = palletResultRsp;
        break;
      case 2:
        data = locationRspList[0];
        break;
      default:
        break;
    }

    if (typeShow === 2) {
      content = <LocationInfo {...this.props} data={data} />;
    } else {
      content = <ContainerPalletInfo {...this.props} data={data} />;
    }

    return (
      <IntroContext.Consumer>
        {(context) => {
          context.changePageStore(store);
          return (
            <View>
              <Header title={headerTitle || t('集货查询')} />
              <UserIntro
                showIntro={context.showIntroVal}
                steps={steps}
                finishHandle={() => {
                  store.init();
                  const { type } = this.props;
                  const className = classNameMap.get(type);
                  classFocus(className);
                }}
              />
              <div style={{ minHeight: '54px' }}>
                {
                type === 0 &&
                (
                  <Form>
                    <FocusInput
                      data-step="1"
                      data-intro={t('第一步，扫描集货周转箱')}
                      placeholder={context.showIntroVal ? t('请扫描集货周转箱') : ''}
                      value={containerCode}
                      className="containerCode"
                      label={label}
                      disabled={dataLoading === 0 || context.showIntroVal}
                      onChange={(e) => {
                        store.changeData({
                          data: {
                            containerCode: e.target.value,
                          },
                        });
                      }}
                      onPressEnter={() => {
                        if (containerCode) {
                          store.changeData({
                            data: {
                              showSearchString: containerCode,
                            },
                          });
                          store.getData({
                            param: {
                              containerCode,
                              type,
                            },
                          });
                        }
                      }}
                    />
                  </Form>
                )
              }
                {
                type === 1 &&
                (
                  <Form>
                    <FocusInput
                      value={palletCode}
                      className="palletCode"
                      label={label}
                      disabled={dataLoading === 0}
                      onChange={(e) => {
                        store.changeData({
                          data: {
                            palletCode: e.target.value,
                          },
                        });
                      }}
                      onPressEnter={() => {
                        if (palletCode) {
                          store.changeData({
                            data: {
                              showSearchString: palletCode,
                            },
                          });
                          store.getData({
                            param: {
                              palletCode,
                              type,
                            },
                          });
                        }
                      }}
                    />
                  </Form>
                )
              }
                {
                type === 2 &&
                (
                  <Form>
                    <FocusInput
                      autoFocus
                      value={collectLocation}
                      className="collectLocation"
                      label={label}
                      disabled={dataLoading === 0}
                      onChange={(e) => {
                        store.changeData({
                          data: {
                            collectLocation: e.target.value,
                          },
                        });
                      }}
                      onPressEnter={() => {
                        if (collectLocation) {
                          store.changeData({
                            data: {
                              showSearchString: collectLocation,
                            },
                          });
                          store.getData({
                            param: {
                              collectLocation,
                              type,
                            },
                          });
                        }
                      }}
                    />
                  </Form>
                )
              }
              </div>
              {
                showContent &&
                (
                  <div className={style.content}>
                      {content}
                  </div>
                )
              }

              <Footer
                dispatch={dispatch}
                beforeBack={(back) => {
                  store.reset();
                  back();
                }}
              >
                <FooterBtn
                  onClick={() => {
                    let t = type + 1;
                    t = t > 2 ? 0 : t;
                    const className = classNameMap.get(t);
                    store.changeData({
                      data: {
                        type: t,
                        collectLocation: '',
                        palletCode: '',
                        containerCode: '',
                        renderContent: false,
                      },
                    });
                    classFocus(className);
                  }}
                >
                  {t('切换')}
                </FooterBtn>
              </Footer>
            </View>
          );
        }}
      </IntroContext.Consumer>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  dataLoading: PropTypes.number,
  containerCode: PropTypes.string,
  palletCode: PropTypes.string,
  collectLocation: PropTypes.string,
  containerResultRsp: PropTypes.shape(),
  palletResultRsp: PropTypes.shape(),
  locationRspList: PropTypes.arrayOf(PropTypes.object),
  type: PropTypes.number,
  typeShow: PropTypes.number,
  showContent: PropTypes.bool,
  headerTitle: PropTypes.string,
  steps: PropTypes.arrayOf(PropTypes.object),
  // collectTips: PropTypes.string,
  // siteTips: PropTypes.string,
  // wellenTypeName: PropTypes.string,
  // packType: PropTypes.bool,
};

export default i18n(Container);
