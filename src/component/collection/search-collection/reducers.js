import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import modal from '../../common/modal';
import collectQueryApi from './server';
// import message from '../../common/message';
import { classFocus, getHeaderTitle } from '../../../lib/util';

export const classNameMap = new Map([
  [0, 'containerCode'],
  [1, 'palletCode'],
  [2, 'collectLocation'],
]);

export const columns = [
  {
    title: t('托盘'),
    dataIndex: 'palletCode',
    default: '- -',
    width: 10,
  },
  {
    title: t('周转箱'),
    dataIndex: 'container',
    width: 10,
  },
  {
    title: t('状态'),
    dataIndex: 'statusName',
    width: 10,
  },
  {
    title: t('库区'),
    dataIndex: 'areaName',
    width: 10,
  },
];

const defaultState = {
  containerCode: '',
  palletCode: '',
  collectLocation: '',
  containerResultRsp: {
    wellenCode: '',
    container: '',
    mainCollectLocation: '',
    statusName: '',
    subCollectLocation: '',
    firstSowingTmpLocation: '',
  },
  palletResultRsp: {
    wellenCode: '',
    mainCollectLocation: '',
    subCollectLocation: '',
    palletCode: '',
    statusName: '',
    container: [],
    firstSowingTmpLocation: '',
  },
  locationRspList: [],
  dataLoading: 1,
  showContent: false,
  currentSubWarehouseId: '',
  showFinished: false,
  showNotFinished: false,
  type: 0, // 0：周转箱，1：托盘，2：集货库位
  typeShow: 0,
  headerTitle: '',
  collectTips: '',
  siteTips: '',
  wellenTypeName: '',
  packType: false,
  isSubWholeCollect: false, // 是否整波集货
  nationalLine: '',
  pickingTip: '', // 拣货分流提示
  subCollectFirstTip: '', // 子仓集货首箱分流提示
  subCollectEndTip: '', // 子仓集货完成分流提示
  palletSubWarehouseIndex: 0,
  isRedOrder: false, // 是否红人订单
  isCombineOrder: false, // 是否合包订单
  isUrgentOrder: false, // 是否紧急订单
  steps: [],
  wellenMarkList: [], // 波次特殊信息提示
  showSearchString: '', // 仅用于扫描数据的展示
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  reset(draft) {
    assign(draft, defaultState);
  },
  // 初始当前页面数据
  resetPageStore(draft) {
    assign(draft, defaultState, { headerTitle: draft.headerTitle });
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  * getData(action, ctx) {
    markStatus('dataLoading');
    const { type } = action.param;
    Reflect.deleteProperty(action.param, 'type');
    const className = classNameMap.get(type);
    const res = yield collectQueryApi(action.param);
    if (res.code === '0') {
      const {
        containerResultRsp, palletResultRsp, locationRspList,
        collectTips, siteTips, wellenTypeName, packType, nationalLine, isSubWholeCollect,
        pickingTip, subCollectFirstTip, subCollectEndTip, isRedOrder, isCombineOrder, isUrgentOrder,
        wellenMarkList,
      } = res.info;
      yield ctx.changeData({
        data: {
          containerResultRsp,
          palletResultRsp,
          locationRspList,
          containerCode: '',
          collectLocation: '',
          palletCode: '',
          showContent: true,
          showFinished: false,
          showNotFinished: false,
          typeShow: type,
          currentSubWarehouseId: '',
          collectTips,
          siteTips,
          wellenTypeName,
          packType,
          nationalLine,
          isSubWholeCollect,
          pickingTip,
          subCollectFirstTip,
          subCollectEndTip,
          isRedOrder,
          isCombineOrder,
          isUrgentOrder,
          palletSubWarehouseIndex: 0,
          wellenMarkList,
        },
      });
      classFocus(className);
    } else {
      yield ctx.changeData({
        data: {
          containerCode: '',
          collectLocation: '',
          palletCode: '',
          showContainerCode: '',
        },
      });
      modal.error({ content: res.msg, className });
    }
  },
};
