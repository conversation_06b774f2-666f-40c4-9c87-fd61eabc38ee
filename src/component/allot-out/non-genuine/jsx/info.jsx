import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, Footer, View,
} from 'common';
import store from '../reducers';

class Info extends Component {
  render() {
    const {
      headerTitle,
      dataLoading,
      boxCode,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle || t('非正品分箱')} />
        <View
          diff={100}
          flex={false}
        >
          <Form>
            <FocusInput
              value={boxCode}
              className="boxCode"
              placeholder={t('请扫描')}
              disabled={!dataLoading}
              autoFocus
              onChange={(e) => {
                const { value } = e.target;
                if (!value.trim()) {
                  store.changeData({
                    boxCode: '',
                  });
                } else {
                  store.changeData({
                    boxCode: value.trim().toUpperCase(),
                  });
                }
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanBoxCode();
              }}
            >
              <label>{t('来源箱号')}</label>
            </FocusInput>
          </Form>
        </View>
        <Footer
          beforeBack={(back) => {
            back();
          }}
        />
      </div>
    );
  }
}

Info.propTypes = {
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  boxCode: PropTypes.string,
};

export default i18n(Info);
