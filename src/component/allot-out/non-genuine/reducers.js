import React from 'react';
import { t } from '@shein-bbl/react';
import { classFocus, getHeaderTitle } from 'lib/util';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { modal, message } from 'common';
import {
  nonGenuineScanBoxCodeAPI,
  nonGenuineScanBarCodeAPI,
  nonGenuineScanContainerCodeAPI,
  nonGenuineCloseContainerAPI,
  nonGenuineEmptyBoxAPI,
  nonGenuineBoxDetailAPI,
  queryDataDict,
  beforeCheckAPI,
} from './server';
import style from './style.less';
import aaoo from '../../../source/audio/aaoo.mp3';
import dingdong from '../../../source/audio/dingdong.mp3';

const dingdongEle = new Audio(dingdong);
dingdongEle.load();// 啊哦提示音
const aaooEle = new Audio(aaoo);
aaooEle.load(); // 叮咚提示音

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  currentPage: 1, // 1 初始页面(只有来源箱号)，2 操作页面，3 明细页面detail
  boxCode: '', // 大箱号
  barCode: '', // 商品条码,
  containerCode: '', // 分播周转箱
  containerCodeList: [], // 周转箱列表
  skc: '',
  size: '',
  goodsLabel: '',
  goodsLabelStr: '', // 商品标签
  scanContainerDisabled: false, // 扫描周转箱是否成功
  boxCodeDisabled: false,
  sowingNum: 0, // 已扫件数
  totalNum: 0, // 总数
  dictList: [], // 类型字典
  arrIndex: 0, // 目标自定义箱位置
  // 存储属性
  storeTypeStr: '',
  // 图形字典
  diagramList: {
    GENERAL: 'null',
    TORT: 'circle',
    UNSALABLE_1: 'circle',
    UNSALABLE_2: 'circle2',
    OVER: 'null',
    BLACK_CODE: 'square',
    DEFECTIVE: 'triangle',
  },
  // 明细列表
  detailList: [],
  // 操作人
  sowingUser: '',
  firstBox: true,
  operationStepStr: t('一分'),
};

// 判断展示图形
const showDiagram = (showObj, diagramList) => style[diagramList[showObj.flag]];

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(state, data) {
    Object.assign(state, data);
  },
  * init() {
    markStatus('dataLoading');
    yield this.changeData({
      headerTitle: getHeaderTitle() || t('非正品分箱'),
    });
    // 获取入库异常枚举值
    const data = yield queryDataDict({
      catCode: ['GoodsLabelType'],
    });
    if (data.code === '0') {
      yield this.changeData({
        dictList: data.info.data.find((i) => i.catCode === 'GoodsLabelType')
          .dictListRsps || [],
      });
    } else {
      modal.error({ title: data.msg });
    }
    const res = yield beforeCheckAPI();
    if (res.code === '0') {
      if (res.info.boxCode) {
        yield this.changeData({
          boxCode: res.info.boxCode,
        });
        yield this.scanBoxCode();
      } else {
        classFocus('boxCode');
      }
    } else {
      classFocus('boxCode');
    }
  },
  // 重置数据
  * clearData() {
    const { headerTitle, dictList, containerCodeList } = yield '';
    yield this.changeData({
      ...defaultState,
      headerTitle,
      dictList,
      containerCodeList,
    });
  },
  // 扫描来源箱号
  * scanBoxCode({ keepSeeding }) {
    markStatus('dataLoading');
    const { boxCode } = yield '';
    const param = { boxCode };
    // 是否继续分箱
    if (keepSeeding) {
      param.keepSeeding = keepSeeding;
    }
    const res = yield nonGenuineScanBoxCodeAPI(param);
    if (res.code === '0') {
      yield this.changeData({
        sowingNum: res.info.sowingNum, // 已扫件数
        totalNum: res.info.totalNum, // 总数
        boxCodeDisabled: true, // 来源箱号不可编辑
        operationStepStr: res.info.operationStepStr,
      });
      if (res.info.isKeepSeeding) {
        yield this.changeData({
          // 继续分箱页面
          currentPage: 3,
        });
      } else {
        yield this.changeData({
          // 操作页
          currentPage: 2,
        });
        classFocus('barCode');
      }
    } else if (res.code === '403511') {
      yield this.closeContainer({ closeContainerCode: boxCode, flag: 'boxCode' });
    } else {
      if (['403512', '403513', '403514', '403515', '403523'].includes(res.code)) {
        aaooEle.play();
      }
      yield this.changeData({ boxCode: '' });
      modal.error({ content: res.msg, className: 'boxCode' });
    }
  },
  // 扫描商品条码
  * scanBarCode({ goodsLabel }) {
    markStatus('dataLoading');
    const {
      barCode, boxCode, dictList, containerCodeList,
    } = yield '';
    const param = {
      barCode,
      boxCode,
    };
    // 是否存在商品品质
    if (goodsLabel) {
      param.goodsLabel = goodsLabel;
      yield this.changeData({
        goodsLabel,
      });
    }
    const res = yield nonGenuineScanBarCodeAPI(param);
    if (res.code === '0') {
      // 交互逻辑
      // resultType===1 需要扫描周转箱
      // resultType===2 不需要扫描周转箱
      if (res.info.resultType) {
        if (res.info.resultType === 1) {
          yield this.changeData({
            containerCode: '',
            scanContainerDisabled: false,
            arrIndex: '', // 需要继续扫描分播箱，当前目标箱号置空
          });
          classFocus('containerCode');
        } else if (res.info.resultType === 2) {
          // 对周转箱号前端缓存列表进行操作
          // 判断当前周转箱号是否存在列表中
          if (!containerCodeList.includes(res.info.matchContainerCode)) {
            // 判断列表中是否存在空值
            if (containerCodeList.findIndex((v) => v === '') !== -1) {
              // 将空值替换为当前周转箱号
              containerCodeList[containerCodeList.findIndex((v) => v === '')] = res.info.matchContainerCode;
            } else {
              // 将当前周转箱号添加到列表中
              containerCodeList.push(res.info.matchContainerCode);
            }
          }
          // 获取当前周转箱号在列表中的位置
          const index = containerCodeList.findIndex((v) => v === res.info.matchContainerCode) + 1;
          // 判断是否直接分箱完成
          // 完成重置数据
          if (res.info.finishFlag) {
            message.success(t('{}非正品箱已全部分箱完成!', boxCode));
            yield this.changeData({
              boxCode: '', // 大箱号
              barCode: '', // 商品条码,
              containerCode: '', // 分播周转箱
              scanContainerDisabled: false, // 扫描周转箱是否成功
              boxCodeDisabled: false,
              sowingNum: 0, // 已扫件数
              totalNum: 0, // 总数
              arrIndex: index,
              firstBox: false,
              operationStepStr: t('一分'),
            });
            classFocus('boxCode');
          } else {
            yield this.changeData({
              barCode: '',
              sowingNum: res.info.sowingNum, // 已扫件数
              totalNum: res.info.totalNum, // 总数
              containerCode: res.info.matchContainerCode,
              scanContainerDisabled: true,
              containerCodeList,
              arrIndex: index,
            });
          }
        }
        yield this.changeData({
          goodsLabel: res.info.goodsLabel,
          skc: res.info.skc,
          size: res.info.size,
          goodsLabelStr: res.info.goodsLabelStr,
          storeTypeStr: res.info.storeTypeStr,
        });
      }
      // 判断是否需要展示图形
      if (res.info.needAlarm) {
        yield this.changeData({
          showDiagramList: res.info.alarmGoodsLabelList.map((v) => ({
            flag: dictList.find((item) => item.dictCode === v).dictNameEn,
            id: v,
          })),
          skc: res.info.skc,
          size: res.info.size,
        });
        const {
          showDiagramList, diagramList, skc, size,
        } = yield '';
        // 提示音
        dingdongEle.play();
        const status = yield new Promise((r) => modal.confirm2({
          title: t('请选择商品品质'),
          content: (
            // eslint-disable-next-line react/jsx-filename-extension
            <div style={{ marginBottom: '10px' }}>
              <div style={{ textAlign: 'left' }}>
                {t('SKC:{}', skc)}
              </div>
              <div style={{ textAlign: 'left' }}>
                {t('尺码:{}', size)}
              </div>
              <div>{t('请选择商品品质')}</div>
            </div>
          ),
          okText: (
            <div
              style={{
                width: '100%', textAlign: 'center', display: 'flex', justifyContent: 'center',
              }}
            >
              <div className={showDiagram(showDiagramList[0], diagramList)} style={{ margin: '10px 0 10px 0' }} />
            </div>
          ),
          cancelText: (
            <div
              style={{
                width: '100%', textAlign: 'center', display: 'flex', justifyContent: 'center',
              }}
            >
              <div className={showDiagram(showDiagramList[1], diagramList)} style={{ margin: '10px 0 10px 0' }} />
            </div>
          ),
          onOk: () => r(true),
          onCancel: () => r(false),
        }));
        if (status) {
          yield this.scanBarCode({ goodsLabel: showDiagramList[0].id });
        } else {
          yield this.scanBarCode({ goodsLabel: showDiagramList[1].id });
        }
      }
    } else if (res.code === '403516') {
      // 尝试关箱
      yield this.closeContainer({ closeContainerCode: barCode, flag: 'barCode' });
    } else {
      yield this.changeData({ barCode: '' });
      modal.error({ content: res.msg, className: 'boxCode' });
    }
  },
  // 扫描周转箱
  * scanContainerCode() {
    markStatus('dataLoading');
    const {
      barCode, boxCode, containerCode, goodsLabel, containerCodeList,
    } = yield '';
    const res = yield nonGenuineScanContainerCodeAPI({
      barCode,
      boxCode,
      containerCode,
      goodsLabel,
    });
    if (res.code === '0') {
      // 判断是否直接分箱完成
      if (res.info.finishFlag) {
        message.success(t('{}非正品箱已全部分箱完成!', boxCode));
        yield this.changeData({
          boxCode: '', // 大箱号
          barCode: '', // 商品条码,
          skc: '',
          size: '',
          goodsLabel: '',
          goodsLabelStr: '', // 商品标签
          scanContainerDisabled: false, // 扫描周转箱是否成功
          boxCodeDisabled: false,
          sowingNum: 0, // 已扫件数
          totalNum: 0, // 总数
          operationStepStr: t('一分'),
        });
        classFocus('boxCode');
      } else {
        // 判断周转箱号是否已经存在
        if (containerCodeList.find((v) => v === containerCode)) {
          yield this.changeData({
            arrIndex: containerCodeList.findIndex((v) => v === containerCode) + 1,
          });
        } else {
        // 周转箱号缓存列表
          if (containerCodeList.findIndex((v) => v === '') !== -1) {
            containerCodeList[containerCodeList.findIndex((v) => v === '')] = containerCode;
          } else {
            containerCodeList.push(containerCode);
          }
        }
        yield this.changeData({
          containerCodeList,
          arrIndex: containerCodeList.findIndex((v) => v === containerCode) + 1,
          scanContainerDisabled: true,
          barCode: '',
          sowingNum: res.info.sowingNum, // 已扫件数
          totalNum: res.info.totalNum, // 总数
        });
        classFocus('barCode');
      }
    } else {
      if (['403517', '403518', '403522'].includes(res.code)) {
        aaooEle.play();
      }
      yield this.changeData({ containerCode: '' });
      modal.error({ content: res.msg, className: 'containerCode' });
    }
  },
  // 关箱
  * closeContainer({ closeContainerCode, flag }) {
    markStatus('dataLoading');
    const {
      containerCode, containerCodeList,
    } = yield '';
    dingdongEle.play();
    const status = yield new Promise((r) => modal.confirm2({
      modalBlurInput: true,
      title: t('关箱'),
      content: (
        <div>{t('是否确定关箱?')}</div>
      ),
      onOk: () => r(true),
      onCancel: () => r(false),
    }));
    if (status) {
      const res = yield nonGenuineCloseContainerAPI({
        containerCode: closeContainerCode || containerCode,
      });
      if (res.code === '0') {
        if (flag) {
          yield this.changeData({
            [flag]: '',
          });
        }
        // 需要关箱的周转箱号
        let nowContainerCode = '';
        if (closeContainerCode && closeContainerCode !== containerCode) {
          nowContainerCode = closeContainerCode;
        } else {
          nowContainerCode = containerCode;
        }
        yield this.changeData({
          containerCode: closeContainerCode ? containerCode : '',
          scanContainerDisabled: false,
          containerCodeList: containerCodeList.map((v) => ((v === nowContainerCode) ? '' : v)),
          arrIndex: '',
        });
        message.success(res.info.frontMsg, 2000);
      } else {
        if (['403519', '403520'].includes(res.code)) {
          aaooEle.play();
        }
        // 是否是商品条码关箱
        if (closeContainerCode) {
          yield this.changeData({
            [flag]: '',
          });
        }
        modal.error({ content: res.msg });
      }
      classFocus(`${flag}`);
    }
  },
  // 箱空
  * emptyBox({ hasDoubleCheck }) {
    markStatus('dataLoading');
    const {
      boxCode, containerCodeList, dictList, headerTitle,
    } = yield '';
    const param = { boxCode };
    // 是否需要二次确认
    if (hasDoubleCheck) {
      param.hasDoubleCheck = hasDoubleCheck;
    }
    const res = yield nonGenuineEmptyBoxAPI(param);
    if (res.code === '0') {
      if (res.info.doubleCheck) {
        const status = yield new Promise((r) => modal.confirm2({
          title: t('是否确认箱空'),
          content: (
            <div>{t('可箱空{}件', res.info.emptyBoxNum)}</div>
          ),
          onOk: () => r(true),
          onCancel: () => r(false),
        }));
        if (status) {
          yield this.emptyBox({ hasDoubleCheck: true });
        }
      } else {
        message.success(t('箱空成功！'), 2000);
        yield this.changeData({
          ...defaultState,
          headerTitle,
          containerCodeList,
          dictList,
        });
      }
    } else {
      if (['403521'].includes(res.code)) {
        aaooEle.play();
      }
      modal.error({ content: res.msg });
    }
  },
  // 装箱明细
  * jumpDetail() {
    markStatus('dataLoading');
    const { boxCode } = yield '';
    const res = yield nonGenuineBoxDetailAPI({ boxCode });
    if (res.code === '0') {
      if (res.info.boxDetailRspList?.length) {
        yield this.changeData({
          detailList: res.info.boxDetailRspList,
          sowingUser: res.info.sowingUser,
          sowingNum: res.info.sowingNum,
          currentPage: 4,
        });
      } else {
        modal.error({ content: t('当前暂无装箱明细!') });
      }
    } else {
      modal.error({ content: res.msg });
    }
  },

};
