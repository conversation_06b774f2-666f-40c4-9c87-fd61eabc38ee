import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { classFocus, getHeaderTitle } from 'lib/util';
import { modal, message } from 'common';
import error from 'source/audio/delete.mp3';
import {
  scanBoxAPI,
  scanDeliverTruckAPI,
} from './server';

const audio = new Audio(error);
audio.load();

export const defaultState = {
  dataLoading: 1,
  headerTitle: '',
  activeTab: 1, // 激活tab
  showDetailPage: false,
  detailPageType: '',
  code: '', // 扫描的条码
  scanCode: '', // 记录请求接口的code
  allocationInfo: null, // 调拨信息
  handoverContainerInfo: null, // 交接信息
  skuInfo: null, // sku信息
  splitBoxInfo: null, // 分箱信息
  deliverTruckInfo: null, // 车辆信息
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(draft, data) {
    assign(draft, data);
  },
  * init() {
    yield this.changeData({ headerTitle: getHeaderTitle() });
    classFocus('code');
  },

  // 重置数据
  * clearData(action) {
    yield this.changeData({
      ...defaultState,
      ...action,
    });
    classFocus('code');
  },

  // 扫描箱子
  * scanCode(action) {
    const { code } = action;
    const params = {
      containerCode: code,
      warehouseId: JSON.parse(localStorage.getItem('warehouse') || '{}').warehouseId,
    };
    markStatus('dataLoading');
    const { code: resCode, info, msg } = yield scanBoxAPI(params);
    const {
      allocationInfo,
      handoverContainerInfo,
      skuInfo,
      splitBoxInfo,
      content,
    } = info || {};
    if (resCode === '0') {
      yield this.changeData({
        allocationInfo,
        handoverContainerInfo,
        skuInfo,
        splitBoxInfo,
        code: '',
        scanCode: code,
      });
      if (content) {
        message.warning(content);
      }
      classFocus('code');
    } else {
      yield this.changeData({ code: '' });
      modal.error({
        content: msg,
        onOk: () => classFocus('code'),
      });
    }
  },

  // 扫车牌
  * scanDeliverTruckNo(action) {
    const { code } = action;
    const params = {
      deliverTruckNo: code,
      warehouseId: JSON.parse(localStorage.getItem('warehouse') || '{}').warehouseId,
    };
    markStatus('dataLoading');
    const { code: resCode, info, msg } = yield scanDeliverTruckAPI(params);
    if (resCode === '0') {
      yield this.changeData({
        deliverTruckInfo: info,
        code: '',
        scanCode: code,
      });
      classFocus('code');
    } else {
      yield this.changeData({ code: '' });
      modal.error({
        content: msg,
        onOk: () => classFocus('code'),
      });
    }
  },
};
