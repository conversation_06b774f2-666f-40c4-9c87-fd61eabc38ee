import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { Toast } from 'react-weui/build/packages';
import classNames from 'classnames';
import {
  Footer, Header, FocusInput, pages,
} from 'common';
import store from './reducers';
import Container from './jsx/content';
import style from './style.less';
import DetailPage from './jsx/detail';

const { View } = pages;

const DefaultPage = (props) => {
  const {
    dispatch,
    headerTitle,
    dataLoading,
    code,
    activeTab,
    showDetailPage,
  } = props;

  const tabPlaceholder = {
    1: t('箱号'),
    2: t('车牌号'),
  };

  useEffect(() => {
    store.init();
  }, []);

  return (
    <div>
      {showDetailPage ? (
        <DetailPage {...props} />
      ) : (
        <View>
          <Header title={headerTitle || t('运输跟踪')} />
          <div className={style.contentWrapper}>
            <div className={style.handleButtonWrapper}>
              {[t('箱号'), t('车牌号')].map((item, index) => (
                <span
                  key={item}
                  className={classNames([activeTab === index + 1 ? style.active : ''])}
                  onClick={() => {
                    store.clearData({ activeTab: index + 1 });
                  }}
                >
                  {item}
                </span>
              ))}
            </div>
          </div>
          <Form>
            <FocusInput
              placeholder={t('请扫描{}', tabPlaceholder[activeTab])}
              className="code"
              autoFocus
              value={code}
              disabled={dataLoading === 0}
              onChange={(e) => {
                store.changeData({ code: e.target.value });
              }}
              onPressEnter={(e) => {
                if (!e.target?.value?.trim()) {
                  return;
                }
                switch (activeTab) {
                  case 1:
                    store.scanCode({ code: e.target.value });
                    break;
                  case 2:
                    store.scanDeliverTruckNo({ code: e.target.value });
                    break;
                  default:
                    break;
                }
              }}
            >
              <label>{tabPlaceholder[activeTab]}</label>
            </FocusInput>
          </Form>
          <Container {...props} />
          <Footer
            beforeBack={(back) => {
              back();
            }}
            dispatch={dispatch}
          />
          <Toast icon="loading" show={dataLoading === 0}>loading...</Toast>
        </View>
      )}
    </div>
  );
};

DefaultPage.propTypes = {
  dispatch: PropTypes.func,
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  code: PropTypes.string,
  activeTab: PropTypes.number,
  showDetailPage: PropTypes.bool,
  routeContainerCode: PropTypes.string,
  match: PropTypes.shape(),
};

export default i18n(DefaultPage);
