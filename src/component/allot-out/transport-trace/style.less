.contentWrapper {
  padding: 6px 12px 0px 12px;
  background-color: #fff;
}

// tabButton部分
.handleButtonWrapper {
  font-size: 14px;
  border-radius: 5px;
  border: 1px solid #197AFA;
  overflow: hidden;
  width: 100%;
  display: flex;
  position: relative;

  span {
    position: relative;
    // width: 98px;
    flex: 1;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 16px;
    padding: 6px 0;
    text-align: center;
    background-color: #fff;
    color: #197afa;
    box-sizing: border-box;

    &.active {
      background-color: #197afa;
      color: #fff;
      box-shadow: -1px 0px #197AFA;
    }
  }

  span::after {
    position: absolute;
    top: 50%;
    right: 0px;
    content: '';
    width: 0px;
    height: 14px;
    border-right: 1px solid #197AFA;
    z-index: 1;
    transform: translateY(-50%);
  }

  span:last-child::after {
    border: none;
  }
}

.sectionWrapper {
  padding: 14px;
  flex: 1;
  overflow: auto;
  .scrollPart {
    margin-bottom: 15px;
    border: 1px solid #DDDDDD;
    padding: 5px 0;
  }

  .subTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 14px;
    color: #197afa;
  }
}

.containerListWrap {
  padding: 0 14px;

  .containerItem {
    font-size: 14px;

    .containerCode {
      margin-right: 15px;
      color: #197afa;
      flex: 3;
    }
  }


  // .carContainerItem {
  //   display: flex;
  //   font-size: 14px;
  //   .containerCode {
  //     color: #197afa;
  //     flex: 3;
  //   }

  //   .status1,
  //   .status2 {
  //     flex: 1;
  //   }
  // }
}
.processInfo {
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  padding: 5px 14px;
  .processType {
    color: orange;
  }
}

.secondPart {
  font-family: PingFangSC-Medium, PingFang SC sans-serif;
  font-size: 12px;
  padding: 0 14px;
  box-sizing: border-box;
  margin-bottom: 16px;

  div {
    margin-bottom: 5px;

    span {
      display: inline-block;
    }

    .dot {
      position: relative;
      width: 6px;
      height: 6px;
      background: #CCCFD7;
      border-radius: 50%;
      margin-right: 8px;

      &::after {
        position: absolute;
        top: 160%;
        left: 50%;
        transform: translateX(-50%);
        content: '';
        width: 1px;
        height: 12px;
        background: #E6EAF0;
      }

      &.lastDot {
        &::after {
          content: none;
        }
      }
    }

    .label {
      color: #333e59;
      margin-right: 4px;
    }

    .desc {
      display: inline;
      color: #666c7c
    }

    .status {
      height: 20px;
      line-height: 20px;
      color: #ff8c00;
      text-align: center;
      padding: 0px 4px;
      background: #FFF3E5;
      border-radius: 4px;
      margin-left: 10px;
      box-sizing: border-box;
    }
  }
}

.allocationInfoLabelList {
  padding-top: 0;
}

// .transitionStep {
//   margin-top: 15px;
//   position: relative;
//   padding-right: 100px;

//   .transitionType {
//     width: 100px;
//     text-align: right;
//     font-size: 14px;
//     position: absolute;
//     top: 0;
//     right: 14px;
//   }
// }
