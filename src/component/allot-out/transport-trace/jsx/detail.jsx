import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Header, Footer, View, Table,
} from 'common';
import { splitBoxColumns, skuColumns, deliverTruckColumns } from '../utils';
import store from '../reducers';

const DetailPage = (props) => {
  const {
    detailPageType,
    skuInfo,
    splitBoxInfo,
    deliverTruckInfo,
  } = props;

  // 点击箱号columns
  const handleColumns = [
    {
      title: t('箱号'),
      dataIndex: 'containerCode',
      render: (record) => (
        <span
          style={{ color: '#197AFA' }}
          onClick={() => {
            store.clearData();
            store.scanCode({ code: record.containerCode });
          }}
        >
          {record.containerCode}
        </span>
      ),
    },
  ];

  // 处理不同类型表头
  const getColumns = () => {
    let columns = [];
    switch (detailPageType) {
      case 1:
        columns = [
          ...handleColumns,
          ...splitBoxColumns,
        ];
        break;
      case 2:
        columns = skuColumns;
        break;
      case 3:
        columns = [
          ...handleColumns,
          ...deliverTruckColumns,
        ];
        break;
      default:
        columns = [];
        break;
    }
    return columns;
  };

  // 处理不同类型明细数据
  const getDataSource = () => {
    let dataSource = [];
    switch (detailPageType) {
      case 1:
        dataSource = splitBoxInfo.splitBoxInfo || [];
        break;
      case 2:
        dataSource = skuInfo.skuInfoList || [];
        break;
      case 3:
        dataSource = deliverTruckInfo.splitBoxInfoList || [];
        break;
      default:
        dataSource = [];
        break;
    }
    return dataSource;
  };

  return (
    <div>
      <Header title={t('明细')} />
      <View
        diff={100}
        flex={false}
      >
        <Table
          maxHeight={window.innerHeight - 100 - 30}
          columns={getColumns()}
          dataSource={getDataSource()}
        />
      </View>
      <Footer beforeBack={() => store.changeData({ showDetailPage: false })} />
    </div>
  );
};

DetailPage.propTypes = {
  detailPageType: PropTypes.arrayOf(PropTypes.shape()),
  skuInfo: PropTypes.shape(),
  splitBoxInfo: PropTypes.shape(),
  deliverTruckInfo: PropTypes.shape(),
};

export default DetailPage;
