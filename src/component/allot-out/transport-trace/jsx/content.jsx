import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { LabelList, RowInfo, Table } from 'common';
import classNames from 'classnames';
import { SCAN_TRANSFER_QUERY } from 'lib/jumpUrl';
import { queryStr } from 'lib/util';
import { splitBoxColumns, skuColumns, deliverTruckColumns } from '../utils';
import store from '../reducers';
import style from '../style.less';

// 转运暂存位信息
function Transfer(props) {
  const {
    allocationInfo,
    handoverContainerInfo,
    skuInfo,
    splitBoxInfo,
    deliverTruckInfo,
    scanCode,
  } = props;

  // 点击箱号columns
  const handleColumns = [
    {
      title: t('箱号'),
      dataIndex: 'containerCode',
      render: (record) => (
        <span
          style={{ color: '#197AFA' }}
          onClick={() => {
            store.clearData();
            store.scanCode({ code: record.containerCode });
          }}
        >
          {record.containerCode}
        </span>
      ),
    },
  ];

  return (
    <div className={style.sectionWrapper}>
      {/* 调拨信息 */}
      {!!allocationInfo && (
        <div className={style.scrollPart}>
          <div className={style.subTitle}>
            <RowInfo
              extraStyle={{
                borderBottom: 'none',
                justifyContent: 'flex-start',
              }}
              label={`${t('调拨信息')}：`}
              type="info"
            />
          </div>
          <div style={{ paddingLeft: 14, fontSize: 14 }}>
            <span style={{ color: '#666C7C' }}>{t('调拨状态')}：</span>
            <span style={{ color: '#141737' }}>
              {allocationInfo.allocationStatusName || ''}
              {(!allocationInfo.allocationStatus && allocationInfo.sourceWarehouseId) ? <span style={{ color: 'red', marginLeft: 10 }}>{t('来源仓未操作调拨')}</span> : ''}
            </span>
          </div>
          <LabelList
            labelList={[
              t('来源仓状态'),
              t('来源仓'),
              t('发货时间'),
              t('收货时间'),
              t('计划卸货子仓'),
              t('实际收货子仓'),
              t('调拨类型'),
            ]}
            valueList={[
              allocationInfo.sourceWarehouseStatusName,
              allocationInfo.sourceWarehouseName,
              allocationInfo.deliveryTime,
              allocationInfo.receiveTime,
              allocationInfo.dischargeSubWarehouseName,
              allocationInfo.receiveSubWarehouseName,
              allocationInfo.allocationTypeName,
            ]}
            lessLabelItem
            className={style.allocationInfoLabelList}
          />
        </div>
      )}

      {/* 分箱信息 */}
      {!!splitBoxInfo && (
        <div className={style.scrollPart}>
          <div className={style.subTitle}>
            <RowInfo
              extraStyle={{
                borderBottom: 'none',
                justifyContent: 'flex-start',
              }}
              label={`${t('分箱信息')}：`}
              type="info"
            />
            <div
              onClick={() => {
                store.changeData({
                  detailPageType: 1,
                  showDetailPage: true,
                });
              }}
            >
              {t('查看全部')}
            </div>
          </div>
          <LabelList
            labelList={[t('分箱状态')]}
            valueList={[splitBoxInfo.splitStatusName]}
            lessLabelItem
          />
          <LabelList
            labelList={[t('箱号列表')]}
            valueList={[]}
            lessLabelItem
          />
          <Table
            columns={[
              ...handleColumns,
              ...splitBoxColumns,
            ]}
            dataSource={((splitBoxInfo.splitBoxInfo || []).slice(0, 3))}
          />
        </div>
      )}

      {/* SKC信息 */}
      {!!skuInfo && (
        <div className={style.scrollPart}>
          <div className={style.subTitle}>
            <RowInfo
              extraStyle={{
                borderBottom: 'none',
                justifyContent: 'flex-start',
              }}
              label={`${t('sku信息')}：`}
              type="info"
              content={<span style={{ fontSize: 14, color: 'orange' }}>{skuInfo.businessTypeName || ''}</span>}
            />
            <div
              onClick={() => {
                store.changeData({
                  detailPageType: 2,
                  showDetailPage: true,
                });
              }}
            >
              {t('查看全部')}
            </div>
          </div>
          <Table
            columns={skuColumns}
            dataSource={((skuInfo.skuInfoList || []).slice(0, 3))}
          />
        </div>
      )}

      {/* 交接信息 */}
      {!!handoverContainerInfo && (
        <div className={style.scrollPart}>
          <div className={style.subTitle}>
            <RowInfo
              extraStyle={{
                borderBottom: 'none',
                justifyContent: 'flex-start',
              }}
              label={`${t('交接信息')}：`}
              type="info"
            />
            <div
              onClick={() => {
                window.location.href = `${SCAN_TRANSFER_QUERY}${queryStr({ routeContainerCode: scanCode })}`;
              }}
            >
              {handoverContainerInfo.handoverTypeName || ''}
            </div>
          </div>
          <LabelList
            labelList={[
              t('计划卸货子仓'),
              t('发货子仓'),
              t('收货子仓'),
            ]}
            valueList={[
              handoverContainerInfo.planReceiveSubWarehouseName,
              handoverContainerInfo.subWarehouseName,
              handoverContainerInfo.receiveSubWarehouseName,
            ]}
            lessLabelItem
          />
          <div className={style.transitionStep}>
            <p className={style.processInfo}>
              <span>{t('交接信息')}</span>
              <span className={style.processType}>{handoverContainerInfo.processType ? handoverContainerInfo.processTypeName : ''}</span>
            </p>
            {/* <div className={style.transitionType}>{t('跨仓交接')}</div> */}
            <div className={style.secondPart}>
              {handoverContainerInfo.handoverProcessList?.map((i, index) => (
                // eslint-disable-next-line react/no-array-index-key
                <div key={index}>
                  <span className={classNames([style.dot, index === handoverContainerInfo.handoverProcessList.length - 1 ? style.lastDot : ''])} />
                  <span className={style.label}>{i.processName}  :</span>
                  <span className={style.desc}>{i.processInfo}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 车辆信息 */}
      {!!deliverTruckInfo && (
        <div className={style.scrollPart}>
          <div className={style.subTitle}>
            <RowInfo
              extraStyle={{
                borderBottom: 'none',
                justifyContent: 'flex-start',
              }}
              label={`${t('车辆信息')}：`}
              type="info"
            />
            <div
              onClick={() => {
                store.changeData({
                  detailPageType: 3,
                  showDetailPage: true,
                });
              }}
            >
              {t('查看全部')}
            </div>
          </div>
          <LabelList
            labelList={[t('调拨单')]}
            valueList={[]}
            lessLabelItem
          />
          <div className={style.containerListWrap}>
            {((deliverTruckInfo.allocationNoList || []).slice(0, 3)).map((item) => (
              <div className={style.containerItem}>{item}</div>
            ))}
          </div>
          <LabelList
            labelList={[t('箱号列表')]}
            valueList={[]}
            lessLabelItem
          />
          <Table
            columns={[
              ...handleColumns,
              ...deliverTruckColumns,
            ]}
            dataSource={((deliverTruckInfo.splitBoxInfoList || []).slice(0, 3))}
          />
        </div>
      )}
    </div>
  );
}

Transfer.propTypes = {
  handoverProcessList: PropTypes.arrayOf(PropTypes.shape()),
  allocationInfo: PropTypes.shape(),
  handoverContainerInfo: PropTypes.shape(),
  skuInfo: PropTypes.shape(),
  splitBoxInfo: PropTypes.shape(),
  deliverTruckInfo: PropTypes.shape(),
  scanCode: PropTypes.string,
};

export default Transfer;
