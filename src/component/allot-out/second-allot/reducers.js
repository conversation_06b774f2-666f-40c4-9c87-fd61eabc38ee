import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { select } from 'redux-saga/effects';
import modal from '../../common/modal';
import {
  getWarehouseApi,
  searchApi,
  closeContainerApi,
  sacnContainerApi,
  scanBigContainerCodeApi,
  scanAllocationNoApi,
  selectDictApi,
  isUserPalletCodeApi,
} from './server';
// import message from '../../common/message';
import { classFocus, getHeaderTitle } from '../../../lib/util';

const defaultState = {
  srcWarehouseId: '',
  warehouseCode: '',
  warehouseCodeValid: false,
  destWarehouseIdValid: true,
  containerCode: '',
  containerCodeValid: false,
  bigContainerCode: '',
  bigContainerCodeValid: false,
  scanNums: 0,
  number: 0,
  searchLoading: false,
  closeLoading: false,
  warehouseList: [],
  destWarehouseList: [],
  warehouseType: '', // 仓库类型
  allocationNo: '', // 调拨单号  在绑定大箱号时，后端会返回调拨单号，非保税仓的关箱和查询 则根据调拨单号进行操作
  dataLoading: 1,
  show1: false,
  show2: false,
  showDetail: false,
  person: '3',
  srcWarehouseLabel: '',
  destWarehouseLabel: '',
  info: {
    allocationNo: '',
    list: [],
    statusName: '',
  },
  headerTitle: '',
  allocationNoIpt: '', // 根据调拨单号查询
  allocationNoIptDisabled: false,
  allotTypeList: [], // 调拨单类型列表
  allocationType: '',
  allocationTypeLabel: '',
  show3: false,
};

export default {
  defaultState,
  $init: () => defaultState,
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  // 获取仓库列表
  * getWarehouse(action, ctx) {
    markStatus('dataLoading');
    const res = yield getWarehouseApi(action.param);
    if (res.code === '0') {
      const list = res.info.data || [];
      const warehouseList = list.map((item) => ({
        label: item.nameZh,
        id: item.id,
        value: item.id,
        type: item.type,
      }));
      yield ctx.changeData({
        data: {
          warehouseList,
        },
      });
      classFocus('bigContainerCode');
    } else {
      modal.error({ content: res.msg, className: 'bigContainerCode' });
    }
  },
  // 获取调拨类型列表
  * getAllotTypeList(action, ctx) {
    markStatus('dataLoading');
    const selectData = yield selectDictApi({ catCode: ['ALLOCATION_TYPE'] });
    if (selectData.code === '0') {
      yield ctx.changeData({
        data: {
          allotTypeList: selectData.info.data[0].dictListRsps.map((item) => ({
            label: item.dictNameZh,
            id: item.dictCode,
            value: item.dictCode,
          })) || [],
        },
      });
    } else {
      modal.error({ content: res.msg, className: 'bigContainerCode' });
    }
  },
  // 扫描大箱号
  * scanBigContainer(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanBigContainerCodeApi(action.param);
    if (res.code === '0') {
      const { number, allocationNo } = res.info;
      const { scanNums } = yield select((state) => state['allot-out/second-allot']);
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
          bigContainerCodeValid: false,
          number,
          scanNums: scanNums + 1,
          allocationNo,
          allocationNoIpt: allocationNo,
          allocationNoIptDisabled: true,
        },
      });
      classFocus('bigContainerCode');
    } else {
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
          bigContainerCodeValid: false,
        },
      });
      modal.error({ content: res.msg, className: 'bigContainerCode' });
    }
  },
  // 扫描集装箱
  * scanContainer(action, ctx) {
    markStatus('dataLoading');
    const res = yield sacnContainerApi(action.param);
    if (res.code === '0') {
      const { number, allocationNo } = res.info;
      yield ctx.changeData({
        data: {
          // containerCode: '',
          containerCodeValid: false,
          number,
          allocationNo,
        },
      });
      classFocus('bigContainerCode');
    } else {
      modal.error({ content: res.msg, className: 'containerCode' });
    }
  },
  // 获取详情
  * search(action, ctx) {
    markStatus('dataLoading');
    const res = yield searchApi(action.param);
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          info: res.info,
          showDetail: true,
        },
      });
    } else {
      modal.error({
        content: res.msg, className: 'bigContainerCode',
      });
    }
  },
  // 关箱
  * close(action, ctx) {
    const result = yield closeContainerApi(action.param);
    if (result.code === '0') {
      yield ctx.changeData({
        data: {
          containerCode: '',
          containerCodeValid: false,
          bigContainerCode: '',
          bigContainerCodeValid: false,
          number: 0,
          allocationNo: '',
          warehousCode: '',
          destWarehouseId: '',
          allocationNoIpt: '',
          allocationNoIptDisabled: true,
          destWarehouseIdValid: false,
        },
      });
      if (action.param.warehouseType !== 3) {
        classFocus('bigContainerCode');
      } else {
        classFocus('containerCode');
      }
    } else {
      yield ctx.changeData({
        data: {
          containerCode: '',
          containerCodeValid: false,
          bigContainerCode: '',
          bigContainerCodeValid: false,
          number: 0,
        },
      });
      modal.error({
        content: result.msg,
        className: action.param.warehouseType !== 3 ? 'bigContainerCode' : 'containerCode',
      });
    }
  },
  // 扫描调拨单号
  * scanAllocationNo(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanAllocationNoApi(action.param);
    if (res.code === '0') {
      const {
        number, allocationNo, warehouseId, srcWarehouseId, containerCode,
      } = res.info;
      const { warehouseList } = yield select((state) => state['allot-out/second-allot']);
      yield ctx.changeData({
        data: {
          number,
          allocationNoIpt: allocationNo,
          allocationNo,
          srcWarehouseId,
          warehouseCode: warehouseId,
          allocationNoIptDisabled: true,
          srcWarehouseLabel: warehouseList.find((wi) => (wi.id === srcWarehouseId)).label,
          destWarehouseLabel: warehouseList.find((wi) => (wi.id === warehouseId)).label,
          warehouseCodeValid: true,
          destWarehouseIdValid: true,
        },
      });
      if (warehouseList.find((wi) => (wi.id === warehouseId)) && warehouseList.find((wi) => (wi.id === warehouseId)).type === 3) {
        yield ctx.changeData({
          data: {
            containerCode,
            warehouseType: 3,
            containerCodeValid: true,
          },
        });
      }
      classFocus('bigContainerCode');
    } else {
      yield ctx.changeData({
        data: {
          warehouseCodeValid: false,
          destWarehouseIdValid: false,
          allocationNoIpt: '',
          allocationNoIptDisabled: false,
        },
      });
      modal.error({ modalBlurInput: true, content: res.msg, className: 'allocationNoIpt' });
    }
  },
  // 是否使用托盘
  * isUserPalletCode(action, ctx) {
    markStatus('dataLoading');
    const res = yield isUserPalletCodeApi(action.param);
    if (res.code === '0') {
      if (action.warehouseType === 3) {
        classFocus('containerCode');
      } else {
        classFocus('bigContainerCode');
      }
    } else {
      modal.error({ content: res.msg });
    }
  },
};
