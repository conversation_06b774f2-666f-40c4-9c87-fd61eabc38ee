import { sendPostRequest } from '../../../lib/public-request';
// import noErrorFetch from '../../../lib/no-error-fetch';
// import { camel2Under, under2Camel } from '../../../lib/util';
// import { getLang, getLocation } from '../../js';

// 退货调拨出库-查询接口
export const searchApi = (param) => sendPostRequest({
  url: '/return_transfer/return_allot/search',
  param,
}, process.env.WTS_FRONT);

// 关箱
export const closeContainerApi = (param) => sendPostRequest({
  url: '/return_transfer/return_allot/close_container',
  param,
}, process.env.WTS_FRONT);

// 扫集装箱号
export const sacnContainerApi = (param) => sendPostRequest({
  url: '/return_transfer/return_allot/get_number',
  param,
}, process.env.WTS_FRONT);

// 扫描大箱号
export const scanBigContainerCodeApi = (param) => sendPostRequest({
  url: '/return_transfer/return_allot/scan_big_container_code',
  param,
}, process.env.WTS_FRONT);

// 获取仓库
export const getWarehouseApi = (param) => sendPostRequest({
  url: '/warehouse/get_list',
  param,
}, process.env.BASE_URI_WMD);

// 扫描调拨单号
export const scanAllocationNoApi = (param) => sendPostRequest({
  url: '/return_transfer/scan_allocation_no',
  param,
}, process.env.WTS_FRONT);

// // 字典明细select查询
// export const selectDictApi = (argObj) => {
//   const uri = `${process.env.BASE_URI_WMD}/dict/select`;
//   return noErrorFetch(uri, {
//     method: 'POST',
//     credentials: 'include',
//     headers: {
//       'Content-Type': 'application/json',
//       'Accept-Language': getLang(),
//       'Accept-Location': getLocation(),
//     },
//     body: JSON.stringify(camel2Under(argObj)),
//   })
//   // .then(data => data.json())
//     .then(under2Camel);
// };

// 字典明细select查询
export const selectDictApi = (param) => sendPostRequest({
  baseUrl: process.env.BASE_URI_WMD,
  url: '/dict/select',
  param,
});

// 是否使用托盘
export const isUserPalletCodeApi = (param) => sendPostRequest({
  url: '/return_transfer/is_user_pallet_code',
  param,
}, process.env.WTS_FRONT);
