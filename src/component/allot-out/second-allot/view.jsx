import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { push } from 'react-router-redux';
import { i18n, t } from '@shein-bbl/react';
import { classFocus } from '../../../lib/util';
import store from './reducers';
import {
  Header,
  Footer,
  FocusInput,
  FooterBtn,
  pages,
  modal,
  PopSheet,
  List,
} from '../../common';

const { View } = pages;

const rows = [
  [
    {
      title: t('序号'),
      render: (rowData) => rowData.bigContainerCode,
      width: 33.3,
    },
    {
      title: t('大箱号'),
      render: (rowData) => rowData.encloseContainerUser,
      width: 20,
    },
    {
      title: t('托盘号'),
      render: (rowData) => rowData.encloseContainerTime,
      width: 46.6,
    },
  ],
];

class Container extends Component {
  componentDidMount() {
    store.init();
    store.getWarehouse();
    store.getAllotTypeList();
    classFocus('bigContainerCode');
  }

  render() {
    const {
      srcWarehouseId,
      warehouseCode,
      containerCode,
      containerCodeValid,
      bigContainerCode,
      bigContainerCodeValid,
      warehouseCodeValid,
      destWarehouseIdValid,
      scanNums,
      number,
      warehouseList,
      destWarehouseList,
      warehouseType, // 仓库类型
      allocationNo, // 调拨单号  在绑定大箱号时，后端会返回调拨单号，非保税仓的关箱和查询 则根据调拨单号进行操作
      dataLoading,
      dispatch,
      show1,
      show2,
      srcWarehouseLabel,
      destWarehouseLabel,
      showDetail,
      info,
      headerTitle,
      allocationNoIpt,
      allocationNoIptDisabled,
      show3,
      allotTypeList,
      allocationTypeLabel,
      allocationType,
    } = this.props;
    return (
      <View>
        <Header title={headerTitle || t('二次调拨')} />

        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: '0 15px',
            fontSize: 14,
            lineHeight: '40px',
            backgroundColor: '#fff',
          }}
        >
          <div>
            <span style={{ marginRight: 10 }}>{t('已扫描箱数')}</span>
            <span style={{ fontWeight: 700 }}>{scanNums}</span>
          </div>
        </div>

        {
          !showDetail &&
          (
            <div style={{ backgroundColor: '#fff' }}>
              <Form>
                <FocusInput
                  value={allocationNoIpt}
                  className="allocationNoIpt"
                  disabled={allocationNoIptDisabled}
                  onChange={(e) => {
                    store.changeData({
                      data: {
                        allocationNoIpt: e.target.value.trim(),
                      },
                    });
                  }}
                  onPressEnter={() => {
                    if (allocationNoIpt) {
                      if (allocationNoIpt.length > 50) {
                        modal.error({
                          modalBlurInput: true,
                          content: t('调拨单号最大输入长度50个字符！'),
                          className: 'allocationNoIpt',
                        });
                      }
                      store.scanAllocationNo({
                        param: {
                          allocationNo: allocationNoIpt,
                        },
                      });
                    }
                  }}
                >
                  <label>{t('调拨单号')}</label>
                </FocusInput>
              </Form>
              <Form>
                <FocusInput
                  value={srcWarehouseLabel}
                  disabled={warehouseCodeValid}
                  readOnly
                  onClick={() => {
                    store.changeData({
                      data: {
                        show1: true,
                      },
                    });
                  }}
                  arrow
                >
                  <label>{t('调出仓库')}</label>
                </FocusInput>
              </Form>

              <Form>
                <FocusInput
                  value={destWarehouseLabel}
                  readOnly
                  disabled={destWarehouseIdValid}
                  onClick={() => {
                    if (warehouseCodeValid) {
                      store.changeData({
                        data: {
                          show2: true,
                        },
                      });
                    }
                  }}
                  arrow
                >
                  <label>{t('调入仓库')}</label>
                </FocusInput>
              </Form>

              <Form>
                <FocusInput
                  value={allocationTypeLabel}
                  readOnly
                  disabled={!srcWarehouseLabel}
                  onClick={() => {
                    store.changeData({
                      data: {
                        show3: true,
                      },
                    });
                  }}
                  arrow
                >
                  <label>{t('调拨类型')}</label>
                </FocusInput>
              </Form>

              {
                warehouseType === 3 &&
                (
                  <Form>
                    <FocusInput
                      value={containerCode}
                      label={t('集装箱号')}
                      className="containerCode"
                      disabled={dataLoading === 0 || containerCodeValid || !allocationTypeLabel}
                      onChange={(e) => {
                        store.changeData({
                          data: {
                            containerCode: e.target.value.trim(),
                          },
                        });
                      }}
                      onPressEnter={() => {
                        if (srcWarehouseId) {
                          if (containerCode) {
                            if (containerCode.length <= 50) {
                              store.scanContainer({
                                param: {
                                  containerCode,
                                  warehouseCode,
                                },
                              });
                            } else {
                              store.changeData({
                                data: {
                                  containerCode: '',
                                },
                              });
                              modal.error({
                                content: t('集装箱号不能超过50字符'),
                                className: 'containerCode',
                              });
                            }
                          }
                        } else {
                          store.changeData({
                            data: {
                              containerCode: '',
                            },
                          });
                          modal.error({
                            content: t('请选择仓库'),
                            className: 'containerCode',
                          });
                        }
                      }}
                    />
                  </Form>
                )
              }

              <Form>
                <FocusInput
                  value={bigContainerCode}
                  className="bigContainerCode"
                  label={t('托盘号/大箱号')}
                  autoFocus
                  disabled={dataLoading === 0 || bigContainerCodeValid || !allocationTypeLabel}
                  onChange={(e) => {
                    store.changeData({
                      data: {
                        bigContainerCode: e.target.value.trim(),
                      },
                    });
                  }}
                  onPressEnter={() => {
                    if (containerCode || warehouseType !== 3) {
                      if (bigContainerCode) {
                        if (!srcWarehouseId) {
                          modal.error({
                            content: t('请选择调出仓库'),
                            className: 'bigContainerCode',
                            onOk: () => {
                              store.changeData({
                                data: {
                                  bigContainerCode: '',
                                },
                              });
                            },
                          });
                          return;
                        }
                        if (!warehouseCode) {
                          modal.error({
                            content: t('请选择调入仓库'),
                            className: 'bigContainerCode',
                            onOk: () => {
                              store.changeData({
                                data: {
                                  bigContainerCode: '',
                                },
                              });
                            },
                          });
                          return;
                        }
                        if (bigContainerCode.length <= 50) {
                          store.scanBigContainer({
                            param: {
                              allocationType,
                              optionType: 2,
                              containerCode,
                              warehouseCode,
                              bigContainerCode,
                              srcWarehouseId,
                              allocationNo: allocationNoIpt,
                            },
                          });
                        } else {
                          store.changeData({
                            data: {
                              bigContainerCode: '',
                            },
                          });
                          modal.error({
                            content: t('大箱号不能超过50字符'),
                            className: 'bigContainerCode',
                          });
                        }
                      }
                    } else {
                      store.changeData({
                        data: {
                          bigContainerCode: '',
                          containerCode: '',
                        },
                      });
                      modal.error({
                        content: t('请录入集装箱号'),
                        className: 'containerCode',
                      });
                    }
                  }}
                />
              </Form>
              <div
                style={{
                  float: 'right',
                  textAlign: 'right',
                  color: '#197AFA',
                  fontSize: 14,
                  padding: '10px 10px 0 0',
                }}
                onClick={() => {
                  store.search({
                    param: {
                      warehouseCode,
                      containerCode,
                      allocationNo: allocationNoIpt,
                      srcWarehouseId,
                    },
                  });
                }}
              >
                {t('查看详情')}
              </div>
            </div>
          )
        }

        {
          showDetail &&
          (
            <View>
              <div style={{ padding: '0 15px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  {/* <div>{t('已扫描箱数')} {scanNums}</div> */}
                  <div>{t('调拨单号')} {info.allocationNo}</div>
                </div>
              </div>
              <List
                rows={rows}
                data={info.list}
              />
            </View>
          )
        }

        <PopSheet
          onClick={(v) => {
            const filterList = warehouseList.filter((item) => item.id !== v.id);
            store.changeData({
              data: {
                srcWarehouseId: v.id,
                warehouseCodeValid: true,
                destWarehouseList: filterList,
                destWarehouseIdValid: false,
                srcWarehouseLabel: v.label,
                show1: false,
              },
            });
          }}
          onClose={() => {
            store.changeData({ data: { show1: false } });
          }}
          cancelBtn
          menus={warehouseList}
          show={show1}
        />

        <PopSheet
          onClick={(v) => {
            const target = warehouseList.find((item) => item.id === v.id);
            store.changeData({
              data: {
                destWarehouseLabel: v.label,
                destWarehouseIdValid: true,
                warehouseType: target.type,
                warehouseCode: v.id,
                show2: false,
              },
            });
            if (target.type !== 3) {
              // classFocus('bigContainerCode');
            } else {
              classFocus('containerCode');
            }
          }}
          onClose={() => {
            store.changeData({ data: { show2: false } });
          }}
          cancelBtn
          menus={destWarehouseList}
          show={show2}
        />

        <PopSheet
          onClick={(v) => {
            store.changeData({
              data: {
                allocationTypeLabel: v.label,
                allocationType: v.id,
                show3: false,
              },
            });
            store.isUserPalletCode({
              param: {
                allocationType: v.id,
                srcWarehouseId,
                warehouseId: warehouseCode,
              },
              warehouseType,
            });
            if (warehouseType !== 3) {
              classFocus('bigContainerCode');
            } else {
              classFocus('containerCode');
            }
          }}
          onClose={() => {
            store.changeData({ data: { show3: false } });
          }}
          cancelBtn
          // 0：退货调拨、4：基础调拨、6：采购转发调拨
          menus={allotTypeList.filter((v) => (v.id === 0 || v.id === 4 || v.id === 6))}
          show={show3}
        />

        <Footer
          dispatch={dispatch}
          beforeBack={(back) => {
            if (showDetail) {
              store.changeData({
                data: {
                  showDetail: false,
                },
              });
              classFocus('bigContainerCode');
            } else {
              back();
            }
          }}
        >
          {
            !showDetail &&
            (
              <FooterBtn
                disabled={!warehouseCodeValid || (allocationNo === '' && !containerCodeValid)}
                onClick={() => {
                  modal.confirm({
                    content: t('是否确认关箱'),
                    onOk: () => {
                      store.close({
                        param: {
                          warehouseCode,
                          containerCode,
                          allocationNo,
                          warehouseType,
                          srcWarehouseId,
                        },
                      });
                    },
                    onCancel: () => { classFocus('bigContainerCode'); },
                  });
                }}
              >
                {t('关单')}
              </FooterBtn>
            )
          }
        </Footer>
      </View>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  dataLoading: PropTypes.number,
  warehouseCode: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  containerCode: PropTypes.string,
  containerCodeValid: PropTypes.bool,
  bigContainerCode: PropTypes.string,
  bigContainerCodeValid: PropTypes.bool,
  warehouseCodeValid: PropTypes.bool,
  destWarehouseIdValid: PropTypes.bool,
  show1: PropTypes.bool,
  show2: PropTypes.bool,
  showDetail: PropTypes.bool,
  scanNums: PropTypes.number,
  number: PropTypes.number,
  warehouseList: PropTypes.arrayOf(PropTypes.shape),
  destWarehouseList: PropTypes.arrayOf(PropTypes.shape),
  warehouseType: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  allocationNo: PropTypes.string,
  srcWarehouseId: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  srcWarehouseLabel: PropTypes.string,
  destWarehouseLabel: PropTypes.string,
  info: PropTypes.shape(),
  headerTitle: PropTypes.string,
  allotTypeList: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Container);
