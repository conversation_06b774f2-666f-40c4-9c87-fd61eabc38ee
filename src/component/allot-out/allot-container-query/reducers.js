import assign from 'object-assign';
import { markStatus } from 'rrc-loader-helper';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import { modal } from '../../common';
import {
  scanBigContainerAPI,
  allocationBoxDetailQueryAPI,
} from './server';
import { getHeaderTitle, classFocus as utilClassFocus } from '../../../lib/util';

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '',
  focusPosition: '',
  pageType: 1, // 1 调拨容器查询首页 2 大箱明细页面 3 分拨列表页面 4 分拨箱明细页面 5 调拨上架明细
  bigContainerCode: '', // 容器号
  containerType: '', // 1上架周转箱信息展示  2 大纸箱信息展示
  upperShelfBoxInfo: '', // 上架周转箱信息展示
  bigCardBoardInfo: '', // 大纸箱信息展示
  allotDetailList: [
  ], // 分拨列表页面明细
  putOnDetailList: [
  ], // 上架明细
  backPageType: '', // 跳转之前的页面
  hasNoData: false, // 是否有数据
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * classFocus(className, ctx) {
    utilClassFocus(className);
    yield ctx.changeData({
      data: {
        focusPosition: className,
      },
    });
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    yield ctx.classFocus('bigContainerCode');
  },
  /**
   * 扫描容器号
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * scanBigContainer(action, ctx) {
    markStatus('dataLoading');
    const { warehouseId } = JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}');
    const data = yield scanBigContainerAPI({ ...action.params, warehouseId });
    if (data.code === '0') {
      if (data.info) {
        // containerType 1上架周转箱  2大纸箱
        let pageType = '';
        if (data.info?.containerType === 1) {
          pageType = 4; // 跳转到大箱明细页面
          yield this.changeData({
            data: {
              upperShelfBoxInfo: (data.info?.detail || []).find((di) => (di.shelfContainerCode === action.params?.bigContainerCode)) || {}, // 上架周转箱
            },
          });
        } else if (data.info?.containerType === 2) {
          pageType = 2; // 跳转到分拨箱明细页面
          yield this.changeData({
            data: {
              upperShelfBoxInfo: (data.info?.detail || [])[0] || {}, // 上架周转箱
            },
          });
        }
        yield this.changeData({
          data: {
            bigContainerCode: '',
            containerType: data.info?.containerType, // 容器类型
            pageType,
            bigCardBoardInfo: data.info || {}, // 大纸箱
            allotDetailList: (data.info || {}).detail || [], // 分拨列表
            backPageType: '', // 清空跳转之前的页面缓存
            hasNoData: false,
          },
        });
      } else {
        yield this.init();
        yield this.changeData({
          data: {
            bigContainerCode: '',
            hasNoData: true, // 无数据
          },
        });
      }
    } else {
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
        },
      });
      modal.error({ content: data.msg, className: 'bigContainerCode' });
    }
  },
  /**
   * 查询上架明细
   * @param action
   * @param ctx
   * @returns {Generator<void|*, void, *>}
   */
  * allocationBoxDetailQuery(action, ctx) {
    markStatus('dataLoading');
    const data = yield allocationBoxDetailQueryAPI(action.params);
    if (data.code === '0') {
      yield this.changeData({
        data: {
          pageType: 5, // 调拨上架明细
          putOnDetailList: data.info || [],
        },
      });
    } else {
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
        },
      });
      modal.error({ content: data.msg, className: 'bigContainerCode' });
    }
  },
};
