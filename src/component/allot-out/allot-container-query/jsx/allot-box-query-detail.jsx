import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import {
  Form,
} from 'react-weui/build/packages';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import Footer from '../../../common/footer';
import styles from '../style.less';
import { SplitBar, Tag } from '../../../common';

class AllotBoxQueryDetail extends Component {
  render() {
    const {
      dataLoading,
      upperShelfBoxInfo,
      bigContainerCode,
      backPageType,
      hasNoData,
    } = this.props;

    return (
      <div>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            disabled={dataLoading === 0}
            className="bigContainerCode"
            value={bigContainerCode}
            onChange={(e) => {
              store.changeData({
                data: {
                  bigContainerCode: e.target.value.trim(),
                },
              });
            }}
            autoFocus
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.scanBigContainer({
                params: {
                  bigContainerCode: e.target.value.trim(),
                },
              });
            }}
          >
            <label>{t('容器号')}</label>
          </FocusInput>
        </Form>
        {upperShelfBoxInfo && (
          <>
            {/* 上架周转箱信息展示 */}
            <div className={styles.topCard}>
              <div className={styles.topCardHeader} style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>
                  <b>{t('分拨箱{}', ' : ')}</b>
                  <span>
                    {upperShelfBoxInfo.shelfContainerCode}
                  </span>
                </span>
                <Icon
                  onClick={() => {
                    store.allocationBoxDetailQuery({
                      params: {
                        id: upperShelfBoxInfo.id,
                      },
                    });
                  }} style={{ color: '#999' }} name="arr-right"
                />
              </div>
              <div className={styles.topCardBottom}>
                <span>{t('容器类型/状态{}', ' : ')}{upperShelfBoxInfo.containerTypeName}/{upperShelfBoxInfo.boxStatusName}</span>
                <span>
                  <span className={styles.red}>{upperShelfBoxInfo.num}{t('件')}</span>
                </span>
              </div>
            </div>
            <SplitBar />
            <div className={styles.bottomCard}>
              <div className={styles.bottomCardItem}>
                <span>{t('来源箱号{}', ' : ')}</span>
                <span>{`${upperShelfBoxInfo.bigContainerCode}【${upperShelfBoxInfo.bigContainerNum}${t('件')}】`}</span>
              </div>
              <div className={styles.bottomCardItem}>
                <span>{t('推荐标识{}', ' : ')}</span>
                <span><Tag style={{ background: '#fff3e5', color: '#FF8C00' }}>{upperShelfBoxInfo.pickStockFlagName}</Tag></span>
              </div>
              <div className={styles.bottomCardItem}>
                <span>{t('分拨状态{}', ' : ')}</span>
                <span><Tag style={{ background: '#fff3e5', color: '#FF8C00' }}>{upperShelfBoxInfo.spiltStatusName}</Tag></span>
              </div>
              <div className={styles.bottomCardItem}>
                <span>{t('开始分拨时间{}', ' : ')}</span>
                <span>{upperShelfBoxInfo.startTime}</span>
              </div>
              <div className={styles.bottomCardItem}>
                <span>{t('分拨人{}', ' : ')}</span>
                <span>{upperShelfBoxInfo.user}</span>
              </div>
            </div>
          </>
        )}
        {hasNoData && <div style={{ fontSize: '16px', padding: '10px' }}>{t('暂无31天内的相关数据，请到电脑端查询或稍后重试')}</div>}
        <Footer
          beforeBack={() => {
            if (backPageType) {
              store.changeData({
                data: {
                  pageType: backPageType, // 分拨列表页面
                },
              });
            } else {
              store.changeData({
                data: {
                  pageType: 1, // 调拨容器查询首页
                },
              });
              store.init();
            }
          }}
        />
      </div>
    );
  }
}

AllotBoxQueryDetail.propTypes = {
  dataLoading: PropTypes.number,
  bigContainerCode: PropTypes.string,
  upperShelfBoxInfo: PropTypes.shape(),
  hasNoData: PropTypes.bool,
  backPageType: PropTypes.number,
};

export default AllotBoxQueryDetail;
