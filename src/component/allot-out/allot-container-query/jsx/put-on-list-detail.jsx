import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import Footer from '../../../common/footer';
import List from '../../../common/list';
import style from '../../../style.css';

const rows = [
  [
    {
      title: 'SKC',
      render: 'skc',
    },
  ],
  [
    {
      title: t('尺码'),
      render: 'size',
    },
    {
      title: t('商品总数量'),
      render: 'quantity',
    },
  ],
  [
    {
      title: t('已上架'),
      render: 'shelvesQuantity',
    },
    {
      title: t('待上架'),
      render: 'diffNum',
    },
  ],
];

class PutOnListDetail extends Component {
  render() {
    const {
      dispatch,
      putOnDetailList,
      focusPosition,
    } = this.props;

    const height = window.innerHeight - 44 - 56;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <List
          rows={rows}
          data={putOnDetailList}
          rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0' }}
        />
        <Footer
          dispatch={dispatch}
          beforeBack={() => {
            store.changeData({
              data: {
                pageType: 4, // 分拨箱明细页面
              },
            });
            store.classFocus(focusPosition);
          }}
        />
      </div>
    );
  }
}

PutOnListDetail.propTypes = {
  dispatch: PropTypes.func,
  putOnDetailList: PropTypes.arrayOf(PropTypes.shape()),
  focusPosition: PropTypes.string,
};

export default PutOnListDetail;
