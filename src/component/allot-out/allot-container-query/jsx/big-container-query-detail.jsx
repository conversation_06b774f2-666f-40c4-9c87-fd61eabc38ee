import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import {
  Form,
} from 'react-weui/build/packages';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import Footer from '../../../common/footer';
import styles from '../style.less';
import { SplitBar, Tag } from '../../../common';

class BigContainerQueryDetail extends Component {
  render() {
    const {
      dataLoading,
      bigCardBoardInfo,
      bigContainerCode,
      hasNoData,
    } = this.props;

    return (
      <div>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            disabled={dataLoading === 0}
            className="bigContainerCode"
            value={bigContainerCode}
            onChange={(e) => {
              store.changeData({
                data: {
                  bigContainerCode: e.target.value.trim(),
                },
              });
            }}
            autoFocus
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.scanBigContainer({
                params: {
                  bigContainerCode: e.target.value.trim(),
                },
              });
            }}
          >
            <label>{t('容器号')}</label>
          </FocusInput>
        </Form>
        {bigCardBoardInfo && (
          <>
            {/* 大纸箱信息展示 */}
            <div className={styles.topCard}>
              <div className={styles.topCardHeader}>
                <b>{t('容器号{}', ' : ')}</b>
                <span>
                  {bigCardBoardInfo.bigContainerCode}
                </span>
              </div>
              <div className={styles.topCardBottom}>
                <span>{t('容器类型{}', ' : ')}{bigCardBoardInfo.containerTypeName}</span>
                <span>
                  <span className={styles.red}>{bigCardBoardInfo.num}{t('件')}</span>
                </span>
              </div>
            </div>
            <SplitBar />
            <div className={styles.bottomCard}>
              <div className={styles.bottomCardItem}>
                <span>{t('收货时间{}', ' : ')}</span>
                <span>
                  {bigCardBoardInfo.receiptTime}
                </span>
              </div>
              <div className={styles.bottomCardItem}>
                <span>{t('需要分拨{}', ' : ')}</span>
                <span className={styles.red}>{bigCardBoardInfo.isSplitBoxName}</span>
              </div>
              <div className={styles.bottomCardItem}>
                <span>{t('开始分拨时间{}', ' : ')}</span>
                <span>{bigCardBoardInfo.startTime}</span>
              </div>
              <div className={styles.bottomCardItem}>
                <span>{t('单据类型{}', ' : ')}</span>
                <span>{bigCardBoardInfo.typeName}</span>
              </div>
              <div className={styles.bottomCardItem}>
                <span>{t('推荐园区{}', ' : ')}</span>
                <span>{bigCardBoardInfo.shelvesParkName}</span>
              </div>
              <div className={styles.bottomCardItem}>
                <span>{t('分拨状态{}', ' : ')}</span>
                <span><Tag style={{ background: '#fff3e5', color: '#FF8C00' }}>{bigCardBoardInfo.spiltStatusName}</Tag></span>
              </div>
              <div className={styles.bottomCardItem}>
                <span>{t('分拨箱列表{}', ' : ')}</span>
                {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
                <a
                  href="javascript:;"
                  onClick={() => {
                    store.changeData({
                      data: {
                        pageType: 3, // 分拨列表页面
                      },
                    });
                  }}
                >
                  {((bigCardBoardInfo.detail || [])[0] || {}).shelfContainerCode}
                </a>
              </div>
              <div className={styles.bottomCardItem}>
                <span>{t('分拨人{}', ' : ')}</span>
                <span>{bigCardBoardInfo.user}</span>
              </div>
            </div>
          </>
        )}
        {hasNoData && <div style={{ fontSize: '16px', padding: '10px' }}>{t('暂无31天内的相关数据，请到电脑端查询或稍后重试')}</div>}
        <Footer
          beforeBack={(back) => {
            store.init();
            back();
          }}
        />
      </div>
    );
  }
}

BigContainerQueryDetail.propTypes = {
  dataLoading: PropTypes.number,
  bigContainerCode: PropTypes.string,
  bigCardBoardInfo: PropTypes.shape(),
  hasNoData: PropTypes.bool,
};

export default BigContainerQueryDetail;
