import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import {
  Form,
} from 'react-weui/build/packages';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import Footer from '../../../common/footer';
import {push} from "react-router-redux";

class ScanContainer extends Component {
  render() {
    const {
      dataLoading,
      bigContainerCode,
      hasNoData,
    } = this.props;

    return (
      <div>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            disabled={dataLoading === 0}
            className="bigContainerCode"
            value={bigContainerCode}
            onChange={(e) => {
              store.changeData({
                data: {
                  bigContainerCode: e.target.value.trim(),
                },
              });
            }}
            autoFocus
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.scanBigContainer({
                params: {
                  bigContainerCode: e.target.value.trim(),
                },
              });
            }}
          >
            <label>{t('容器号')}</label>
          </FocusInput>
        </Form>
        {hasNoData && <div style={{ fontSize: '16px', padding: '10px' }}>{t('暂无31天内的相关数据，请到电脑端查询或稍后重试')}</div>}
        <Footer
          beforeBack={(back) => {
            store.init();
            back();
          }}
        />
      </div>
    );
  }
}

ScanContainer.propTypes = {
  dataLoading: PropTypes.number,
  bigContainerCode: PropTypes.string,
  hasNoData: PropTypes.bool
};

export default ScanContainer;
