import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import store from '../reducers';
import Footer from '../../../common/footer';
import List from '../../../common/list';
import style from '../../../style.css';
import styles from '../style.less';
import { Tag } from '../../../common';

class AllotListDetail extends Component {
  render() {
    const {
      dispatch,
      focusPosition,
      bigCardBoardInfo,
      allotDetailList,
    } = this.props;

    const rows = [
      [
        {
          title: '',
          render: (record) => <span>{record.shelfContainerCode}</span>,
        },
        {
          title: '',
          render: (record) => {
            switch (record.spiltStatus) {
              case 1:
                return <Tag style={{ color: '#FF8C00' }}>{record.boxStatusName}</Tag>;
              case 2:
                return <Tag style={{ color: '#2FB300' }}>{record.boxStatusName}</Tag>;
              case 0:
                return <Tag>{record.boxStatusName}</Tag>;
              default:
                return '';
            }
          },
        },
        {
          title: '',
          render: (r) => (`${r.num}件`),
        },
        {
          title: '',
          render: (record) => (
            <Icon
              onClick={() => {
                store.changeData({
                  data: {
                    pageType: 4, // 分拨箱明细页面
                    upperShelfBoxInfo: record || {},
                    backPageType: 3, // 当前页面type
                  },
                });
                store.classFocus(focusPosition);
              }} style={{ color: '#999' }} name="arr-right"
            />
          ),
        },
      ],
    ];

    const height = window.innerHeight - 44 - 56;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <List
          header={(
            <div style={{
              color: '#000',
              paddingRight: '15px',
            }}
            >
              <div className={styles.allotListDetailHeader}>
                <b>{t('大箱号{}', ' : ')}{bigCardBoardInfo.bigContainerCode}</b>
                <span>{bigCardBoardInfo.num}{t('件')}</span>
              </div>
              <div className={styles.allotListDetailSubHeader}>
                <span>{t('分拨箱明细')}</span>
                <span>{(bigCardBoardInfo.detail || []).length}{t('箱')}</span>
              </div>
            </div>
          )}
          rows={rows}
          data={allotDetailList}
          rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0' }}
        />
        <Footer
          dispatch={dispatch}
          beforeBack={() => {
            store.changeData({
              data: {
                pageType: 2,
              },
            });
            store.classFocus(focusPosition);
          }}
        />
      </div>
    );
  }
}

AllotListDetail.propTypes = {
  dispatch: PropTypes.func,
  focusPosition: PropTypes.string,
  bigCardBoardInfo: PropTypes.shape(),
  allotDetailList: PropTypes.arrayOf(PropTypes.shape()),
};

export default AllotListDetail;
