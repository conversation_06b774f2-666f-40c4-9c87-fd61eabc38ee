import React, { Component } from 'react';
import { i18n, t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import ScanContainer from './jsx/scan-container';
import PutOnListDetail from './jsx/put-on-list-detail';
import AllotListDetail from './jsx/allot-list-detail';
import BigContainerQueryDetail from './jsx/big-container-query-detail';
import AllotBoxQueryDetail from './jsx/allot-box-query-detail';
import { Header } from '../../common';
import store from './reducers';
import style from './style.less';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      pageType,
      dataLoading,
    } = this.props;
    let children;
    switch (pageType) {
      case 1:
        children = (
          <>
            <Header title={headerTitle || t('调拨容器查询')}>
              <Icon className={style.loader} name="loading" data-if={dataLoading === 0} />
            </Header>
            <ScanContainer {...this.props} />
          </>
        );
        break;
      case 2:
        children = (
          <>
            <Header title={headerTitle || t('调拨容器查询')} />
            <BigContainerQueryDetail {...this.props} />
          </>
        );
        break;
      case 3:
        children = (
          <>
            <Header title={headerTitle || t('分拨列表明细')} />
            <AllotListDetail {...this.props} />
          </>
        );
        break;
      case 4:
        children = (
          <>
            <Header title={headerTitle || t('调拨容器查询')} />
            <AllotBoxQueryDetail {...this.props} />
          </>
        );
        break;
      case 5:
        children = (
          <>
            <Header title={headerTitle || t('上架明细页面')} />
            <PutOnListDetail {...this.props} />
          </>
        );
        break;
      default:
        break;
    }
    return (
      <div>
        {children}
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  pageType: PropTypes.number,
  dataLoading: PropTypes.number,
};

export default i18n(Container);
