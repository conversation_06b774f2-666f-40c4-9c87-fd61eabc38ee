import { sendPostRequest } from '../../../lib/public-request';

/**
 * 扫描容器号
 * @param param
 * @returns {*}
 */
export const scanBigContainerAPI = (param) => sendPostRequest({
  url: '/allocation_split/allocation_box_query',
  param,
}, process.env.WTS_FRONT);

/**
 *获取上架明细
 * @param param
 * @returns {*}
 */
export const allocationBoxDetailQueryAPI = (param) => sendPostRequest({
  url: '/allocation_split/allocation_box_detail_query',
  param,
}, process.env.WTS_FRONT);
