.loader {
    animation: loader-effect 1s infinite linear;
}

@keyframes loader-effect {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.red {
    color: red;
}
.warning {
    background: orange;
    color: #fff;
}

.topCard {
    padding: 10px;
    font-size: 12px;
    .topCardHeader {
        font-size: 16px;
    }
    .topCardBottom {
        display: flex;
        justify-content: space-between;
    }
}
.bottomCard {
    padding: 10px;
    font-size: 12px;
    .bottomCardItem {
        padding-bottom: 5px;
    }
}
.allotListDetailHeader {
    display: flex;
    justify-content: space-between;
}
.allotListDetailSubHeader {
    display: flex;
    justify-content: space-between;
}
