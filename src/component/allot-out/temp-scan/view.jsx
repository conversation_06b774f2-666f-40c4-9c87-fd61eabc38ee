import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import { Form } from 'react-weui/build/packages/components/form';
import store from './reducers';
import Header from '../../common/header';
import Footer from '../../common/footer';
import FocusInput from '../../common/focus-input';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      dispatch,
      boxDisabled,
      locationDisabled,
      shelfContainerCode,
      location,
      headerTitle,
    } = this.props;
    return (
      <div>
        <Header title={t('调拨暂存扫描')} />
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            className="shelfContainerCode"
            data-bind="shelfContainerCode"
            autoFocus
            disabled={boxDisabled === 0}
            onPressEnter={(e) => {
              if (e.target.value) {
                store.scanBoxCode({
                  shelfContainerCode,
                });
              }
            }}
          >
            <label>{t('上架箱号')}</label>
          </FocusInput>
          <FocusInput
            placeholder={t('请扫描')}
            className="location"
            data-bind="location"
            disabled={locationDisabled === 0}
            onPressEnter={(e) => {
              if (e.target.value) {
                store.scanLocation({
                  shelfContainerCode,
                  collectLocation: location,
                });
              }
            }}
          >
            <label>{t('暂存库位')}</label>
          </FocusInput>
        </Form>
        <Footer />
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  headerTitle: PropTypes.string.isRequired,
};

export default i18n(Container);
