import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import * as servers from './server';
import { message } from '../../common';

const defaultState = {
  shelfContainerCode: '',
  boxDisabled: 1,
  location: '',
  locationDisabled: 0,
  headerTitle: '',
};

export default {
  defaultState,
  $init: () => {
    classFocus('shelfContainerCode');
    return defaultState;
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * errorClear(action, ctx) {
    yield ctx.changeData({
      data: {
        [action.data]: '',
        [action.disabled]: 1,
      },
    });
    Modal.error({
      content: action.msg,
      onOk: () => {
        classFocus(action.data);
      },
    });
  },
  * scanBoxCode(action, ctx) {
    yield ctx.changeData({
      data: {
        boxDisabled: 0,
      },
    });
    const res = yield servers.scanBoxToServer(action);
    if (res.code !== '0') {
      yield ctx.errorClear({
        data: 'shelfContainerCode',
        disabled: 'boxDisabled',
        msg: res.msg,
      });
    } else {
      yield ctx.changeData({
        data: {
          locationDisabled: 1,
        },
      });
      classFocus('location');
    }
  },
  * scanLocation(action, ctx) {
    yield ctx.changeData({
      data: {
        locationDisabled: 0,
      },
    });
    const res = yield servers.scanLocationToServer(action);
    if (res.code !== '0') {
      yield ctx.errorClear({
        data: 'location',
        disabled: 'locationDisabled',
        msg: res.msg,
      });
    } else {
      message.success(t('成功'));
      yield ctx.init();
    }
  },
};
