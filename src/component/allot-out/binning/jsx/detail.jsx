import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Header, Footer, View, Table, LabelList,
} from 'common';
import store from '../reducers';

class Detail extends React.Component {
  render() {
    const {
      dataLoading,
      detailInfo,
    } = this.props;

    const columns = [
      {
        title: t('上架周转箱'),
        dataIndex: 'shelfContainerCode',
      },
      {
        title: t('尺码'),
        dataIndex: 'size',
      },
      {
        title: t('已分/待分'),
        render: (row) => `${row.spiltNum}/${row.waitSpiltNum}`,
      },
    ];

    return (
      <div>
        <Header title={t('装箱明细')} />

        <View
          diff={100} // 默认56; 页面内容高度：window.innerHeight - diff 中的 diff 值【必填: 100是头部标题44+底部按钮56之和】
          flex
          loading={dataLoading}
          style={{ flexDirection: 'column' }}
        >
          <LabelList
            labelList={[t('大纸箱'), t('已分周转箱个数'), t('操作人')]}
            valueList={[detailInfo.bigContainerCode, detailInfo.detail?.length || 0, detailInfo.detail[0]?.user || '']}
            labelMinWidth="unset"
          />
          <Table
            dataSource={detailInfo.detail || []}
            columns={columns}
          />
        </View>

        <Footer
          beforeBack={() => {
            store.returnContentPage();
          }}
        />
      </div>
    );
  }
}

Detail.propTypes = {
  dataLoading: PropTypes.number,
  detailInfo: PropTypes.shape(),
};

export default Detail;
