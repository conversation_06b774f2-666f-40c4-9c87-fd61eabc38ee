/* eslint-disable max-len */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { Button } from 'react-weui/build/packages/components/button';
import {
  Header, FocusInput, Footer, View, FooterBtn, ProgressBar, LabelList, TextBar,
} from 'common';
import store from '../reducers';
import style from '../style.less';

class Default extends Component {
  render() {
    const {
      headerTitle,
      dataLoading,
      bigContainerCode,
      barCode,
      shelfContainerCode,
      showShelfInput,
      spiltNum,
      totalNum,
      defaultInfo,
      shelfDisabled,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle || t('调拨分箱')}>
          <div onClick={() => store.getDetail()}>
            {t('装箱明细')}
          </div>
        </Header>

        <View
          diff={100} // 默认56; 页面内容高度：window.innerHeight - diff 中的 diff 值【必填: 100是头部标题44+底部按钮56之和】
          flex={false} // flex布局，默认为true，当需要固定单个输入框时，不启用【选填: 根据实际情况决定】
        >
          <div className={style.progressBox}>
            <ProgressBar percentage={spiltNum / totalNum} trackColor="#f4f6fa" color="#0059cf" style={{ borderRadius: 10, border: '1px solid #0758b3' }} />
            <div className={style.progressText}>{`${t('已分')}${spiltNum} / ${t('共')}${totalNum}`}</div>
          </div>
          <Form>
            <FocusInput
              value={bigContainerCode}
              disabled
              footer={
              (
                <Button
                  size="small"
                  style={{ fontSize: 12 }}
                  disabled={!bigContainerCode || !dataLoading}
                  onClick={() => {
                    store.emptyBox();
                  }}
                >
                  {t('箱空')}
                </Button>
              )
            }
            >
              <label>{t('大箱号')}</label>
            </FocusInput>
            <FocusInput
              value={barCode}
              className="barCode"
              placeholder={t('请扫描')}
              disabled={!dataLoading}
              autoFocus
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  barCode: value,
                });
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanGoods();
              }}
            >
              <label>{t('商品条码')}</label>
            </FocusInput>
            {showShelfInput ? (
              <FocusInput
                value={shelfContainerCode}
                className="shelfContainerCode"
                placeholder={t('请扫描')}
                disabled={!dataLoading || shelfDisabled}
                autoFocus
                onChange={(e) => {
                  const { value } = e.target;
                  store.changeData({
                    shelfContainerCode: value,
                  });
                }}
                onPressEnter={(e) => {
                  if (!e.target.value) {
                    return;
                  }
                  store.scanBox();
                }}
              >
                <label>{t('上架周转箱')}</label>
              </FocusInput>
            ) : null}
          </Form>

          {defaultInfo.serialNumber ? (
            <TextBar text={[`#${defaultInfo.serialNumber}`]} style={{ marginTop: 8 }} />
          ) : null}

          {defaultInfo.skc ? (
            <LabelList
              labelMinWidth="unset"
              labelList={[t('SKC'), t('尺码')]}
              valueList={[defaultInfo.skc, defaultInfo.size]}
            />
          ) : null}

        </View>

        <Footer beforeBack={() => store.returnInfoPage()}>
          <FooterBtn
            disabled={dataLoading === 0}
            onClick={() => store.closeBox()}
          >
            {t('关箱')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Default.propTypes = {
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  bigContainerCode: PropTypes.string,
  barCode: PropTypes.string,
  shelfContainerCode: PropTypes.string,
  spiltNum: PropTypes.number,
  totalNum: PropTypes.number,
  showShelfInput: PropTypes.bool,
  defaultInfo: PropTypes.shape(),
  shelfDisabled: PropTypes.bool,
};

export default i18n(Default);
