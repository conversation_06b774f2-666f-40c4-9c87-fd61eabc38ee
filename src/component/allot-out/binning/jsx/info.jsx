import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, Footer, View,
} from 'common';
import store from '../reducers';

class Info extends Component {
  render() {
    const {
      headerTitle,
      dataLoading,
      bigContainerCode,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle || t('调拨分箱')} />
        <View
          diff={100}
          flex={false}
        >
          <Form>
            <FocusInput
              value={bigContainerCode}
              className="bigContainerCode"
              placeholder={t('请扫描')}
              disabled={!dataLoading}
              autoFocus
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  bigContainerCode: value,
                });
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.handleBinning();
              }}
            >
              <label>{t('大箱号')}</label>
            </FocusInput>
          </Form>
        </View>
        <Footer
          beforeBack={(back) => {
            back();
          }}
        />
      </div>
    );
  }
}

Info.propTypes = {
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  bigContainerCode: PropTypes.string,
};

export default i18n(Info);
