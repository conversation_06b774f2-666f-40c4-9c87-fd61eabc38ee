import { sendPostRequest } from 'lib/public-request';

/**
 * 关箱
 */
export const closeBoxAPI = (param) => sendPostRequest({
  url: '/allocation_split/close_box',
  param,
}, process.env.WTS_FRONT);

/**
 * 扫描大箱号
 */
export const scanBigBoxAPI = (param) => sendPostRequest({
  url: '/allocation_split/scan_container_code',
  param,
}, process.env.WTS_FRONT);

/**
 * 扫描商品条码
 */
export const scanGoodsAPI = (param) => sendPostRequest({
  url: '/allocation_split/scan_bar_code',
  param,
}, process.env.WTS_FRONT);

/**
 * 扫描上架周转箱
 */
export const scanBoxAPI = (param) => sendPostRequest({
  url: '/allocation_split/scan_shelf_container_code',
  param,
}, process.env.WTS_FRONT);

/**
 * 箱空
 */
export const emptyBoxAPI = (param) => sendPostRequest({
  url: '/allocation_split/box_empty',
  param,
}, process.env.WTS_FRONT);

/**
 * 箱空确认
 */
export const emptyBoxConfirmAPI = (param) => sendPostRequest({
  url: '/allocation_split/box_empty_confirm',
  param,
}, process.env.WTS_FRONT);

/**
 * 装箱明细
 */
export const getDetailAPI = (param) => sendPostRequest({
  url: '/allocation_split/query_split_detail',
  param,
}, process.env.WTS_FRONT);
