import React from 'react';
import { t } from '@shein-bbl/react';
import { classFocus, getHeaderTitle } from 'lib/util';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { modal, message } from 'common';
import { push } from 'react-router-redux';
import { put } from 'redux-saga/effects';
import {
  scanBigBoxAPI,
  closeBoxAPI,
  scanGoodsAPI,
  scanBoxAPI,
  getDetailAPI,
  emptyBoxAPI,
  emptyBoxConfirmAPI,
} from './server';

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  currentPage: 1, // 1 初始页面(只有大箱号)，2 操作页面，3 明细页面detail
  bigContainerCode: '', // 大箱号
  barCode: '', // 商品条码
  shelfContainerCode: '', // 上架周转箱
  showShelfInput: false, // 上架周转箱 显示
  shelfDisabled: true, // 上架周转箱 禁用
  spiltNum: 0, // 已分
  totalNum: 0, // 总数量
  defaultInfo: {}, // 调拨分箱skc、尺码等数据
  detailInfo: {}, // 明细数据
  operator: '', // 当前用户
  warehouseId: '', // 当前仓库
  subWarehouseId: '', // 当前子仓
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(state, data) {
    Object.assign(state, data);
  },
  * init() {
    yield this.getSubwarhouseData();
    yield this.changeData({
      headerTitle: getHeaderTitle() || t('调拨分箱'),
      operator: JSON.parse(localStorage.getItem('user') || '{}').username,
      warehouseId: JSON.parse(localStorage.getItem('warehouse') || '{}').warehouseId,
    });
  },

  /**
   * 回车-大箱号
   */
  * handleBinning() {
    const {
      bigContainerCode,
      operator,
      warehouseId,
      subWarehouseId,
    } = yield '';
    markStatus('dataLoading');
    const { code, info, msg } = yield scanBigBoxAPI({
      bigContainerCode,
      operator,
      warehouseId,
      subWarehouseId,
    });
    if (code === '0') {
      yield this.changeData({
        currentPage: 2,
        spiltNum: info?.spiltNum || 0, // 已分
        totalNum: info?.quantity || 0, // 总数量
        shelfContainerCode: info?.shelfContainerCode || '', // 上架周转箱
        showShelfInput: Boolean(info.shelfContainerCode),
      });
      classFocus('barCode');
    } else {
      yield this.changeData({ bigContainerCode: '' });
      modal.error({ content: msg, className: 'bigContainerCode' });
    }
  },

  /**
   * 回到初始页面(只有大箱号), 并聚焦, 初始化数据
   */
  * returnInfoPage() {
    yield this.changeData({
      currentPage: 1,
      bigContainerCode: '',
      barCode: '',
      shelfContainerCode: '',
      shelfDisabled: true,
      showShelfInput: false,
      defaultInfo: {},
      spiltNum: 0,
      totalNum: 0,
    });
    classFocus('bigContainerCode');
  },

  /**
   * 扫描商品条码
   */
  * scanGoods() {
    const {
      barCode,
      bigContainerCode,
      operator,
      warehouseId,
      subWarehouseId,
    } = yield '';
    markStatus('dataLoading');
    const { code, info, msg } = yield scanGoodsAPI({
      barCode,
      bigContainerCode,
      operator,
      warehouseId,
      subWarehouseId,
    });
    if (code === '0') {
      // type: 0-继续扫描商品条码， 1-扫描上架箱号 2-此条码商品已分完 3-大箱已经关箱
      if (info.type === 0) {
        yield this.changeData({
          barCode: '',
          shelfContainerCode: `${info.shelfContainerCode}（${t('已装{}', info.goodsNum)}）`,
          showShelfInput: Boolean(info.shelfContainerCode),
          shelfDisabled: true,
          defaultInfo: info,
          spiltNum: info?.spiltNum || 0, // 已分
          totalNum: info?.quantity || 0, // 总数量
        });
        classFocus('barCode');
      } else if (info.type === 1) {
        yield this.changeData({
          shelfContainerCode: '',
          shelfDisabled: false,
          showShelfInput: true,
        });
        classFocus('shelfContainerCode');
      } else if ([2, 3].includes(info.type)) {
        yield this.showModal(info);
      }
    } else {
      yield this.changeData({ barCode: '' });
      modal.error({ content: msg, className: 'barCode' });
    }
  },

  /**
   * 扫描上架周转箱
   */
  * scanBox() {
    const {
      barCode,
      bigContainerCode,
      shelfContainerCode,
      operator,
      warehouseId,
      subWarehouseId,
    } = yield '';
    markStatus('dataLoading');
    const { code, info, msg } = yield scanBoxAPI({
      barCode,
      bigContainerCode,
      shelfContainerCode: shelfContainerCode.indexOf('（') > 0 ? shelfContainerCode.substr(0, shelfContainerCode.indexOf('（')) : shelfContainerCode,
      operator,
      warehouseId,
      subWarehouseId,
    });
    if (code === '0') {
      // type: 0-继续扫描商品条码， 1-扫描上架箱号 2-此条码商品已分完 3-大箱已经关箱
      if (info.type === 0) {
        yield this.changeData({
          shelfContainerCode: info.shelfContainerCode,
          shelfDisabled: true,
          barCode: '',
          defaultInfo: info,
          spiltNum: info?.spiltNum || 0, // 已分
          totalNum: info?.quantity || 0, // 总数量
        });
        classFocus('barCode');
      } else if (info.type === 1) {
        yield this.changeData({
          shelfContainerCode: '',
        });
        classFocus('shelfContainerCode');
      } else if ([2, 3].includes(info.type)) {
        yield this.showModal(info);
      }
    } else {
      yield this.changeData({
        shelfContainerCode: '',
      });
      modal.error({ content: msg, className: 'shelfContainerCode' });
    }
  },

  /**
   * 展示商品尺码已分完提示
   */
  * showModal(info) {
    const { defaultInfo, shelfContainerCode } = yield '';
    const status = yield new Promise((r) => modal.confirm({
      modalBlurInput: true,
      content: (
        <div>
          <div>{t('箱号{}#：{}，{}已全部分完，系统已关箱', defaultInfo?.serialNumber || info?.serialNumber, shelfContainerCode, info.size)}</div>
          <div>【{info.flagName}】</div>
        </div>
      ),
      buttons: [{
        type: 'primary',
        label: t('我知道了'),
        onClick: () => r(true),
      }],
    }));
    if (status) {
      if (info.type === 2) {
        // 分完清空展示信息
        yield this.changeData({
          barCode: '',
          shelfContainerCode: '',
          shelfDisabled: true,
          defaultInfo: {},
          spiltNum: info?.spiltNum || 0, // 已分
          totalNum: info?.quantity || 0, // 总数量
        });
        classFocus('barCode');
      } else if (info.type === 3) {
        yield this.returnInfoPage();
      }
    }
  },

  /**
   * 按钮-箱空
   */
  * emptyBox() {
    const { bigContainerCode, operator, warehouseId } = yield '';
    markStatus('dataLoading');
    const { code, info, msg } = yield emptyBoxAPI({ bigContainerCode, operator, warehouseId });
    if (code === '0') {
      const status = yield new Promise((r) => modal.confirm2({
        modalBlurInput: true,
        title: t('可箱空{}件', info.goodsNum || 0),
        content: (
          <div>
            <div>{t('系统自动关闭周转箱{}个', info.boxNum || 0)}</div>
            <div>（{t('请在装箱明细查看推荐目的地')}）</div>
          </div>
        ),
        buttons: [{
          type: 'default',
          label: t('取消'),
          onClick: () => r(false),
        }, {
          type: 'primary',
          label: t('我知道了'),
          onClick: () => r(true),
        }],
      }));
      if (status) {
        yield this.emptyBoxConfirm();
      }
    } else {
      modal.error({ content: msg });
    }
  },
  /**
   * 按钮-箱空确认
   */
  * emptyBoxConfirm() {
    const {
      bigContainerCode,
      operator,
      warehouseId,
      subWarehouseId,
    } = yield '';
    markStatus('dataLoading');
    const { code, msg } = yield emptyBoxConfirmAPI({
      bigContainerCode,
      operator,
      warehouseId,
      subWarehouseId,
    });
    if (code === '0') {
      yield this.returnInfoPage();
    } else {
      modal.error({ content: msg });
    }
  },

  /**
   * 按钮-关箱
   */
  * closeBox() {
    const status = yield new Promise((r) => modal.confirm({
      modalBlurInput: true,
      title: t('是否确认操作关箱'),
      onOk: () => r(true),
    }));
    if (status) {
      const {
        shelfContainerCode,
        bigContainerCode,
        operator,
        warehouseId,
        subWarehouseId,
      } = yield '';
      markStatus('dataLoading');
      const { code, info, msg } = yield closeBoxAPI({
        shelfContainerCode: shelfContainerCode.indexOf('（') > 0 ? shelfContainerCode.substr(0, shelfContainerCode.indexOf('（')) : shelfContainerCode,
        bigContainerCode,
        operator,
        warehouseId,
        subWarehouseId,
      });
      if (code === '0') {
        message.success(t('关箱成功！【{}】', info.flagName), 2000);
        // 清空商品条码、周转箱号，定位在商品条码
        yield this.changeData({
          barCode: '',
          shelfContainerCode: '',
          shelfDisabled: true,
          defaultInfo: {},
        });
        classFocus('barCode');
      } else {
        modal.error({ content: msg });
      }
    }
  },

  /**
   * 回到default页面，并聚焦在商品条码或分波周转箱上
   */
  * returnContentPage() {
    yield this.changeData({
      currentPage: 2,
    });
    const { shelfDisabled } = yield '';
    if (shelfDisabled) {
      classFocus('barCode');
    } else {
      classFocus('shelfContainerCode');
    }
  },

  /**
   * 获取明细页面数据
   */
  * getDetail() {
    const { bigContainerCode, operator, warehouseId } = yield '';
    markStatus('dataLoading');
    const { code, info, msg } = yield getDetailAPI({ bigContainerCode, operator, warehouseId });
    if (code === '0') {
      yield this.changeData({
        currentPage: 3,
        detailInfo: info || {},
      });
    } else {
      modal.error({ content: msg });
    }
  },

  // 获取数据字典下拉数据
  * getSubwarhouseData(action, ctx) {
  // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }
    yield ctx.changeData({
      subWarehouseId: preSubMenu.subWarehouseId,
    });
  },
};
