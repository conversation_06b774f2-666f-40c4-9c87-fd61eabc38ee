import { sendPostRequest } from 'lib/public-request';

/**
 * 扫描车牌号
 */
export const scanVehicleNoAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/scan_deliver_truck_no',
  param,
}, process.env.WTS_FRONT);

/**
 * 扫描大箱号
 * @param param
 * @returns {*}
 */
export const scanBigBoxNoAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/scan_big_container_code',
  param,
}, process.env.WTS_FRONT);

/**
 * 获取明细列表
 * @param param
 * @returns {*}
 */
export const jumpDetailAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/return_allot/search',
  param,
}, process.env.WTS_FRONT);

/**
 * 装车
 * @param param
 * @returns {*}
 */
export const receiveReportAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/loading_ship_container',
  param,
}, process.env.WTS_FRONT);

// 缺箱
export const shortageBoxAPI = (param) => sendPostRequest({
  url: '/pda/handover_receive/update_shortage_box',
  param,
}, process.env.WIS_FRONT);

// 获取仓库
export const getWarehouseAPI = (param) => sendPostRequest({
  url: '/data_permission/warehouse/list',
  param,
}, process.env.WAS_FRONT);

// 选择出发仓
export const choseSrcWarehouseAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/chose_src_warehouse',
  param,
}, process.env.WTS_FRONT);
