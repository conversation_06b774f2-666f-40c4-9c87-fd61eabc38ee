import { t } from '@shein-bbl/react';
import { getHeaderTitle, classFocus } from 'lib/util';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { modal, message } from 'common';
import aaoo from 'source/audio/aaoo.mp3';
import dingdong from 'source/audio/dingdong.mp3';
import assign from 'object-assign';
import {
  scanVehicleNoAPI,
  scanBigBoxNoAPI,
  jumpDetailAPI,
  receiveReportAPI,
  shortageBoxAPI,
  getWarehouseAPI,
  choseSrcWarehouseAPI,
} from './server';

const errAudio = new Audio(aaoo);
errAudio.load();
const okAudio = new Audio(dingdong);
okAudio.load();

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  currentPage: 1, // 1 初始操作页面，2 明细页面detail, 3 报账单明细页面
  vehicleNo: '', // 车牌号
  bigBoxNo: '', // 大箱号
  detailList: [], // 明细列表
  scanVehicleNoDetail: {}, // 扫描车牌的返回
  waitScanBoxList: [], // 报账单号-应收箱明细 -箱号
  hasScanBoxList: [], // 已经扫描的
  reportOrderNum: '', // 报账单号
  waitScanBoxNum: 0, // 报账单箱子数
  warehouseShow: false, // 出发仓选择
  warehouseValue: '', // 出发仓选择的值
  warehouseValueLabel: '', // 出发仓选择的名称
  warehousePickerData: [], // 出发仓选择下拉
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(state, data) {
    Object.assign(state, data);
  },
  * init() {
    markStatus('dataLoading');
    yield this.changeData({
      headerTitle: getHeaderTitle() || t('调拨装车'),
    });
    yield this.getWarehouse(); // 初始化获取仓库
    classFocus('vehicleNo'); // 默认聚焦车牌
  },
  // 获取仓库列表
  * getWarehouse(action, ctx) {
    markStatus('dataLoading');
    const res = yield getWarehouseAPI({});
    if (res.code === '0') {
      const list = res.info.data || [];
      yield ctx.changeData({
        warehousePickerData: list.map((item) => ({
          label: item.nameZh,
          value: item.id,
        })),
      });
      classFocus('vehicleNo');
    } else {
      modal.error({ content: res.msg });
    }
  },
  // 重置数据
  * clearData() {
    const { headerTitle } = yield '';
    yield this.changeData({
      ...defaultState,
      headerTitle,
    });
  },
  // 扫描车牌号
  * scanVehicleNo(params = {}) {
    markStatus('dataLoading');
    const {
      vehicleNo,
    } = yield '';
    const param = {
      warehouseId: JSON.parse(localStorage.getItem('warehouse') || '{}').warehouseId,
      deliverTruckNo: vehicleNo,
      ...params,
    };
    const res = yield scanVehicleNoAPI(param);
    if (res.code === '0') {
      const scanVehicleRes = res.info || {};
      yield this.changeData({
        scanVehicleNoDetail: {
          ...scanVehicleRes,
          deliverTruckNo: vehicleNo, // 车牌号
          shippingTrackNo: scanVehicleRes.shippingTrackNo, // 运输单号
          shippingTrackNoBoxNum: scanVehicleRes.shippingTrackNoBoxNum, // 运输单已装车箱数
        },
      });
      classFocus('departureWarehouse'); // 跳转到出发仓
    } else if (res.code === '470340') {
      // 该车牌存在当天关单的运输单，是否确认生成新的运输单？
      const confirmFlag = yield new Promise((r) => {
        modal.confirm({
          content: res.msg,
          onOk: () => r(1),
          onCancel: () => r(0),
        });
      });
      // 点确认
      if (confirmFlag === 1) {
        yield this.scanVehicleNo({
          confirm: true, // 弹框确认
        });
      } else {
        // 点取消
        yield this.changeData({ vehicleNo: '' }); // 清空车牌号
        classFocus('vehicleNo'); // 焦点置于车牌号
      }
    } else {
      yield this.changeData({ vehicleNo: '' });
      modal.error({ content: res.msg, className: 'vehicleNo' });
    }
  },
  // 扫描大箱
  * scanBigBoxNo(action) {
    const { reportOrderNumFlag = false } = action || {};
    const {
      waitScanBoxList, bigBoxNo, reportOrderNum, warehouseValue,
    } = yield '';
    if (waitScanBoxList.some((item) => item.containerCode === bigBoxNo && item.status === 1)) {
      errAudio.play();
      modal.error({
        content: t('请不要重复扫描'),
        className: 'bigBoxNo',
      });
      yield this.changeData({
        bigBoxNo: '',
      });
      return;
    }
    markStatus('dataLoading');
    const {
      scanVehicleNoDetail,
    } = yield '';
    const param = {
      warehouseId: warehouseValue,
      deliverTruckNo: scanVehicleNoDetail.deliverTruckNo,
      allocationId: scanVehicleNoDetail.allocationId,
      allocationNo: scanVehicleNoDetail.allocationNo,
      bigContainerCode: bigBoxNo,
      reportOrderNum,
      reportOrderNumFlag,
    };
    const res = yield scanBigBoxNoAPI(param);
    if (res.code === '0') {
      okAudio.play();
      const scanBigBoxDetail = res.info || {};
      yield this.changeData({
        bigBoxNo: '',
        scanVehicleNoDetail: {
          ...scanVehicleNoDetail,
          boxNum: scanBigBoxDetail.boxNum,
          allocationNo: scanBigBoxDetail.allocationNo,
          allocationId: `${scanBigBoxDetail.allocationId}`,
          shippingTrackNoBoxNum: scanBigBoxDetail.shippingTrackNoBoxNum, // 运输单已装车箱数
        },
        reportOrderNum: scanBigBoxDetail.reportOrderNum,
        waitScanBoxNum: scanBigBoxDetail.waitScanBoxNum,
      });
      if (!scanBigBoxDetail.reportOrderNum) {
        yield this.changeData({
          waitScanBoxList: [],
          hasScanBoxList: [],
        });
      } else {
        yield this.addScanList({ id: bigBoxNo, list: scanBigBoxDetail.waitScanBoxList || [] });
      }
      classFocus('bigBoxNo'); // 继续聚焦扫箱号
    } else {
      errAudio.play();
      // 当前有操作中的报账单
      if (res.code === '470339') {
        const flag = yield new Promise((r) => {
          modal.confirm({
            content: res.msg,
            onOk: () => r(1),
            onCancel: () => r(0),
          });
        });
        if (flag) {
          // 收货逻辑
          yield this.changeData({
            waitScanBoxList: [],
            hasScanBoxList: [],
            reportOrderNum: '',
          });
          yield this.scanBigBoxNo({ reportOrderNumFlag: true });
        } else {
          yield this.changeData({ bigBoxNo: '' });
          classFocus('bigBoxNo');
        }
        return;
      }
      yield this.changeData({ bigBoxNo: '' });
      modal.error({ content: res.msg, className: 'bigBoxNo' });
    }
  },
  // 装箱明细
  * jumpDetail() {
    markStatus('dataLoading');
    const {
      scanVehicleNoDetail,
    } = yield '';
    const param = {
      shippingTrackNo: scanVehicleNoDetail.shippingTrackNo,
    };
    const res = yield jumpDetailAPI(param);
    if (res.code === '0') {
      if (res.info.bigContainerCodeList?.length) {
        yield this.changeData({
          currentPage: 2,
          detailList: (res.info?.bigContainerCodeList || []).map((bi) => ({
            bigContainerCode: bi,
          })),
        });
      } else {
        modal.error({ content: t('当前暂无明细!') });
      }
    } else {
      modal.error({ content: res.msg });
    }
  },

  // 添加已扫描箱号记录
  * addScanList(action) {
    const { id, list } = action;
    const { hasScanBoxList } = yield '';
    const newHasScan = [...new Set([...hasScanBoxList, id])];
    const waitList = list.map((item) => ({
      containerCode: item,
      status: newHasScan.includes(item) ? 1 : 0,
    }));
    yield this.changeData({
      hasScanBoxList: newHasScan,
      waitScanBoxList: [...waitList],
    });
  },

  // 处理缺箱
  * handleMissBox(action) {
    const { id } = action;
    const { reportOrderNum } = yield '';
    markStatus('dataLoading');
    const res = yield shortageBoxAPI({ containerCode: id, reportOrderNum, type: 2 });
    if (res.code === '0') {
      const { waitScanBoxList } = yield '';
      const newList = [...waitScanBoxList];
      const index = newList.findIndex((item) => item.containerCode === id);
      if (index >= 0) {
        newList.splice(index, 1);
        yield this.changeData({ waitScanBoxList: [...newList] });
      }
    } else {
      modal.error({
        content: res.msg,
        className: '',
      });
    }
  },

  // 装车
  * handleReceive() {
    const { waitScanBoxList, scanVehicleNoDetail } = yield '';
    const hasScanList = waitScanBoxList.filter((item) => item.status === 1);
    const containerCodeList = hasScanList.map((item) => item.containerCode);
    markStatus('dataLoading');
    const res = yield receiveReportAPI({
      containerCodeList,
      allocationId: scanVehicleNoDetail?.allocationId,
      deliverTruckNo: scanVehicleNoDetail?.deliverTruckNo,
    });
    if (res.code === '0') {
      message.success(t('装车成功'));
      yield this.changeData({
        bigBoxNo: '',
        waitScanBoxList: [],
        hasScanBoxList: [],
        reportOrderNum: '',
        waitScanBoxNum: 0,
        scanVehicleNoDetail: {
          ...scanVehicleNoDetail,
          boxNum: res.info?.boxNum || 0,
          shippingTrackNoBoxNum: res.info?.shippingTrackNoBoxNum, // 运输单已装车箱数
        },
      });
      classFocus('bigBoxNo');
    } else {
      yield this.clearData();
      yield this.getWarehouse(); // 初始化获取仓库
      modal.error({ content: res.msg, className: 'vehicleNo' });
    }
  },
  // 选择出发仓
  * choseSrcWarehouse(params = { warehouseValueSelected: {}, confirm: false }) {
    const { scanVehicleNoDetail, waitScanBoxList } = yield '';
    markStatus('dataLoading');
    const res = yield choseSrcWarehouseAPI({
      shippingTrackNo: scanVehicleNoDetail.shippingTrackNo, // 运输单号
      warehouseId: params.warehouseValueSelected?.value, // 选择的出发仓
      deliverTruckNo: scanVehicleNoDetail.deliverTruckNo, // 本地保存扫车牌成功的车牌号
      confirm: params.confirm,
    });
    if (res.code === '0') {
      const choseSrcWarehouseRes = res.info || {};
      yield this.changeData({
        scanVehicleNoDetail: {
          ...scanVehicleNoDetail,
          allocationId: `${choseSrcWarehouseRes.allocationId}`, // 调拨单id
          allocationNo: choseSrcWarehouseRes.allocationNo, // 调拨单号
          shippingTrackNo: choseSrcWarehouseRes.shippingTrackNo, // 运输单号
          srcWarehouseId: choseSrcWarehouseRes.srcWarehouseId, // 出发仓id
          srcWarehouseName: choseSrcWarehouseRes.srcWarehouseName, // 出发仓名称
          warehouseId: choseSrcWarehouseRes.warehouseId, // 计划到达仓id
          warehouseCodeName: choseSrcWarehouseRes.warehouseIdName, // 计划到达仓名称
        },
        reportOrderNum: '', // 清空报账单号
        hasScanBoxList: [], // 清空已扫描
        waitScanBoxList: (waitScanBoxList || []).map((wi) => ({
          ...wi,
          status: 0,
        })), // 清空已扫描箱子
        warehouseValue: params.warehouseValueSelected?.value,
        warehouseValueLabel: params.warehouseValueSelected?.label,
      });
      classFocus('bigBoxNo');
    } else if (res.code === '470341') {
      const choseSrcWarehouseFlag = yield new Promise((r) => {
        modal.confirm({
          content: res.msg,
          onOk: () => r(1),
          onCancel: () => r(0),
        });
      });
      if (choseSrcWarehouseFlag) {
        yield this.choseSrcWarehouse({
          ...params,
          confirm: true,
        });
      } else {
        // 清空出发仓库选择值
        yield this.changeData({
          warehouseValue: '',
          warehouseValueLabel: '',
        });
        classFocus('departureWarehouse');
      }
    } else {
      // 清空出发仓库选择值
      yield this.changeData({
        warehouseValue: '',
        warehouseValueLabel: '',
      });
      message.error(res.msg);
      classFocus('departureWarehouse');
    }
  },
};
