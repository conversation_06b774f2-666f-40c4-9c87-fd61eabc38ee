import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import {
  Header, Footer, View, Table,
} from 'common';
import store from '../reducers';

class Info extends Component {
  render() {
    const {
      headerTitle,
      detailList,
    } = this.props;

    const columns = [
      {
        title: '',
        dataIndex: 'bigContainerCode',
      },
    ];

    return (
      <div>
        <Header title={headerTitle || t('调拨装车')} />
        <View
          diff={100}
          flex={false}
        >
          <Table
            maxHeight={window.innerHeight - 100 - 30}
            columns={columns}
            dataSource={detailList}
          />
        </View>
        <Footer beforeBack={() => store.changeData({ currentPage: 1 })} />
      </div>
    );
  }
}

Info.propTypes = {
  headerTitle: PropTypes.string,
  detailList: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Info);
