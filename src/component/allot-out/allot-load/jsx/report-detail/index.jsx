import { t } from '@shein-bbl/react';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Tab, NavBar, NavBarItem, Button,
} from 'react-weui/build/packages';
import { View, Footer, Header } from 'common';
import { classFocus } from 'lib/util';
import styles from './style.less';
import store from '../../reducers';

function Details(props) {
  const { waitScanBoxList, dataLoading } = props;

  const [activeKey, setActiveKey] = useState(0);
  const [tabList, setTabList] = useState([
    { key: 0, title: t('待扫描'), data: [] },
    { key: 1, title: t('已扫描'), data: [] },
  ]);

  // 缺箱
  const clickMissBox = (id) => {
    store.handleMissBox({ id });
  };

  useEffect(() => {
    setTabList((prevTab) => {
      const newTab = prevTab.map((_tabItem) => {
        const currList = (waitScanBoxList || [])
          .filter((_listItem) => _listItem.status === _tabItem.key);
        return { ..._tabItem, data: currList };
      });
      return newTab;
    });
  }, [waitScanBoxList]);

  return (
    <div>
      <Header title={t('报账单明细')} />
      <View diff={100} flex={false}>
        <Tab className={styles.billTab}>
          <NavBar>
            {tabList.map((item) => (
              <NavBarItem
                key={item.key}
                active={item.key === activeKey}
                onClick={() => setActiveKey(item.key)}
              >
                {`${item.title}(${item.data?.length || 0})`}
              </NavBarItem>
            ))}
          </NavBar>
          {tabList.map((item) => (
            item.key === activeKey && (
            <div className={styles.listBox} key={item.key}>
              {item.data?.map((_listItem) => (
                <div className={styles.listItem} key={_listItem.containerCode}>
                  <div className={styles.boxNo}>{_listItem.containerCode}</div>
                  {_listItem.status === 0 && (
                    <Button
                      size="small"
                      onClick={() => clickMissBox(_listItem.containerCode)}
                      disabled={dataLoading === 0}
                    >
                      {t('缺箱')}
                    </Button>
                  )}
                </div>
              ))}
            </div>
            )
          ))}
        </Tab>
        <Footer
          beforeBack={() => {
            store.changeData({ currentPage: 1 });
            classFocus('bigBoxNo');
          }}
        />
      </View>
    </div>

  );
}

Details.propTypes = {
  waitScanBoxList: PropTypes.arrayOf(PropTypes.shape()),
  dataLoading: PropTypes.number,
};

export default Details;
