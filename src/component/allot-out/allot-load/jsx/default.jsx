import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, Footer, View, FooterBtn,
  PopSheet,
} from 'common';
import store from '../reducers';
import style from '../style.less';

class Info extends Component {
  render() {
    const {
      headerTitle,
      dataLoading,
      vehicleNo,
      bigBoxNo,
      scanVehicleNoDetail,
      reportOrderNum,
      waitScanBoxList,
      warehouseShow, // 出发仓选择
      warehouseValueLabel, // 出发仓选择-名称
      warehousePickerData, // 出发仓选择下拉
    } = this.props;

    return (
      <div>
        <Header title={headerTitle || t('调拨装车')} />
        <View
          diff={100}
          flex={false}
        >
          <Form>
            <FocusInput
              value={vehicleNo}
              className="vehicleNo"
              allowClear
              placeholder={t('请扫描')}
              disabled={false}
              onChange={(e) => {
                const { value } = e.target;
                if (!value.trim()) {
                  store.changeData({
                    vehicleNo: '',
                  });
                } else {
                  store.changeData({
                    vehicleNo: value.trim(),
                  });
                }
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanVehicleNo();
              }}
              onClear={() => {
                store.init();
              }}
            >
              <label>{t('车牌号')}</label>
            </FocusInput>
            <FocusInput
              value={warehouseValueLabel}
              readOnly
              className="departureWarehouse"
              onClick={() => {
                store.changeData({
                  warehouseShow: true,
                });
              }}
              arrow
            >
              <label>{t('出发仓')}</label>
            </FocusInput>
            <PopSheet
              onClick={(v) => {
                store.changeData({
                  warehouseShow: false,
                });
                store.choseSrcWarehouse({
                  warehouseValueSelected: v,
                }); // 选择出发仓
              }}
              onClose={() => {
                store.changeData({ warehouseShow: false });
              }}
              cancelBtn
              menus={warehousePickerData}
              show={warehouseShow}
            />
            <FocusInput
              value={bigBoxNo}
              className="bigBoxNo"
              allowClear
              placeholder={t('请扫描')}
              disabled={!dataLoading || !warehouseValueLabel || !vehicleNo}
              onChange={(e) => {
                const { value } = e.target;
                if (!value.trim()) {
                  store.changeData({
                    bigBoxNo: '',
                  });
                } else {
                  store.changeData({
                    bigBoxNo: value.trim(),
                  });
                }
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanBigBoxNo();
              }}
            >
              <label>{t('大箱号')}</label>
            </FocusInput>
          </Form>
          {scanVehicleNoDetail && Object.keys(scanVehicleNoDetail).length > 0 && (
            <>
              <div className={style.scanDetailInfo}>
                <div className={style.vehicleNoDisplay}>
                  <div>{scanVehicleNoDetail.deliverTruckNo}</div>
                </div>
                <div className={style.scanDetailInfoItem}>
                  <div>{scanVehicleNoDetail.srcWarehouseName}</div>
                  <div>{scanVehicleNoDetail.warehouseCodeName}</div>
                </div>
              </div>
              <div className={style.scanDetailInfo}>
                <hr />
              </div>
              <div className={style.scanDetailInfo}>
                <div className={style.scanDetailInfoItem}>
                  <b>{t('运输单号:')}</b>
                  <div>{scanVehicleNoDetail.shippingTrackNo}</div>
                </div>
                <div className={style.scanDetailInfoItem}>
                  <b>{t('调拨单号:')}</b>
                  <div>{scanVehicleNoDetail.allocationNo}</div>
                </div>
                <div className={style.scanDetailInfoItem}>
                  <b>{t('运输单已装车:')}</b>
                  {/* eslint-disable-next-line max-len */}
                  {/* eslint-disable-next-line react/jsx-no-script-url,no-script-url,jsx-a11y/anchor-is-valid */}
                  <a href="javascript:;" onClick={() => store.jumpDetail()}>{scanVehicleNoDetail.shippingTrackNoBoxNum}</a>
                </div>
              </div>
            </>
          )}
          {!!reportOrderNum && (
            <div className={style.reportOrderInfo}>
              <div className={style.reportOrderNum}>
                <span style={{ fontWeight: 'bold' }}>{t('报账单')}:</span>
                <span>{reportOrderNum}</span>
              </div>
              <div
                style={{ color: '#1890ff', cursor: 'pointer' }}
                onClick={() => store.changeData({ currentPage: 3 })}
              >
                {`${waitScanBoxList.filter((_obj) => _obj.status === 1).length}/${waitScanBoxList.length}`}
              </div>
            </div>
          )}
        </View>
        <Footer>
          <FooterBtn
            disabled={!reportOrderNum || !dataLoading}
            onClick={() => {
              store.handleReceive();
            }}
          >
            {t('装车')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Info.propTypes = {
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  vehicleNo: PropTypes.string,
  bigBoxNo: PropTypes.string,
  scanVehicleNoDetail: PropTypes.shape(),
  reportOrderNum: PropTypes.number,
  waitScanBoxList: PropTypes.arrayOf(PropTypes.shape()),
  warehouseShow: PropTypes.bool, // 出发仓选择
  warehouseValueLabel: PropTypes.string, // 出发仓选择
  warehousePickerData: PropTypes.arrayOf(PropTypes.shape()), // 出发仓选择下拉
};

export default i18n(Info);
