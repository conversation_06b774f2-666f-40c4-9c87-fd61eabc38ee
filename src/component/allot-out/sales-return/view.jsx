import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form, Button } from 'react-weui/build/packages';
import { i18n, t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import UploadPlus from 'common/upload-file-plus';
import { classFocus } from '../../../lib/util';
import store from './reducers';
import {
  Header,
  Footer,
  FocusInput,
  FooterBtn,
  pages,
  modal,
  PopSheet,
  List,
  SelectInput,
  message,
} from '../../common';

const { View } = pages;

const rows = [
  [
    {
      title: t('大箱号'),
      render: 'bigContainerCode',
      width: 50,
    },
    {
      title: t('装箱人'),
      render: 'encloseContainerUser',
      width: 50,
    },
  ],
  [
    {
      title: t('推荐园区'),
      render: 'shelvesParkName',
      width: 50,
    },
    {
      title: t('装箱时间'),
      render: 'encloseContainerTime',
      width: 50,
    },
  ],
];

class Container extends Component {
  componentDidMount() {
    store.init();
    store.getWarehouse();
    store.getAllotTypeList();
    classFocus('bigContainerCode');
  }

  render() {
    const {
      srcWarehouseId,
      warehouseCode,
      containerCode,
      containerCodeValid,
      bigContainerCode,
      bigContainerCodeValid,
      warehouseCodeValid,
      destWarehouseIdValid,
      scanNums,
      number,
      warehouseList,
      destWarehouseList,
      warehouseType, // 仓库类型
      allocationNo, // 调拨单号  在绑定大箱号时，后端会返回调拨单号，非保税仓的关箱和查询 则根据调拨单号进行操作
      dataLoading,
      dispatch,
      show1,
      show2,
      srcWarehouseLabel,
      destWarehouseLabel,
      showDetail,
      info,
      headerTitle,
      allocationNoIpt,
      allocationNoIptDisabled,
      show3,
      allotTypeList,
      allocationTypeLabel,
      palletCode,
      allocationType,
      isUserPallet,
      deliverTruckNo,
      scanCarCodeFlag,
      permissionSubWarehouseList,
      dischargeSubWarehouseIdName,
      dischargeSubWarehouseId,
      dischargeSubWarehouseIds,
      shippingTrackNo,
      isDisableSubWarehouseAndDeliverTruck,
      scanTransportBillNo,
      transportSelectList,
      fileList,
      subWarehouseId,
      isDisableTransportNo,
      isDisableAllocationType,
      showMoreTransportNo,
    } = this.props;
    const uploadFileURL = `${process.env.WGS_FRONT}/upload/file_upload`;

    // 查看更多运输单号
    const renderMoreBtn = () => (showMoreTransportNo ? (
      <div
        style={{ textAlign: 'center', width: '100%', color: '#197afa' }}
        onClick={() => {
          if (dataLoading === 0) return;
          store.getTransportNoExtra();
        }}
      >
        {t('查看更多')}
      </div>
    ) : null);

    return (
      <View>
        <Header title={headerTitle || t('退货调拨出库')} />

        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: '0 15px',
            fontSize: 14,
            lineHeight: '40px',
            backgroundColor: '#fff',
          }}
        >
          <div>
            <span style={{ marginRight: 10 }}>{t('已扫描箱数')}</span>
            <span style={{ fontWeight: 700 }}>{scanNums}</span>
          </div>
          <div>
            <span style={{ marginRight: 10 }}>{t('已装箱数')}</span>
            <span style={{ fontWeight: 700 }}>{number}</span>
          </div>
        </div>

        {
          !showDetail &&
          (
            <div style={{ backgroundColor: '#fff' }}>
              <Form>
                <FocusInput
                  value={allocationNoIpt}
                  className="allocationNoIpt"
                  disabled={allocationNoIptDisabled}
                  onChange={(e) => {
                    store.changeData({
                      data: {
                        allocationNoIpt: e.target.value.trim(),
                      },
                    });
                  }}
                  onPressEnter={() => {
                    if (allocationNoIpt) {
                      store.scanAllocationNo({
                        param: {
                          allocationNo: allocationNoIpt,
                          deliverTruckNo,
                        },
                      });
                    }
                  }}
                >
                  <label>{t('调拨单号')}</label>
                </FocusInput>
              </Form>
              <Form>
                <FocusInput
                  value={srcWarehouseLabel}
                  disabled={warehouseCodeValid}
                  readOnly
                  onClick={() => {
                    store.changeData({
                      data: {
                        show1: true,
                      },
                    });
                  }}
                  arrow
                >
                  <label>{t('调出仓库')}</label>
                </FocusInput>
              </Form>

              <Form>
                <FocusInput
                  value={destWarehouseLabel}
                  readOnly
                  disabled={destWarehouseIdValid}
                  onClick={() => {
                    if (warehouseCodeValid) {
                      store.changeData({
                        data: {
                          show2: true,
                        },
                      });
                    }
                  }}
                  arrow
                >
                  <label>{t('调入仓库')}</label>
                </FocusInput>
              </Form>

              <Form>
                <FocusInput
                  value={allocationTypeLabel}
                  readOnly
                  disabled={isDisableAllocationType}
                  onClick={() => {
                    store.changeData({
                      data: {
                        show3: true,
                      },
                    });
                  }}
                  arrow
                >
                  <label>{t('调拨类型')}</label>
                </FocusInput>
              </Form>

              {
                warehouseType === 3 &&
                (
                  <Form>
                    <FocusInput
                      value={containerCode}
                      label={t('集装箱号')}
                      className="containerCode"
                      disabled={dataLoading === 0 || containerCodeValid}
                      onChange={(e) => {
                        store.changeData({
                          data: {
                            containerCode: e.target.value.trim(),
                          },
                        });
                      }}
                      onPressEnter={() => {
                        if (srcWarehouseId) {
                          if (containerCode) {
                            if (containerCode.length <= 50) {
                              store.scanContainer({
                                param: {
                                  containerCode,
                                  warehouseCode,
                                },
                              });
                            } else {
                              store.changeData({
                                data: {
                                  containerCode: '',
                                },
                              });
                              modal.error({
                                content: t('集装箱号不能超过50字符'),
                                className: 'containerCode',
                              });
                            }
                          }
                        } else {
                          store.changeData({
                            data: {
                              containerCode: '',
                            },
                          });
                          modal.error({
                            content: t('请选择仓库'),
                            className: 'containerCode',
                          });
                        }
                      }}
                    />
                  </Form>
                )
              }

              <Form>
                <FocusInput
                  value={palletCode}
                  label={t('托盘号')}
                  className="palletCode"
                  disabled={dataLoading === 0 || !allocationTypeLabel}
                  onChange={(e) => {
                    store.changeData({
                      data: {
                        palletCode: e.target.value.trim(),
                      },
                    });
                  }}
                  onPressEnter={() => {
                    if (palletCode.length <= 50) {
                      store.scanPalletCode({
                        param: {
                          palletCode,
                          warehouseId: srcWarehouseId,
                        },
                      });
                    } else {
                      store.changeData({
                        data: {
                          palletCode: '',
                        },
                      });
                      modal.error({
                        content: t('托盘号不能超过50字符'),
                        className: 'palletCode',
                      });
                    }
                  }}
                  footer={(
                    <Button
                      style={{ marginRight: 5, marginBottom: 5 }}
                      size="small"
                      onClick={() => {
                        store.changeData({
                          data: {
                            palletCode: '',
                          },
                        });
                        classFocus('palletCode');
                      }}
                    >
                      {t('换托')}
                    </Button>
                  )}
                />
              </Form>
              {
                scanTransportBillNo && (
                  <Form>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <div style={{ flex: 1 }}>
                        <SelectInput
                          value={shippingTrackNo}
                          selectValue={shippingTrackNo}
                          lineBreak={false}
                          label={t('运输单号')}
                          placeHolder={t('请选择运输单号')}
                          enterShow
                          disabled={dataLoading === 0 || isDisableTransportNo}
                          className="shippingTrackNo"
                          selectList={transportSelectList}
                          onSelect={(value) => {
                            store.changeData({
                              data: {
                                shippingTrackNo: value,
                                bigContainerCode: '',
                              },
                            });
                            classFocus('bigContainerCode');
                          }}
                          footerContent={renderMoreBtn()}
                        />
                      </div>
                      <Icon
                        name="plus-o"
                        style={{ marginRight: '10px', fontSize: 20, color: 'rgb(25, 122, 250)' }}
                        onClick={() => {
                          this.uploadRef.startUpload();
                        }}
                      />
                    </div>
                  </Form>
                )
              }
              {scanCarCodeFlag && (
                <Form>
                  <FocusInput
                    value={deliverTruckNo}
                    className="deliverTruckNo"
                    allowClear
                    disabled={isDisableSubWarehouseAndDeliverTruck}
                    placeholder={t('请扫描车牌')}
                    onChange={(e) => {
                      const { value } = e.target;
                      const trimValue = `${value}`.trim();
                      if (!trimValue) {
                        store.changeData({
                          data: {
                            deliverTruckNo: '',
                            bigContainerCode: '',
                          },
                        });
                      } else if (trimValue.length > 50 || !/^[a-zA-Z0-9\s]+$/.test(trimValue)) {
                        modal.error({
                          content: t('英文、数字，前后无空格,不能超过50字符'),
                          className: 'deliverTruckNo',
                        });
                        store.changeData({
                          data: {
                            deliverTruckNo: '',
                            bigContainerCode: '',
                          },
                        });
                      } else {
                        store.changeData({
                          data: {
                            deliverTruckNo: trimValue.toUpperCase(),
                            bigContainerCode: '',
                          },
                        });
                      }
                      store.changeData({
                        data: {
                          permissionSubWarehouseList: [], // 输入车牌清空调入仓下有权限的子仓下拉
                          dischargeSubWarehouseIds: [], // 输入车牌清空扫车牌返回的卸货子仓下拉
                          dischargeSubWarehouseId: '', // 输入车牌清空已选择卸货子仓
                          dischargeSubWarehouseIdName: '', // 输入车牌清空已选择卸货子仓
                        },
                      });
                    }}
                    onPressEnter={(e) => {
                      if (!e.target.value) {
                        return;
                      }
                      store.scanCarCode();
                    }}
                  >
                    <label>{t('车牌号')}</label>
                  </FocusInput>
                </Form>
              )}
              {scanCarCodeFlag && (
                <Form>
                  <SelectInput
                    value={dischargeSubWarehouseIdName}
                    selectValue={dischargeSubWarehouseId}
                    lineBreak={false}
                    label={t('计划卸货子仓')}
                    placeHolder={t('请扫描子仓')}
                    enterShow
                    disabled={isDisableSubWarehouseAndDeliverTruck}
                    className="subWarehouse"
                    selectList={
                      // eslint-disable-next-line max-len
                      dischargeSubWarehouseIds.length ? dischargeSubWarehouseIds : permissionSubWarehouseList
                    }
                    onSelect={(value) => {
                      // eslint-disable-next-line max-len
                      const item = (dischargeSubWarehouseIds.length ? dischargeSubWarehouseIds : permissionSubWarehouseList).find((e) => e.value === value);
                      store.changeData({
                        data: {
                          dischargeSubWarehouseId: item.value,
                          dischargeSubWarehouseIdName: item.name,
                          bigContainerCode: '',
                        },
                      });
                      classFocus('bigContainerCode');
                    }}
                  />
                </Form>
              )}
              <Form>
                <FocusInput
                  value={bigContainerCode}
                  className="bigContainerCode"
                  label={t('大箱号')}
                  autoFocus
                  key={allocationType}
                  disabled={dataLoading === 0 || bigContainerCodeValid ||
                    (isUserPallet && !palletCode) || (!isUserPallet && !allocationTypeLabel)
                    || (scanCarCodeFlag && (!dischargeSubWarehouseIdName || !deliverTruckNo))
                    || (scanTransportBillNo && !shippingTrackNo)}
                  onChange={(e) => {
                    store.changeData({
                      data: {
                        bigContainerCode: e.target.value.trim(),
                      },
                    });
                  }}
                  onPressEnter={() => {
                    if (containerCode || warehouseType !== 3) {
                      if (bigContainerCode) {
                        if (!srcWarehouseId) {
                          modal.error({
                            content: t('请选择调出仓库'),
                            className: 'bigContainerCode',
                            onOk: () => {
                              store.changeData({
                                data: {
                                  bigContainerCode: '',
                                },
                              });
                            },
                          });
                          return;
                        }
                        if (!warehouseCode) {
                          modal.error({
                            content: t('请选择调入仓库'),
                            className: 'bigContainerCode',
                            onOk: () => {
                              store.changeData({
                                data: {
                                  bigContainerCode: '',
                                },
                              });
                            },
                          });
                          return;
                        }
                        if (bigContainerCode.length <= 50) {
                          store.scanBigContainer({
                            param: {
                              allocationType,
                              optionType: 1,
                              palletCode,
                              containerCode,
                              warehouseCode,
                              bigContainerCode,
                              srcWarehouseId,
                              allocationNo: allocationNoIpt,
                              dischargeSubWarehouseId, // 卸货子仓
                              shippingTrackNo, // 运输单号
                              deliverTruckNo, // 车牌号
                              srcSubWarehouseId: subWarehouseId,
                            },
                          });
                        } else {
                          store.changeData({
                            data: {
                              bigContainerCode: '',
                            },
                          });
                          modal.error({
                            content: t('大箱号不能超过50字符'),
                            className: 'bigContainerCode',
                          });
                        }
                      }
                    } else {
                      store.changeData({
                        data: {
                          bigContainerCode: '',
                          containerCode: '',
                        },
                      });
                      modal.error({
                        content: t('请录入集装箱号'),
                        className: 'containerCode',
                      });
                    }
                  }}
                />
              </Form>
              <div
                style={{
                  textAlign: 'right',
                  color: '#197AFA',
                  fontSize: 14,
                  padding: '10px 10px 0 0',
                }}
                onClick={() => {
                  store.search({
                    param: {
                      warehouseCode,
                      containerCode,
                      allocationNo: allocationNoIpt,
                      srcWarehouseId,
                    },
                  });
                }}
              >
                {t('查看详情')}
              </div>
              <div style={{ padding: 10 }}>
                <UploadPlus
                  ref={(ref) => { this.uploadRef = ref; }}
                  disabled={dataLoading === 0}
                  accept="image/png,image/jpg,image/jpeg,image/gif,application/pdf"
                  autoUpload
                  hideAddBtn
                  action={uploadFileURL}
                  fileList={fileList || []}
                  maxSize={20}
                  limit={3}
                  autoUploadKeyName="file"
                  filePathKeyName="imageUrl"
                  onDelete={async (_, newFileList) => {
                    store.changeData({
                      data: {
                        fileList: newFileList,
                      },
                    });
                  }}
                  onFailUpload={async (_, _info) => message.error(_info)}
                  onSuccessUpload={async ({ file, info: fileInfo }) => {
                    store.changeData({
                      data: {
                        fileList: [...fileList, {
                          imageUrl: fileInfo.image_url,
                          name: file.name,
                        }],
                      },
                    });
                  }}
                />
              </div>
            </div>
          )
        }
        {
          showDetail &&
          (
            <View>
              <div style={{ padding: '0 15px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <div>{t('调拨单号')} {info.allocationNo}</div>
                  <div>{info.statusName}</div>
                </div>
                <div>
                  <div>{t('集装箱号')} {containerCode}</div>
                </div>
              </div>
              <List
                rows={rows}
                data={info.list}
              />
            </View>
          )
        }
        <PopSheet
          onClick={(v) => {
            const filterList = warehouseList.filter((item) => item.id !== v.id);
            store.changeData({
              data: {
                srcWarehouseId: v.id,
                warehouseCodeValid: true,
                destWarehouseList: filterList,
                destWarehouseIdValid: false,
                srcWarehouseLabel: v.label,
                show1: false,
                isDisableAllocationType: false,
              },
            });
            store.checkScanCarCode();
          }}
          onClose={() => {
            store.changeData({ data: { show1: false } });
          }}
          cancelBtn
          menus={warehouseList}
          show={show1}
        />

        <PopSheet
          onClick={(v) => {
            const target = warehouseList.find((item) => item.id === v.id);
            store.changeData({
              data: {
                destWarehouseLabel: v.label,
                destWarehouseIdValid: true,
                warehouseType: target.type,
                warehouseCode: v.id,
                show2: false,
                permissionSubWarehouseList: [],
                dischargeSubWarehouseId: '',
                dischargeSubWarehouseIdName: '',
              },
            });
            store.checkScanCarCode();
          }}
          onClose={() => {
            store.changeData({ data: { show2: false } });
          }}
          cancelBtn
          menus={destWarehouseList}
          show={show2}
        />

        <PopSheet
          onClick={(v) => {
            store.changeData({
              data: {
                allocationTypeLabel: v.label,
                allocationType: v.id,
                show3: false,
              },
            });
            store.isUserPalletCode({
              param: {
                allocationType: v.id,
                srcWarehouseId,
                warehouseId: warehouseCode,
              },
              warehouseType,
            });
          }}
          onClose={() => {
            store.changeData({ data: { show3: false } });
          }}
          cancelBtn
          menus={allotTypeList.filter((v) => [0, 2, 4, 5, 6].includes(v.id))}
          show={show3}
        />

        <Footer
          dispatch={dispatch}
          beforeBack={(back) => {
            if (showDetail) {
              store.changeData({
                data: {
                  showDetail: false,
                },
              });
              classFocus('bigContainerCode');
            } else {
              back();
            }
          }}
        >
          {
            !showDetail &&
            (
              <FooterBtn
                disabled={!warehouseCodeValid || (allocationNo === '' && !containerCodeValid)}
                onClick={() => {
                  modal.confirm({
                    content: allocationType === 0 ? t('发货后不允许继续装车，是否确认关单') : t('是否确认关单'),
                    onOk: () => {
                      store.close({
                        param: {
                          warehouseCode,
                          containerCode,
                          allocationNo,
                          warehouseType,
                          srcWarehouseId,
                          fileUrlList: fileList.map((item) => item.imageUrl),
                        },
                      });
                    },
                    onCancel: () => { classFocus('bigContainerCode'); },
                  });
                }}
              >
                {t('关单')}
              </FooterBtn>
            )
          }
        </Footer>
      </View>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  dataLoading: PropTypes.number,
  warehouseCode: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  containerCode: PropTypes.string,
  containerCodeValid: PropTypes.bool,
  bigContainerCode: PropTypes.string,
  bigContainerCodeValid: PropTypes.bool,
  warehouseCodeValid: PropTypes.bool,
  destWarehouseIdValid: PropTypes.bool,
  show1: PropTypes.bool,
  show2: PropTypes.bool,
  showDetail: PropTypes.bool,
  scanNums: PropTypes.number,
  number: PropTypes.number,
  warehouseList: PropTypes.arrayOf(PropTypes.shape),
  destWarehouseList: PropTypes.arrayOf(PropTypes.shape),
  warehouseType: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  allocationNo: PropTypes.string,
  srcWarehouseId: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  srcWarehouseLabel: PropTypes.string,
  destWarehouseLabel: PropTypes.string,
  info: PropTypes.shape(),
  headerTitle: PropTypes.string,
  allocationNoIpt: PropTypes.string,
  allocationNoIptDisabled: PropTypes.bool,
  show3: PropTypes.bool,
  allotTypeList: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  allocationTypeLabel: PropTypes.string,
  palletCode: PropTypes.string,
  allocationType: PropTypes.string,
  isUserPallet: PropTypes.bool,
  deliverTruckNo: PropTypes.string,
  scanCarCodeFlag: PropTypes.bool,
  permissionSubWarehouseList: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  dischargeSubWarehouseIdName: PropTypes.string,
  dischargeSubWarehouseId: PropTypes.string,
  dischargeSubWarehouseIds: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  shippingTrackNo: PropTypes.string,
  isDisableSubWarehouseAndDeliverTruck: PropTypes.bool,
  transportSelectList: PropTypes.arrayOf(PropTypes.shape),
  scanTransportBillNo: PropTypes.bool,
  fileList: PropTypes.arrayOf(PropTypes.shape),
  subWarehouseId: PropTypes.number,
  isDisableTransportNo: PropTypes.bool,
  isDisableAllocationType: PropTypes.bool,
  showMoreTransportNo: PropTypes.bool,
};

export default i18n(Container);
