import { sendPostRequest } from '../../../lib/public-request';
// import noErrorFetch from '../../../lib/no-error-fetch';
// import { camel2Under, under2Camel } from '../../../lib/util';
// import { getLang, getLocation } from '../../js';

// 退货调拨出库-查询接口
export const searchApi = (param) => sendPostRequest({
  url: '/return_transfer/return_allot/search',
  param,
}, process.env.WTS_FRONT);

// 关箱
export const closeContainerApi = (param) => sendPostRequest({
  url: '/return_transfer/return_allot/close_container',
  param,
}, process.env.WTS_FRONT);

// 扫集装箱号
export const sacnContainerApi = (param) => sendPostRequest({
  url: '/return_transfer/return_allot/get_number',
  param,
}, process.env.WTS_FRONT);

// 扫描大箱号
export const scanBigContainerCodeApi = (param) => sendPostRequest({
  url: '/return_transfer/return_allot/scan_big_container_code',
  param,
}, process.env.WTS_FRONT);

// 获取仓库
export const getWarehouseApi = (param) => sendPostRequest({
  url: '/warehouse/get_list',
  param,
}, process.env.BASE_URI_WMD);

// 扫描调拨单号
export const scanAllocationNoApi = (param) => sendPostRequest({
  url: '/return_transfer/scan_allocation_no',
  param,
}, process.env.WTS_FRONT);

// // 字典明细select查询
// export const selectDictApi = (argObj) => {
//   const uri = `${process.env.BASE_URI_WMD}/dict/select`;
//   return noErrorFetch(uri, {
//     method: 'POST',
//     credentials: 'include',
//     headers: {
//       'Content-Type': 'application/json',
//       'Accept-Language': getLang(),
//       'Accept-Location': getLocation(),
//     },
//     body: JSON.stringify(camel2Under(argObj)),
//   })
//   // .then(data => data.json())
//     .then(under2Camel);
// };

// 字典明细select查询
export const selectDictApi = (param) => sendPostRequest({
  baseUrl: process.env.BASE_URI_WMD,
  url: '/dict/select',
  param,
});

// 扫描托盘号
export const scanPalletCodeApi = (param) => sendPostRequest({
  url: '/return_transfer/scan_pallet_code',
  param,
}, process.env.WTS_FRONT);
// 是否使用托盘
export const isUserPalletCodeApi = (param) => sendPostRequest({
  url: '/return_transfer/is_user_pallet_code',
  param,
}, process.env.WTS_FRONT);

// 是否使用托盘
export const checkScanCarCodeAPI = (param) => sendPostRequest({
  url: '/return_transfer/check_scan_car_code',
  param,
}, process.env.WTS_FRONT);

// 扫码车牌号
export const scanCarCodeAPI = (param) => sendPostRequest({
  url: '/return_transfer/scan_car_code',
  param,
}, process.env.WTS_FRONT);

// 根据选择的仓库查询有权限的子仓
export const getPermissionSubWarehouseListAPI = (param) => sendPostRequest({
  url: '/data_permission/sub_warehouse/list',
  param,
}, process.env.WAS_FRONT);

// 查询运单列表
export const queryTransport = (param) => sendPostRequest({
  url: '/return_transfer/return_allot/query_shipping_track_no',
  param,
}, process.env.WTS_FRONT);

// 查询更多运单列表
export const queryTransportExtraAPI = (param) => sendPostRequest({
  url: '/return_transfer/return_allot/query_shipping_track_no_extra',
  param,
}, process.env.WTS_FRONT);
