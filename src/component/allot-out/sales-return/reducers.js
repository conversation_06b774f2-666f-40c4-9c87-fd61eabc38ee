import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { markStatus } from 'rrc-loader-helper';
import { select } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import modal from '../../common/modal';
import {
  getWarehouseApi,
  searchApi,
  closeContainerApi,
  sacnContainerApi,
  scanBigContainerCodeApi,
  scanAllocationNoApi,
  selectDictApi,
  scanPalletCodeApi,
  isUserPalletCodeApi,
  checkScanCarCodeAPI,
  scanCarCodeAPI,
  getPermissionSubWarehouseListAPI,
  queryTransport,
  queryTransportExtraAPI,
} from './server';
import aaoo from '../../../source/audio/aaoo.mp3';
import { classFocus, getHeaderTitle } from '../../../lib/util';

const aaooEle = new Audio(aaoo);
aaooEle.load();// 啊哦提示音

const defaultState = {
  srcWarehouseId: '',
  warehouseCode: '',
  warehouseCodeValid: false,
  destWarehouseIdValid: true,
  containerCode: '',
  containerCodeValid: false,
  bigContainerCode: '',
  bigContainerCodeValid: false,
  scanNums: 0,
  number: 0,
  searchLoading: false,
  closeLoading: false,
  warehouseList: [],
  destWarehouseList: [],
  warehouseType: '', // 仓库类型
  allocationNo: '', // 调拨单号  在绑定大箱号时，后端会返回调拨单号，非保税仓的关箱和查询 则根据调拨单号进行操作
  dataLoading: 1,
  show1: false,
  show2: false,
  showDetail: false,
  person: '3',
  srcWarehouseLabel: '',
  destWarehouseLabel: '',
  info: {
    allocationNo: '',
    list: [],
    statusName: '',
  },
  headerTitle: '',
  allocationNoIpt: '', // 根据调拨单号查询
  allocationNoIptDisabled: false,
  allotTypeList: [], // 调拨单类型列表
  allocationType: '',
  allocationTypeLabel: '',
  show3: false,
  palletCode: '',
  isUserPallet: false,
  dischargeSubWarehouseId: '', // 卸货子仓
  dischargeSubWarehouseIdName: '', // 卸货子仓名称
  scanCarCodeFlag: false, // 是否扫描车车牌号
  shippingTrackNo: '', // 运输单号
  deliverTruckNo: '', // 车牌号
  permissionSubWarehouseList: [], // 根据选择的调入仓库获取下面的子仓
  dischargeSubWarehouseIds: [], // 卸货子仓下拉过滤
  subWarehouseId: '', // 出发子仓
  isDisableSubWarehouseAndDeliverTruck: false, // 是否禁用卸货子仓和车牌
  transportSelectList: [],
  scanTransportBillNo: false, // 是否扫运输单号
  fileList: [], // 上传的文件
  isDisableTransportNo: true, // 禁用运输单号
  isDisableAllocationType: true, // 禁用调拨类型
  showMoreTransportNo: false, // 是否显示更多运输单号按钮
};

export default {
  defaultState,
  $init: () => defaultState,
  * init(action, ctx, put) {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }
    yield ctx.changeData({
      data: {
        headerTitle: getHeaderTitle(),
        subWarehouseId: preSubMenu.subWarehouseId,
      },
    });
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  // 获取调拨类型列表
  * getAllotTypeList(action, ctx) {
    markStatus('dataLoading');
    const selectData = yield selectDictApi({ catCode: ['ALLOCATION_TYPE'] });
    if (selectData.code === '0') {
      yield ctx.changeData({
        data: {
          allotTypeList: selectData.info.data[0].dictListRsps.map((item) => ({
            label: item.dictNameZh,
            id: item.dictCode,
            value: item.dictCode,
          })) || [],
        },
      });
    } else {
      modal.error({ content: selectData.msg, className: 'bigContainerCode' });
    }
  },
  // 获取仓库列表
  * getWarehouse(action, ctx) {
    markStatus('dataLoading');
    const res = yield getWarehouseApi(action.param);
    if (res.code === '0') {
      const list = res.info.data || [];
      const warehouseList = list.map((item) => ({
        label: item.nameZh,
        id: item.id,
        value: item.id,
        type: item.type,
      }));
      yield ctx.changeData({
        data: {
          warehouseList,
        },
      });
      classFocus('bigContainerCode');
    } else {
      modal.error({ content: res.msg, className: 'bigContainerCode' });
    }
  },
  // 扫描大箱号
  * scanBigContainer(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanBigContainerCodeApi(action.param);
    if (res.code === '0') {
      const { number, allocationNo } = res.info;
      const { scanNums } = yield select((state) => state['allot-out/sales-return']);
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
          bigContainerCodeValid: false,
          number,
          scanNums: scanNums + 1,
          allocationNo,
          allocationNoIpt: allocationNo,
          allocationNoIptDisabled: true,
          isDisableSubWarehouseAndDeliverTruck: true,
          isDisableTransportNo: true,
          isDisableAllocationType: true,
        },
      });
      classFocus('bigContainerCode');
    } else {
      if (res.code === '471052') {
        aaooEle.play();
      }
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
          bigContainerCodeValid: false,
        },
      });
      modal.error({ content: res.msg, className: 'bigContainerCode' });
    }
  },
  // 扫描集装箱
  * scanContainer(action, ctx) {
    markStatus('dataLoading');
    const res = yield sacnContainerApi(action.param);
    if (res.code === '0') {
      const { number, allocationNo } = res.info;
      yield ctx.changeData({
        data: {
          // containerCode: '',
          containerCodeValid: false,
          number,
          allocationNo,
        },
      });
      classFocus('bigContainerCode');
    } else {
      modal.error({ content: res.msg, className: 'containerCode' });
    }
  },
  // 获取详情
  * search(action, ctx) {
    markStatus('dataLoading');
    const res = yield searchApi(action.param);
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          info: res.info,
          showDetail: true,
        },
      });
    } else {
      modal.error({
        content: res.msg, className: 'bigContainerCode',
      });
    }
  },
  // 关箱
  * close(action, ctx) {
    const result = yield closeContainerApi(action.param);
    if (result.code === '0') {
      yield ctx.changeData({
        data: {
          containerCode: '',
          containerCodeValid: false,
          bigContainerCode: '',
          bigContainerCodeValid: false,
          number: 0,
          allocationNo: '',
          destWarehouseId: '',
          allocationNoIpt: '',
          allocationNoIptDisabled: true,
          destWarehouseIdValid: false,
          palletCode: '',
          dischargeSubWarehouseId: '', // 卸货子仓
          dischargeSubWarehouseIdName: '', // 卸货子仓名称
          scanCarCodeFlag: false, // 是否扫描车车牌号
          shippingTrackNo: '', // 运输单号
          deliverTruckNo: '', // 车牌号
          permissionSubWarehouseList: [], // 根据选择的调入仓库获取下面的子仓
          dischargeSubWarehouseIds: [], // 卸货子仓下拉过滤
          isDisableSubWarehouseAndDeliverTruck: false, // 是否禁用卸货子仓和车牌
          scanTransportBillNo: false, // 是否扫运输单号
          isDisableTransportNo: true,
          fileList: [],
        },
      });
      // if (action.param.warehouseType !== 3) {
      //   classFocus('bigContainerCode');
      // } else {
      //   classFocus('containerCode');
      // }
      const {
        allocationType, srcWarehouseId, warehouseCode,
      } = yield '';
      if (allocationType === 0 && srcWarehouseId && warehouseCode) {
        yield ctx.checkScanCarCode();
      } else {
        classFocus('bigContainerCode');
      }
    } else {
      yield ctx.changeData({
        data: {
          containerCode: '',
          containerCodeValid: false,
          bigContainerCode: '',
          bigContainerCodeValid: false,
          number: 0,
        },
      });
      modal.error({
        content: result.msg,
        className: action.param.warehouseType !== 3 ? 'bigContainerCode' : 'containerCode',
      });
    }
  },
  // 扫描调拨单号
  * scanAllocationNo(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanAllocationNoApi(action.param);
    if (res.code === '0') {
      const {
        number, allocationNo, warehouseId, srcWarehouseId,
        containerCode, allocationType, needPallet, dischargeSubWarehouseId, shippingTrackNo,
        deliverTruckNo, flag, dischargeSubWarehouseName,
      } = res.info;
      const { warehouseList, allotTypeList } = yield select((state) => state['allot-out/sales-return']);
      yield ctx.changeData({
        data: {
          number,
          allocationNoIpt: allocationNo,
          allocationNo,
          srcWarehouseId,
          allocationType,
          warehouseCode: warehouseId,
          allocationNoIptDisabled: true,
          srcWarehouseLabel: warehouseList.find((wi) => (wi.id === srcWarehouseId))?.label,
          destWarehouseLabel: warehouseList.find((wi) => (wi.id === warehouseId))?.label,
          allocationTypeLabel: allotTypeList.find((wi) => (wi.id === allocationType))?.label,
          warehouseCodeValid: true,
          destWarehouseIdValid: true,
          dischargeSubWarehouseId, // 卸货子仓
          dischargeSubWarehouseIdName: dischargeSubWarehouseName, // 卸货子仓名称
          shippingTrackNo, // 运输单号
          deliverTruckNo, // 车牌
          scanTransportBillNo: !!shippingTrackNo,
          isDisableTransportNo: true,
          isDisableAllocationType: true,
        },
      });
      if (warehouseList.find((wi) => (wi.id === warehouseId)) &&
        warehouseList.find((wi) => (wi.id === warehouseId)).type === 3) {
        yield ctx.changeData({
          data: {
            containerCode,
            warehouseType: 3,
            containerCodeValid: true,
          },
        });
      }
      // 0-不展示车牌卸货子仓  1展示车牌卸货子仓
      if (flag === 1) {
        // 开启车牌卸货子仓输入框
        yield this.changeData({
          data: {
            scanCarCodeFlag: true,
          },
        });
        // 如果禁用车牌 卸货子仓 光标定位大箱号
        yield this.changeData({
          data: {
            isDisableSubWarehouseAndDeliverTruck: true,
          },
        });
      }
      // 如果配置要走扫托盘 则光标跳转到扫托盘
      if (needPallet) {
        classFocus('palletCode');
      } else {
        classFocus('bigContainerCode');
      }
    } else {
      yield ctx.changeData({
        data: {
          warehouseCodeValid: false,
          destWarehouseIdValid: false,
          allocationNoIpt: '',
          allocationNoIptDisabled: false,
        },
      });
      modal.error({ modalBlurInput: true, content: res.msg, className: 'allocationNoIpt' });
    }
  },
  // 扫描托盘号
  * scanPalletCode(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanPalletCodeApi(action.param);
    if (res.code === '0') {
      yield ctx.checkScanCarCode();
    } else {
      yield ctx.changeData({
        data: {
          palletCode: '',
        },
      });
      modal.error({ content: res.msg, className: 'palletCode' });
    }
  },
  // 是否使用托盘
  * isUserPalletCode(action, ctx) {
    markStatus('dataLoading');
    const res = yield isUserPalletCodeApi(action.param);
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          isUserPallet: res.info.flag,
        },
      });
      if (res.info.flag) {
        classFocus('palletCode');
      } else {
        classFocus('bigContainerCode'); // 先跳到大箱号 如果走到是否需要扫车牌 再决定光标定位
        yield ctx.checkScanCarCode();
      }
    } else {
      modal.error({
        content: res.msg,
        onOk: () => window.location.reload(),
      });
    }
  },
  // 检查是否需要扫描车牌
  * checkScanCarCode(action, ctx) {
    // 调拨类型 必须为退货调拨、出发仓、目的仓
    const {
      allocationType, srcWarehouseId, warehouseCode,
    } = yield '';
    if (allocationType === 0 && srcWarehouseId && warehouseCode) {
      markStatus('dataLoading');
      const res = yield checkScanCarCodeAPI({
        allocationType,
        srcWarehouseId,
        warehouseId: warehouseCode,
      });
      if (res.code === '0') {
        const scanCarCodeFlag = Number(res.info?.scanCarCodeFlag);
        yield ctx.changeData({
          data: {
            dischargeSubWarehouseId: res.info?.dischargeSubWarehouseId, // 卸货子仓
            dischargeSubWarehouseIdName: res.info?.dischargeSubWarehouseName, // 卸货子仓名称
            scanCarCodeFlag: scanCarCodeFlag === 1 || scanCarCodeFlag === 2, // 是否扫描车车牌号
            deliverTruckNo: res.info?.deliverTruckNo, // 车牌
            shippingTrackNo: res.info?.shippingTrackNo, // 运输单
            // eslint-disable-next-line max-len
            isDisableSubWarehouseAndDeliverTruck: res.info?.dischargeSubWarehouseId || res.info?.shippingTrackNo,
            scanTransportBillNo: scanCarCodeFlag === 3 || res.info?.shippingTrackNo, // 是否显示运输单号
            isDisableTransportNo: !(scanCarCodeFlag === 3 && !res.info?.shippingTrackNo),
          },
        });
        // 调出仓是否在 参数【仓间转运同步tms重量仓库】中  0-跳过（不需要扫车牌、卸货子仓）也直接跳扫大箱号 ， 1-有卸货子仓、车牌也直接跳大箱号  2-扫描车牌号
        if (scanCarCodeFlag === 2) {
          classFocus('deliverTruckNo');
        } else if (scanCarCodeFlag === 3) {
          yield this.getTransportNo();
          classFocus('shippingTrackNo');
        } else {
          classFocus('bigContainerCode');
        }
      } else {
        modal.error({ content: res.msg });
      }
    } else {
      yield ctx.changeData({
        data: {
          dischargeSubWarehouseId: '', // 卸货子仓
          dischargeSubWarehouseIdName: '', // 卸货子仓名称
          scanCarCodeFlag: false, // 是否扫描车车牌号
          deliverTruckNo: '', // 车牌号
          permissionSubWarehouseList: [], // 根据选择的调入仓库获取下面的子仓
          dischargeSubWarehouseIds: [], // 卸货子仓下拉过滤
          isDisableSubWarehouseAndDeliverTruck: false, // 是否禁用卸货子仓和车牌
          scanTransportBillNo: false, // 是否扫运输单号
          fileList: [],
          isDisableTransportNo: true,
        },
      });
    }
  },
  // 扫描车牌号
  * scanCarCode(action, ctx) {
    const {
      allocationType, srcWarehouseId, warehouseCode, deliverTruckNo, subWarehouseId,
    } = yield '';
    markStatus('dataLoading');
    const res = yield scanCarCodeAPI({
      allocationType, // 调拨类型
      srcWarehouseId, // 调出仓库
      warehouseId: warehouseCode, // 调入仓
      deliverTruckNo, // 车牌号
      subWarehouseId, // 出发子仓
    });
    if (res.code === '0') {
      // 根据选择的调入仓库获取下面的子仓
      if (warehouseCode) {
        yield ctx.getPermissionSubWarehouseList({
          warehouseCode,
        });
      }
      yield ctx.changeData({
        data: {
          dischargeSubWarehouseIds: (res?.info?.dischargeSubWarehouseIds || []).map((di) => ({
            ...di,
            value: di.subWarehouseId,
            name: di.subWarehouseName,
          })), // 卸货子仓ids
          shippingTrackNo: res.info?.shippingTrackNo, // 运输单号
          dischargeSubWarehouseId: '', // 清空已选择的卸货子仓
          dischargeSubWarehouseIdName: '', // 清空已选择的卸货子仓名称
        },
      });
      classFocus('subWarehouse');
    } else {
      modal.error({ content: res.msg, className: 'deliverTruckNo' });
      yield ctx.changeData({
        data: {
          deliverTruckNo: '', // 报错清空车牌
        },
      });
    }
  },
  // 根据调入仓库获取有权限的子仓
  * getPermissionSubWarehouseList(action, ctx) {
    markStatus('dataLoading');
    const res = yield getPermissionSubWarehouseListAPI({
      enabled: 1,
      warehouseId: action.warehouseCode,
    });
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          permissionSubWarehouseList: (res.info?.data || []).map((ri) => ({
            ...ri,
            value: ri.id,
            name: ri.nameZh,
          })), // 有权限的子仓
        },
      });
    } else {
      modal.error({ content: res.msg });
    }
  },

  * getTransportNo() {
    const {
      allocationType,
      srcWarehouseId,
      warehouseCode,
      subWarehouseId,
    } = yield '';
    markStatus('dataLoading');
    const res = yield queryTransport({
      allocationType,
      srcWarehouseId,
      destWarehouseId: warehouseCode,
      srcSubWarehouseId: subWarehouseId,
    });
    if (res.code === '0') {
      yield this.changeData({
        data: {
          // eslint-disable-next-line max-len
          transportSelectList: (res.info?.list || []).map(({ shippingTrackNo }) => ({ name: shippingTrackNo, value: shippingTrackNo })),
          showMoreTransportNo: true,
        },
      });
    } else {
      modal.error({ content: res.msg });
    }
  },

  // 获取更多运输单
  * getTransportNoExtra() {
    const {
      allocationType,
      srcWarehouseId,
      warehouseCode,
      subWarehouseId,
    } = yield '';
    markStatus('dataLoading');
    const res = yield queryTransportExtraAPI({
      allocationType,
      srcWarehouseId,
      destWarehouseId: warehouseCode,
      srcSubWarehouseId: subWarehouseId,
    });
    if (res.code === '0') {
      yield this.changeData({
        data: {
        // eslint-disable-next-line max-len
          transportSelectList: (res.info?.list || []).map(({ shippingTrackNo }) => ({ name: shippingTrackNo, value: shippingTrackNo })),
          showMoreTransportNo: false,
        },
      });
    } else {
      modal.error({ content: res.msg });
    }
  },
};
