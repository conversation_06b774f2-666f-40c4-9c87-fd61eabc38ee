import { sendPostRequest } from '../../../lib/public-request';
/**
 * 获取到达仓列表
 * @param param
 * @returns {*}
 */
export const getArrivedWarehouse = (param) => sendPostRequest({
  url: '/pda_temporary/query_receive_warehouse',
  param,
});
/**
 * 获取仓库绑定关系
 * @param param
 * @returns {*}
 */
export const getWarehouse = (param) => sendPostRequest({
  url: '/warehouse/query_user_manage_bind',
  param,
}, process.env.WAS_FRONT);

/**
 * 退货调拨收货
 * 校验调拨单号/集装箱号
 * @param param
 * @returns {*}
 */
export const checkReceiving = (param) => sendPostRequest({
  url: '/pda_receiving/check_receiving',
  param,
});

/**
 * 退货调拨收货
 * @param param
 * @returns {*}
 */
export const receiving = (param) => sendPostRequest({
  url: '/pda_receiving/receiving',
  param,
}, process.env.WTS_FRONT);

/**
 * 获取绑定子仓列表
 */
export const getUserBindSubWarehouseAPI = (param) => sendPostRequest({
  url: '/sub_warehouse_user/get_user_bind_sub_warehouse',
  param,
}, process.env.WMS_INTERNAL_FRONT);
