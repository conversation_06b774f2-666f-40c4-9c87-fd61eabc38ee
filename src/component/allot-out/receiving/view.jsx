import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  FormCell, CellBody, CellFooter, Switch,
} from 'react-weui/build/packages';
import store from './reducers';
import Header from '../../common/header';
import Footer from '../../common/footer';
import FocusInput from '../../common/focus-input';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import RowInfo from '../../common/row-info';
import {
  Table,
  Tag,
} from '../../common';

class Container extends Component {
  componentDidMount() {
    // store.init();
    store.changeData({ data: { headerTitle: getHeaderTitle() } });

    store.getSubwarhouseData();

    classFocus('bigContainerCode');
    this.listHeight = window.innerHeight - 52.8 * 2 - 40 - 44 - 56 - 5;
    this.onresize = () => requestAnimationFrame(() => this.forceUpdate());
    window.onresize = this.onresize;
  }

  componentDidUpdate() {
    this.updateListHeight();
  }

  updateListHeight() {
    const h = window.innerHeight - 52.8 * 2 - 40 - 44 - 56 - 5;
    if (h && h !== this.listHeight) {
      this.listHeight = h;
      this.forceUpdate();
    }
  }

  render() {
    const {
      disabled,
      bigContainerCode,
      headerTitle,
      billMark,
      list,
      splitFlagDisabled,
      splitFlag,
      isShowFlag,
      shelvesParkName,
    } = this.props;

    const columns = [
      {
        title: t('序号'),
        dataIndex: 'index',
        width: 18,
      },
      {
        title: t('大箱号'),
        dataIndex: 'bigContainerCodes',
        width: 50,
      },
      {
        title: t('调拨单号'),
        dataIndex: 'allocationNo',
        width: 40,
      },
    ];

    return (
      <div>
        <Header title={headerTitle || t('调拨收货')} />
        {
          isShowFlag && (
            <Form>
              <FormCell switch>
                <CellBody>{t('分箱')}</CellBody>
                <CellFooter>
                  <Switch
                    disabled={splitFlagDisabled}
                    checked={splitFlag}
                    onChange={() => {
                      store.changeData({ data: { splitFlag: !splitFlag } });
                    }}
                  />
                </CellFooter>
              </FormCell>
            </Form>
          )
        }
        <Form>
          <FocusInput
            keepFocus={false}
            placeholder={t('请扫描')}
            className="bigContainerCode"
            data-bind="bigContainerCode"
            disabled={disabled === 0}
            onPressEnter={(e) => {
              if (e.target.value) {
                store.receivingNew({
                  params: {
                    bigContainerCode,
                    splitFlag,
                  },
                  list,
                });
              }
            }}
            onBlur={() => {
            }}
          >
            <label>{t('托盘号/大箱号')}</label>
          </FocusInput>
        </Form>
        {
          shelvesParkName && (
            <div style={{ margin: '10px' }}>
              <span>{t('推荐园区')}：</span>
              <span>{shelvesParkName}</span>
            </div>
          )
        }
        {
          list.length ? (
            <Form style={{ marginTop: '5px' }}>
              <RowInfo
                label={t('已收箱号')}
                extraStyle={{
                  borderBottom: 'none',
                }}
              />
              <div style={{ height: '100%' }}>
                <Table
                  columns={columns}
                  dataSource={list.map((ele, i) => ({
                    ...ele,
                    index: list.length - i,
                    bigContainerCodes: ele.bigContainerCodeList && ele.bigContainerCodeList.length
                      ? ele.bigContainerCodeList.map((code) => (
                        <div>
                          {code.bigContainerCode}
                          {!!ele.tags && <Tag type="red">{ele.tags}</Tag>}
                          {!ele.tags && !!code.qcType && (<Tag type="red">{code.qcType === 1 ? t('A类质检') : t('B类质检')}</Tag>)}
                          {!!ele.isSplit && <Tag type="red">{ele.isSplit}</Tag>}
                        </div>
                      ))
                      : '',
                  }))}
                  maxHeight={200}
                />
              </div>
            </Form>
          ) : ''
        }

        {
          billMark && (
            <div style={{ textAlign: 'right', margin: '10px 10px 0 0' }}>
              <span>{t('单据标识')}：</span>
              <span style={{ color: 'red', fontSize: 18, fontWeight: 'bold' }}>{billMark}</span>
            </div>
          )
        }
        <Footer />
      </div>
    );
  }
}

Container.propTypes = {
  disabled: PropTypes.number,
  bigContainerCode: PropTypes.string,
  headerTitle: PropTypes.string,
  billMark: PropTypes.string,
  list: PropTypes.arrayOf(PropTypes.shape()),
  splitFlagDisabled: PropTypes.bool,
  splitFlag: PropTypes.bool,
  isShowFlag: PropTypes.bool,
  shelvesParkName: PropTypes.string,
};

export default i18n(Container);
