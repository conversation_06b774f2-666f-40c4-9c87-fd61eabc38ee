import React from 'react';
import assign from 'object-assign';
import { push } from 'react-router-redux';
import { put } from 'redux-saga/effects';
import { t } from '@shein-bbl/react';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle, getParentHref } from '../../../lib/util';
import * as servers from './server';
import ok from '../../../source/audio/ok.mp3';
import { getConfigByCodeApi } from '../../../server/basic/common';

const okAudio = new Audio(ok);
okAudio.load();

const defaultState = {
  bigContainerCode: '',
  allocationNo: '',
  containerCode: '',
  warehouseType: '',
  warehouseDisabled: 1,
  allocationNoDisabled: 1,
  containerDisabled: 1,
  disabled: 1,
  showSheet: false,
  headerTitle: '',
  bigContainerList: [],
  showBigContainerList: true,
  billMark: '',
  list: [],
  splitFlagDisabled: true, // switch 分箱 置灰
  splitFlag: false, // switch 分箱
  isShowFlag: false, // 是否展示按钮
  subWarehouseId: '', // 子仓
  shelvesParkName: '', // 推荐园区
};

export default {
  defaultState,
  $init: () => {
    classFocus('warehouse');
    return defaultState;
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    const data = yield servers.getWarehouse({});
    const res = yield servers.getArrivedWarehouse({});
    if (data.code !== '0') {
      Modal.error({
        content: data.msg,
      });
    } else if (res.code !== '0') {
      Modal.error({
        content: data.msg,
      });
    } else {
      if (data.info.warehouseList.length === 0) {
        const p1 = yield new Promise((r) => Modal.error({
          content: t('当前用户没有绑定仓库'),
          onOk: () => r(1),
        }));
        if (p1 === 1) {
          yield put(push(getParentHref()));
        }
        return p1;
      } else if (data.info.warehouseList.length > 1) {
        const p2 = yield new Promise((r) => Modal.error({
          content: t('收货用户只能绑定一个仓库，请联系管理员重新绑定'),
          onOk: () => r(1),
        }));
        if (p2 === 1) {
          yield put(push(getParentHref()));
        }
        return p2;
      }
      const warehouseId = data.info.warehouseList[0];
      const types = res.info.warehouseList.find((i) => i.warehouseId === +warehouseId);
      yield ctx.changeData({
        data: {
          warehouseType: types && types.type,
        },
      });
      classFocus('bigContainerCode');
    }
    return null;
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * errorClear(action, ctx) {
    yield ctx.changeData({
      data: {
        [action.data]: '',
        [action.disabled]: 1,
      },
    });
    Modal.error({
      content: action.msg,
      onOk: () => {
        classFocus(action.data);
      },
    });
  },
  * checkReceiving(action, ctx) {
    const bool = action.type === 'allocationNo';
    yield ctx.changeData({
      data: {
        allocationNoDisabled: 0,
        containerDisabled: 0,
      },
    });
    const res = yield servers.checkReceiving(action.params);
    if (res.code !== '0') {
      yield ctx.changeData({
        data: {
          [bool ? 'allocationNo' : 'containerCode']: '',
          allocationNoDisabled: 1,
          containerDisabled: 1,
        },
      });
      Modal.error({
        content: res.msg,
        onOk: () => {
          classFocus(bool ? 'allocationNo' : 'containerCode');
        },
      });
    } else {
      yield ctx.changeData({
        data: {
          disabled: 1,
          bigContainerList: res.info.bigContainerCodes || [],
        },
      });
      classFocus('bigContainerCode');
    }
  },
  * receiving(action, ctx) {
    yield ctx.changeData({
      data: {
        disabled: 0,
      },
    });
    const res = yield servers.receiving(action.params);
    if (res.code !== '0') {
      yield ctx.errorClear({
        data: 'bigContainerCode',
        disabled: 'disabled',
        msg: res.msg,
      });
    } else {
      okAudio.play();
      yield ctx.changeData({
        data: {
          bigContainerList: res.info.bigContainerCodes || [],
          billMark: res.info.billMark,
          shelvesParkName: res.info.shelvesParkName,
        },
      });
      const bool = res.info.status === 2 || res.info.status === 3;
      if (bool) {
        Modal.success({
          content: (
            <div>
              <div>
                {res.info.status === 2 ? t('此提单号已收货完成') : t('此调拨单已收货完成')}
              </div>
              {
                res.info.billMark && (
                  <div style={{ color: 'red', fontSize: 20, fontWeight: 'bold' }}>{res.info.billMark}</div>
                )
              }
            </div>
          ),
          onOk: () => window.location.reload(),
        });
      } else {
        yield ctx.changeData({
          data: {
            bigContainerCode: '',
            disabled: 1,
          },
        });
        classFocus('bigContainerCode');
      }
    }
  },
  * receivingNew(action, ctx) {
    const { subWarehouseId } = yield '';
    const res = yield servers.receiving({
      ...action.params,
      subWarehouseId,
    });
    if (res.code === '0') {
      okAudio.play();
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
          list: [{
            bigContainerCodes: res.info.bigContainerCodes,
            bigContainerCodeList: res.info.bigContainerCodeList || [],
            allocationNo: res.info.allocationNo,
            tags: res.info.isQualityCheck ? t('免质检') : '',
            isSplit: res.info.splitMark ? t('分箱') : '',
          }, ...action.list],
          billMark: res.info.billMark,
          shelvesParkName: res.info.shelvesParkName,
        },
      });
    } else if (res.code === '471084') {
      const confirmStatus = yield new Promise((r) => Modal.confirm({
        content: t('推荐园区与当前用户绑定园区不符，是否继续收货?'),
        onOk: () => r('ok'),
        onCancel: () => r('cancel'),
      }));
      if (confirmStatus === 'ok') {
        yield ctx.receivingNew({
          params: { ...action.params, secondConfirmation: 1 },
          list: action.list || [],
        });
      }
      if (confirmStatus === 'cancel') {
        yield ctx.changeData({
          data: {
            bigContainerCode: '',
          },
        });
        classFocus('bigContainerCode');
      }
    } else {
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
        },
      });
      Modal.error({
        content: res.msg,
        onOk: () => {
          classFocus('bigContainerCode');
        },
      });
    }
  },
  // 获取数据字典下拉数据
  * getSubwarhouseData(action, ctx) {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }
    yield ctx.changeData({
      data: {
        subWarehouseId: preSubMenu.subWarehouseId,
      },
    });

    const [selectDictData, warehouseData, getUserBindSubWarehouseData] = yield Promise.all([
      getConfigByCodeApi({ param: 'DISTRIBUTION_BOX_SUBWAREHOUSE' }),
      getConfigByCodeApi({ param: 'DISTRIBUTION_BOX_WAREHOUSE' }),
      servers.getUserBindSubWarehouseAPI({}),
    ]);

    const { warehouseId } = JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}');
    if (selectDictData.code === '0' && getUserBindSubWarehouseData.code === '0' && warehouseData.code === '0') {
      const subWarehouseList = selectDictData?.info?.configValue?.split(',').map((x) => Number(x)) || [];
      const warehouseList = warehouseData?.info?.configValue?.split(',').map((x) => Number(x)) || [];
      const bindSubWarehouseList = getUserBindSubWarehouseData?.info.map((x) => Number(x)) || [];
      // 当已配置且能匹配到当前仓库时才展示分箱按钮
      const isShowFlag = warehouseList.length !== 0 &&
        warehouseList.some((id) => id === Number(warehouseId));
      // eslint-disable-next-line max-len
      if (!bindSubWarehouseList.length || bindSubWarehouseList.find((x) => subWarehouseList.includes(Number(x)))) {
        yield ctx.changeData({
          data: {
            splitFlagDisabled: true,
            splitFlag: false,
            isShowFlag,
          },
        });
      } else {
        yield ctx.changeData({
          data: {
            splitFlagDisabled: false,
            splitFlag: true,
            isShowFlag,
          },
        });
      }
    } else {
      Modal.error({ content: selectDictData.msg || getUserBindSubWarehouseData.msg });
    }
  },
};
