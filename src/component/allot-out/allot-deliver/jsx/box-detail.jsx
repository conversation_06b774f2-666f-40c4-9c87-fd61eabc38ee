import { t } from '@shein-bbl/react';
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Tab, NavBar, NavBarItem } from 'react-weui/build/packages';
import {
  View, Footer, Header,
} from 'common';
import store from '../reducers';
import styles from '../style.less';

const tabList = [
  { key: 0, title: t('待扫描') },
  { key: 1, title: t('已扫描') },
];

function BoxDetail(props) {
  const {
    waitContainerCodeList,
    shipContainerCodeList,
  } = props;

  const [activeKey, setActiveKey] = useState(0);

  return (
    <div>
      <Header title={t('调拨扫描明细')} />
      <View diff={100} flex={false}>
        <Tab style={{ display: 'flex', flexDirection: 'column' }}>
          <NavBar>
            {tabList.map((item) => (
              <NavBarItem
                key={item.key}
                active={item.key === activeKey}
                onClick={() => setActiveKey(item.key)}
              >
                {`${item.title}(${item.key === 0 ? waitContainerCodeList.length : shipContainerCodeList.length})`}
              </NavBarItem>
            ))}
          </NavBar>
          <div className={styles.listBox}>
            {(activeKey === 0 ? waitContainerCodeList : shipContainerCodeList)?.map((_listItem) => (
              <div className={styles.listItem} key={_listItem}>
                <div className={styles.boxNo}>{_listItem}</div>
              </div>
            ))}
          </div>
        </Tab>
        <Footer
          beforeBack={() => store.changeData({ currentPage: 1 })}
        />
      </View>
    </div>
  );
}
BoxDetail.propTypes = {
  waitContainerCodeList: PropTypes.arrayOf(PropTypes.shape()),
  shipContainerCodeList: PropTypes.arrayOf(PropTypes.shape()),
};

export default BoxDetail;
