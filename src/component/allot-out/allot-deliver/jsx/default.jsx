import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, Footer, View,
} from 'common';
import store from '../reducers';
import style from '../style.less';
import FooterBtn from '../../../common/footer-btn';

class Info extends Component {
  render() {
    const {
      headerTitle,
      dataLoading,
      bigBoxNo,
      scanBigBoxNoDetail,
      shipNum,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle || t('调拨发货')} />
        <View
          diff={100}
          flex={false}
        >
          <Form>
            <FocusInput
              value={bigBoxNo}
              className="bigBoxNo"
              allowClear
              placeholder={t('请扫描')}
              disabled={!dataLoading}
              onChange={(e) => {
                const { value } = e.target;
                if (!value.trim()) {
                  store.changeData({
                    bigBoxNo: '',
                  });
                } else {
                  store.changeData({
                    bigBoxNo: value.trim(),
                  });
                }
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanBigBoxNo();
              }}
            >
              <label>{t('大箱号')}</label>
            </FocusInput>
          </Form>
          {scanBigBoxNoDetail && Object.keys(scanBigBoxNoDetail).length > 0 && (
            <>
              <div className={style.scanDetailInfo}>
                <div className={style.vehicleNoDisplay}>
                  <div>{scanBigBoxNoDetail.deliverTruckNo}</div>
                </div>
                <div className={style.scanDetailInfoItem}>
                  <div>{scanBigBoxNoDetail.srcWarehouseName}</div>
                  <div>{scanBigBoxNoDetail.warehouseCodeName}</div>
                </div>
              </div>
              <div className={style.scanDetailInfo}>
                <hr />
              </div>
              <div className={style.scanDetailInfo}>
                <div className={style.scanDetailInfoItem}>
                  <b>{t('运输单号:')}</b>
                  <div>{scanBigBoxNoDetail.shippingTrackNo}</div>
                </div>
                <div className={style.scanDetailInfoItem}>
                  <b>{t('已装车:')}</b>
                  {/* eslint-disable-next-line max-len */}
                  {/* eslint-disable-next-line react/jsx-no-script-url,no-script-url,jsx-a11y/anchor-is-valid */}
                  <a href="javascript:;" onClick={() => store.jumpDetail()}>{scanBigBoxNoDetail.boxNum}</a>
                </div>
                <div className={style.scanDetailInfoItem}>
                  <b>{t('已发货扫描:')}</b>
                  <span style={{ color: '#1890ff', cursor: 'pointer' }} onClick={() => store.changeData({ currentPage: 3 })}>{shipNum}</span>
                </div>
              </div>
            </>
          )}
        </View>
        <Footer>
          <FooterBtn
            disabled={
            !dataLoading ||
            !(scanBigBoxNoDetail && Object.keys(scanBigBoxNoDetail).length > 0)
          }
            onClick={() => {
              store.loadingCloseContainer();
            }}
          >
            {t('确认发货')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Info.propTypes = {
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  bigBoxNo: PropTypes.string,
  scanBigBoxNoDetail: PropTypes.shape(),
  shipNum: PropTypes.number,
};

export default i18n(Info);
