import { t } from '@shein-bbl/react';
import { getHeaderTitle, classFocus } from 'lib/util';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { modal, message } from 'common';
import {
  scanBigBoxNoAPI,
  jumpDetailAPI,
  loadingCloseContainerAPI,
} from './server';
import Modal from '../../common/modal';

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  currentPage: 1, // 1 初始操作页面，2 明细页面detail, 3-调拨扫描明细
  vehicleNo: '', // 车牌号
  bigBoxNo: '', // 大箱号
  detailList: [], // 明细列表
  scanBigBoxNoDetail: {}, // 扫描车牌的返回
  shipNum: 0, // 已发货扫描数量
  waitContainerCodeList: [], // 未扫描箱明细
  shipContainerCodeList: [], // 已扫描箱明细
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(state, data) {
    Object.assign(state, data);
  },
  * init() {
    markStatus('dataLoading');
    yield this.changeData({
      headerTitle: getHeaderTitle() || t('调拨发货'),
    });
    classFocus('bigBoxNo'); // 默认聚焦
  },
  // 重置数据
  * clearData() {
    const { headerTitle } = yield '';
    yield this.changeData({
      ...defaultState,
      headerTitle,
    });
  },
  // 扫描周转箱
  * scanBigBoxNo() {
    markStatus('dataLoading');
    const {
      bigBoxNo,
    } = yield '';
    const param = {
      warehouseId: JSON.parse(localStorage.getItem('warehouse') || '{}').warehouseId,
      bigContainerCode: bigBoxNo,
    };
    const res = yield scanBigBoxNoAPI(param);
    if (res.code === '0') {
      yield this.changeData({
        bigBoxNo: '',
        scanBigBoxNoDetail: res.info || {},
        shipNum: res.info?.shipNum || 0,
        waitContainerCodeList: res.info?.waitContainerCodeList || [],
        shipContainerCodeList: res.info?.shipContainerCodeList || [],
      });
      classFocus('bigBoxNo'); // 继续聚焦扫箱号
    } else {
      yield this.changeData({ bigBoxNo: '' });
      modal.error({ content: res.msg, className: 'bigBoxNo' });
    }
  },
  // 装箱明细
  * jumpDetail() {
    markStatus('dataLoading');
    const {
      scanBigBoxNoDetail,
    } = yield '';
    const param = {
      shippingTrackNo: scanBigBoxNoDetail.shippingTrackNo,
    };
    const res = yield jumpDetailAPI(param);
    if (res.code === '0') {
      if (res.info.bigContainerCodeList?.length) {
        yield this.changeData({
          currentPage: 2,
          detailList: (res.info?.bigContainerCodeList || []).map((bi) => ({
            bigContainerCode: bi,
          })),
        });
      } else {
        modal.error({ content: t('当前暂无明细!') });
      }
    } else {
      modal.error({ content: res.msg });
    }
  },
  // 确认发货
  * loadingCloseContainer() {
    const {
      scanBigBoxNoDetail,
    } = yield '';
    const preConfirmRes = yield new Promise((r) => (
      Modal.confirm({
        content: t('大箱已全部装车完毕，我确认进行发货'),
        onOk: () => r('ok'),
        onCancel: () => r('cancel'),
      })
    ));
    if (preConfirmRes === 'cancel') {
      yield this.changeData({
        bigBoxNo: '',
      });
      classFocus('bigBoxNo'); // 聚焦扫箱号
      return;
    }
    markStatus('dataLoading');
    const param = {
      allocationId: scanBigBoxNoDetail.allocationId,
    };
    const res = yield loadingCloseContainerAPI(param);
    if (res.code === '0') {
      message.success(t('操作成功'));
      yield this.init();
    } else {
      yield this.changeData({ bigBoxNo: '' });
      modal.error({ content: res.msg, className: 'bigBoxNo' });
    }
  },
};
