import { sendPostRequest } from 'lib/public-request';

/**
 * 扫描大箱号
 * @param param
 * @returns {*}
 */
export const scanBigBoxNoAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/ship_scan_big_container_code',
  param,
}, process.env.WTS_FRONT);

/**
 * 获取明细列表
 * @param param
 * @returns {*}
 */
export const jumpDetailAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/return_allot/search',
  param,
}, process.env.WTS_FRONT);

/**
 * 确认发货
 * @param param
 * @returns {*}
 */
export const loadingCloseContainerAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/loading_close_container',
  param,
}, process.env.WTS_FRONT);
