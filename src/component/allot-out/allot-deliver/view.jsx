import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import DefaultPage from './jsx/default';
import DetailPage from './jsx/detail';
import BoxDetail from './jsx/box-detail';
import store from './reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const { currentPage } = this.props;

    switch (currentPage) {
      case 2:
        return (<DetailPage {...this.props} />);
      case 3:
        return (<BoxDetail {...this.props} />);
      default:
        return (<DefaultPage {...this.props} />);
    }
  }
}

Container.propTypes = {
  currentPage: PropTypes.number,
};

export default i18n(Container);
