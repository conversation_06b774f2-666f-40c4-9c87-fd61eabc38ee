import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import {
  Header, Footer, View, Table,
} from 'common';
import store from '../reducers';

class Info extends Component {
  render() {
    const {
      headerTitle,
      boxCode,
      sowingNum,
      sowingUser,
      detailList,
    } = this.props;

    const columns = [
      {
        title: '',
        dataIndex: 'containerCode',
        width: 50,
      },
    ];

    return (
      <div>
        <Header title={headerTitle || t('统计')} />
        <View
          diff={100}
          flex={false}
        >
          <Table
            columns={columns}
            dataSource={detailList}
          />
        </View>
        <Footer beforeBack={() => store.changeData({ currentPage: 2 })} />
      </div>
    );
  }
}

Info.propTypes = {
  headerTitle: PropTypes.string,
  boxCode: PropTypes.string,
  sowingNum: PropTypes.number,
  sowingUser: PropTypes.string,
  detailList: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Info);
