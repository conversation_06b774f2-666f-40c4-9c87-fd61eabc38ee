import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import {
  Header, Footer, View, Table,
} from 'common';
import store from '../reducers';

class Detail extends Component {
  render() {
    const {
      headerTitle,
      waitReceiveBoxDetails,
    } = this.props;

    const columns = [
      {
        title: '',
        dataIndex: 'bigContainerCode',
        width: 50,
      },
    ];

    return (
      <div>
        <Header title={headerTitle || t('调拨箱号')} />
        <View
          diff={100}
          flex={false}
        >
          <Table
            columns={columns}
            dataSource={waitReceiveBoxDetails}
          />
        </View>
        <Footer beforeBack={() => store.changeData({ data: { currentPage: 1 } })} />
      </div>
    );
  }
}

Detail.propTypes = {
  headerTitle: PropTypes.string,
  waitReceiveBoxDetails: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Detail);
