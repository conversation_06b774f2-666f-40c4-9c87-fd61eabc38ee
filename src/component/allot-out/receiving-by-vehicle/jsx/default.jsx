import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  FormCell, CellBody, CellFooter, Switch,
} from 'react-weui/build/packages';
import FooterBtn from '../../../common/footer-btn';
import store from '../reducers';
import Footer from '../../../common/footer';
import FocusInput from '../../../common/focus-input';
import { classFocus, getHeaderTitle } from '../../../../lib/util';
import RowInfo from '../../../common/row-info';
import {
  Table,
  Tag,
  modal,
} from '../../../common';

class Container extends Component {
  componentDidMount() {
    // store.init();
    store.changeData({ data: { headerTitle: getHeaderTitle() } });

    store.getSubwarhouseData();

    classFocus('deliverTruckNo');
    this.listHeight = window.innerHeight - 52.8 * 2 - 40 - 44 - 56 - 5;
    this.onresize = () => requestAnimationFrame(() => this.forceUpdate());
    window.onresize = this.onresize;
  }

  componentDidUpdate() {
    this.updateListHeight();
  }

  updateListHeight() {
    const h = window.innerHeight - 52.8 * 2 - 40 - 44 - 56 - 5;
    if (h && h !== this.listHeight) {
      this.listHeight = h;
      this.forceUpdate();
    }
  }

  render() {
    const {
      disabled,
      bigContainerCode,
      billMark,
      list,
      splitFlagDisabled,
      splitFlag,
      isShowFlag,
      deliverTruckNoDisabled,
      totalBoxNum,
      waitReceiveNum,
      deliverTruckNo,
      allocationNo,
      dataLoading,
    } = this.props;
    const columns = [
      {
        title: t('序号'),
        dataIndex: 'index',
        width: 18,
      },
      {
        title: t('大箱号'),
        dataIndex: 'bigContainerCodes',
        width: 50,
      },
    ];
    return (
      <div>
        {
          isShowFlag && (
            <Form>
              <FormCell switch>
                <CellBody>{t('分箱')}</CellBody>
                <CellFooter>
                  <Switch
                    disabled={splitFlagDisabled}
                    checked={splitFlag}
                    onChange={() => {
                      store.changeData({ data: { splitFlag: !splitFlag } });
                    }}
                  />
                </CellFooter>
              </FormCell>
            </Form>
          )
        }
        <Form>
          <FocusInput
            keepFocus={false}
            placeholder={t('请扫描')}
            className="deliverTruckNo"
            data-bind="deliverTruckNo"
            disabled={deliverTruckNoDisabled === 0 || dataLoading === 0}
            onPressEnter={(e) => {
              if (e.target.value) {
                store.scanDeliverTruckNo({
                  params: {
                    deliverTruckNo: e.target.value,
                  },
                });
              }
            }}
            onBlur={() => {
            }}
          >
            <label>{t('车牌号')}</label>
          </FocusInput>
        </Form>
        <Form>
          <FocusInput
            keepFocus={false}
            placeholder={t('请扫描')}
            className="bigContainerCode"
            data-bind="bigContainerCode"
            disabled={disabled === 0 || dataLoading === 0}
            onPressEnter={(e) => {
              if (e.target.value) {
                store.carReceive({
                  params: {
                    bigContainerCode,
                    splitFlag,
                    deliverTruckNo,
                  },
                  list,
                });
              }
            }}
            onBlur={() => {
            }}
          >
            <label>{t('托盘号/大箱号')}</label>
          </FocusInput>
        </Form>
        {totalBoxNum > 0 && (
          <div style={{ margin: '10px' }}>
            <span>{t('待收箱数/总箱数')}：</span>
            <span>
              {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
              <a
                style={{ color: '#0059ce' }}
                onClick={() => {
                  store.deliverTruckNoWaitNum();
                }}
              >
                {t('{}箱/{}箱', waitReceiveNum, totalBoxNum)}
              </a>
            </span>
          </div>
        )}
        {
          list.length ? (
            <Form style={{ marginTop: '5px' }}>
              <RowInfo
                label={t('已收箱号')}
                extraStyle={{
                  borderBottom: 'none',
                }}
              />
              <div style={{ height: '100%' }}>
                <Table
                  columns={columns}
                  dataSource={list.map((ele, i) => ({
                    ...ele,
                    index: list.length - i,
                    bigContainerCodes: ele.bigContainerCodeList && ele.bigContainerCodeList.length
                      ? ele.bigContainerCodeList.map((code) => (
                        <div>
                          {code.bigContainerCode}
                          {!!ele.tags && <Tag type="red">{ele.tags}</Tag>}
                          {!ele.tags && !!code.qcType && (<Tag type="red">{code.qcType === 1 ? t('A类质检') : t('B类质检')}</Tag>)}
                          {!!ele.isSplit && <Tag type="red">{ele.isSplit}</Tag>}
                        </div>
                      ))
                      : '',
                  }))}
                  maxHeight={200}
                />
              </div>
            </Form>
          ) : ''
        }

        {
          billMark && (
            <div style={{ textAlign: 'right', margin: '10px 10px 0 0' }}>
              <span>{t('单据标识')}：</span>
              <span style={{ color: 'red', fontSize: 18, fontWeight: 'bold' }}>{billMark}</span>
            </div>
          )
        }
        <Footer
          disabled={dataLoading === 0}
          beforeBack={(back) => {
            // 若调拨单有值 且 待收箱大于0时 提醒用户 是否收货完成
            if (allocationNo && waitReceiveNum > 0) {
              modal.confirm({
                content: t('是否收货完成?'),
                onOk: () => {
                  store.receiveComplete({
                    params: {
                      deliverTruckNo,
                    },
                  });
                },
                onCancel: () => {
                  back();
                  store.reset();
                },
              });
            } else {
              back();
              store.reset();
            }
          }}
        >
          <FooterBtn
            disabled={dataLoading === 0}
            onClick={() => {
              store.receiveComplete({
                params: {
                  deliverTruckNo,
                },
              });
            }}
          >
            {t('收货完成')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  disabled: PropTypes.number,
  bigContainerCode: PropTypes.string,
  billMark: PropTypes.string,
  list: PropTypes.arrayOf(PropTypes.shape()),
  splitFlagDisabled: PropTypes.bool,
  splitFlag: PropTypes.bool,
  isShowFlag: PropTypes.bool,
  deliverTruckNoDisabled: PropTypes.bool,
  totalBoxNum: PropTypes.number,
  waitReceiveNum: PropTypes.number,
  deliverTruckNo: PropTypes.string,
  allocationNo: PropTypes.string,
  dataLoading: PropTypes.number,
};

export default i18n(Container);
