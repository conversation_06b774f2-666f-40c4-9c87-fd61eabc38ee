import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import Header from '../../common/header';
import Default from './jsx/default';
import Detail from './jsx/detail';
import Statistic from './jsx/statistic';

class Container extends Component {
  render() {
    const {
      headerTitle,
      currentPage,
    } = this.props;

    switch (currentPage) {
      case 2:
        return (
          <Detail {...this.props} />
        );
      case 3:
        return (
          <Statistic {...this.props} />
        );
      default:
        return (
          <div>
            <Header title={headerTitle || t('调拨收货（按车）')} />
            <Default {...this.props} />
          </div>
        );
    }
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  currentPage: PropTypes.number,
};

export default i18n(Container);
