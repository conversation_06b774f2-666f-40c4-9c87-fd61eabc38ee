import { sendPostRequest } from '../../../lib/public-request';
/**
 * 获取到达仓列表
 * @param param
 * @returns {*}
 */
export const getArrivedWarehouse = (param) => sendPostRequest({
  url: '/pda_temporary/query_receive_warehouse',
  param,
});
/**
 * 获取仓库绑定关系
 * @param param
 * @returns {*}
 */
export const getWarehouse = (param) => sendPostRequest({
  url: '/warehouse/query_user_manage_bind',
  param,
}, process.env.WAS_FRONT);

/**
 * 获取绑定子仓列表
 */
export const getUserBindSubWarehouseAPI = (param) => sendPostRequest({
  url: '/sub_warehouse_user/get_user_bind_sub_warehouse',
  param,
}, process.env.WMS_INTERNAL_FRONT);

/**
 * PDA-调拨收货-按车-扫描车牌号
 */
export const scanDeliverTruckNoAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/car_receive/scan_deliver_truck_no',
  param,
}, process.env.WTS_FRONT);

/**
 * 扫描车牌号-待收货箱数
 */
export const deliverTruckNoWaitNumAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/car_receive/deliver_truck_no_wait_num',
  param,
}, process.env.WTS_FRONT);

/**
 * 按车-收货
 */
export const carReceiveAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/car_receive/receive',
  param,
}, process.env.WTS_FRONT);

/**
 * 收货完成
 */
export const receiveCompleteAPI = (param) => sendPostRequest({
  url: '/return_transfer_loading/car_receive/receive_complete',
  param,
}, process.env.WTS_FRONT);
