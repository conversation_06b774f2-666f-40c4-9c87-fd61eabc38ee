import React from 'react';
import assign from 'object-assign';
import { push } from 'react-router-redux';
import { put } from 'redux-saga/effects';
import { t } from '@shein-bbl/react';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import { message } from 'common/index';
import { markStatus } from 'rrc-loader-helper';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle, getParentHref } from '../../../lib/util';
import * as servers from './server';
import ok from '../../../source/audio/ok.mp3';
import { getConfigByCodeApi } from '../../../server/basic/common';

const okAudio = new Audio(ok);
okAudio.load();

const defaultState = {
  bigContainerCode: '',
  allocationNo: '',
  warehouseType: '',
  disabled: 1,
  headerTitle: '',
  billMark: '',
  list: [],
  splitFlagDisabled: true, // switch 分箱 置灰
  splitFlag: false, // switch 分箱
  isShowFlag: false, // 是否展示按钮
  subWarehouseId: '', // 子仓
  shelvesParkName: '', // 推荐园区
  currentPage: 1, // 1 或空 默认页面 2 待收箱数/总箱数明细页面 3 统计页面
  deliverTruckNo: '', // 车牌号
  deliverTruckNoDisabled: 1, // 扫车牌是否禁用
  totalBoxNum: 0, // 后端返回的总的需要收的箱数
  waitReceiveNum: 0, // 初始化时跟总待收箱数一致 前端扫成功一个箱子 待收就减1
  waitReceiveBoxDetails: [], // 扫描车牌号获取待收货箱明细
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
};

export default {
  defaultState,
  $init: () => {
    classFocus('warehouse');
    return defaultState;
  },
  reset(draft) {
    assign(draft, defaultState);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    const data = yield servers.getWarehouse({});
    const res = yield servers.getArrivedWarehouse({});
    if (data.code !== '0') {
      Modal.error({
        content: data.msg,
      });
    } else if (res.code !== '0') {
      Modal.error({
        content: data.msg,
      });
    } else {
      if (data.info.warehouseList.length === 0) {
        const p1 = yield new Promise((r) => Modal.error({
          content: t('当前用户没有绑定仓库'),
          onOk: () => r(1),
        }));
        if (p1 === 1) {
          yield put(push(getParentHref()));
        }
        return p1;
      } else if (data.info.warehouseList.length > 1) {
        const p2 = yield new Promise((r) => Modal.error({
          content: t('收货用户只能绑定一个仓库，请联系管理员重新绑定'),
          onOk: () => r(1),
        }));
        if (p2 === 1) {
          yield put(push(getParentHref()));
        }
        return p2;
      }
      const warehouseId = data.info.warehouseList[0];
      const types = res.info.warehouseList.find((i) => i.warehouseId === +warehouseId);
      yield ctx.changeData({
        data: {
          warehouseType: types && types.type,
        },
      });
      classFocus('deliverTruckNo');
    }
    return null;
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * errorClear(action, ctx) {
    yield ctx.changeData({
      data: {
        [action.data]: '',
        [action.disabled]: 1,
      },
    });
    Modal.error({
      content: action.msg,
      onOk: () => {
        classFocus(action.data);
      },
    });
  },

  // 获取数据字典下拉数据
  * getSubwarhouseData(action, ctx) {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }
    yield ctx.changeData({
      data: {
        subWarehouseId: preSubMenu.subWarehouseId,
      },
    });

    const [selectDictData, warehouseData, getUserBindSubWarehouseData] = yield Promise.all([
      getConfigByCodeApi({ param: 'DISTRIBUTION_BOX_SUBWAREHOUSE' }),
      getConfigByCodeApi({ param: 'DISTRIBUTION_BOX_WAREHOUSE' }),
      servers.getUserBindSubWarehouseAPI({}),
    ]);

    const { warehouseId } = JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}');
    if (selectDictData.code === '0' && getUserBindSubWarehouseData.code === '0' && warehouseData.code === '0') {
      const subWarehouseList = selectDictData?.info?.configValue?.split(',').map((x) => Number(x)) || [];
      const warehouseList = warehouseData?.info?.configValue?.split(',').map((x) => Number(x)) || [];
      const bindSubWarehouseList = getUserBindSubWarehouseData?.info.map((x) => Number(x)) || [];
      // 当已配置且能匹配到当前仓库时才展示分箱按钮
      const isShowFlag = warehouseList.length !== 0 &&
        warehouseList.some((id) => id === Number(warehouseId));
      // eslint-disable-next-line max-len
      if (!bindSubWarehouseList.length || bindSubWarehouseList.find((x) => subWarehouseList.includes(Number(x)))) {
        yield ctx.changeData({
          data: {
            splitFlagDisabled: true,
            splitFlag: false,
            isShowFlag,
          },
        });
      } else {
        yield ctx.changeData({
          data: {
            splitFlagDisabled: false,
            splitFlag: true,
            isShowFlag,
          },
        });
      }
    } else {
      Modal.error({ content: selectDictData.msg || getUserBindSubWarehouseData.msg });
    }
  },
  // 扫描车牌号
  * scanDeliverTruckNo(action, ctx) {
    // 前端校验车牌号 位数必须>= 7 否则报错
    if (action.params?.deliverTruckNo.length < 7) {
      const status = yield new Promise((r) => {
        Modal.error({
          title: t('请输入正确的车牌号'),
          onOk: () => r(true),
          autoFocusButton: 'ok',
        });
      });
      if (status) {
        yield ctx.changeData({ data: { deliverTruckNo: '' } });
        classFocus('deliverTruckNo');
      }
      return;
    }
    yield ctx.changeData({
      data: {
        deliverTruckNoDisabled: 0,
      },
    });
    const res = yield servers.scanDeliverTruckNoAPI(action.params);
    if (res.code !== '0') {
      yield ctx.errorClear({
        data: 'deliverTruckNo',
        disabled: 'deliverTruckNoDisabled',
        msg: res.msg,
      });
    } else {
      okAudio.play();
      yield ctx.changeData({
        data: {
          totalBoxNum: res.info?.waitReceiveNum || 0, // 后端返回的总的需要收的箱数
          waitReceiveNum: res.info?.waitReceiveNum || 0, // 初始化时跟总待收箱数一致 前端扫成功一个箱子 待收就减1
        },
      });
      classFocus('bigContainerCode');
    }
  },
  // 扫描车牌号-获取待收货箱明细
  * deliverTruckNoWaitNum(action, ctx) {
    const {
      deliverTruckNo,
    } = yield '';
    markStatus('dataLoading');
    const res = yield servers.deliverTruckNoWaitNumAPI({ deliverTruckNo });
    if (res.code !== '0') {
      Modal.error({
        content: res.msg,
      });
    } else {
      yield ctx.changeData({
        data: {
          waitReceiveBoxDetails: res.info || [],
          currentPage: 2, // 跳转到明细页面
        },
      });
    }
  },
  // 按车收货-扫箱子
  * carReceive(action, ctx) {
    markStatus('dataLoading');
    const { subWarehouseId, waitReceiveNum } = yield '';
    const res = yield servers.carReceiveAPI({
      ...action.params,
      subWarehouseId,
    });
    if (res.code === '0') {
      okAudio.play();
      // 不是异车收货才进行待收数量扣减
      if (!action.params?.truckConfirmation) {
        yield ctx.changeData({
          data: {
            waitReceiveNum: waitReceiveNum - 1 > 0 ? waitReceiveNum - 1 : 0,
          },
        });
      }
      yield ctx.changeData({
        data: {
          allocationNo: res.info.allocationNo,
          bigContainerCode: '',
          list: [{
            bigContainerCodes: res.info.bigContainerCodes,
            bigContainerCodeList: res.info.bigContainerCodeList || [],
            allocationNo: res.info.allocationNo,
            tags: res.info.isQualityCheck ? t('免质检') : '',
            isSplit: res.info.splitMark ? t('分箱') : '',
          }, ...action.list],
          billMark: res.info.billMark,
          shelvesParkName: res.info.shelvesParkName,
        },
      });
      classFocus('bigContainerCode');
    } else if (res.code === '471084') {
      const confirmStatus = yield new Promise((r) => Modal.confirm({
        content: t('推荐园区与当前用户绑定园区不符，是否继续收货?'),
        onOk: () => r('ok'),
        onCancel: () => r('cancel'),
      }));
      if (confirmStatus === 'ok') {
        yield ctx.carReceive({
          params: { ...action.params, secondConfirmation: 1 },
          list: action.list || [],
        });
      }
      if (confirmStatus === 'cancel') {
        yield ctx.changeData({
          data: {
            bigContainerCode: '',
          },
        });
        classFocus('bigContainerCode');
      }
    } else if (res.code === '471085') {
      const confirmStatus = yield new Promise((r) => Modal.confirm({
        content: t('当前车无此箱号，是否确认收货?'),
        onOk: () => r('ok'),
        onCancel: () => r('cancel'),
      }));
      if (confirmStatus === 'ok') {
        yield ctx.carReceive({
          params: { ...action.params, truckConfirmation: true },
          list: action.list || [],
        });
      }
      if (confirmStatus === 'cancel') {
        yield ctx.changeData({
          data: {
            bigContainerCode: '',
          },
        });
        classFocus('bigContainerCode');
      }
    } else {
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
        },
      });
      Modal.error({
        content: res.msg,
        onOk: () => {
          classFocus('bigContainerCode');
        },
      });
    }
  },
  // 按车-收货完成
  * receiveComplete(action, ctx) {
    markStatus('dataLoading');
    const res = yield servers.receiveCompleteAPI({
      ...action.params,
    });
    if (res.code === '0') {
      if (res.info?.waitReceiveNum > 0) {
        const confirmStatus = yield new Promise((r) => Modal.confirm({
          content: t('请确认收货完成？确认后 未收货的箱数将不可按车收货'),
          onOk: () => r('ok'),
          onCancel: () => r('cancel'),
        }));
        if (confirmStatus === 'ok') {
          yield ctx.receiveComplete({
            params: { ...action.params, secondConfirmation: 1 },
          });
        }
        if (confirmStatus === 'cancel') {
          yield ctx.changeData({
            data: {
              bigContainerCode: '',
            },
          });
          classFocus('bigContainerCode');
        }
      } else {
        message.success(t('整车收货成功！'));
        const { subWarehouseId } = yield '';
        yield ctx.reset();
        yield ctx.changeData({
          data: {
            subWarehouseId,
          },
        });
        classFocus('deliverTruckNo');
      }
    } else {
      Modal.error({
        content: res.msg,
      });
    }
  },
};
