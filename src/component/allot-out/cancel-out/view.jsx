import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import store from './reducers';
import Header from '../../common/header';
import Footer from '../../common/footer';
import FocusInput from '../../common/focus-input';
import Modal from '../../common/modal';
import { classFocus } from '../../../lib/util';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      dispatch,
      disabled,
      bigContainerCode,
      headerTitle,
    } = this.props;
    return (
      <div>
        <Header title={headerTitle || t('取消调拨出库')} />
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            className="bigContainerCode"
            data-bind="bigContainerCode"
            autoFocus
            disabled={disabled === 0}
            onPressEnter={(e) => {
              if (e.target.value) {
                Modal.confirm({
                  content: t('是否确认取消？'),
                  onOk: () => {
                    store.scanBigContainer({
                      bigContainerCode,
                    });
                  },
                  onCancel: () => {
                    store.changeData({
                      data: {
                        bigContainerCode: '',
                      },
                    });
                    classFocus('bigContainerCode');
                  },
                });
              }
            }}
          >
            <label>{t('大箱号')}</label>
          </FocusInput>
        </Form>
        <Footer />
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
};

export default i18n(Container);
