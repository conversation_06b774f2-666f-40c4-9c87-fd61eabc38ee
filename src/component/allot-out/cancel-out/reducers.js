import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import * as servers from './server';
import { message } from '../../common';

const defaultState = {
  bigContainerCode: '',
  disabled: 1,
  headerTitle: '',
};

export default {
  defaultState,
  $init: () => defaultState,
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * errorClear(action, ctx) {
    yield ctx.changeData({
      data: {
        [action.data]: '',
      },
    });
    Modal.error({
      content: action.msg,
      onOk: () => {
        classFocus(action.data);
      },
    });
  },
  * scanBigContainer(action, ctx) {
    markStatus('disabled');
    const res = yield servers.scanBigContainer(action);
    if (res.code !== '0') {
      yield ctx.errorClear({
        data: 'bigContainerCode',
        msg: res.msg,
      });
    } else {
      message.success(t('成功'));
      yield ctx.changeData({
        data: {
          bigContainerCode: '',
        },
      });
      classFocus('bigContainerCode');
    }
  },
};
