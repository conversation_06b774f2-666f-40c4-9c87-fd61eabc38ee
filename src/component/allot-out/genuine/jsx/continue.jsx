import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import {
  Header, Footer, View, RowInfo,
} from 'common';
import style from '../style.less';
import store from '../reducers';

class Info extends Component {
  render() {
    const {
      headerTitle,
      boxCode,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle || t('正品分箱')} />
        <View
          diff={100}
          flex={false}
        >
          <RowInfo
            extraStyle={{
              borderBottom: 'none',
              backgroundColor: '#fff',
            }}
            label={`${t('来源箱号')}:${boxCode}`}
          />
          <div
            onClick={() => {
              store.scanBoxCode({ keepSeeding: true });
            }}
            className={style.continueContent}
          >
            <div
              className={style.continueDiv}
            >
              <div
                style={{ color: '#0059CE', fontSize: '24px', marginTop: '40px' }}
              >
                {t('继续分箱')}
              </div>
            </div>
          </div>
        </View>
        <Footer />
      </div>
    );
  }
}

Info.propTypes = {
  headerTitle: PropTypes.string,
  boxCode: PropTypes.string,
};

export default i18n(Info);
