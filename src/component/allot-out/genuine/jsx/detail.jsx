import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import {
  Header, Footer, View, Table,
} from 'common';
import store from '../reducers';

class Info extends Component {
  render() {
    const {
      headerTitle,
      boxCode,
      sowingUser,
      detailList,
    } = this.props;

    const columns = [
      {
        title: t('分箱周转箱'),
        dataIndex: 'containerCode',
        width: 50,
      },
      {
        title: t('推荐上架园区'),
        dataIndex: 'shelvesParkName',
        width: 50,
      },
      {
        title: t('装箱数'),
        dataIndex: 'totalNumSum',
        width: 20,
      },
    ];

    return (
      <div>
        <Header title={headerTitle || t('正品分箱')} />
        <View
          diff={100}
          flex={false}
        >
          <div style={{ fontSize: '15px', marginLeft: '7px' }}>
            <div style={{ marginTop: '5px' }}>{t('来源箱号：')}{boxCode}<span style={{ color: '#e89e42' }}>{t('【正品】')}</span></div>
            <div>{t('已分周转箱个数：')}{detailList.length}</div>
            <div>{t('操作人：')}{sowingUser}</div>
          </div>
          <Table
            columns={columns}
            dataSource={detailList}
          />
        </View>
        <Footer beforeBack={() => store.changeData({ currentPage: 2 })} />
      </div>
    );
  }
}

Info.propTypes = {
  headerTitle: PropTypes.string,
  boxCode: PropTypes.string,
  sowingUser: PropTypes.string,
  detailList: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Info);
