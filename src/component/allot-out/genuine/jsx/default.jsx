import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { Button } from 'react-weui/build/packages/components/button';
import {
  Header, FocusInput, Footer, View, ProgressBar, FooterBtn, TextBar,
} from 'common';
import Modal from '../../../common/modal';
import store from '../reducers';

class Info extends Component {
  render() {
    const {
      headerTitle,
      dataLoading,
      boxCode,
      barCode,
      containerCode,
      scanContainerDisabled,
      boxCodeDisabled,
      skc,
      size,
      sowingNum,
      totalNum,
      arrIndex,
      storeTypeStr,
      shelvesPark,
      firstBox,
      operationStepStr,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle || t('正品分箱')}>
          <div
            onClick={() => {
              store.jumpDetail();
            }}
          >
            {t('装箱明细')}
          </div>
        </Header>
        <View
          diff={100}
          flex={false}
        >
          <div style={{ fontSize: 14, padding: '5px 0 5px 17px' }}>
            <span style={{ color: '#666C7C' }}>{operationStepStr}：</span>
            <ProgressBar key={sowingNum} percentage={sowingNum / totalNum} style={{ width: 100, display: 'inline-block', margin: '0 10px' }} />
            <span>{t('已分')}{sowingNum}{t('（共{}）', totalNum)}</span>
          </div>
          <Form>
            <FocusInput
              value={boxCode}
              className="boxCode"
              placeholder={t('请扫描')}
              disabled={!dataLoading || boxCodeDisabled}
              onChange={(e) => {
                const { value } = e.target;
                if (!value.trim()) {
                  store.changeData({
                    boxCode: '',
                  });
                } else {
                  store.changeData({
                    boxCode: value.trim().toUpperCase(),
                  });
                }
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanBoxCode();
              }}
              footer={(
                <Button
                  size="small"
                  style={{ fontSize: 12 }}
                  disabled={!boxCode}
                  onClick={() => {
                    store.emptyBox();
                  }}
                >
                  {t('箱空')}
                </Button>
              )}
            >
              <label>{t('来源箱号')}</label>
            </FocusInput>
            <FocusInput
              value={barCode}
              className="barCode"
              placeholder={t('请扫描')}
              disabled={false}
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  barCode: value,
                });
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanBarCode();
              }}
            >
              <label>{t('商品条码')}</label>
            </FocusInput>
            <FocusInput
              value={containerCode}
              className="containerCode"
              placeholder={t('请扫描')}
              disabled={!dataLoading && scanContainerDisabled}
              onChange={(e) => {
                const { value } = e.target;
                if (!value.trim()) {
                  store.changeData({
                    containerCode: '',
                  });
                } else {
                  store.changeData({
                    containerCode: value.trim().toUpperCase(),
                  });
                }
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanContainerCode();
              }}
            >
              <label>{t('分播周转箱')}</label>
            </FocusInput>
          </Form>
          <TextBar text={scanContainerDisabled || !firstBox ? [`${arrIndex}  ${shelvesPark || ''}/${storeTypeStr}`] : [t('请绑定周转箱')]} style={{ marginTop: 8 }} />
          <div style={{ margin: '7px 0 0 17px' }} data-if={scanContainerDisabled || !firstBox}>
            <div>
              SKC:{skc}
            </div>
            <div>{t('尺码:{}', size)}</div>
          </div>
        </View>
        <Footer
          beforeBack={() => {
            Modal.confirm({
              content: t('是否确认返回?'),
              modalBlurInput: true,
              onOk: () => {
                store.clearData();
              },
            });
          }}
        >
          <FooterBtn
            disabled={!scanContainerDisabled}
            onClick={() => {
              store.closeContainer();
            }}
          >
            {t('关箱')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Info.propTypes = {
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  boxCode: PropTypes.string,
  barCode: PropTypes.string,
  containerCode: PropTypes.string,
  scanContainerDisabled: PropTypes.bool,
  boxCodeDisabled: PropTypes.bool,
  sowingNum: PropTypes.number,
  totalNum: PropTypes.number,
  arrIndex: PropTypes.number,
  storeTypeStr: PropTypes.string,
  shelvesPark: PropTypes.string,
  skc: PropTypes.string,
  size: PropTypes.string,
  firstBox: PropTypes.bool,
  operationStepStr: PropTypes.string,
};

export default i18n(Info);
