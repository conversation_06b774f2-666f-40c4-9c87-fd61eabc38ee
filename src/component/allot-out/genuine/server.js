import { sendPostRequest } from 'lib/public-request';

/**
 * 正品分箱-扫描来源箱号
 */
export const nonGenuineScanBoxCodeAPI = (param) => sendPostRequest({
  url: '/special/quality_genuine/scan_box_code',
  param,
}, process.env.WIS_FRONT);

/**
 * 正品分箱-扫描商品条码
 */
export const nonGenuineScanBarCodeAPI = (param) => sendPostRequest({
  url: '/special/quality_genuine/scan_bar_code',
  param,
}, process.env.WIS_FRONT);

/**
 * 正品分箱-扫描周转箱
 */
export const nonGenuineScanContainerCodeAPI = (param) => sendPostRequest({
  url: '/special/quality_genuine/scan_container_code',
  param,
}, process.env.WIS_FRONT);

/**
 * 正品分箱-关箱
 */
export const nonGenuineCloseContainerAPI = (param) => sendPostRequest({
  url: '/special/quality_genuine/close_container',
  param,
}, process.env.WIS_FRONT);

/**
 * 正品分箱 - 箱空
 */
export const nonGenuineEmptyBoxAPI = (param) => sendPostRequest({
  url: '/special/quality_genuine/empty_box',
  param,
}, process.env.WIS_FRONT);

/**
 * 正品分箱 - 明细
 */
export const nonGenuineBoxDetailAPI = (param) => sendPostRequest({
  url: '/special/quality_genuine/box_detail',
  param,
}, process.env.WIS_FRONT);

/**
 * 获取入库异常枚举值
 * @param param
 * @returns {*}
 */
export const queryDataDict = (param) => sendPostRequest({
  url: '/outside/dict/select',
  param,
}, process.env.WIS);

/**
 * 默认调用查询是否存在未完成的任务
 * @param param
 * @returns {*}
 */
export const beforeCheckAPI = (param) => sendPostRequest({
  url: '/special/quality_genuine/before_check',
  param,
}, process.env.WIS_FRONT);
