.progressBox {
  padding: 10px;
  background-color: #f4f6fa;

  .progressText {
    text-align: center;
    font-size: 12px;
    color: #221f40;
  }
}

.triangle {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 10px 20px 10px; /* 底边的长度是20px的两倍，左右两侧的长度为10px */
  border-color: transparent transparent #007bff transparent;
}

.circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #007bff;
}

.circle2 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #007bff;  /* 边框的样式 */
}


.square {
  width: 20px;
  height: 20px;
  border: 1px solid #007bff;
  background-color: #007bff;
}

.continueContent {
  background: #0059CE;
  width: 140px;
  height: 140px;
  border-radius: 50%;
  position: relative;
  margin: 120px auto 0 auto;
}

.continueDiv{
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: #fff;
  text-align: center;
  position: absolute;
  left: 10px;
  top: 10px;
}


