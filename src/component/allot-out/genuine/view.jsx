import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import InfoPage from './jsx/info';
import DefaultPage from './jsx/default';
import DetailPage from './jsx/detail';
import ContinuePage from './jsx/continue';
import store from './reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const { currentPage } = this.props;

    switch (currentPage) {
      case 1:
        return (<InfoPage {...this.props} />);
      case 2:
        return (<DefaultPage {...this.props} />);
      case 3:
        return (<ContinuePage {...this.props} />);
      default:
        return (<DetailPage {...this.props} />);
    }
  }
}

Container.propTypes = {
  currentPage: PropTypes.number,
};

export default i18n(Container);
