import { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useAppFn, useGlobalData } from '@alita/react';
import store from '../reducers';

const AlitaDataRegister = (props) => {
  const {
    openJiugongge,
    showGridWarehouse,
    openSplitABFrame,
    showABWarehouse,
    openPicturePickingModel,
    showIntro,
    showUserIntro,
    pageData,
    pageStore,
  } = props;
  // 子应用共享数据
  const [, setGlobalData] = useGlobalData();
  useAppFn('changeBaseData', store.changeData);
  useAppFn('changeBaseLimit', store.changeLimit);
  useEffect(
    () => {
      setGlobalData({
        openJiugongge,
        showGridWarehouse,
        openSplitABFrame,
        showABWarehouse,
        openPicturePickingModel,
        showIntro,
        showUserIntro,
        pageData,
        pageStore,
      });
    },
    [
      openJiugongge,
      showGridWarehouse,
      openSplitABFrame,
      showABWarehouse,
      openPicturePickingModel,
      showIntro,
      showUserIntro,
      pageData,
      pageStore,
    ],
  );
  return null; // 不渲染任何内容
};
AlitaDataRegister.propTypes = {
  showUserIntro: PropTypes.bool.isRequired,
  showIntro: PropTypes.bool.isRequired,
  pageStore: PropTypes.shape(),
  openJiugongge: PropTypes.bool,
  showGridWarehouse: PropTypes.arrayOf(PropTypes.shape()),
  openSplitABFrame: PropTypes.bool,
  showABWarehouse: PropTypes.arrayOf(PropTypes.shape()),
  openPicturePickingModel: PropTypes.bool,
  pageData: PropTypes.shape(),
};

export default AlitaDataRegister;
