import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import MessageContainer from 'common/messageContainer/src';
import store from '../reducers';

function MessageWrapper(props) {
  const { wsToken } = props;

  return (
    <MessageContainer
      getUnreadMessage={false}
      useDefaultMessageContainer={false}
      token={wsToken}
      onUnreadMessagesReceive={() => {}}
      onNewMessageReceive={(data) => {
        console.log(t('收到消息-------'), data);
        if ([2, 3].includes(data?.module)) {
          store.changeData({
            data: {
              showNoticeDataId: data.content,
            },
          });
        }
      }}
      onConnectionEstablished={(ws) => {
        window.ws = ws;
      }}
      onError={(err) => {
        // 连接建立失败
        console.log(t('连接失败-------'), err);
      }}
    />
  );
}
MessageWrapper.propTypes = {
  wsToken: PropTypes.string,
};

export default MessageWrapper;
