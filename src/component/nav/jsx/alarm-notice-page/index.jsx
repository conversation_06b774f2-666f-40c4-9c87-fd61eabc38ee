/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import { t } from '@shein-bbl/react';
// import {
//   Header,
// } from 'common';
import { Button } from 'react-weui/build/packages/components/button';
import { useCountDown, useSetState } from '@ofeu/hooks';
import Icon from '@shein-components/Icon';
import ConfirmModal from './confirm-modal';
import message from '../../../common/message';
import {
  recordReadUser, getUserNotReadNumAPI,
  queryNoticeDetail,
} from '../../server';
// import store from '../../reducers';
import styles from '../../style.css';

function AlarmNoticePage(props) {
  const { showNoticeDataId } = props;
  const [state, setState] = useSetState({
    show: false,
    nextId: '',
    detail: {},
    currentId: '',
    nextCurrentId: '',
    visible: false,
    btnDisabled: false,
    idList: [],
    targetDate: 0,
  });
  const [countdown] = useCountDown({
    targetDate: state.targetDate,
    onEnd: () => {
      setState({
        btnDisabled: true,
      });
    },
  });

  // 检查刷新之后或者用户登录后是否需要告警
  const checkUserAlarm = async () => {
    try {
      const res = await getUserNotReadNumAPI();
      if (+res.code === 0) {
        if (res.info?.ids?.length > 0) {
          const resp = await queryNoticeDetail({ id: res.info.ids[0] });
          if (+resp.code === 0) {
            setState({
              detail: resp.info,
              currentId: res.info.ids[0],
              visible: true,
              idList: res.info.ids,
              targetDate: +new Date() + 1000 * 3,
              btnDisabled: false,
            });
          }
        }
      } else {
        setState({
          visible: false,
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    // 判断是否为第一次进去，如果是第一次登录进来就请求数量
    if (window.location.hash.split('#')[1] !== '/login') { // 登录页不请求告警接口，避免登录前一直刷新页面出现302弹窗
      // 检查刷新之后是否需要告警
      checkUserAlarm();
    }
  }, []);

  // 根据推送获取真正的数据
  const getDetailAndUnReadNum = async (id) => {
    const resp = await queryNoticeDetail({ id });
    const resNum = await getUserNotReadNumAPI();
    if (+resp.code === 0) {
      setState({
        detail: resp.info,
        currentId: id,
        visible: true,
        show: false,
        targetDate: +new Date() + 1000 * 3,
        btnDisabled: false,
      });
      if (+resNum.code === 0 && resNum.info.ids.length > 0) {
        const list = [...resNum.info.ids];
        const resList = list.filter((item) => item !== id);
        setState({
          idList: [id, ...resList],
        });
      }
    } else {
      if (+resNum.code === 0 && resNum.info.ids.length > 0) {
        const resList = [...resNum.info.ids];
        setState({
          idList: [id, ...resList],
        });
      }
      message.error(t('获取告警通知详情失败'));
    }
  };

  // 更新弹窗显示数据
  const getNoticeDetail = async (id) => {
    const resp = await queryNoticeDetail({ id });
    if (+resp.code === 0) {
      setState({
        detail: resp.info,
        currentId: id,
        visible: true,
        show: false,
        targetDate: +new Date() + 1000 * 3,
        btnDisabled: false,
      });
    } else {
      message.error(t('获取告警通知详情失败'));
    }
  };

  useEffect(() => {
    if (!showNoticeDataId) return;
    let noticeId = showNoticeDataId;
    if (`${noticeId}`.indexOf('{') > -1) {
      const {
        id, terminal, pushWarehouseType, pushUserType,
      } = JSON.parse(noticeId);
      if (+terminal === 1 || +pushWarehouseType !== 4 || +pushUserType !== 4) return;
      noticeId = id;
      getDetailAndUnReadNum(noticeId);
    }
  }, [showNoticeDataId]);

  const readAlarmNotice = async ({
    id,
  }) => {
    const resp = await recordReadUser({ id });
    if (+resp.code === 0) {
      if (resp.info?.id) {
        const resNum = await getUserNotReadNumAPI();
        if (+resNum.code === 0) {
          const list = [...resNum.info.ids];
          const resList = list.filter((item) => item !== id);
          setState({
            idList: resList,
            nextCurrentId: resp.info?.id,
            show: true,
          });
        } else {
          message.error(resNum.msg || t('获取未读数量失败'));
        }
      } else {
        setState({
          visible: false,
        });
      }
    } else {
      message.error(resp.msg || t('操作失败'));
    }
  };

  // 点击忽略关闭弹窗
  const closeAlarmNotice = async () => {
    const { idList } = state;
    const resp = await recordReadUser({ id: idList });
    if (+resp.code === 0) {
      setState({
        visible: false,
        show: false,
        idList: [],
      });
    } else {
      message.error(resp.msg || t('操作失败'));
    }
  };

  const { theme, content } = state.detail || {};
  if (!state.visible) {
    return null;
  }
  return (
    <div className={styles.alarmPage}>
      {/* <Header homeIcon={false} title={t('通知')} /> */}
      <div className={styles.contentView}>
        <div>
          <Icon name="message" className={styles.dangerousIcon} />
        </div>
        <div className={styles.contentTips}>
          <div style={{
            marginTop: '20px',
          }}
          >{theme}
          </div>
          <div style={{
            marginTop: '20px',
          }}
          >
            <div
              style={{
              }} dangerouslySetInnerHTML={{ __html: content }}
            />
          </div>
        </div>
        <div style={{ paddingTop: '15%' }}>
          <Button
            className={styles.buttonStyle}
            size="small"
            disabled={!state.btnDisabled}
            onClick={() => {
              readAlarmNotice({ id: state.currentId });
            }}
          >{countdown > 0 ? t('({}S)确定', Math.round(countdown / 1000)) : t('确定')}
          </Button>
        </div>
      </div>
      <ConfirmModal
        title=""
        content={t('您还有{}条未读公告，请确认是否继续', state.idList?.length > 99 ? '99+' : state.idList?.length)}
        show={state.show}
        onClose={() => {
          closeAlarmNotice();
        }}
        onConfirm={() => {
          getNoticeDetail(state.nextCurrentId);
        }}
      />
    </div>
  );
}

AlarmNoticePage.propTypes = {

};

export default AlarmNoticePage;
