import { t } from '@shein-bbl/react';
/* eslint-disable react/prop-types */
import React from 'react';
import { Dialog } from 'react-weui/build/packages';

function ConfirmModal(props) {
  const {
    title, content, show, onClose, onConfirm,
  } = props;
  return (
    <Dialog
      title={title}
      show={show}
      buttons={[
        {
          type: 'default',
          label: t('忽略'),
          onClick: () => {
            onClose?.();
          },
        },
        {
          type: 'primary',
          label: t('继续'),
          onClick: () => {
            onConfirm?.();
          },
        },
      ]}
    >
      {content}
    </Dialog>
  );
}

ConfirmModal.propTypes = {
};

export default ConfirmModal;
