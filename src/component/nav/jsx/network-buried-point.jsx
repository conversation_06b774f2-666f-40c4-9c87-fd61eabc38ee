import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { camel2Under, getWarehouseId } from '../../../lib/util';

// fetch请求配置参数
const fetchParam = {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json',
    warehouseCode: getWarehouseId(),
  },
};

// 佛山仓园区和ip网段对应关系
const IP_RANGE_LIST = [
  { ipRange: '*********/16', subWarehouseId: '455', parkName: t('佛山鹤山嘉民熙麦仓') },
  { ipRange: '*********/17', subWarehouseId: '317', parkName: t('清远易商仓') },
  { ipRange: '***********/17', subWarehouseId: '83', parkName: t('广州花都面料厂') },
  { ipRange: '**********/16', subWarehouseId: '27', parkName: t('佛山安博仓') },
  { ipRange: '**********/16', subWarehouseId: '30', parkName: t('佛山易升仓') },
  { ipRange: '**********/17', subWarehouseId: '42', parkName: t('佛山中达仓') },
  { ipRange: '************/17', subWarehouseId: '49', parkName: t('肇庆普洛斯') },
  { ipRange: '**********/16', subWarehouseId: '313', parkName: t('佛山格洛仓') },
  { ipRange: '**********/16', subWarehouseId: '370', parkName: t('佛山东百仓') },
  { ipRange: '**********/16', subWarehouseId: '96', parkName: t('佛山宏盛嘉业仓') },
  { ipRange: '**********/16', subWarehouseId: '97', parkName: t('肇庆新迪仓') },
  { ipRange: '**********/16', subWarehouseId: '87', parkName: t('肇庆大旺维龙仓') },
  { ipRange: '**********/17', subWarehouseId: '68', parkName: t('佛山更合仓') },
  { ipRange: '************/17', subWarehouseId: '335', parkName: t('佛山高明嘉民仓') },
  { ipRange: '**********/16', subWarehouseId: '100', parkName: t('江门鹤山维龙仓') },
  { ipRange: '**********/17', subWarehouseId: '118', parkName: t('佛山新宜仓') },
  { ipRange: '************/17', subWarehouseId: '131', parkName: t('佛山金志通仓') },
  { ipRange: '**********/16', subWarehouseId: '96', parkName: t('佛山易升/格洛/宏盛无线') },
];

/**
 * 将 IPv4 地址转换为整数
 * @param {string} ip - IPv4 地址，如 '**********'
 * @returns {number} - 整数形式的 IPv4 地址
 */
function ipToInt(ip) {
  // eslint-disable-next-line no-bitwise
  return ip.split('.')
    .reduce(
      (acc, cur) =>
        // 将上一个字节向左移 8 位，再加上这个字节对应的数字
        // eslint-disable-next-line implicit-arrow-linebreak,no-bitwise
        (acc << 8) + parseInt(cur, 10),
      0,
    ) >>> 0; // 使用无符号右位移运算符，转换为无符号整数
}
/**
 * 判断 IP 地址是否在 IP 网段范围内
 * @param {string} ip - 要判断的 IP 地址
 * @param {string} ipRange - IP 网段，如 '**********/16'
 * @returns {boolean} - IP 地址是否在 IP 网段范围内
 */
function isIPInRange(ip, ipRange) {
  // 将网段和子网掩码分开
  const [subnet, subnetMask] = ipRange.split('/');
  // 计算子网掩码位数
  const maskBits = 32 - parseInt(subnetMask, 10);
  // 将网段地址转换为整数
  const subnetInt = ipToInt(subnet);
  // 将 IP 地址转换为整数
  const ipInt = ipToInt(ip);
  // 计算掩码（全部是 1 的二进制数字）
  // eslint-disable-next-line no-bitwise
  const maskInt = ~(2 ** maskBits - 1);

  // 将 IP 地址和掩码按位与操作，与网段地址比较是否匹配
  // eslint-disable-next-line no-bitwise
  return (ipInt & maskInt) === subnetInt;
}

/**
 * 佛山仓根据ip获取园区id，空则返回''
 * @param warehouseId
 * @param ip
 * @returns {string}
 */
function transformIpToParkId(warehouseId, ip) {
  let subWarehouseId = '';
  if (warehouseId !== '1') {
    return subWarehouseId;
  }
  IP_RANGE_LIST.forEach((item) => {
    if (!subWarehouseId && isIPInRange(ip, item.ipRange)) {
      subWarehouseId = item.subWarehouseId;
    }
  });
  return subWarehouseId;
}

/**
 * 根据配置值，判断是否请求网络埋点接口
 * @param configObj
 * @param comRequestObj
 * @returns {{isFetch: boolean}|{setIntervalNum, isFetch: boolean}}
 */
function getFetchConfig(configObj, comRequestObj) {
  // 若总开关关闭，则不请求网络埋点接口
  if (configObj.switchOpen !== 1) {
    return {
      isFetch: false,
    };
  }
  // 当前用户所在仓库id和园区id
  const { warehouseId, subWarehouseId } = comRequestObj;
  // apollo配置值
  const {
    warehouseList, parkList, defaultCookieABNum,
    ...comFetchConfig
  } = configObj;

  // 当前用户最终请求配置值: 佛山仓按园区分流，非佛山仓按仓库分流，匹配不到则用公共配置值
  let itemFetchConfig = {};
  if (warehouseId === '1') {
    itemFetchConfig = parkList.find((item) => item.id === subWarehouseId) || {};
  } else {
    itemFetchConfig = warehouseList.find((item) => item.id === warehouseId) || {};
  }
  const finalFetchConfig = { ...comFetchConfig, ...itemFetchConfig };
  // apollo最终配置值：cookieABNum(蓝绿分组值)、setIntervalNum(请求频率)、randomNum(随机数)
  const { cookieABNum, setIntervalNum, randomNum } = finalFetchConfig;
  // 获取cookie中的蓝绿分组值，用于用户分流【固定用户】
  let userCookieABNum = Number(document.cookie.match(`(^|;)\\s*${encodeURIComponent('@@SHEIN___AB_GROUP')}\\s*=\\s*([^;]+)`)?.pop());
  // 测试和灰度环境验证：cookie中没有蓝绿分组值，根据配置的默认值进行分流
  if (!userCookieABNum && defaultCookieABNum) {
    userCookieABNum = Number(defaultCookieABNum);
  }
  // 加上1-100随机比例值，防止数据量过多服务压力过大【固定用户下的随机用户】
  const userRandomNum = Math.floor(Math.random() * 100) + 1;
  return {
    // 是否请求网络埋点接口: 用户蓝绿和随机值在配置值范围内，且配置值不为0
    isFetch: cookieABNum && userCookieABNum <= cookieABNum && userRandomNum <= randomNum,
    setIntervalNum,
  };
}

class NetworkBuriedPoint extends React.Component {
  constructor(props) {
    super(props);
    // 网络埋点接口固定请求参数：系统、页面地址、用户名、用户工号
    let user = {};
    if (localStorage.getItem('shein_auth_login_info')) {
      user = JSON.parse(localStorage.getItem('shein_auth_login_info') || '{}');
    }
    // 当前用户所在仓库id和ip
    // 当前用户所在仓库，取用户绑定仓库和选择仓库交集
    let { warehouseId } = JSON.parse(localStorage.getItem('warehouse') || '{}');
    // 用户选择仓库(可能为空)不属于其绑定仓库，则默认取用户绑定仓库第一个仓库
    if (!props.warehouseIdList.includes(Number(warehouseId))) {
      warehouseId = String(props.warehouseIdList[0]);
    }
    // 当前用户所在ip
    const ip = props.ip || '';
    const subWarehouseId = transformIpToParkId(warehouseId, ip);
    const comRequestObj = {
      url: window.location.href,
      warehouseId,
      subWarehouseId,
      ip,
      ...user,
    };
    this.state = {
      timer: 0, // 定时器
      requestObj: {}, // 网络接口埋点请求参数
      comRequestObj, // 网络埋点接口固定请求参数
      noRequest: false, // 是否不请求网络接口，多tab页仅一个tab页请求
    };
  }

  componentDidMount() {
    // 浏览器支持BroadcastChannel，则使用BroadcastChannel实现多tab页仅一个tab请求网络接口
    if (typeof BroadcastChannel === 'function') {
      this.channel = new BroadcastChannel('networkBuriedPointChannel');
      this.channel.addEventListener('message', (event) => {
        // 已有其它页面去请求数据，本页面不再请求
        this.setState({ noRequest: event?.data?.lockOtherTabs });
      });
      this.channel.postMessage({ lockOtherTabs: true });
      this.getConfig();
      return;
    }
    // 浏览器不支持BroadcastChannel，则使用localStorage实现多tab页仅一个tab请求网络接口【锁1小时】
    const lockExpiration = localStorage.getItem('lock_expiration');
    if (!lockExpiration || Number(lockExpiration) <= Date.now()) {
      localStorage.setItem('lock_expiration', Date.now() + 3600 * 1000);
      this.getConfig();
    }
  }

  componentWillUnmount() {
    this.abnormalHandle();
  }

  // 获取埋点apollo配置
  getConfig() {
    try {
      const { comRequestObj, timer } = this.state;
      // eslint-disable-next-line react/destructuring-assignment
      const configObj = this.props.config || {};
      // 获取网络埋点接口请求配置：是否请求和请求时间间隔
      const { setIntervalNum, isFetch } = getFetchConfig(configObj, comRequestObj);
      if (isFetch) {
        this.fetchData();
        clearInterval(timer);
        const timerVal = setInterval(() => {
          this.fetchData();
        }, setIntervalNum);
        this.setState({ timer: timerVal });
      }
    } catch (e) {
      console.error(e);
    }
  }

  // 组件销毁、网络(断网)或接口(302等)异常时，不再请求并关闭定时器等
  abnormalHandle() {
    const { timer } = this.state;
    clearInterval(timer);
    localStorage.removeItem('lock_expiration');
    this.channel?.close();
  }

  // 调用网络埋点接口
  async fetchData() {
    const { noRequest, comRequestObj, requestObj } = this.state;
    if (noRequest) return;
    try {
      // 记录前端请求和响应时间
      const reqTime = Date.now();
      const response = await fetch('/watt/front/collect_delay_record', {
        ...fetchParam,
        body: JSON.stringify(camel2Under({ ...comRequestObj, ...requestObj })),
      });
      // 接口200才继续请求
      if (response.status !== 200) {
        this.abnormalHandle();
      }
      // 从响应头获取后端、网关、域名请求响应时间
      const resKes = ['backend_req_time', 'backend_rsp_time', 'wgw_req_time', 'wgw_rsp_time', 'dom_req_time', 'dom_rsp_time'];
      const resHeader = ['Backend-Req-Time', 'Backend-Rsp-Time', 'Wgw-Req-Time', 'Wgw-Rsp-Time', 'Dom-Req-Time', 'Dom-Rsp-Time']
        .reduce((prev, key, idx) => {
          prev[resKes[idx]] = Number(response.headers.get(key));
          return prev;
        }, {
          req_time: reqTime,
          rsp_time: Date.now(),
        });
      this.setState({ requestObj: resHeader });
    } catch (error) {
      console.error(error);
      // 断网等
      this.abnormalHandle();
    }
  }

  render() {
    return null;
  }
}

NetworkBuriedPoint.propTypes = {
  warehouseIdList: PropTypes.arrayOf(PropTypes.number),
  ip: PropTypes.string,
  config: PropTypes.shape(),
};
export default NetworkBuriedPoint;
