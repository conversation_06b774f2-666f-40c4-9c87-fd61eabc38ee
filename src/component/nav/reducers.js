import assign from 'object-assign';
import { push } from 'react-router-redux';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { select, all } from 'redux-saga/effects';
import { delay } from 'redux-saga';
import { conf } from 'sheinq';
import React from 'react';
import jsCookie from 'js-cookie';
import {
  SESSION_MOT_IS_SHOW_RECORD, SESSION_IS_SHOW_QUICK_ENTRY,
  SESSION_PDA_GUIDELINE_DATA, SESSION_QUICK_ENTRY_OBJ,
  LOCAL_WAREHOUSE, LOCAL_WAREHOUSE_NAME,
  LOCAL_USER, SESSION_ANDON_SUBMIT_OBJ, SESSION_REASON_DATA_OBJ,
  SESSION_ANDON_SET_OBJ, SESSION_ANDON_LOGIN, LOCAL_UPLOAD_ERROR_COUNT,
  SESSION_MOT_MENU_OBJ, LOCAL_USER_MENU, LOCAL_IS_REMEMBER,
  // eslint-disable-next-line no-unused-vars
  SESSION_MOT_ROUTER_PAGE, SESSION_MOT_NAV_PAGE_ROLLBACK,
} from 'lib/storage';
import { noPwdAuthLocalStorage, timeDiffNumStorage } from '../../lib/storage-new';
import { getConfigByCodeApi, getApolloConfigAPI } from '../../server/basic/common';
import {
  getPdaMenu,
  timeoutApi,
  logoutApi,
  getCloudMessageTokenAPI,
} from './server';
import modal from '../common/modal';
import message from '../common/message';
import {
  clearSomeCookie, getWarehouseId, getUsername, getFormatName, clearEmpty, classFocus,
  clearLoginTimeoutInterval, apolloFormatObj,
} from '../../lib/util';
import { setData, getData } from '../../lib/cloud-sdk';
import { commonWmsCloud } from '../../lib/cloud';
import { getLang } from '../js';

const executeRequestData = (apiFn, ...args) => {
  try {
    const result = apiFn(...args);
    if (result instanceof Promise) {
      return result.then((res) => res).catch(() => null);
    } else {
      return result;
    }
  } catch (err) {
    // apm报错处理-res为null报错
    return {};
  }
};

const defaultState = {
  menus: null,
  ready: false,
  showDrawer: false,
  isShowQuickEntry: false, // 快捷入口开关
  isShowRecord: sessionStorage.getItem(SESSION_MOT_IS_SHOW_RECORD) === 'true', // 调试日志开关
  quickEntryList: [], // 快捷菜单数组
  quickEntryLang: 'zh', // 默认值是中文
  topMenusObj: {}, // menus一级菜单汇总对象{rule:{rule,title,...}...}
  childMenusObj: {}, // menus二级菜单汇总对象{rule:{rule,title,...}...}
  isOnline: true,
  limit: {
    location: '',
    firstReasonId: '', // 一级异常原因
    secondReasonId: '', // 二级异常原因
    remark: '',
  },
  showRemarkPop: false, // 备注弹框展示
  showJiugongge: true, // 左侧栏显示一分九宫格
  openJiugongge: false, // 左侧栏开启一分九宫格
  openSplitABFrame: false, // 左侧栏开启二分AB架模式
  showSplitABFrame: true, // 左侧栏显示二分AB架模式
  openPicturePickingModel: true, // 左侧栏开启图片拣货模式
  // OFC-9230安灯需求
  // 1 - 提交问题， 2 - 我的问题， 3 - 待我解决 4 - 显示提交问题的设置页面 pageStatus
  warehouseId: 1,
  pageStatus: 1,
  isUserLogin: false,
  categoryData: [],
  typeName: '',
  showUserIntro: false, // 展示用户指引界面
  showIntro: false, // 开启用户指引
  showImgs: false, // 用户指引界面是否展示弹窗，是的话则阻止左滑出现左边栏
  pageStore: null, // 用于初始当前指引页面数据
  pdaGuidelineData: {}, // 指引视频等数据 以url为key，如'/new-pda/put-shelves/whole-box-shelves'
  showGridWarehouse: [], // 一分网格展示仓库
  showABWarehouse: [], // 二分AB模式展示仓库
  userWarehouseIdList: [], // 用户绑定仓库id
  userIP: '', // 用户所在ip
  showNoticePage: false, // 是否展示公告页面
  showNoticeDataId: '', // 公告id
  wattConfigObj: {}, // watt配置
  wsToken: '',
  timeDiff: timeDiffNumStorage.getItem() * 1 || 0,
};

/**
 * 获取二级菜单
 * @param obj
 * @returns {Array|*[]}
 */
const getMenuList = (obj = {}) => {
  const list = obj.children || [];
  let res = [];
  for (let i = 0; i < list.length; i++) {
    res = [...res, ...(list[i].children || [])];
  }
  return res;
};
// 兼容多路径页面：多路径页面对象
const multiPathObj = {
  '/new-pda/order-picking/picking/get-task/1': ['/new-pda/order-picking/picking/picking-page/1'],
  '/new-pda/order-picking/picking/get-task/2': ['/new-pda/order-picking/picking/picking-page/2'],
  '/new-pda/put-shelves/put-away': ['/new-pda/put-shelves/put-away/put-page'],
};

export default {
  defaultState,
  getData: (draft, action) => draft[action.key],
  changeData: (draft, action) => {
    // 面板隐藏时，再 调接口保存快捷入口
    if (Object.keys(action.data).includes('showDrawer') && !action.data.showDrawer && draft.showDrawer) {
      // 防止重复保存 相同数据
      if (draft.isShowQuickEntry !== undefined &&
        sessionStorage.getItem(SESSION_IS_SHOW_QUICK_ENTRY) !== draft.isShowQuickEntry.toString()) {
        const quickEntryObj = {
          isShowQuickEntry: draft.isShowQuickEntry,
          quickEntryList: draft.quickEntryList,
          openJiugongge: draft.openJiugongge,
          openSplitABFrame: draft.openSplitABFrame,
          quickEntryLang: getLang(),
          openPicturePickingModel: draft.openPicturePickingModel,
        };
        setData({ quickEntryObj }).then(() => {
          console.log(t('quickEntryObj，存云服务器成功'));
        });
        sessionStorage.setItem(SESSION_IS_SHOW_QUICK_ENTRY, draft.isShowQuickEntry);
      }
    }
    // 若是快捷菜单isShowQuickEntry或quickEntryList；则更新云服务器值
    if (action.data.quickEntryObjKey === 'quickEntryList') {
      const quickEntryObj = {
        quickEntryList: action.data.quickEntryList,
        isShowQuickEntry: draft.isShowQuickEntry,
        openJiugongge: draft.openJiugongge,
        openSplitABFrame: draft.openSplitABFrame,
        quickEntryLang: getLang(),
        openPicturePickingModel: draft.openPicturePickingModel,
      };
      setData({ quickEntryObj }).then(() => console.log(t('quickEntryObj，存云服务器成功')));
    }
    // 左侧栏开启一分九宫格
    if (action.data.quickEntryObjKey === 'openJiugongge') {
      const quickEntryObj = {
        openJiugongge: action.data.openJiugongge,
        openSplitABFrame: draft.openSplitABFrame,
        quickEntryList: draft.quickEntryList,
        isShowQuickEntry: draft.isShowQuickEntry,
        quickEntryLang: getLang(),
        openPicturePickingModel: draft.openPicturePickingModel,
      };
      setData({ quickEntryObj }).then(() => console.log(t('quickEntryObj，存云服务器成功')));
    }
    // 左侧栏开启AB架模式
    if (action.data.quickEntryObjKey === 'openSplitABFrame') {
      const quickEntryObj = {
        openSplitABFrame: action.data.openSplitABFrame,
        openJiugongge: draft.openJiugongge,
        quickEntryList: draft.quickEntryList,
        isShowQuickEntry: draft.isShowQuickEntry,
        quickEntryLang: getLang(),
        openPicturePickingModel: draft.openPicturePickingModel,
      };
      setData({ quickEntryObj }).then(() => console.log(t('quickEntryObj，存云服务器成功')));
    }
    // 左侧栏开启图片拣货模式
    if (action.data.quickEntryObjKey === 'openPicturePickingModel') {
      const quickEntryObj = {
        openSplitABFrame: action.openSplitABFrame,
        openJiugongge: draft.openJiugongge,
        quickEntryList: draft.quickEntryList,
        isShowQuickEntry: draft.isShowQuickEntry,
        quickEntryLang: getLang(),
        openPicturePickingModel: action.data.openPicturePickingModel, // 左侧栏开启二分AB架模式
      };
      setData({ quickEntryObj }).then(() => console.log(t('quickEntryObj，存云服务器成功')));
    }
    assign(draft, action.data);
  },
  changeLimit: (draft, action) => {
    assign(draft.limit, action.data);
  },
  $init: () => defaultState,
  // 初始化操作指引数据
  * pdaGuidelineData(action, ctx) {
    // 若sessionStorage存有值，则获取更新；否则请求并存储
    if (sessionStorage.getItem(SESSION_PDA_GUIDELINE_DATA)) {
      const pdaGuidelineData = JSON.parse(sessionStorage.getItem(SESSION_PDA_GUIDELINE_DATA));
      yield ctx.changeData({ data: { pdaGuidelineData } });
    } else {
      // 获取指引视频等数据
      const pdaGuidelineRes = yield commonWmsCloud.get(process.env.CLOUD_KEY);
      if (pdaGuidelineRes && pdaGuidelineRes.forEach) {
        const pdaGuidelineData = {};
        pdaGuidelineRes.forEach((r) => {
          // 兼容多页面路径
          if (multiPathObj[r.rule]) {
            multiPathObj[r.rule].forEach((url) => {
              pdaGuidelineData[url] = r;
            });
          }
          pdaGuidelineData[r.rule] = r;
        });
        // 存于sessionStorage，防止刷新重复请求
        sessionStorage.setItem(SESSION_PDA_GUIDELINE_DATA, JSON.stringify(pdaGuidelineData));
        yield ctx.changeData({ data: { pdaGuidelineData } });
      }
    }
  },
  // 获取初始公共配置和快捷菜单相关数据
  * getConfigData(action, ctx, put) {
    const [resConfig, secondConfig] = yield all([
      executeRequestData(getConfigByCodeApi, { param: 'FIRST_SOWING_GRID_SHOW_WAREHOUSE' }),
      executeRequestData(getConfigByCodeApi, { param: 'SECOND_WAREHOUSE_OPENED_BY_RACK_AB' }),
    ]);
    // 请求公共配置
    // const resConfig = yield getConfigByCodeApi({ param: 'FIRST_SOWING_GRID_SHOW_WAREHOUSE' });
    const showGridWarehouseConfig = resConfig?.code === '0' && resConfig?.info && resConfig.info.configValue ? resConfig.info.configValue.split(',') : [];
    yield this.changeData({
      data: {
        showGridWarehouse: showGridWarehouseConfig,
      },
    });
    // eslint-disable-next-line max-len
    // const secondConfig = yield getConfigByCodeApi({ param: 'SECOND_WAREHOUSE_OPENED_BY_RACK_AB' });
    const showABWarehouseConfig = secondConfig?.code === '0' && secondConfig?.info && secondConfig.info.configValue ? secondConfig.info.configValue.split(',') : [];
    yield this.changeData({
      data: {
        showABWarehouse: showABWarehouseConfig,
      },
    });
    // 进去页面时再调获取接口 从云服务器获取快捷菜单相关值
    let quickEntryRes;
    const cachedQuickEntryRes = sessionStorage.getItem(SESSION_QUICK_ENTRY_OBJ);
    if (cachedQuickEntryRes) {
      try {
        quickEntryRes = JSON.parse(cachedQuickEntryRes);
      } catch (err) {
        console.error('quickEntryRes parse error', err);
      }
    }
    if (!quickEntryRes) {
      quickEntryRes = yield getData('quickEntryObj');
    }
    // const quickEntryRes = yield getData('quickEntryObj');
    if (quickEntryRes) {
      // 存于sessionStorage，防止刷新重复请求
      sessionStorage.setItem(SESSION_QUICK_ENTRY_OBJ, JSON.stringify(quickEntryRes));
      yield ctx.changeData({
        data: {
          isShowQuickEntry: quickEntryRes.isShowQuickEntry,
          quickEntryList: quickEntryRes.quickEntryList || [],
          quickEntryLang: quickEntryRes.quickEntryLang || 'zh',
          openJiugongge: quickEntryRes.openJiugongge === undefined ? showGridWarehouseConfig.includes(JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId) : quickEntryRes.openJiugongge,
          openSplitABFrame: quickEntryRes.openSplitABFrame === undefined ? showABWarehouseConfig.includes(JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId) : quickEntryRes.openSplitABFrame,
          openPicturePickingModel: quickEntryRes.openPicturePickingModel,
        },
      });
      // 兼容先执行main-menu的initQuickData函数，后接口响应的初始化问题: 补执行一次initQuickData函数
      if (window.location.hash === '#/main-menu') {
        yield put({ type: 'main-menu/initQuickData' });
      }
    }
  },
  * init(action, ctx, put) {
    if (window.location.hash.split('#')[1] !== '/login') { // 登录页不请求告警接口，避免登录前一直刷新页面出现302弹窗
      // 设置token，链接websoket
      yield this.getNewToken();
    }
    // 获取当前路由是否和process.env.MOT_EU_URL 一致 不比较http和https
    const currentUrl = window.location.href
      .replace('http://', '')
      .replace('https://', '');
    const isSameUrl = currentUrl.startsWith(
      process.env.MOT_EU_URL?.replace('http://', '').replace('https://', ''),
    ) || currentUrl.startsWith(
      process.env.MOT_EU_OFFICE_URL?.replace('http://', '').replace('https://', ''),
    );
    if (!isSameUrl) {
      const { code, info } = yield getApolloConfigAPI({
        params: ['switch_new_url'],
      });
      if (code === '0') {
        const switchUrl = apolloFormatObj(info)?.switch_new_url || 0;
        if (switchUrl === '1') {
          modal.info({
            content: t('MOT-EU欧洲站域名已更换,请保存新域名！') + process.env.MOT_EU_URL,
            okText: t('跳转'),
            onOk: () => {
              window.location.href = `${process.env.MOT_EU_URL}/#/login`;
            },
          });
        }
      }
    }
    // window.mDevice APP手持设备对象
    // PHPSESSID 由于是后端设置httponly,前端获取不到, 但是从mot跳到app, 由app端前端设置
    // 所以可以判断是h5跳转到app跳转进入
    let APP_ROUTER_ENTER = false;
    // 因为前期PHPSESSID退出登陆设置了'', 先兼容,后续稳定之后删除
    if (window.mDevice && jsCookie.get('PHPSESSID') && jsCookie.get('PHPSESSID') !== "''") {
      APP_ROUTER_ENTER = true;
      // 设置wms
      localStorage.setItem(LOCAL_USER, '{"system":["wms"],"username":"","password":"","formatName":"","enName": ""}');
    }
    // 执行其它相关初始化数据
    yield ctx.pdaGuidelineData();

    // 获取菜单, 分tms/wms菜单，按权限分配
    const user = JSON.parse(localStorage.getItem(LOCAL_USER) || '{}');
    if (user && user.system && user.system.length) {
      // 获取初始公共配置和快捷菜单相关数据
      yield ctx.getConfigData();
      if ((user.system || []).includes('wms') && window.location.hash !== '#/login') {
        // 如果既是wms用户，又是tms用户，获取menu接口报403，直接进到主页
        const res = yield getPdaMenu();
        if (res === 'isTmsUser') {
          yield ctx.changeData({
            data: {
              ready: true,
              isUserLogin: true,
              menus: {},
            },
          });
          return;
        }
        if (res.code === '0') {
          const listObj = res.info.list[0] || {};
          const { warehouseIds, ip } = res.info;
          // 获取二级目录
          const menuList = getMenuList(listObj);
          // 三级目录对象，之前的二级变成三级了
          const childMenusObj = {};
          // 二级目录对象，之前的一级变成二级了
          const topMenusObj = {};
          if (menuList && menuList.length) {
            menuList.forEach((obj) => {
              topMenusObj[obj.rule] = obj;
              obj.children.forEach((sub) => {
                childMenusObj[sub.rule] = sub;
              });
            });
          }
          yield ctx.changeData({
            data: {
              menus: listObj || {},
              ready: true,
              childMenusObj,
              topMenusObj,
              userWarehouseIdList: warehouseIds || [],
              userIP: ip,
            },
          });
          // sessionStorage存储页面路径对象
          if (childMenusObj && Object.keys(childMenusObj).length) {
            sessionStorage.setItem(SESSION_MOT_MENU_OBJ, JSON.stringify(childMenusObj));
          }
          if (listObj) {
            window.localStorage.setItem(LOCAL_USER_MENU, JSON.stringify(listObj));
          }
          // APP_ROUTER_ENTER  判断是h5跳转到app跳转进入 重新设置用户名称【免密登录也需要重新设置】
          if (APP_ROUTER_ENTER || noPwdAuthLocalStorage.getItem() === '1') {
          // eslint-disable-next-line
            localStorage.setItem(LOCAL_USER, JSON.stringify({
              system: ['wms'],
              username: res.info.userName,
              password: '',
              enName: res.info.enName || res.info.userName,
              userNo: res.info.userNo,
              // eslint-disable-next-line no-nested-ternary
              formatName: res.info.enName ?
                (res.info.userNo ? `${res.info.enName}(${res.info.userNo})` : `${res.info.enName}`)
                : res.info.userName,
            }));
          }
          // 增加没选择仓库,跳转到仓库的reloadURl方法
          const hashUrl = window.location.hash.split('#')[1].split('?')[0];
          if (hashUrl === '/login/select-warehouse') {
            // 防止出错 跳回主页
            // TODO 测试一下没有权限的页面有没有问题
            try {
              const reloadUrl = window.location.hash.split('#')[1].split('?reloadurl=')[1];
              if (reloadUrl) {
                yield put(push(reloadUrl));
              } else {
                yield put(push('/main-menu'));
              }
            } catch (e) {
              yield put(push('/main-menu'));
            }
          }
          // 不管在哪个页面，始终要判断用户是否绑定仓库！
          const warehouseName = localStorage.getItem(LOCAL_WAREHOUSE_NAME);
          if (!warehouseName) {
            // 如果是仓库绑定页面，选择仓库之后就没必要再次重定向到仓库绑定页面了
            if (hashUrl === '/login/select-warehouse') {
              yield put(push('/login/select-warehouse'));
            } else {
              yield put(push(`/login/select-warehouse?reloadurl=${hashUrl}`));
            }
          }
        } else {
          console.log(t('获取菜单失败！'));
        }
      } else if ((user.system || []).includes('tms')) {
        yield ctx.changeData({
          data: {
            menus: {},
            ready: true,
          },
        });
      }
    } else {
      clearSomeCookie();
      yield put(push('/login'));
    }
    // 用户埋点
    try {
      conf({
        customAuthInfoSync: {
          name: user.username,
          enName: user.enName,
          emplid: user.userNo,
          systemName: 'wms_pl_pda',
        },
      });
    } catch (e) {
      console.error(e);
    }
  },
  * getNewToken() {
    yield this.changeData({
      data: {
        wsToken: '',
      },
    });
    const res = yield getCloudMessageTokenAPI();
    if (res.code === '0') {
      const { token } = res.info;
      yield this.changeData({
        data: {
          wsToken: token,
        },
      });
    } else {
      modal.error({ title: res.msg });
      yield this.changeData({
        data: {
          wsToken: '',
        },
      });
    }
  },
  // 验证登录是否超时
  * validTimeout(action, ctx) {
    try {
      const res = yield timeoutApi();
      if (res.code === '0') {
        const { msgCode } = res.info;
        if (msgCode === 2) {
          message.warning(res.info.message, 2000);
        } else if (msgCode === 3) {
          message.error(res.info.message, 2000);
          yield ctx.logout();
        }
      }
    } catch (e) {
      console.error(e);
    }
  },
  * logout(action, ctx, put) {
    markStatus('dataLoading'); // 搜索loading状态
    yield delay(5000);
    const res = yield logoutApi();
    if (res.code === '0') {
      // 退出登录，清除轮询是否登录过期的的setInterval
      clearLoginTimeoutInterval();
      // 跳转至登录页面，清除sessionId
      window.document.cookie = 'PHPSESSID=\'\'';
      if (!localStorage.getItem(LOCAL_IS_REMEMBER)) {
        if (localStorage.getItem(LOCAL_USER)) {
          const user = JSON.parse(localStorage.getItem(LOCAL_USER) || '{}');
          user.username = '';
          user.password = '';
          user.enName = '';
          user.formatName = '';
          user.userNo = '';
          localStorage.setItem(LOCAL_USER, JSON.stringify(user));
        }
      }
      localStorage.setItem(LOCAL_WAREHOUSE_NAME, '');
      localStorage.setItem(LOCAL_WAREHOUSE, '');
      clearSomeCookie();
      yield put(push('/login'));
    } else {
      // 提示错误
      modal.error({ content: res.msg || t('退出登录失败') });
    }
  },
  // 保存当前页面路由，用于请求接口时传当前页面路径，进行权限校验
  * saveRouterPage() {
    const { location, locationBeforeTransitions } = yield select((state) => state.routing);
    sessionStorage.setItem(
      SESSION_MOT_ROUTER_PAGE,
      (location || locationBeforeTransitions || {}).page,
    );
  },
};
