/*上传报错页面(弹窗)的样式*/
.UploadErrorContainer {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 90;
  background-color: #ffffff;
}

.tabBar {
  display: flex;
  border-bottom: 1px solid #eee;
}

.tabItem {
  width: 33%;
  text-align: center;
  height: 30px;
  line-height: 30px;
  color: #333e59;
  font-size: 14px;
}

.tabItemActive {
  position: relative;
  background: #ffffff;
  color: #197afa;
  font-weight: bold;
}
.tabItemActive:after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 12px;
  right: 12px;
  height: 3px;
  background: #0059ce;
  border-radius: 4px;
}

.modalPageWrap {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #ffffff;
}

.modalPageFooterWrap {
  position: absolute;
  bottom: 0;
  display: flex;
  justify-content: space-around;
  width: 100%;
  height: 35px;
}
.modalPageFooterBtn {
  width: 30%;
  border: 1px solid #197AFA;
  text-align: center;
  line-height: 35px;
}

.modalMask {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.65);
}

.modalSolveDiv > div {
  padding-left: 15px;
}

.historyTabs {
  display: flex;
}
.historyTabs>span {
  flex: 1;
  text-align: center;
  color: #333e59;
}
.historyTabs>span.activeFocus {
  color: #0059ce;
  position: relative;
}
.activeFocus::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 60px;
  width:30px;
  height:3px;
  background:rgba(0,89,206,1);
  border-radius:2px;
}

 /* --------警告页面css start--------- */

 .alarmPage{
  position: fixed;
  height: 100%;
  width: 100%;
  z-index: 100;
  top: 0;
  left: 0;
  background-color: white;
 }

 .contentView{
  padding: 30px 15px;
  text-align: center;
  
}
.contentTips{
  color: #d9001c;
  word-wrap: break-word;
  word-break: 'break-all';
  max-height: 240px;
  overflow: auto;
  /* line-height: 30px; */
}
.buttonStyle{
  width: 95% !important;
  border-radius: 20px !important;
  height: 35px;
  background-color: #d9001c !important;
}
.buttonStyle:active {
  /* 添加点击状态的样式 */
  background-color: #d9001c !important;
}

.buttonStyle:disabled {
  /* 禁用状态的颜色 */
  background-color: #dcdcdc !important;
  color: #999;
  cursor: not-allowed;
}

.dangerousIcon {
  /* 设置图标的基础样式 */
  font-size: 140px;
  color: #d9001c;
  transition: all 0.3s ease-in-out;
  animation: zoom 2s infinite; /* 两个动画组合 */
}

/* 自动放大图标 */
@keyframes zoom {
  0% {
    transform: scale(1); /* 恢复为默认大小 */
  }
  100% {
    transform: scale(1.1); /* 在5秒时放大到1.5倍 */
  }
}
 /* --------警告页面css end--------- */