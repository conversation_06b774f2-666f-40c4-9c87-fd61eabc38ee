import fetch from './tmsFetch';
// import noErrorFetch from '../../lib/no-error-fetch';
import { camel2Under, under2Camel } from '../../lib/util';
import { sendPostRequest } from '../../lib/public-request';
import { getLang } from '../js';
import { toQueryString } from '../../lib/query-string';
import { fetchSwitch } from '../../lib/fetch-switch';

const sendGetRequest = async (argObj, base = process.env.BASE_URI) => {
  let queryString = '';
  if (argObj.keys) {
    queryString = `?${toQueryString(camel2Under(argObj.keys), camel2Under(argObj.param))}`;
  }
  const prevUrl = `${base}${argObj.url}${queryString}`;
  // 接口开关替换
  const url = await fetchSwitch(prevUrl);
  // tms后台接口国际化传参要求
  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept-Language': getLang(),
    },
  })
    .then(under2Camel);
};
// 获取当前登录用户pda菜单(GET方式)
export const getPdaMenu = () => sendGetRequest({
  url: '/pda/menu?origin_system=1',
}, process.env.WAS_FRONT);

// const sendPostRequestApi = (argObj) => {
//   const url = `${baseUrl}${argObj.url}`;
//   // tms后台接口国际化传参要求
//   return noErrorFetch(url, {
//     method: 'POST',
//     body: argObj.param ? JSON.stringify(camel2Under(argObj.param)) : null,
//     headers: {
//       'Content-Type': 'application/json',
//       'Accept-Language': getLang(),
//       'Accept-Location': getLocation(),
//     },
//   })
//     .then(under2Camel);
// };

// 获取子仓下拉
// export const selectSubWarehouseApi = (argObj) => {
//   const uri = `${process.env.BASE_URI_WMD}/sub_warehouse/select`;
//   return noErrorFetch(uri, {
//     method: 'POST',
//     credentials: 'include',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify(camel2Under(argObj)),
//   })
//     .then(under2Camel);
// };

// 更换获取部门接口
// export const getStaffListApi = (argObj) => {
//   const uri = '/wms/internal/front/staff/query_joint_dept';
//   return noErrorFetch(uri, {
//     method: 'POST',
//     credentials: 'include',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify(camel2Under(argObj)),
//   })
//     .then(under2Camel);
// };

// 根据部门获取小组接口
// export const getGroupListApi = (argObj) => {
//   const uri = '/wms/internal/front/staff/query_group_by_dept';
//   return noErrorFetch(uri, {
//     method: 'POST',
//     credentials: 'include',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify(camel2Under(argObj)),
//   })
//     .then(under2Camel);
// };

// 搜索待我解决
// 也要处理接口报错
// export const querySolveApi = (argObj) => {
//   const uri = `${baseUrl}/query_wait_solve_question`;
//   return noErrorFetch(uri, {
//     method: 'POST',
//     credentials: 'include',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify(camel2Under(argObj)),
//   })
//     .then(under2Camel);
// };

// 获取所有用户
// export const getSystemUsersApi = (argObj) => {
//   const uri = '/wms/internal/andon/front/query/user_list';
//   return noErrorFetch(uri, {
//     method: 'POST',
//     credentials: 'include',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify(camel2Under(argObj)),
//   })
//     .then(under2Camel);
// };

// 获取所有用户
export const getSystemUsersApi = (param) => sendPostRequest({
  url: '/andon/front/query/user_list',
  param,
}, process.env.WMS_INTERNAL);

// 字典明细select查询
// export const selectDictApi = (argObj) => {
//   const uri = `${process.env.BASE_URI_WMD}/dict/select`;
//   return noErrorFetch(uri, {
//     method: 'POST',
//     credentials: 'include',
//     headers: {
//       'Content-Type': 'application/json',
//       'Accept-Language': getLang(),
//       'Accept-Location': getLocation(),
//     },
//     body: JSON.stringify(camel2Under(argObj)),
//   })
//   // .then(data => data.json())
//     .then(under2Camel);
// };

// 字典明细select查询
export const selectDictApi = (param) => sendPostRequest({
  url: '/dict/select',
  param,
}, process.env.BASE_URI_WMD);

// 组员排班-验收登录过期
// export const timeoutApi = (argObj) => {
//   const uri = '/wms/internal/front/member_login_time_limit_config/check_login_time';
//   return noErrorFetch(uri, {
//     method: 'POST',
//     credentials: 'include',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify(camel2Under(argObj)),
//   }, '', true).then(under2Camel);
// };

// 组员排班-验收登录过期
export const timeoutApi = (param) => sendPostRequest({
  url: '/member_login_time_limit_config/check_login_time',
  param,
  noErrorStatus: true,
}, process.env.WMS_INTERNAL_FRONT);

// 退出登录
export const logoutApi = () => sendGetRequest({
  url: '/pda/logout',
}, process.env.WAS_FRONT);

export const selectUserAPI = (param) => sendPostRequest({
  url: '/inf_user/get_user_by_enname',
  param,
}, process.env.WMS_INTERNAL_FRONT);

export const getUserNotReadNumAPI = (param) => {
  const reqData = { terminal: 2, ...param };
  return sendPostRequest({
    url: '/bulletin_board/get_user_not_read_num',
    param: reqData,
  }, process.env.WMS_INTERNAL_FRONT);
};

/**
 * 查看详情
 * @param param
 * @returns
 */
export const queryNoticeDetail = (param) => sendPostRequest({
  url: '/bulletin_board/query_by_id',
  param: { terminal: 2, ...param },
}, process.env.WMS_INTERNAL_FRONT);

/**
 * 已读
 * @param param
 * @returns
 */
export const recordReadUser = (param) => {
  // 兼容后端批量改了接口类型
  const { id } = param;
  let resIds = [];
  if (Array.isArray(id)) {
    resIds = id;
  } else {
    resIds = [id];
  }
  const reqData = { terminal: 2, ...param, id: resIds };
  return sendPostRequest({
    url: '/bulletin_board/record_read_user',
    param: reqData,
  }, process.env.WMS_INTERNAL_FRONT);
};

export const getCloudMessageTokenAPI = () => sendPostRequest({
  url: '/cloud_message/generate_token',
}, process.env.WGS_FRONT_BRIDGE);
