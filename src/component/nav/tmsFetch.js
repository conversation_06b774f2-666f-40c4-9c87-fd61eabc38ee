import { fetch, getTraceInfo } from 'sheinq';
import { t } from '@shein-bbl/react';
import alterErrorResponse from 'lib/fetch/util';
import { clearSomeCookie, clearLoginTimeoutInterval } from '../../lib/util';

// 这个fetch 专门用来处理tms登录获取menu菜单权限的问题
export default (url, options = {}) => {
  const { uberTraceId, traceID } = getTraceInfo();
  return fetch(url, {
    credentials: 'include',
    ...options,
    headers: {
      ...options.headers,
      'uber-trace-id': uberTraceId,
    },
  }).then((res) => {
    const { status } = res;
    if (status === 302) {
      clearLoginTimeoutInterval();
      clearSomeCookie();
      // window.location.href = '#/login';
      const err = new Error();
      err.reason = {
        type: 'no-login',
        status: 302,
        message: t('账号已在其他地方登录或当前登录已过期'),
        callback: () => {
          window.location.href = '#/login';
        },
      };
      err.noSend = true;
      throw err;
    }
    if (status === 403) {
      const userStr = localStorage.getItem('user');
      const user = JSON.parse(userStr);
      if (user && user.system.length && (user.system || []).includes('tms')) {
      // 如果是tms用户，又是wms用户，获取菜单的接口没有权限不必报错，返回 isTmsUser，用于识别
        return 'isTmsUser';
      } else {
        const err = new Error();
        err.reason = {
          type: 'permission-failure',
          status: 403,
          message: alterErrorResponse({
            traceID,
            url,
            msg: `${t('您没有相关权限，url地址')}：${url}`,
          }),
        };
        err.noSend = true;
        throw err;
      }
    }
    if (status !== 200 && status !== 403 && status !== 302 && status !== 500) {
      const err = new Error();
      err.reason = {
        type: 'network-failure',
        status,
        message: alterErrorResponse({
          traceID,
          url,
          msg: t('服务器响应出错,请刷新页面'),
        }),
      };
      throw err;
    }
    return res.json().then((r) => {
      if (r.code && r.code !== '0') {
        // 修改报错的返回信息
        r.msg = alterErrorResponse({
          traceID, // 返回数据
          url: res.url, // 请求的url
          msg: r.msg, // traceID
        });
      }
      return r;
    });
  });
};
