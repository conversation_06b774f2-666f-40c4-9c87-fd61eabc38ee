import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { i18n, t } from '@shein-bbl/react';
import LatestVersionDetection from '@shein-components/LatestVersionDetection';
import WaterMarkBg from '@shein-components/WaterMarkBg';
import { jumpOldPage } from '@wms/config-roll-back';
import { LOCAL_USER } from 'lib/storage';
import { LoadMore } from 'react-weui';
import moment from 'moment/moment';
// import AlarmNoticePage from './jsx/alarm-notice-page';
import store from './reducers';
import mainMenuStore from '../main-menu/reducers';
import {
  getParentHref, getVersion, setLoginTimeoutInterval, clearLoginTimeoutInterval, getWarehouseId,
} from '../../lib/util';
import { pages } from '../common';
import { showToast } from '../common/common';
import { setWarehouseName, getUser } from '../js';
import DrawerPage from '../drawer-page/view';
import styles from '../style.css';
import { prohibitPageUrlArr } from '../common/constant';
import MessageWrapper from './jsx/message-wrapper';
import DragCircle from '../common/drag-circle';
import UserIntroPage from '../user-intro-page/view';
import IntroContext from '../user-intro-page/IntroContext';
import NetworkBuriedPoint from './jsx/network-buried-point';
import AlitaDataRegister from './jsx/alita-data-register';

const { PageContainer } = pages;

// 监听是否有网络
const NUMBER = 2;// 网络不稳定[一分钟内断开2次]
let disconnectNumber = NUMBER;
let lastDisconnectTime = null; // 上一次断开的时间
const updateUserOnline = (isOnline) => {
  showToast(isOnline ? t('已连接上网络') : t('断开网络'));
  // 检测网络不稳定情况
  if (!isOnline) {
    const nowTime = new Date().getTime();
    if (lastDisconnectTime === null) {
      lastDisconnectTime = nowTime;
    } else if ((nowTime - lastDisconnectTime) / 1000 < 60) {
      disconnectNumber--;
    } else {
      lastDisconnectTime = null;
      disconnectNumber = NUMBER;
    }
    if (disconnectNumber <= 1) {
      showToast(t('网络不稳定'));
    }
  }
  store.changeData({ data: { isOnline } });
  mainMenuStore.changeData({ data: { isOnline } });
};

class Container extends Component {
  constructor(props) {
    super(props);
    this.state = {
      versionDetectContainer: null,
    };
    this.versionDetectRef = React.createRef();
  }

  componentDidMount() {
    setWarehouseName();
    store.init();
    /**
     * 根据localStorage里存的菜单来控制返回功能
     */
    this.addHistory = (e) => {
      // 微前端劫持触发的不执行，防止死循环
      if (e?.singleSpa) {
        return;
      }
      const curUrl = document.URL;
      const parentHref = getParentHref();
      // eslint-disable-next-line no-restricted-globals
      history.pushState(null, null, `#${parentHref}`);
      // eslint-disable-next-line no-restricted-globals
      history.pushState(null, null, curUrl);
      // 保存当前页面对应路由：切换页面时; 得用延时异步，不然获取到的是上一个的路由路径
      setTimeout(() => store.saveRouterPage(), 1000);
    };
    this.addHistory();

    window.addEventListener('popstate', this.addHistory);
    // 监听是否有网络
    window.addEventListener('online', () => {
      updateUserOnline(true);
    });
    window.addEventListener('offline', () => {
      updateUserOnline(false);
    });
    // 先清除定时器
    clearLoginTimeoutInterval();
    // 先判断是否登录，如果登录了，就轮询是否登录过期
    const isLongined = localStorage.getItem(LOCAL_USER) && JSON.parse(localStorage.getItem(LOCAL_USER)).system && JSON.parse(localStorage.getItem(LOCAL_USER)).system.includes('wms');
    if (isLongined) {
      store.validTimeout();
      setLoginTimeoutInterval(store.validTimeout.bind(this));
    }
    // 判断是否要跳转到旧页面
    jumpOldPage('MOT_NAV_PAGE_ROLLBACK');
  }

  componentDidUpdate() {
    // 判断是否要跳转到旧页面
    jumpOldPage('MOT_NAV_PAGE_ROLLBACK');
  }

  componentWillUnmount() {
    clearLoginTimeoutInterval();
  }

  render() {
    const {
      ready,
      showDrawer,
      showUserIntro,
      showIntro,
      showImgs,
      pageStore,
      pdaGuidelineData,
      userWarehouseIdList,
      userIP,
      children,
      wattConfigObj,
      // showNoticePage,
      // showNoticeDataId,
      wsToken,
    } = this.props;
    // 根据页面路径获取对应指引视频等数据
    const pageKey = window.location.hash.replace('#', '/new-pda');
    const pageData = pdaGuidelineData && pdaGuidelineData[pageKey];
    // eslint-disable-next-line react/destructuring-assignment
    const { pathname } = this.props.location;
    if (!ready && pathname !== '/login' && pathname !== '/login/select-warehouse') {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          height: '100vh',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        >
          <LoadMore loading />
          <p style={{ color: '#fff' }}>
            The application is currently loading...<br />
            We appreciate your patience and will have everything ready shortly<br />
            Thank you for waiting<br />
          </p>
        </div>
      );
    }
    let user = getUser();
    const minH = (window.innerHeight - 60) || 450;
    const style = {
      fontSize: 16,
      minHeight: minH,
    };
    // if (user) {
    //   const wm = waterMark(user.username);
    //   style.backgroundImage = `url(${wm})`
    // }
    if (!(user && user.system && user.system.length)) {
      user = { username: '' };
    }
    const version = getVersion();
    // 滑动事件
    const forbiddenUrl = [
      '/login',
      '/login/select-warehouse',
    ];
    let x = 0;
    let y = 0;
    const touchStart = () => {
      if (forbiddenUrl.find((i) => i === pathname)) {
        return;
      }
      x = window.event.targetTouches['0'].clientX;
      y = window.event.targetTouches['0'].clientY;
    };

    const hideDrawerFunc = () => {
      store.changeData({ data: { showDrawer: false, showLangPicker: false } });
      mainMenuStore.changeData({ data: { showDrawer: false } });
      // 展示用户指引界面：主菜单和子菜单右滑不显示；没有主流程且没有指引视频图片 也不展示
      if (!['#/main-menu', '#/sub-menu'].includes(window.location.hash) && (pageStore || pageData)) {
        store.changeData({ data: { showUserIntro: true } });
      }
    };

    const showDrawerFunc = () => {
      if (!showImgs) {
        // 展示左侧功能页，并隐藏用户指引界面
        store.changeData({
          data: {
            showDrawer: true, showUserIntro: false, showIntro: false, pageStore: null,
          },
        });
        mainMenuStore.changeData({ data: { showDrawer: true } });
      }
    };
    const touchEnd = () => {
      if (forbiddenUrl.find((i) => i === pathname)) {
        return;
      }
      if (
        window.event.changedTouches['0'].clientX - x > 50 &&
        Math.abs(window.event.changedTouches['0'].clientY - y) < 30
      ) {
        showDrawerFunc();
      }
      if (
        window.event.changedTouches['0'].clientX - x < -30 &&
        Math.abs(window.event.changedTouches['0'].clientY - y) < 50
      ) {
        hideDrawerFunc();
      }
    };
    // 需要禁止下拉刷新的页面，给body设置overscrollBehaviorY = 'contain'；
    if (prohibitPageUrlArr.indexOf(pathname) !== -1) {
      document.getElementsByTagName('body')[0].style.overscrollBehaviorY = 'contain';
    } else {
      document.getElementsByTagName('body')[0].style.overscrollBehaviorY = '';
    }
    return (
      <PageContainer>
        {/* alita数据注册 */}
        <AlitaDataRegister {...this.props} pageData={pageData} />
        <WaterMarkBg text={user.formatName ? `${user.formatName}${version} ${moment().format('YYYY-MM-DD HH:mm:ss')}` : ''} />
        <IntroContext.Provider
          // eslint-disable-next-line react/jsx-no-constructed-context-values
          value={{
            showIntroVal: showIntro,
            pageStoreVal: pageStore,
            changePageStore: (val) => store.changeData({ data: { pageStore: val } }),
          }}
        >
          <div
            className={styles.bg}
            style={style}
            onTouchStart={() => touchStart()}
            onTouchEnd={() => touchEnd()}
          >
            <DrawerPage
              {...this.props}
              show={showDrawer}
            />
            <UserIntroPage show={showUserIntro} pageData={pageData} pageStore={pageStore} />
            {
              window.location.hash === '#/main-menu' && (
                <DragCircle
                  ifOpenMotStandardApp
                />
              )
             }
            {/* 版本探测组件 */}
            <div
              ref={(dom) => {
                // eslint-disable-next-line react/destructuring-assignment
                if (!this.state.versionDetectContainer) {
                  this.setState({ versionDetectContainer: dom });
                }
              }}
              style={{ margin: '0' }}
            >
              {
                // eslint-disable-next-line react/destructuring-assignment
                this.state.versionDetectContainer && (
                  <LatestVersionDetection
                    // eslint-disable-next-line react/destructuring-assignment
                    container={this.state.versionDetectContainer}
                    ref={this.versionDetectRef}
                    headers={{ 'W-Version-Detection': 1, warehouseCode: getWarehouseId() }}
                    interval={30}
                    onError={(e) => console.error(e)}
                    className={styles.versionTipLayout}
                    footer={false}
                    onDetect={() => {
                      window.hasLatestVersion = true;
                    }}
                  >
                    <div
                      className={styles.versionTip}
                      onClick={() => {
                        if (this.versionDetectRef.current) {
                          this.versionDetectRef.current.chanelRefresh();
                        } else {
                          // 兜底
                          window.location.reload();
                        }
                      }}
                    >
                      {t('存在新版本，是否加载')}？
                      <span className={styles.refreshBtn}>{t('点击刷新')}</span>
                    </div>
                  </LatestVersionDetection>
                )
              }
            </div>
            {children}
            {
              !['#/login', '#/login/select-warehouse'].includes(window.location.hash) && !!userWarehouseIdList?.length && !!Object.keys(wattConfigObj).length && (<NetworkBuriedPoint warehouseIdList={userWarehouseIdList} ip={userIP} config={wattConfigObj} />)
            }
            {/* 系统公告/消息推送 */}
            <div className={styles.messageIcon} id="messageIconWrap" style={{ zIndex: 9 }}>
              {wsToken && <MessageWrapper {...this.props} />}
            </div>
            {/* <AlarmNoticePage showNoticeDataId={showNoticeDataId} /> */}
          </div>
        </IntroContext.Provider>

      </PageContainer>
    );
  }
}

Container.propTypes = {
  ready: PropTypes.bool.isRequired,
  showDrawer: PropTypes.bool.isRequired,
  showUserIntro: PropTypes.bool.isRequired,
  showIntro: PropTypes.bool.isRequired,
  showImgs: PropTypes.bool.isRequired,
  pageStore: PropTypes.shape(),
  pdaGuidelineData: PropTypes.shape(),
  userWarehouseIdList: PropTypes.arrayOf(PropTypes.number),
  userIP: PropTypes.string,
  children: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  location: PropTypes.shape(),
  wattConfigObj: PropTypes.shape(),
  // showNoticePage: PropTypes.bool.isRequired,
  showNoticeDataId: PropTypes.string,
  wsToken: PropTypes.string,

};

const stateToProps = (state) => state.nav;
export default connect(stateToProps)(i18n(Container));
