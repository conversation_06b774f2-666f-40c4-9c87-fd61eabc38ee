import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Header, SwitchBar, SelectList,
  SearchInput, Gapbar,
} from 'common';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      switchVal,
      selectVal,
      searchVal,
    } = this.props;
    return (
      <div style={{ overscrollBehavior: 'contain' }}>
        <Header title={t('feedback组件-demo页面')} />
        <SwitchBar
          title={t('车牌信息')}
          value={switchVal}
          list={[{ val: 1, text: t('扫码') }, { val: 2, text: t('手写') }]}
          onChange={(val, item) => {
            store.changeData({ switchVal: val });
            console.log('switch', val, item);
          }}
        />
        <SearchInput
          autoSearch
          value={searchVal}
          onClear={() => store.changeData({ searchVal: '' })}
          onChange={e => store.changeData({ searchVal: e.target.value })}
          onSearch={v => console.log(88888, v)}
        />
        <Gapbar />
        <SelectList
          value={selectVal}
          listKeys={['subWarehouseId', 'subWarehouseName']}
          list={[...new Array(6).keys()].map(v => ({ subWarehouseId: v, subWarehouseName: `${t('安博')}${v}` }))}
          onChange={(val, item) => {
            store.changeData({ selectVal: val });
            console.log('switch', val, item);
          }}
        />
      </div>
    );
  }
}

Container.propTypes = {
  switchVal: PropTypes.number,
  selectVal: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  searchVal: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
};

export default i18n(Container);
