import { t } from '@shein-bbl/react';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages/components/form';
import { CellsTitle } from 'react-weui/build/packages/components/cell';
import Icon from '@shein-components/Icon';
import store from './reducers';
import {
  Header,
  Tag,
  Check,
  TimePicker,
  List,
  SplitBar,
  DatePicker,
  FocusInput,
  Popo,
} from '../../common';
import styles from '../style.css';

const tags = [t('张姗姗'), t('孙午餐'), t('小混子'), t('熊孩子'), t('亚历山大'), t('跳跳糖'), t('冲鸭')];
const nowDate = new Date();
const rows = [
  [
    {
      title: '',
      render: 'name',
    },
    {
      title: '',
      render: (v) => (v.isFree ? <span className={styles.greenDot}>{null}</span> : null),
    },
  ],
  [
    {
      title: '',
      render: 'date',
    },
    {
      title: '',
      render: 'type',
    },
  ],
];
const data = [
  {
    id: 1,
    name: t('王悟武'),
    isFree: true,
    date: '09/19 08:00-18:00',
    type: t('上架'),
    checked: true,
  },
  {
    id: 2,
    name: t('詹三'),
    isFree: false,
    date: '- -',
    type: t('尚未安排'),
  },
  {
    id: 3,
    name: t('詹三'),
    isFree: false,
    date: '09/19 08:00-20:00',
    type: t('上架'),
    checked: true,
  },
  {
    id: 4,
    name: t('达四'),
    isFree: false,
    date: '09/19 08:00-20:00',
    type: t('上架'),
  },
  {
    id: 5,
    name: t('达四'),
    isFree: false,
    date: '09/19 08:00-20:00',
    type: t('上架'),
  },
  {
    id: 6,
    name: t('达四'),
    isFree: false,
    date: '09/19 08:00-20:00',
    type: t('上架'),
    checked: true,
    disabled: true,
  },
  {
    id: 7,
    name: t('小五'),
    isFree: false,
    date: '09/18 22:00-09/19 08:00',
    type: t('拣货'),
    disabled: true,
  },
];

class Container extends Component {
  constructor(props) {
    super(props);
    this.state = {
      checkedList: [1, 2, 3, 4].map((v) => ({ text: v, checked: v % 2 === 0, disabled: v >= 3 })),
      isAllChecked: false,
      startTimeText: '',
      endTimeText: '',
      startTimeSelected: [nowDate.getHours(), nowDate.getMinutes()],
      endTimeSelected: [nowDate.getHours(), nowDate.getMinutes()],
      startShowPicker: false,
      endShowPicker: false,
      listAllChecked: false,
      listData: data,
      showDatePicker: false,
      dateSelected: '2019-02-07',
      pickedItem1: { value: 2, display: t('选项三') },
      pickedItem2: { value: '2026-05-06 22:10', display: '2026-05-06 22:10' },
      pickedItem3: {},
      pickedItem5: { value: '2026-05-06 22:10'.match(/\d+/g), display: '2026-05-06 22:10' },
    };
  }

  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
    } = this.props;
    const {
      checkedList,
      isAllChecked,
      startTimeText,
      endTimeText,
      startTimeSelected,
      endTimeSelected,
      startShowPicker,
      endShowPicker,
      listData,
      listAllChecked,
      showDatePicker,
      dateSelected,
      pickedItem1,
      pickedItem2,
      pickedItem3,
      pickedItem5,
    } = this.state;

    // 处理tags的选中状态
    const handleItemsChecked = (v) => {
      checkedList.forEach((i) => {
        if (!i.disabled) {
          i.checked = v;
        }
      });
    };
    const handleAllItemChecked = () => {
      const allChecked = checkedList.every((i) => i.disabled || i.checked);
      this.setState({ isAllChecked: allChecked });
    };
    // 日期格式小于10则在前面填充0，且统一变成字符串
    /* eslint-disable */
    const fillZero = num => num < 10 ? `0${num}` : String(num);
    /* eslint-enable */
    // 获取日期字符串
    const getDateText = (timeArr, isNextDay = false) => {
      let nD = nowDate;
      if (isNextDay) {
        nD = new Date(nowDate.getTime() + 86400000);
      }
      const monthNum = nD.getMonth() + 1;
      return `${nD.getFullYear()}-${fillZero(monthNum)}-${fillZero(nD.getDate())} ${fillZero(timeArr[0])}:${fillZero(timeArr[1])}`;
    };
    // 处理list的选中状态
    const handleListAllChecked = (v) => {
      listData.forEach((i) => {
        if (!i.disabled) {
          i.checked = v;
        }
      });
      this.setState({ listData: [...listData] });
    };
    return (
      <div style={{ marginBottom: 0 }}>
        <Header title={headerTitle} />
        <CellsTitle>{t('tag与tags')}</CellsTitle>
        <Form>
          <Tag type="red">red</Tag><br />
          <Tag dataSource={tags} />
        </Form>
        <SplitBar />
        <CellsTitle>
          check
        </CellsTitle>
        <Form style={{ paddingBottom: 6 }}>
          <div
            onClick={() => {
              this.setState({ isAllChecked: !isAllChecked });
              handleItemsChecked(!isAllChecked);
            }}
            style={{ marginBottom: 10 }}
          >
            <Check
              checked={isAllChecked}
            />{t('全选')}
          </div>
          {
            checkedList.map((i) => (
              <span
                key={i.text}
                style={{ marginRight: 18 }}
                onClick={() => {
                  if (i.disabled) {
                    return;
                  }
                  i.checked = !i.checked;
                  handleAllItemChecked();
                }}
              >
                <Check
                  checked={i.checked}
                  disabled={i.disabled}
                />
                <span style={{ marginLeft: 10 }}>{i.text}</span>
              </span>
            ))
          }
        </Form>
        <SplitBar>{t('灰色间隔组件')}</SplitBar>
        <CellsTitle>
          TimePicker
        </CellsTitle>
        <div>
          <div className={styles.pickItem} onClick={() => this.setState({ startShowPicker: true })}>
            <span>{t('开始时间')}</span>
            <div>{startTimeText}</div>
            <Icon name="arr-right" />
          </div>
          <div className={styles.pickItem} onClick={() => this.setState({ endShowPicker: true })}>
            <span>{t('结束时间')}</span>
            <div>{endTimeText}</div>
            <Icon name="arr-right" />
          </div>
        </div>
        <div>
          <div className={styles.pickItem} onClick={() => this.setState({ showDatePicker: true })}>
            <span>{t('选择日期')}</span>
            <div>{dateSelected}</div>
            <Icon name="arr-right" />
          </div>
        </div>
        <DatePicker
          show={showDatePicker}
          selected={dateSelected}
          onCancel={() => {
            this.setState({ showDatePicker: false });
          }}
          onSelected={(valArr) => {
            this.setState({
              showDatePicker: false,
              dateSelected: valArr.join('-'),
            });
          }}
        />

        <TimePicker
          show={startShowPicker}
          selected={startTimeSelected}
          onCancel={() => this.setState({ startShowPicker: false })}
          onSelected={((timeArr) => this.setState({
            startTimeSelected: timeArr,
            startShowPicker: false,
            startTimeText: getDateText(timeArr),
          }))}
        />
        <TimePicker
          show={endShowPicker}
          selected={endTimeSelected}
          onCancel={() => this.setState({ endShowPicker: false })}
          onSelected={(timeArr) => {
            let isNextDay = false;
            // eslint-disable-next-line max-len
            if (timeArr[0] < startTimeSelected[0] || (timeArr[0] === startTimeSelected[0] && timeArr[1] === startTimeSelected[1])) {
              isNextDay = true;
            }
            this.setState({
              endTimeSelected: timeArr,
              endShowPicker: false,
              endTimeText: getDateText(timeArr, isNextDay),
            });
          }}
        />
        <FocusInput
          arrow
          cellstyle={{ borderBottom: '1px solid #e8ebf0', margin: '0 10px' }}
          className="uniqueClassName"
          value={pickedItem1.display}
          onClick={(e) => {
            Popo.picker({
              el: '.uniqueClassName',
              wheels: [
                {
                  infinite: true,
                  selected: pickedItem1.value,
                  data: [
                    { value: 0, display: t('选项一') },
                    { value: 1, display: t('选项二') },
                    { value: 2, display: t('选项三') },
                    { value: 3, display: t('选项四') },
                    { value: 4, display: t('选项五') },
                    { value: 5, display: t('选项六') },
                    { value: 6, display: t('选项七') },
                  ],
                },
              ],
              save: (rps) => {
                this.setState({
                  pickedItem1: rps.result[0],
                }, () => {
                  console.info(t('选择的选项'), this.state.pickedItem1);
                });
              },
            });
          }}
        >
          <label>{t('选择项')}</label>
        </FocusInput>
        <FocusInput
          arrow
          cellstyle={{ borderBottom: '1px solid #e8ebf0', margin: '0 10px' }}
          value={pickedItem2.display}
          className="uniqueClassName2"
          onClick={(e) => {
            Popo.dateTime({
              el: '.uniqueClassName2',
              infinite: true,
              // time: false,
              save: (rps, b) => {
                this.setState({
                  pickedItem2: {
                    value: rps,
                    display: rps,
                  },
                }, () => {
                  console.info(t('选择的时间'), this.state.pickedItem2);
                });
              },
            });
          }}
        >
          <label>{t('选择时间')}</label>
        </FocusInput>
        <FocusInput
          arrow
          cellstyle={{ borderBottom: '1px solid #e8ebf0', margin: '0 10px' }}
          value={pickedItem3.display}
          className="uniqueClassName3"
          onClick={(e) => {
            Popo.dateTime({
              el: '.uniqueClassName3',
              infinite: true,
              date: false,
              save: (rps, b) => {
                this.setState({
                  pickedItem3: {
                    value: rps,
                    display: rps,
                  },
                }, () => {
                  console.info(t('选择的'), this.state.pickedItem3);
                });
              },
            });
          }}
        >
          <label>{t('选择的时分显示时分')}</label>
        </FocusInput>
        <FocusInput
          arrow
          cellstyle={{ borderBottom: '1px solid #e8ebf0', margin: '0 10px' }}
          className="uniqueClassName5"
          value={pickedItem5.display}
          onClick={(e) => {
            console.info('pickedItem5', pickedItem5);
            Popo.picker({
              el: '.uniqueClassName5',
              wheels: [
                {
                  selected: pickedItem5.display ? pickedItem5.display.split(' ')[1].split(':')[0] : '',
                  infinite: true,
                  data: new Array(24).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                },
                {
                  selected: pickedItem5.display ? pickedItem5.display.split(' ')[1].split(':')[1] : '',
                  infinite: true,
                  data: new Array(60).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                },
              ],
              save: (rps) => {
                this.setState({
                  pickedItem5: { display: `${pickedItem5.display ? pickedItem5.display.split(' ')[0] : ''} ${rps.result.map((v) => (v.value)).join(':')}`, value: rps.result.map((v) => (v.value)) },
                }, () => {
                  console.info(t('选择的选项'), this.state.pickedItem5);
                });
              },
            });
          }}
        >
          <label>{t('选择时分获取年月日加时分')}</label>
        </FocusInput>
        <button
          className="uniqueClassName4" onClick={() => {
            Popo.dateTime({
              el: '.uniqueClassName4',
              infinite: true,
              date: false,
              save: (rps, b) => {
                this.setState({
                  pickedItem3: {
                    value: rps,
                    display: rps,
                  },
                }, () => {
                  console.info(t('选择的时间'), this.state.pickedItem3);
                });
              },
            });
          }}
        >{t('按钮点击调用')}
        </button>
        <CellsTitle>
          {t('List修改')}
          <span
            onClick={() => {
              this.setState({ listAllChecked: !listAllChecked });
              handleListAllChecked(!listAllChecked);
            }}
            style={{ marginLeft: 10 }}
          >
            <Check checked={listAllChecked} />
            {t('全选')}
          </span>
        </CellsTitle>
        <List
          rows={rows}
          data={listData}
          checkbox
          rowStyleOrClass={(item) => {
            if (item.disabled && !item.checked) {
              return styles.disabledItem;
            }
            return '';
          }}
          onSelected={(selectedArr) => {
            // eslint-disable-next-line max-len
            const cheked = !!selectedArr.length && selectedArr.length === listData.filter((i) => !i.disabled || (i.disabled && i.checked)).length;
            this.setState({ listAllChecked: cheked });
          }}
          style={{ height: 360, overflowY: 'auto' }}
        />
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
};

export default Container;
