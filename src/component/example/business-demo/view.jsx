import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Button } from 'react-weui/build/packages';
import {
  Header, Battery, TargetBar,
  Gapbar, TargetModal,
} from 'common';
import store from './reducers';


class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const { showModal, modalInput, yesterdayProfit } = this.props;
    return (
      <div style={{ overscrollBehavior: 'contain', backgroundColor: '#fff' }}>
        <Header title={t('business组件-business-demo页面')} />
        <TargetBar
          targetProfit={1000}
          haveProfit={200.882}
          targetProcess={39.8}
          userRank={25}
          totalRank={1239}
        />
        <Gapbar />
        <TargetBar
          userRank={25}
          totalRank={1239}
        />
        <Battery
          style={{ margin: 8 }}
          haveProfit={200.88}
          targetProcess={40}
        />
        <Button
          onClick={() => store.changeData({ showModal: true })}
        >
          {t('展示弹窗')}
        </Button>
        <TargetModal
          show={showModal}
          yesterdayProfit={yesterdayProfit}
          target={modalInput}
          onChange={(val) => {
            console.log('change', val);
            store.changeData({ modalInput: val });
          }}
          onOk={(val) => {
            store.changeData({ showModal: false });
            console.log('ok', val, modalInput);
          }}
        />
      </div>
    );
  }
}

Container.propTypes = {
  showModal: PropTypes.bool,
  yesterdayProfit: PropTypes.number,
  modalInput: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
};

export default i18n(Container);
