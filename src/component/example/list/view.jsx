import React, { Component } from 'react';
import PropTypes from 'prop-types';
import CellsMenu from '../../common/cells-menu';
import Header from '../../common/header';
import Footer from '../../common/footer';

const cells = [
  {
    title: 'test1',
    href: '#/example/test1',
    footer: '123',
    showRedDotted: true,
  },
  {
    title: 'test2',
    href: '#/example/test2',
    showRedDotted: true,
  },
  {
    title: 'test3',
    href: '#/example/test3',
  },
  {
    title: 'test4',
    href: '#/example/test4',
  },
  {
    title: 'test5',
    href: '#/example/test5',
  },
];

  class Container extends Component {
  render() {
    const {
      dispatch,
    } = this.props;
    return (
      <div>
        <Header title="测试页面" />
        <CellsMenu
          cells={cells}
        />
        <Footer dispatch={dispatch} />
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
};

export default Container;

