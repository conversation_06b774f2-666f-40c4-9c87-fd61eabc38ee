import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';

const defaultState = {
  username: '',
  password: '',
  location: '',
  num: 0,
  demoSelect: '',
  demoSelectId: '',
  showSelect: false,
  list: [{ items: [{ label: 'name1', value: '1' }, { label: 'name2', value: '2' }, { label: 'name3', value: '3' }]}],
};

export default {
  defaultState,
  init: () => defaultState,
  changeData (draft, action) {
    assign(draft, action.data);
  }
}

