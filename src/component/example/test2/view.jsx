import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import { Form } from 'react-weui/build/packages';
import store from './reducers';
import List from '../../common/list';
import Header from '../../common/header';
import Footer from '../../common/footer';
import FooterBtn from '../../common/footer-btn';
import PickersSearch from '../../common/pickers-search';
import { classFocus } from '../../../lib/util';

const rows = [
  [
    {
      title: '库位',
      render: 'location'
    },
    {
      title: '短拣数',
      render: 'num'
    }
  ],
  [
    {
      title: 'SKC',
      render: 'skc'
    }
  ],
  [
    {
      title: '尺码',
      render: 'size'
    },
    {
      title: '姓名',
      render: 'name'
    },
    {
      title: '年龄',
      render: 'age'
    },
    {
      title: '处理',
      render: (v, i) => {
        return <span onClick={() => { console.log('处理')}}>处理</span>
      }
    }
  ]
]

const data = [
  {
    id: 1,
    skc: '123',
    location: 'kuwei1',
    size: 'XL',
    num: 12,
    name: 'wp',
    age: 28
  },
  {
    id: 2,
    skc: '456',
    location: 'kuwei2',
    size: 'M',
    num: 13,
    name: 'wp',
    age: 28,
  },
  {
    id: 3,
    skc: '789',
    location: 'kuwei3',
    size: 'XXL',
    num: 14,
    name: 'wp',
    age: 28
  },
]


class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      num,
      dispatch,
      demoSelect,
      demoSelectId,
      list,
      showSelect,
    } = this.props;
    return (
      <div>
        <Header title="测试页面">
          <div onClick={() => console.log(111)}>
            <Icon name="shouhuoruku" />按钮1
          </div>
          <div onClick={() => console.log(222)}>
            <Icon name="shouquan" />按钮2
          </div>
        </Header>
        <List
          rows={rows}
          data={data}
          style={{ height: 300, overflowY: 'auto' }}
        />
        <Form>
          <PickersSearch
            label={'服务商产品'}
            value={demoSelect}
            pickerData={list}
            show={showSelect}
            onClick={() => {
              store.changeData({ data: { showSelect: true } });
              classFocus('Input');
            }}
            onCancel={() => store.changeData({ data: { showSelect: false } })}
            onOk={(value, name) => {
              store.changeData({ data: { demoSelect: name, demoSelectId: value, showSelect: false } });
            }}
          />
        </Form>
        <Footer
          dispatch={dispatch}
          beforeBack={(back) => {
            console.log('返回');
            back();
          }}
        >
          <FooterBtn
            disabled
            onClick={() => console.log('查看')}
          >
            查看
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  num: PropTypes.number.isRequired,
  dispatch: PropTypes.func.isRequired,
  demoSelect: PropTypes.string.isRequired,
  demoSelectId: PropTypes.string.isRequired,
  showSelect: PropTypes.bool.isRequired,
  list: PropTypes.arrayOf(PropTypes.shape()).isRequired,
};

export default Container;
