import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import moment from 'moment';
import { Form, Dialog } from 'react-weui/build/packages';
import {
  message,
  Footer,
  FooterBtn,
  pages,
  Header,
  FocusInput,
} from 'common';
import store from './reducers';

const { View } = pages;

const getDate = (value) => {
  const numbers = value
    .replace(/\s+/g, '')
    .split('')
    .filter((x) => x !== '-');

  if (numbers.length > 6) {
    return `${numbers.slice(0, 4).join('')}-${numbers.slice(4, 6).join('')}-${numbers.slice(6, 8).join('')}`;
  }
  if (numbers.length > 4) {
    return `${numbers.slice(0, 4).join('')}-${numbers.slice(4).join('')}`;
  }
  return numbers.slice(0).join('');
};

class EditDate extends Component {
  constructor(props) {
    super(props);
    this.state = {
      sum: 10, // 盘点总数
      goodsList: [
        {
          productionDate: '2019-01-07',
          expiringDate: '2019-01-07',
          shelfLifeDays: 120,
          canEditNum: false,
          number: 10,
        },
      ],

      showDatePicker: false,
      datePickerSelected: '',
      datePickerData: {
        index: 0,
        type: '',
      },
    };
  }

  render() {
    const {
      sum,
      goodsList,
      showDatePicker,
      datePickerSelected,
      datePickerData,
    } = this.state;

    // 修改日期
    const handleDatePickerSelected = () => {
      const { index, type } = datePickerData;
      // const arr = [...goodsList];
      const arr = JSON.parse(JSON.stringify(goodsList));
      const item = arr[index];
      item[type] = datePickerSelected;

      // 日期校验
      if (!moment(datePickerSelected).isValid()) {
        message.error(t('请输入正确的日期'));
        return;
      }

      // 年份限制
      if (Number(moment(datePickerSelected).year()) > 2100 ||
        Number(moment(datePickerSelected).year()) < 1970) {
        message.error(t('日期不能超过2100年或者小于1970年'));
        return;
      }

      switch (type) {
        case 'productionDate':
          item.expiringDate = moment(item.productionDate).add(item.shelfLifeDays, 'd').format('YYYY-MM-DD');
          break;
        case 'expiringDate':
          item.productionDate = moment(item.expiringDate).subtract(item.shelfLifeDays, 'd').format('YYYY-MM-DD');
          break;
        default:
          break;
      }

      const productionDates = arr.map((x) => x.productionDate);

      if (new Set(productionDates).size !== productionDates.length) {
        message.error(t('请输入不同的生产日期'));
        return;
      }

      item.canEditNum = true;
      arr[index] = item;

      this.setState({
        showDatePicker: false,
        goodsList: arr,
      });
    };

    // 修改数量
    const handleChangeNumber = (index) => {
      const arr = [...goodsList];
      const curSum = arr.map((x) => Number(x.number)).reduce(
        (preVal, curVal) => preVal + curVal,
        0,
      );

      if (curSum > sum) {
        message.error(t('不允许超过盘点数量，如有必要，请重新盘点'));
        return;
      }

      if (index === goodsList.length - 1 && sum !== Number(curSum)) {
        // 新增
        arr.push({
          ...arr[index],
          canEditNum: false,
          number: String(sum - Number(curSum)),
        });
        this.setState({
          goodsList: arr,
        });
      } else {
        // 修改后的差值数量，累加到最后一行
        const num = Number(arr[goodsList.length - 1].number);
        arr[goodsList.length - 1].number = sum - (curSum - num);

        this.setState({
          goodsList: arr,
        });
      }
    };

    // 点击确认
    const handleSubmit = () => {
      // todo
    };

    return (
      <View>
        <Header title={t('修改效期')} />
        {
          goodsList.map((item, index) => (
            <div key={index} style={{ marginBottom: '10px' }}>
              <div style={{ fontSize: '14px', padding: '0 15px' }}>
                <div
                  onClick={() => this.setState({
                    showDatePicker: true,
                    datePickerSelected: '',
                    datePickerData: {
                      index,
                      type: 'productionDate',
                    },
                  })}
                >
                  <span>{t('生产日期')}</span>
                  <div>{item.productionDate}</div>
                </div>
              </div>
              <div style={{ fontSize: '14px', padding: '0 15px' }}>
                <div
                  onClick={() => this.setState({
                    showDatePicker: true,
                    datePickerSelected: '',
                    datePickerData: {
                      index,
                      type: 'expiringDate',
                    },
                  })}
                >
                  <span>{t('到期日期')}</span>
                  <div>{item.expiringDate}</div>
                </div>
              </div>
              <Form>
                <FocusInput
                  label={t('盘点数量')}
                  value={item.number}
                  placeholder={t('请输入')}
                  disabled={!item.canEditNum}
                  onChange={(e) => {
                    const arr = [...goodsList];
                    arr[index].number = e.target.value;

                    this.setState({
                      goodsList: arr,
                    });
                  }}
                  onPressEnter={() => {
                    handleChangeNumber(index);
                  }}
                />
              </Form>
            </div>
          ))
        }

        <Dialog
          title={t('修改日期')}
          show={showDatePicker}
          buttons={[{
            type: 'default',
            label: t('关闭'),
            onClick: () => {
              this.setState({ showDatePicker: false });
            },
          }, {
            type: 'primary',
            label: t('确定'),
            onClick: () => {
              handleDatePickerSelected();
            },
          }]}
        >
          <FocusInput
            value={datePickerSelected}
            placeholder={t('请输入日期')}
            onChange={(e) => {
              const date = getDate(e.target.value);

              if (date.length !== 10 && date.length <= datePickerSelected.length) {
                this.setState({
                  datePickerSelected: '',
                });
              } else {
                this.setState({
                  datePickerSelected: date,
                });
              }
            }}
          />
        </Dialog>

        <Footer
          footerText={t('放弃')}
          beforeBack={(back) => {
            store.init();
            back();
          }}
        >
          <FooterBtn
            type="primary"
            onClick={handleSubmit}
          >
            {t('确认')}
          </FooterBtn>
        </Footer>
      </View>
    );
  }
}

export default EditDate;
