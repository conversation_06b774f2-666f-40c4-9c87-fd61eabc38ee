import { t } from '@shein-bbl/react';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages/components/form';
import { Button } from 'react-weui/build/packages';
import { CellsTitle } from 'react-weui/build/packages/components/cell';
import { jumpAppOnTheWeb, isInTheApp } from 'lib/app';
import store from './reducers';
import {
  Header, Footer, FooterBtn, FocusInput, Tag, modal, SelectInput,
} from '../../common';

import Table from '../../common/table';

class Container extends Component {
  componentDidMount() {
    store.init();
    const flag = isInTheApp();
    console.log(flag);
  }

  render() {
    const {
      dispatch,
      goods1Dis,
      reason,
    } = this.props;
    const arr = [
      {
        name: t('张一'),
        value: t('张一'),
      },
      {
        name: t('张二'),
        value: t('张二'),
      },
      {
        name: t('张三'),
        value: t('张三'),
      },
      {
        name: t('李四'),
        value: t('李四'),
      },
    ];
    return (
      <div>
        <Header title={t('测试页面')} />
        <Form>
          <FocusInput
            placeholder={t('请扫描商品条码')}
            data-bind="goodsSn"
            arrow
            disabled={goods1Dis === 0}
            onPressEnter={() => {
              store.scanGoods();
            }}
          >
            <label>{t('商品条码')}</label>
          </FocusInput>
          <SelectInput
            placeholder={t('请扫描')}
            data-bind="reason"
            selectValue={reason}
            label={t('原因')}
            enterShow
            selectList={reason ? arr.filter((v) => v.value.includes(reason)) : []}
            onSelect={(value) => store.changeData({ data: { reason: value } })}
          />
          <FocusInput
            placeholder={t('请扫描商品条码')}
            data-bind="goodsSn"
            allowClear
            className="goods2"
            onPressEnter={() => {
              store.test();
            }}
          >
            <label>{t('商品条码')}</label>
          </FocusInput>
        </Form>
        <Button
          onClick={() => {
            jumpAppOnTheWeb();
          }}
        >{t('scheme打开AMOT-test')}
        </Button>
        <Form>
          <Tag type="app">red</Tag>
        </Form>
        <CellsTitle>
          table
        </CellsTitle>
        <Form>
          <Table
            columns={[
              {
                title: t('序号'),
                dataIndex: 'test1',
                width: 5,
              },
              {
                title: t('大箱号'),
                dataIndex: 'test2',
                width: 20,
              },
              {
                title: t('运单号'),
                dataIndex: 'test3',
                width: 20,
              },
            ]}
            dataSource={[
              {
                test1: '1',
                test2: 'DB93894993',
                test3: 'DB93894993',
              },
              {
                test1: '2',
                test2: 'DB93894993',
                test3: 'DB93894993',
              },
              {
                test1: '3',
                test2: 'DB93894993',
                test3: 'DB93894993',
              },
            ]}
          />
        </Form>
        <Footer
          dispatch={dispatch}
        >
          <FooterBtn
            disabled
            onClick={() => console.log(t('查看'))}
          >{t('查看')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  goods1Dis: PropTypes.number,
  dispatch: PropTypes.func.isRequired,
  reason: PropTypes.string.isRequired,
};

export default Container;
