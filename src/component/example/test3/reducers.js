import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { delay } from 'redux-saga';
import { push } from 'react-router-redux';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import Modal from '../../common/modal';
import { classFocus } from '../../../lib/util';

const defaultState = {
  username: '',
  password: '',
  location: '',
  num: 0,
  goodsSn: '',
  show: false,
  person: '',
  show2: false,
  val: '',
  goods1Dis: 1,
  reason: '',
};

export default {
  defaultState,
  init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * test(action, ctx, put) {
    const status = yield new Promise((r) => Modal.success({
      content: t('成功'),
      onOk: () => r(1),
    }));
    if (status === 1) {
      yield put(push('/put-shelves/shift-up'));
    }
  },
  * scanGoods() {
    markStatus('goods1Dis');
    yield delay(2000);
    classFocus('goods2');
  },
};
