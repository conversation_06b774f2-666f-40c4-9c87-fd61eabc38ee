import React, { Component } from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import { push } from 'react-router-redux';
import { Form } from 'react-weui/build/packages/components/form';
import store from './reducers';
import Pickers from "../../common/pickers";
import {
  PopSelect,
  PopRadio,
  Footer,
  // DragCircle,
  Header,
} from '../../common';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {

    const {
      dispatch,
      show1,
      show2,
      show3,
      selectData1,
      selectData2,
      selectData3,
      selectValue1,
      selectValue2,
      selectValue3,
    } = this.props;
    const handleClick = () => {
      console.log('上传报错');
    };
    return (
      <div>
        <Header title="测试页面-多选下拉-测试超长的文字显示的title" />
        <Form>
          <PopSelect
            label="属性"
            show={show1}
            selectValue={selectValue1}
            selectList={selectData1}
            onClick={() => {
              store.changeData({ data: { show1: true } });
            }}
            onCancel={() => {
              store.changeData({ data: { show1: false } });
            }}
            onOk={(val, item) => {
              // 拼接用于input显示的文本
              const arr = item.map((item, index) => item.dictNameZh);
              store.changeData({
                data: {
                  show1: false,
                  selectValue1: val,
                }
              });
            }}

          />
          <PopRadio
            label="状态"
            show={show2}
            selectValue={selectValue2}
            valueName="id"
            labelName="name"
            selectList={selectData2}
            onClick={() => {
              store.changeData({ data: { show2: true } });
            }}
            onCancel={() => {
              store.changeData({ data: { show2: false } });
            }}
            onOk={(val, item) => {
              // 拼接用于input显示的文本
              store.changeData({
                data: {
                  show2: false,
                  selectValue2: val,
                }
              });
            }}
          />
          <PopSelect
            label="属性3"
            show={show3}
            selectValue={selectValue3}
            selectList={selectData3}
            onClick={() => {
              store.changeData({ data: { show3: true } });
            }}
            onCancel={() => {
              store.changeData({ data: { show3: false } });
            }}
            onOk={(val, item) => {
              // 拼接用于input显示的文本
              const arr = item.map((item, index) => item.dictNameZh);
              store.changeData({
                data: {
                  show3: false,
                  selectValue3: val,
                }
              });
            }}

          />
        </Form>
        <Footer
          beforeBack={() => {
            dispatch(push('/example'));
          }}
        />
        {/*<DragCircle*/}
        {/*  onClick={() => handleClick()}*/}
        {/*  icon="announcement"*/}
        {/*  size={64}*/}
        {/*  backgroundColor="#FF9636"*/}
        {/*/>*/}
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  show1: PropTypes.bool.isRequired,
  show2: PropTypes.bool.isRequired,
  show3: PropTypes.bool.isRequired,
  selectData1: PropTypes.arrayOf(PropTypes.shape).isRequired,
  selectData2: PropTypes.arrayOf(PropTypes.shape).isRequired,
  selectData3: PropTypes.arrayOf(PropTypes.shape).isRequired,
  selectValue1: PropTypes.arrayOf(PropTypes.number).isRequired,
  selectValue2: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  selectValue3: PropTypes.arrayOf(PropTypes.number).isRequired,

};

export default Container;
