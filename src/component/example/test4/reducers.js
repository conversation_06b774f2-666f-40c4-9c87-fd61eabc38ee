import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import {selectDict} from "../../../server/basic/data-dictionary";
import Modal from "../../common/modal";

const defaultState = {
  show1: false,
  show2: false,
  show3: false,
  selectData1: [], // 数据字典下拉数据 dictCode, dictNameZh
  // 为了和selectData1对比使用 字段名是可以自定义的，比如id -> dictCode,
  // 但是请注意，id或者dictCode的类型 number/string，要统一
  selectData2: [
    {
      id: 1,
      name: '空闲',
    },
    {
      id: 2,
      name: '占用',
    },
  ],
  selectData3: [],
  selectValue1: [1], // 默认的值
  selectValue2: '', // 默认下拉值
  selectValue3: [1, 2],
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData (draft, action) {
    assign(draft, action.data);
  },
  // 从数字字典取
  * init(action,ctx) {
    const selectData = yield selectDict({ catCode: ['SPECIAL_ATTRIBUTE'] });
    if (selectData.code === '0') {
      yield ctx.changeData({
        data: {
          selectData1: selectData.info.data[0].dictListRsps,
          selectData3: selectData.info.data[0].dictListRsps,
        }
      });
    } else {
      Modal.error({
        content: selectData.msg,
      });
    }
  }
}

