import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages/components/form';
import { CellsTitle } from 'react-weui/build/packages/components/cell';
import { Button } from 'react-weui/build/packages/components/button';
// import { DragCircle } from 'common';
import { i18n, t } from '@shein-bbl/react';
import Header from 'common/header';
import { getData, setData } from 'lib/cloud-sdk';
import UserIntro from 'common/user-intro';

import store from './reducers';
import Footer from '../../common/footer';
import FooterBtn from '../../common/footer-btn';
import FocusInput from '../../common/focus-input';
import Pickers from '../../common/pickers';
import PopSheet from '../../common/pop-sheet';
import RowInfo from '../../common/row-info';

import Modal from '../../common/modal';

const pickerData = [
  {
    items: [
      {
        label: 'a',
        value: '1',
      },
      {
        label: 'b',
        value: '2',
      },
      {
        label: 'c',
        value: '3',
      },
      {
        label: 'd',
        value: '4',
      },
    ],
  },
];


class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      num,
      dispatch,
      goodsSn,
      show,
      show2,
      person,
      goodsSnDisabled,
      steps,
    } = this.props;
    const items = [
      {
        label: 'a',
        value: '1',
      },
      {
        label: 'b',
        value: '2',
      },
      {
        label: 'c',
        value: '3',
      },
      {
        label: 'd',
        value: '4',
      },
    ];
    return (
      <div style={{ overscrollBehavior: 'contain' }}>
        {/*<DragCircle />*/}
        <UserIntro
          steps={steps}
        />
        <Header title={t('测试页面')}>
          <div onClick={() => console.log(111)}>
            {t('重置')}
          </div>
        </Header>

        <Form>
          <FocusInput
            className="test1-step1"
            placeholder="请扫描周转箱"
            autoFocus
            value={goodsSn}
            disabled={goodsSnDisabled === 0}
            onChange={(e) => {
              store.changeData({
                data: {
                  goodsSn: e.target.value,
                },
              });
            }}
            onPressEnter={() => {
              // Modal.confirm({
              //   title: '商品条码不能为空',
              // });
              const a = {};
              store.scanGoods();
              // console.log(a.data?.name);
              // console.log('提交数据');
            }}
          >
            <label>周转箱</label>
          </FocusInput>
          <FocusInput
            className="test1-step2"
            placeholder="请扫描商品条码"
            // keepFocus={false}
            onPressEnter={() => {
              Modal.success({
                title: '商品条码不能为空',
              });
              console.log('提交数据');
            }}
          >
            <label>商品条码</label>
          </FocusInput>
        </Form>
        <CellsTitle>推荐信息</CellsTitle>
        <Form>
          <FocusInput
            value="SS211-0305-21"
            label="货位号"
            disabled
          >
            <label>SKC</label>
          </FocusInput>
        </Form>
        <CellsTitle>选择框</CellsTitle>
        <Form>
          <Pickers
            value={person}
            label="测试"
            placeholder="请选择内容"
            onClick={() => store.changeData({ data: { show: true } })}
            onChange={(select) => {
              store.changeData({ data: { show: false, person: select.label } });
            }}
            show={show}
            pickerData={pickerData}
            onCancel={() => store.changeData({ data: { show: false } })}
          />
        </Form>

        <CellsTitle>pop-sheet</CellsTitle>
        <Form>
          <Button onClick={() => store.changeData({ data: { show2: true } })}>测试按钮</Button>
          <Button
            onClick={() => {
              console.log('123');
            }}
          >123
          </Button>
        </Form>
        <PopSheet
          onClick={(v) => {
            console.log(v);
            store.changeData({ data: { person: v.label, show2: false } });
          }}
          onClose={() => {
            store.changeData({ data: { show2: false } });
          }}
          cancelBtn
          menus={items}
          show={show2}
        />

        <CellsTitle>信息展示</CellsTitle>
        <Form>
          <RowInfo
            label="已扫件数"
            content="12件"
            type="info"
          />
          <RowInfo
            data={[
              {
                label: '已扫件数',
                content: '22个',
              },
              {
                label: '完成件数',
                content: '12个',
                type: 'warn',
              },
            ]}
          />
        </Form>
        <Form>
          <Button
            onClick={() => {
              setData({ keyName: 123 }).then(v => console.log('设置成功'));
            }}
          >设置
          </Button>
          <br />
          <Button
            onClick={() => {
              getData('keyName', '第二个参数是默认值').then(v => console.log(v));
            }}
          >获取数据
          </Button>
        </Form>

        <Footer
          dispatch={dispatch}
        >
          <FooterBtn
            disabled
            onClick={() => console.log('查看')}
          >
            查看
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  num: PropTypes.number.isRequired,
  dispatch: PropTypes.func.isRequired,
  steps: PropTypes.arrayOf(PropTypes.shape()).isRequired,
};

export default i18n(Container);
