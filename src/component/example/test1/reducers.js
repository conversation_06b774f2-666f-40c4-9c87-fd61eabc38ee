import assign from 'object-assign';
import { delay } from 'redux-saga';
import markStatus from 'rrc-loader-helper/lib/mark-status';

const defaultState = {
  username: '',
  password: '',
  location: '',
  num: 0,
  goodsSn: '',
  show: false,
  person: '',
  show2: false,
  goodsSnDisabled: 1,
  steps: [
    {
      element: '.test1-step1',
      intro: '第一步,扫描周转箱',
    },
    {
      element: '.test1-step2',
      intro: '第二步,扫描商品条码',
    },
  ],
};

export default {
  defaultState,
  init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * scanGoods() {
    markStatus('goodsSnDisabled');
    console.log(111);
  }
}

