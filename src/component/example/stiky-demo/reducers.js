import { getHeaderTitle } from 'lib/util';

const defaultState = {
  initLoading: false, // 初始化loading
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  location: '', // 库位
  barCode: '', // 条码
};

export default {
  state: defaultState,
  $init: () => defaultState,
  * init() {
    yield this.changeData({ headerTitle: getHeaderTitle() || 'base demo' });
    yield this.changeData({ initLoading: false });
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 扫描库位
  * scanLocation(action) {
    const { location } = action;
    yield this.changeData({ location });
  },
  // 扫描条码
  * scanBarcode(action) {
    const { barCode } = action;
    yield this.changeData({ barCode });
  },
};
