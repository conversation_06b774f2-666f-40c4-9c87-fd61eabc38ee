import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, Footer, FooterBtn, View,
} from 'common';
import styles from 'common/common.css';
import store from './reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      initLoading,
      dataLoading,
      location,
      barCode,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle} />
        <View
          flex={false} // flex布局，默认为true，当需要固定单个输入框是，不启用
          diff={100} // 页面高度：window.innerHeight - diff 中的 diff 值
          initLoading={initLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面
        >
          {/** styles.pageStikyChild 公共样式 */}
          {/** 适用于固定单个输入框， */}
          {/** 不适用于页面有多个输入框的场景 */}
          <Form className={styles.pageStikyChild}>
            <FocusInput
              value={location}
              className="location"
              placeholder={t('请扫描')}
              disabled={dataLoading === 0}
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  location: value,
                });
              }}
              onPressEnter={() => {
                store.scanLocation({
                  param: {
                    // ...
                    location,
                  },
                });
              }}
            >
              <label>{t('库位')}</label>
            </FocusInput>
          </Form>
          <Form>
            <FocusInput
              value={barCode}
              className="barCode"
              placeholder={t('请扫描')}
              disabled={dataLoading === 0}
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  barCode: value,
                });
              }}
              onPressEnter={() => {
                store.scanBarcode({
                  param: {
                    // ...
                    barCode,
                  },
                });
              }}
            >
              <label>{t('条码')}</label>
            </FocusInput>
          </Form>
          <div style={{ height: '1000px' }} />
        </View>
        <Footer
          beforeBack={(back) => {
            console.log(t('点击了返回'));
            back();
          }}
        >
          <FooterBtn
            onClick={() => {
              console.log(t('点击了查看'));
            }}
          >{t('查看')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  initLoading: PropTypes.bool,
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  location: PropTypes.string,
  barCode: PropTypes.string,
};

export default i18n(Container);
