import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import store from './reducers';
import { Header, FocusInput, Footer } from '../../common';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle} />
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            autoFocus
            className="containerCode"
            data-bind="containerCode"
            onPressEnter={() => {
              console.log('enter');
            }}
          >
            <label>{t('周转箱')}</label>
          </FocusInput>
        </Form>
        <Footer />
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
};

const mapStateToProps = state => state['example/demo'];
export default connect(mapStateToProps)(i18n(Container));
