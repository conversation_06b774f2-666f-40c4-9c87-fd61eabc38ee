import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Header, TextBar, ProgressBar,
  Gapbar, DrawerContainer, DrawerList,
  DrawerTimeline, LabelList,
} from 'common';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';


class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      showDrawer,
      showList,
      showTimeline,
    } = this.props;
    return (
      <div style={{ overscrollBehavior: 'contain', backgroundColor: '#fff' }}>
        <Header title={t('show组件-demo页面')} />
        <TextBar text={t('4-11')} />
        <TextBar text={[t('首单')]} type="red" />
        <TextBar text={[t('换行：好多好多好多好多好多多多多多多多多')]} />
        <TextBar text={[t('一行超出省略：好多好多好多好多好多多多多多多多多')]} ellipsis type="red" />
        <TextBar text={[t('ZC-001-002'), t('鞋子')]} />
        <Gapbar />
        <ProgressBar percentage={0.2} />
        <div style={{ fontSize: 12 }}>
          <span>{t('包裹总数')}：</span>
          <ProgressBar percentage={0.5} style={{ width: 120, display: 'inline-block', margin: '0 10px' }} />
          <span>{t('已收2（共4包)')}</span>
        </div>
        <LabelList
          labelList={[t('接收子仓'), t('工位号')]}
          valueList={['安博1号仓', 'WG0010009']}
        />
        {
          ['showDrawer', 'showList', 'showTimeline'].map(key => (
            <div onClick={() => store.changeData({ [key]: true })} style={{ lineHeight: '26px', color: 'blue' }} key={key}>show-{key}</div>
          ))
        }
        <DrawerContainer
          visible={showDrawer}
          onClose={() => store.changeData({ showDrawer: false })}
        >
          6666
        </DrawerContainer>
        <DrawerList
          drawerWidth="68%"
          visible={showList}
          onClose={() => store.changeData({ showList: false })}
          title={t('包裹明细（共10个）')}
          list={[...new Array(30).keys()].map(v => `20211220117136-1-${v}`)}
        />
        <DrawerTimeline
          drawerWidth="68%"
          visible={showTimeline}
          onClose={() => store.changeData({ showTimeline: false })}
          title={t('托盘清单')}
          config={{
            mainTitle: t('单号/包裹号'), // 文案标题
            subTitle: t('收货时间'), // 时间标题
            mainKey: 'text', // 文案属性名，即timelineList里边对象的属性名
            subKey: 'time', // 时间属性名，即timelineList里边对象的属性名
          }}
          list={[...new Array(30).keys()].map(v => ({ text: `20211209100004-3-${v}`, time: `12-03 10:49:2${v}` }))}
        />
      </div>
    );
  }
}

Container.propTypes = {
  showDrawer: PropTypes.bool,
  showList: PropTypes.bool,
  showTimeline: PropTypes.bool,
};

export default i18n(Container);
