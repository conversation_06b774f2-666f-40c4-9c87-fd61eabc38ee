import { getHeaderTitle } from 'lib/util';

const defaultState = {
  initLoading: false, // 初始化loading
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  location: '', // 库位
  barCode: '', // 条码
};

export default {
  state: defaultState,
  $init: () => defaultState,
  * init() {
    yield this.changeData({ headerTitle: getHeaderTitle() || 'base demo' });
    // 部分页面进页面会请求获取数据
    yield this.changeData({ initLoading: true });
    const status = yield new Promise((r) => {
      setTimeout(() => r(1), 800);
    });
    if (status === 1) {
      yield this.changeData({ initLoading: false });
    }
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 扫描库位
  * scanLocation(action) {
    const { location } = action;
    console.log(111, action, location);
    yield this.changeData({ location });
  },
  // 扫描条码
  * scanBarcode(action) {
    const { barCode } = action;
    yield this.changeData({ barCode });
  },
};
