import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, Footer, FooterBtn, View,
} from 'common';
import styles from 'common/common.css';
import store from './reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      initLoading,
      dataLoading,
      location,
      barCode,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle} />
        <View
          diff={100} // 默认56; 页面内容高度：window.innerHeight - diff 中的 diff 值【必填: 100是头部标题44+底部按钮56之和】
          flex={false} // flex布局，默认为true，当需要固定单个输入框时，不启用【选填: 根据实际情况决定】
          initLoading={initLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面【选填: 根据实际情况决定】
        >
          <Form>
            <FocusInput
              autoFocus
              value={location}
              className="location"
              placeholder={t('请扫描')}
              disabled={dataLoading === 0}
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  location: value,
                });
              }}
              onPressEnter={(e) => {
                // 空值不触发请求【是否在FocusInput统一处理】
                if (!e.target.value) {
                  return;
                }
                store.scanLocation({
                  // ...
                  location,
                });
              }}
            >
              <label>{t('库位')}</label>
            </FocusInput>
          </Form>
          <Form>
            <FocusInput
              value={barCode}
              className="barCode"
              placeholder={t('请扫描')}
              disabled={dataLoading === 0}
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  barCode: value,
                });
              }}
              onPressEnter={(e) => {
                // 空值不触发请求
                if (!e.target.value) {
                  return;
                }
                store.scanBarcode({
                  // ...
                  barCode,
                });
              }}
            >
              <label>{t('条码')}</label>
            </FocusInput>
          </Form>
          {/* 用于测试页面滚动 */}
          {/*
          <div style={{ height: '1000px' }} />
          */}
        </View>
        <Footer
          beforeBack={(back) => {
            console.log(t('点击了返回'));
            back();
          }}
        >
          <FooterBtn
            onClick={() => {
              console.log(t('点击了查看'));
            }}
          >{t('查看')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  initLoading: PropTypes.bool,
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  location: PropTypes.string,
  barCode: PropTypes.string,
};

export default i18n(Container);
