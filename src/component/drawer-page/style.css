.language {
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 14px;
}
.warehouse {
  position: absolute;
  right: 15px;
  top: 16px;
  font-size: 12px;
}

/*左侧抽屉样式*/
.drawerContainer {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
}

.drawerPage{
  position: absolute;
  left: 0;
  top: 0;
  z-index: 20;
  height: 100%;
  background-color: #ffffff;
}
.drawerMask{
  position: absolute;
  left: 0;
  top: 0;
  z-index: 10;
  width: 100%;
  height: 100%;
  background:rgba(20,23,55,0.3);
}

.logoutItem {
  width:100%;
  height:44px;
  line-height: 44px;
  background:rgba(255,255,255,1);
  box-shadow:0px 1px 0px 0px rgba(232,235,240,1);
  color: #333e59;
  text-align: center;
  position: absolute;
  bottom: 0;
  border-top: 1px solid #E8EBF0;
}

/*展开左侧抽屉时，要禁掉下方主页面的滚动条*/
.hideScroll {
  height: 100%;
  overflow: hidden;
}

.chooseLang {
  margin-top: 16px;
  height: 44px;
  box-shadow:0px 1px 0px 0px rgba(232,235,240,1);
  position: relative;
}

.chooseLang span {
  margin-left: 15px;
  line-height: 44px;
}

.chooseLang .arrRight {
  position: absolute;
  right: 15px;
  top: 16px;
}

.drawerPage .username {
  position: absolute;
  width: 100%;
  height: 24px;
  bottom: 44px;
  left: 15px;
  color: #ffffff;
  font-size: 14px;
  line-height: 56px;
}

.languagePicker {
  position: absolute;
  top: 144px;
  height: 330px;
  overflow-y: auto;
  width: 100%;
  z-index: 10;
  background-color: #ffffff;
}

.languagePicker img {
  width: 16px;
  height: 16px;
}

.avatarWrap {
  width: 24px;
  height: 24px;
  background-color: #ffffff;
  border-radius: 50%;
  float: left;
  position: relative;
  margin-right: 5px;
}

.avatar {
  position: absolute;
  left: 4px;
  top: 4px;
  color: #bde2ff;
}

.avatarWrapBig {
  width: 56px;
  height: 56px;
  overflow: hidden;
}

.avatarBig {
  font-size: 56px !important;
  left: 0;
  top: 0;
}

.itemMargin {
  margin-top: 8px;
}

.labelMargin {
  margin-right: 10px;
}

.aboutModalBody {
  padding: 14px 0 0 0;
  text-align: left;
  border-top: 1px solid #F0F0F0;
  margin-top: 10px;
}

.bottomTip {
  color: #f5bd02;
  margin-top: 15px;
}
.chooseLang .arrRight .loader {
  position: absolute;
  right: 0;
  top: 0;
}

.loader {
  animation: loader-effect 1s infinite linear;
  color: #197afa;
}

@keyframes loader-effect {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
