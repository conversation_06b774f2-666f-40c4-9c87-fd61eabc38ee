import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import {
  Radio,
  Form,
  FormCell,
  CellBody,
  CellFooter,
} from 'react-weui/build/packages';
import Icon from '@shein-components/Icon';
import classnames from 'classnames';
import { SESSION_MOT_IS_SHOW_RECORD, LOCAL_MOT_RECORD_LIST } from 'lib/storage';
import { jumpByLang, getLang, getUser } from '../js';
import store from '../main-menu/reducers';
import navStore from '../nav/reducers';
import firstStore from '../sowing/first/reducers';
import overseaFirstStore from '../oversea/sowing-first/reducers';
import secondStore from '../sowing/second/reducers';
import overseaSecondStore from '../oversea/sowing-second/reducers';
import containerQueryStore from '../query/container-query/reducers';
import groupStore from '../oversea/oversea-relay-group/reducers';
import pickingStore from '../order-picking/picking/reducers';
import otherOutboundPickingStore from '../order-picking/other-outbound/reducers';
import styles from './style.css';
import styles2 from '../main-menu/style.css';
import { SwitchIcon, Table } from '../common';
import Modal from '../common/modal';
import { langPickerGroup } from '../language-enum';
import { getVersion } from '../../lib/util';
import { APP_DEBUG_INFO, APP_RELEASE_INFO } from '../../lib/app';

let appName = ''; // app 环境名称
let appVersion = ''; // app 版本
let appDownloadUrl = ''; // app 最新版本地址

class DrawerPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showLangPicker: false,
      isTranslated: false,
    };
  }

  componentDidMount() {
    // 监听页面是否被翻译
    const observer = new MutationObserver((() => {
      if (document.documentElement.className.match('translated')) {
        this.setState({
          isTranslated: true,
        });
      } else {
        this.setState({
          isTranslated: false,
        });
      }
    }));
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class'],
      childList: false,
      characterData: false,
    });
    // 获取app打包环境
    if (window.mDevice) {
      switch (window.mDevice.getBuildType()) {
        case 'release':
          appName = APP_RELEASE_INFO.name;
          appDownloadUrl = APP_RELEASE_INFO.url;
          appVersion = APP_RELEASE_INFO.version;
          break;
        default:
          appName = APP_DEBUG_INFO.name;
          appDownloadUrl = APP_DEBUG_INFO.url;
          appVersion = APP_DEBUG_INFO.version;
          break;
      }
    }
  }

  componentWillReceiveProps(nextProps) {
    if (!nextProps.show) {
      this.setState({
        showLangPicker: false,
      });
    }
  }

  render() {
    const {
      show,
      isShowQuickEntry,
      isOnline,
      showJiugongge,
      openJiugongge,
      showSplitABFrame,
      openSplitABFrame,
      isShowRecord,
      dataLoading,
      openPicturePickingModel,
    } = this.props;
    const {
      showLangPicker,
      isTranslated,
    } = this.state;
    // 获取用户名
    // let username = '';
    let enName = '';
    if (getUser()) {
      // username = getUser().username || '';
      enName = getUser().enName || '';
    }
    const lang = getLang();
    const languageLabel = langPickerGroup.find(({ id }) => id === lang)?.label;
    // 选择语言
    const chooseLang = () => {
      this.setState({ showLangPicker: true });
    };
    // 隐藏drawer
    const hideDrawer = () => {
      this.setState({ showLangPicker: false });
      store.changeData({ data: { showDrawer: false, showLangPicker: false } });
      navStore.changeData({ data: { showDrawer: false } });
    };
    // 获取chrome浏览器版本
    const getChromeVersion = () => {
      const arr = navigator.userAgent.split(' ');
      let chromeVersion = '';
      for (let i = 0; i < arr.length; i++) {
        if (/chrome/i.test(arr[i])) {
          chromeVersion = arr[i];
        }
      }
      if (chromeVersion) {
        const versionArr = chromeVersion.split('/')[1].split('.');
        return `${versionArr[0]}.${versionArr[1]}`;
      }
      return false;
    };

    return (
      <div
        className={styles.drawerContainer}
        style={{ display: show ? 'block' : 'none', zIndex: 998 }}
      >
        <div
          className={styles.drawerPage}
          style={{ width: '82.5%' }}
        >
          <div style={{
            width: '100%', height: '144px', background: '#0059ce', position: 'relative',
          }}
          >
            <div style={{ paddingTop: '12px', paddingLeft: '15px' }}>
              <Icon
                name="arr-left"
                style={{
                  color: '#FFFFFF',
                  marginRight: 8,
                  fontSize: 14,
                }}
                onClick={() => hideDrawer()}
              />
              <span style={{ color: '#FFFFFF', fontSize: '14px' }}>{t('移动作业终端')}(EU)</span>
            </div>
            <div className={styles.username}>
              <div className={classnames(styles.avatarWrap, styles.avatarWrapBig)}>
                <Icon className={classnames(styles.avatar, styles.avatarBig)} name="shein-s" />
              </div>
              <span
                className={classnames(
                  styles2.lineIcon,
                  isOnline ? styles2.onlineIcon : styles2.offlineIcon,
                )}
              >
                {enName}
              </span>
            </div>
          </div>
          <div style={{ width: '100%', height: '144px' }}>
            <div
              className={styles.chooseLang}
              onClick={(e) => {
                e.stopPropagation();
                chooseLang();
              }}
            >
              <span>{languageLabel}</span>
              <Icon name="arr-right" className={styles.arrRight} style={{ fontSize: 12 }} />
            </div>
            <div
              className={styles.chooseLang}
              style={{ marginTop: 0 }}
              onClick={(e) => {
                e.stopPropagation();
                store.changeData({ data: { isShowQuickEntry: !isShowQuickEntry } });
                navStore.changeData({ data: { isShowQuickEntry: !isShowQuickEntry } });
              }}
            >
              <span>{t('快捷入口')}</span>
              <span className={styles.arrRight}>
                <SwitchIcon value={isShowQuickEntry} size="small" />
              </span>
            </div>
            <div
              className={styles.chooseLang}
              style={{ marginTop: 0 }}
              onClick={(e) => {
                e.stopPropagation();
                store.changeData({ data: { isShowRecord: !isShowRecord } });
                navStore.changeData({ data: { isShowRecord: !isShowRecord } });
                sessionStorage.setItem(SESSION_MOT_IS_SHOW_RECORD, !isShowRecord);
              }}
            >
              <span>{t('调试模式')}</span>
              <span className={styles.arrRight}>
                <SwitchIcon value={isShowRecord} size="small" />
              </span>
            </div>
            {
              (window.location.hash.includes('#/order-picking/picking/picking-page') || window.location.hash.includes('#/order-picking/other-outbound')) && (
                <div
                  className={styles.chooseLang}
                  style={{ marginTop: 0 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    // eslint-disable-next-line max-len
                    pickingStore.changeData({ data: { openPicturePickingModel: !openPicturePickingModel } });
                    // eslint-disable-next-line max-len
                    otherOutboundPickingStore.changeData({ data: { openPicturePickingModel: !openPicturePickingModel } });
                    navStore.changeData({ data: { openPicturePickingModel: !openPicturePickingModel, quickEntryObjKey: 'openPicturePickingModel' } });
                  }}
                >
                  <span>{t('图片拣货模式')}</span>
                  <span className={styles.arrRight}>
                    <SwitchIcon value={openPicturePickingModel} size="small" />
                  </span>
                </div>
              )
            }
            <div
              className={styles.chooseLang}
              style={{ marginTop: 0 }}
              onClick={(e) => {
                e.stopPropagation();
                store.clearCache({ hideDrawer });
              }}
            >
              <span>{t('清除缓存')}</span>
              <span className={styles.arrRight}>
                <Icon type="primary" className={styles.loader} name="loading" data-show={dataLoading === 0} />
              </span>
            </div>
            <div
              className={styles.chooseLang}
              style={{ marginTop: 0 }}
              onClick={(e) => {
                e.stopPropagation();
                Modal.info({
                  className: 'aboutModal',
                  title: (
                    <div style={{ marginTop: -40, backgroundColor: '#fff', position: 'relative' }}>
                      <div>{t('关于 MOT 管理系统')}</div>
                      <div className={styles.aboutModalBody}>
                        <div>
                          <span className={styles.labelMargin}>{t('当前用户')}:</span> { enName }
                        </div>
                        <div className={styles.itemMargin}>
                          <span className={styles.labelMargin}>{t('系统版本')}:</span> { getVersion().replace('__', '') || ' - -' }
                          {window.hasLatestVersion ? (
                            <span
                              style={{ color: '#0059CE' }}
                              onClick={() => {
                                window.location.reload();
                              }}
                            >
                              {`(${t('点击更新最新版本')})`}
                            </span>
                          ) : (
                            <span
                              style={{ color: '#0059CE' }}
                            >
                              {`(${t('此版本为最新版本')})`}
                            </span>
                          )}
                        </div>
                        <div className={styles.itemMargin}>
                          <span>
                            <span className={styles.labelMargin}>{t('Chrome版本')}:</span> { getChromeVersion() ? getChromeVersion() : ' - -' }
                          </span>
                          <span style={{
                            color: '#EC3942', fontSize: 13, marginLeft: 6, display: getChromeVersion() && getChromeVersion() < 70 ? 'inline-block' : 'none',
                          }}
                          >{t('建议升级版本')}
                          </span>
                          <span style={{ color: '#F5BD02', fontSize: 13, display: !getChromeVersion() ? 'inline-block' : 'none' }}>{t('推荐使用Chrome浏览器')}</span>
                        </div>
                        { window.mDevice && (
                          <div className={styles.itemMargin}>
                            <span>
                              <span className={styles.labelMargin}>{t('app版本')}:</span> {appName} { window.mDevice?.getVersionName() || ' - -' }
                            </span>
                            { window.mDevice.getVersionName() !== appVersion && (
                              <span>
                                <span
                                  style={{
                                    color: '#EC3942', fontSize: 13, marginLeft: 6, display: 'inline-block',
                                  }}
                                >
                                  {t('建议升级版本')}
                                </span>
                                <span
                                  style={{
                                    color: '#F5BD02', fontSize: 13, display: 'inline-block', margin: '10px 0 20px 0',
                                  }}
                                  onClick={() => {
                                    window.open(appDownloadUrl);
                                  }}
                                >
                                  {t('点击下载最新版app')}
                                </span>
                              </span>
                            )}
                          </div>
                        )}
                        { isTranslated && getChromeVersion() && <p className={styles.bottomTip}>{t('您已开启Chrome的翻译功能，可能会导致系统显示异常！')}</p> }
                      </div>
                    </div>
                  ),
                });
              }}
            >
              <span>{t('关于')}</span>
              <Icon name="arr-right" className={styles.arrRight} style={{ fontSize: 12 }} />
            </div>
            <div
              className={styles.chooseLang}
              style={{ marginTop: 0, display: isShowRecord ? 'block' : 'none' }}
              onClick={(e) => {
                e.stopPropagation();
                Modal.info({
                  className: 'motRecordList',
                  title: (
                    <div style={{ backgroundColor: '#fff', position: 'relative' }}>
                      <div>{t('调试日志')}</div>
                      <div style={{ overflowX: 'auto' }}>
                        <Table
                          styleObj={{ width: 400 }}
                          height={320}
                          columns={[
                            {
                              title: t('接口地址'),
                              dataIndex: 'apiUrl',
                              width: 46,
                            },
                            {
                              title: t('耗时'),
                              dataIndex: 'responseTimeMs',
                              width: 15,
                            },
                            {
                              title: t('开始时间'),
                              dataIndex: 'startTime',
                              width: 19.5,
                            },
                            {
                              title: t('结束时间'),
                              dataIndex: 'endTime',
                              width: 19.5,
                            },
                          ]}
                          dataSource={(JSON.parse(localStorage.getItem(LOCAL_MOT_RECORD_LIST)) || []).reverse()}
                        />
                      </div>
                    </div>
                  ),
                });
              }}
            >
              <span>{t('调试日志')}</span>
              <Icon name="arr-right" className={styles.arrRight} style={{ fontSize: 12 }} />
            </div>
            {
             ['#/oversea/oversea-relay-group', '#/sowing/first', '#/oversea/sowing-first'].includes(window.location.hash) && showJiugongge && (
             <div
               className={styles.chooseLang}
               style={{ marginTop: 0 }}
               onClick={(e) => {
                 e.stopPropagation();
                 store.changeData({ data: { openJiugongge: !openJiugongge } });
                 firstStore.changeData({ data: { openJiugongge: !openJiugongge } });
                 overseaFirstStore.changeData({ openJiugongge: !openJiugongge });
                 groupStore.changeData({ data: { openJiugongge: !openJiugongge } });
                 navStore.changeData({ data: { openJiugongge: !openJiugongge, quickEntryObjKey: 'openJiugongge' } });
               }}
             >
               {window.location.hash === '#/oversea/oversea-relay-group' ? (<span>{t('海外转发分组九宫格模式')}</span>) : (<span>{t('一分九宫格模式')}</span>)}
               <span className={styles.arrRight}>
                 <SwitchIcon value={openJiugongge} size="small" />
               </span>
             </div>
             )
            }
            {/* 容器查询页面不需要展示开关 */}
            {
             ['#/sowing/second', '#/oversea/sowing-second'].includes(window.location.hash) && showSplitABFrame && (
             <div
               className={styles.chooseLang}
               style={{ marginTop: 0 }}
               onClick={(e) => {
                 e.stopPropagation();
                 store.changeData({ data: { openSplitABFrame: !openSplitABFrame } });
                 secondStore.changeData({ data: { openSplitABFrame: !openSplitABFrame } });
                 overseaSecondStore.changeData({ openSplitABFrame: !openSplitABFrame });
                 // 二分界面AB架开关影响容器查询的序号显示
                 containerQueryStore.changeData({ data: { openSplitABFrame: !openSplitABFrame } });
                 navStore.changeData({ data: { openSplitABFrame: !openSplitABFrame, quickEntryObjKey: 'openSplitABFrame' } });
               }}
             >
               <span>{t('二分AB架模式')}</span>
               <span className={styles.arrRight}>
                 <SwitchIcon value={openSplitABFrame} size="small" />
               </span>
             </div>
             )
            }
            <div
              className={styles.logoutItem}
              onClick={() => {
                store.logout({ hideDrawer });
              }}
            >
              {t('退出登录')}
            </div>
          </div>
          <div className={styles.languagePicker} style={{ display: showLangPicker ? '' : 'none' }}>
            <Form
              radio
            >
              {
                langPickerGroup.map((item) => (
                  <FormCell key={item.id} radio>
                    <CellBody>{item.label}</CellBody>
                    <CellFooter>
                      <Radio
                        name={item.id}
                        value={item.id}
                        checked={lang === item.id || false}
                        onChange={(e) => {
                          store.changeData({ data: { lang: e.target.value } });
                        }}
                        onClick={(e) => {
                          this.setState({ showLangPicker: false });
                          if (lang !== item.id) {
                            store.changeData({ data: { lang: e.target.value } });
                            jumpByLang(e.target.value);
                          }
                        }}
                      />
                    </CellFooter>
                  </FormCell>
                ))
              }
            </Form>
          </div>
        </div>
        <div
          className={styles.drawerMask}
          onClick={() => hideDrawer()}
        >
          <br />

        </div>
      </div>
    );
  }
}

DrawerPage.defaultProps = {
  show: false,
  isShowQuickEntry: false,
  isOnline: true,
};

DrawerPage.propTypes = {
  show: PropTypes.bool,
  isShowQuickEntry: PropTypes.bool,
  isOnline: PropTypes.bool,
  showJiugongge: PropTypes.bool,
  openJiugongge: PropTypes.bool,
  openSplitABFrame: PropTypes.bool,
  showSplitABFrame: PropTypes.bool,
  isShowRecord: PropTypes.bool,
  dataLoading: PropTypes.number,
  openPicturePickingModel: PropTypes.bool,
};
export default i18n(DrawerPage);
