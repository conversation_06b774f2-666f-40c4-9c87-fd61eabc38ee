import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form, CellsTitle } from 'react-weui/build/packages';
import store from '../reducers';
import {
  Footer,
  FocusInput,
  RowInfo,
  FooterBtn,
  modal,
} from '../../../common';
import style from '../style.css';

const getBatchCodeNode = (str, isRed = true) => {
  const len = str.length;
  return (
    <div className={style.batchNo}>
      <span>{t('协作批次号')}：</span>
      <span className={style.colorBlack}>{str.slice(0, len - 1)}</span>-
      <span className={isRed ? style.colorRed : style.colorBlue}>{str.slice(-1)}</span>
    </div>
  );
};

const ActionPage = (props) => {
  const {
    boxNo,
    isboxNoDisabled,
    location,
    isLocationDisabled,
    isRed,
    coBatchCode, // 协助批次号
    waitingSplitNum, // 待分箱数
    hasSplitNum, //  已分箱数
    coWellenCode, // 协作波次号
  } = props;

  return (
    <div>
      <Form>
        <RowInfo
          data={[
            {
              label: t('待分箱'),
              content: waitingSplitNum,
              type: 'warn',
            },
            {
              label: t('已分箱'),
              content: hasSplitNum,
            },
          ]}
        />
      </Form>
      <Form style={{ marginTop: 10 }}>
        <FocusInput
          label={t('箱号')}
          placeholder={t('请扫描')}
          className="boxNo"
          data-bind="boxNo"
          disabled={isboxNoDisabled}
          onPressEnter={(e) => {
            if (!e.target.value) {
              return;
            }
            store.scanBoxNo({ params: { boxNo } });
          }}
        />
        <FocusInput
          label={t('集货库位')}
          placeholder={t('请扫描')}
          className="location"
          data-bind="location"
          disabled={isLocationDisabled}
          onPressEnter={(e) => {
            if (!e.target.value) {
              return;
            }
            store.scanLocation({
              params: {
                location,
                boxNo,
              },
            });
          }}
        />
      </Form>
      <CellsTitle>{t('信息')}</CellsTitle>
      {getBatchCodeNode(coBatchCode, isRed)}
      <Footer
        beforeBack={() => {
          store.init();
        }}
      >
        <FooterBtn
          onClick={() => {
            modal.confirm({
              content: t('是否强制分批完成?'),
              onOk: () => {
                store.forceFinishSplit({ params: { coWellenCode } });
              },
            });
          }}
        >
          {t('强制分批完成')}
        </FooterBtn>
      </Footer>
    </div>
  );
};

ActionPage.propTypes = {
  boxNo: PropTypes.string,
  isboxNoDisabled: PropTypes.bool,
  location: PropTypes.string,
  isLocationDisabled: PropTypes.bool,
  isRed: PropTypes.bool,
  coBatchCode: PropTypes.string,
  waitingSplitNum: PropTypes.number,
  hasSplitNum: PropTypes.number,
  coWellenCode: PropTypes.string,
};

export default ActionPage;
