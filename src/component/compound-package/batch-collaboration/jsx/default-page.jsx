import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import {
  Footer,
  FocusInput,
} from '../../../common';

const DefaultPage = (props) => {
  const {
    boxNo,
    isboxNoDisabled,
  } = props;

  return (
    <div>
      <Form style={{ marginTop: 10 }}>
        <FocusInput
          disabled={isboxNoDisabled}
          label={t('箱号')}
          placeholder={t('请扫描')}
          className="boxNo"
          data-bind="boxNo"
          autoFocus
          onPressEnter={(e) => {
            if (!e.target.value) {
              return;
            }
            store.scanBoxNo({
              params: {
                boxNo,
                confirmSure: false,
              },
            });
          }}
        />
      </Form>
      <Footer
        beforeBack={(back) => {
          store.init();
          back();
        }}
      />
    </div>
  );
};

DefaultPage.propTypes = {
  boxNo: PropTypes.string,
  isboxNoDisabled: PropTypes.bool,
};

export default DefaultPage;
