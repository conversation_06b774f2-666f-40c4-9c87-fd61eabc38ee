import { sendPostRequest } from '../../../lib/public-request';

// 扫描箱子
export const scanBoxNoApi = param => sendPostRequest({
  url: '/split_batch/scan_box_no',
  param,
}, process.env.WPOC_URI);

// 扫描库位
export const scanLocationApi = param => sendPostRequest({
  url: '/split_batch/scan_location',
  param,
}, process.env.WPOC_URI);


export const forceFinishSplitApi = param => sendPostRequest({
  url: '/wellen_collect_split/force_finish_split',
  param,
}, process.env.WPOC_URI);
