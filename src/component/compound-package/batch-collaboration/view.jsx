import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import { Header } from '../../common';
import DefaultPage from './jsx/default-page';
import ActionPage from './jsx/action-page';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      status,
      headerTitle,
    } = this.props;
    let children;
    switch (status) {
      case 'actionPage':
        children = (<ActionPage {...this.props} />);
        break;
      default:
        children = (<DefaultPage {...this.props} />);
        break;
    }

    return (
      <div>
        <Header
          title={headerTitle}
        />
        {children}
      </div>
    );
  }
}

Container.propTypes = {
  status: PropTypes.string,
  headerTitle: PropTypes.string,
};

const mapStateToProps = state => state['compound-package/batch-collaboration'];
export default connect(mapStateToProps)(i18n(Container));
