import React from 'react';
import { select } from 'redux-saga/effects';
import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import { scanBoxNoApi, scanLocationApi, forceFinishSplitApi } from './server';
import { modal, message } from '../../common';

const defaultState = {
  status: '',
  headerTitle: '', // 页面标题：统一从后端接口获取
  location: '',
  isLocationDisabled: false,
  boxNo: '',
  isboxNoDisabled: false,
  isRed: true, // 协助批次号最后一位颜色展示
  coWellenCode: '', // 协作波次号
  coBatchCode: '', // 协助批次号
  waitingSplitNum: 0, // 待分箱数
  hasSplitNum: 0, //  已分箱数
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() || t('分协作批次') } });
  },
  * scanBoxNo(action, ctx) {
    yield ctx.changeData({ data: { isboxNoDisabled: true } });
    const res = yield scanBoxNoApi(action.params);
    if (res.code === '0') {
      // 清空集货库位
      yield ctx.changeData({
        data: {
          location: '',
        },
      });
      const {
        boxNo,
        // 协作波次号
        coWellenCode,
        // 协作批次数
        coBatchNum,
        // 转运箱总数
        boxTotalNum,
        // 集箱异常的转运箱
        expCollectBoxList,
        // 是否需要弹窗
        needConfirm,
      } = res.info;
      if (needConfirm) {
        const redColor = { color: 'red' };
        const status = yield new Promise((r) => modal.confirm({
          content: (
            <div>
              {boxNo}{t('对应协作波次号')}{coWellenCode}{t('包括')}
              <span style={redColor}>{coBatchNum}{t('个协作批次')}</span>,
              <span style={redColor}>{boxTotalNum}{t('个转运箱')}</span>,
              {expCollectBoxList && !!expCollectBoxList.length &&
              <span>{t('其中')}{expCollectBoxList.join(',')}{t('转运箱集货异常,')}</span>}
              {t('请确认！')}
            </div>
          ),
          onOk: () => r('ok'),
          onCancel: () => r('cancel'),
        }));

        if (status === 'ok') {
          yield ctx.scanBoxNo({
            params: {
              boxNo: action.params.boxNo,
              confirmSure: true,
            },
          });
        }
        if (status === 'cancel') {
          yield ctx.changeData({
            data: {
              isboxNoDisabled: false,
              boxNo: '',
            },
          });
          classFocus('boxNo');
        }
      } else {
        yield ctx.boxDone({ info: res.info });
      }
    } else {
      modal.error({
        content: res.msg,
        className: 'boxNo',
      });
      // 清空箱号
      yield ctx.changeData({
        data: {
          boxNo: '',
          isboxNoDisabled: false,
        },
      });
    }
  },
  * scanLocation(action, ctx) {
    yield ctx.changeData({ data: { isLocationDisabled: true } });
    const res = yield scanLocationApi(action.params);
    yield ctx.changeData({ data: { isLocationDisabled: false } });
    if (res.code === '0') {
      yield ctx.boxDone({ info: res.info });
    } else {
      modal.error({
        content: res.msg,
        className: 'location',
      });
      // 清空集货库位
      yield ctx.changeData({
        data: {
          location: '',
        },
      });
    }
  },
  * boxDone(action, ctx) {
    const { isRed } = yield select((state) => state['compound-package/batch-collaboration']);
    const {
      coBatchCode,
      coWellenCode,
      location,
      waitingSplitNum,
      hasSplitNum,
      coBatchComplete,
      coWellenComplete,
      boxNo,
    } = action.info;

    yield ctx.changeData({
      data: {
        status: 'actionPage',
        coBatchCode,
        coWellenCode,
        waitingSplitNum,
        hasSplitNum,
        location,
        boxNo,
        isboxNoDisabled: false,
        isRed: !isRed,
      },
    });

    if (location) {
      // 有绑定库位
      yield ctx.changeData({
        data: {
          boxNo: '',
          isboxNoDisabled: false,
        },
      });
      classFocus('boxNo');
    } else {
      // 没有绑定库位，需扫描库位
      yield ctx.changeData({
        data: {
          boxNo,
          isboxNoDisabled: true,
        },
      });
      classFocus('location');
    }

    const batchCompleteTips = t('协作批次号：{}， 已分批完成', coBatchCode);
    const wellenCompleteTips = t('协作波次号：{}， 已分批完成', coWellenCode);

    if (coWellenComplete && coBatchComplete) {
      // 波次完成，给出弹窗提醒，点击回到上一页
      const status = yield new Promise((r) => modal.info({
        content: `${wellenCompleteTips};${batchCompleteTips}`,
        onOk: () => r('ok'),
      }));
      if (status === 'ok') {
        yield ctx.init();
      }
    } else if (coBatchComplete) {
      message.success(batchCompleteTips);
      yield ctx.changeData({ data: { location: '' } });
    }
  },
  * forceFinishSplit(action, ctx) {
    const res = yield forceFinishSplitApi(action.params);
    if (res.code === '0') {
      message.success(t('强制分批完成成功'));
      yield ctx.init();
    } else {
      modal.error({
        content: res.msg,
      });
    }
  },
};
