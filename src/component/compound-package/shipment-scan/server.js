import { sendPostRequest } from '../../../lib/public-request';

/**
 * 装车扫描检查车牌号
 * <AUTHOR>
 * @DateTime 2018-11-23T11:55:00+0800
 * @param    {[type]}
 * @return   {[type]}
 */
export const shipmentScanCheckCarNumber = param => sendPostRequest({
  url: '/truck_loading/scan_car_no',
  param,
}, process.env.WPOC_URI);

/**
 * 装车扫描检查箱号
 * <AUTHOR>
 * @DateTime 2018-11-23T12:07:53+0800
 * @param    {[type]}                 param [description]
 * @return   {[type]}                       [description]
 */
export const shipmentScanCheckBoxNumber = param => sendPostRequest({
  url: '/truck_loading/scan_box_no',
  param,
}, process.env.WPOC_URI);
