import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { CellsTitle } from 'react-weui/build/packages/components/cell';
import { Form } from 'react-weui/build/packages';
import { i18n, t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import { classFocus } from '../../../lib/util';
import store from './reducers';
import style from '../../style.css';
import {
  Header,
  FocusInput,
  Footer,
  RowInfo,
  modal,
} from '../../common';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      dispatch,
      carNumber,
      boxNumber,
      boxCount,
      ableBoxNumberInput,
      palletCode,
      carNumberDataLoading,
      boxNumberDataLoading,
      headerTitle,
      combineAddress,
    } = this.props;
    return (
      <div className={style.flexColContainer}>
        <Header title={headerTitle || t('装车扫描')} />
        <Form>
          <RowInfo
            extraStyle={{ borderBottom: '0 none' }}
            label={`${t('已采箱数')}: `}
            content={boxCount}
            type="info"
          />
          <RowInfo
            extraStyle={{ borderBottom: '0 none' }}
            label={t('合包地址')}
            content={combineAddress || '--'}
            type="info"
          />
        </Form>
        <CellsTitle style={{ height: 8 }} />
        <Form>
          <FocusInput
            allowClear
            data-bind="carNumber"
            className="carNumberInput"
            maxLength="11"
            autoFocus
            disabled={carNumberDataLoading === 0}
            placeholder={t('请扫描')}
            onPressEnter={(e) => {
              if (!e.target.value.trim()) {
                return;
              }
              store.checkCarNumber({
                data: {
                  carNo: carNumber.toUpperCase(),
                },
              });
            }}
          >
            <label>{t('车牌号')}</label>
          </FocusInput>
          <FocusInput
            allowClear
            data-bind="palletCode"
            className="palletCodeInput"
            placeholder={t('请扫描')}
            onPressEnter={() => {
              const reg = /^TPNN\d{12,13}[a-zA-Z]{2}$/;
              if (reg.test(palletCode)) {
                store.changeData({
                  data: {
                    ableBoxNumberInput: true,
                    palletCode,
                  },
                });
                classFocus('boxNumberInput');
              } else {
                modal.error({
                  modalBlurInput: true,
                  content: t('托盘号格式错误'),
                  onOk: () => {
                    store.changeData({
                      data: {
                        palletCode: '',
                      },
                    });
                    classFocus('palletCodeInput');
                  },
                  autoFocusButton: null,
                });
              }
            }}
          >
            <label>{t('托盘号')}</label>
          </FocusInput>
          <FocusInput
            data-bind="boxNumber"
            className="boxNumberInput"
            placeholder={t('请扫描')}
            disabled={!ableBoxNumberInput || boxNumberDataLoading === 0}
            onPressEnter={(e) => {
              if (!e.target.value.trim()) {
                return;
              }
              store.checkBoxNumber({
                data: {
                  carNo: carNumber.toUpperCase(),
                  boxNo: boxNumber,
                  palletCode,
                  isConfirmed: false,
                },
              });
            }}
          >
            <label>{t('大箱号')}</label>
          </FocusInput>
        </Form>
        <Footer />
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  boxCount: PropTypes.number.isRequired,
  carNumber: PropTypes.string.isRequired,
  boxNumber: PropTypes.string.isRequired,
  palletCode: PropTypes.string.isRequired,
  ableBoxNumberInput: PropTypes.bool.isRequired,
  carNumberDataLoading: PropTypes.number.isRequired,
  boxNumberDataLoading: PropTypes.number.isRequired,
  headerTitle: PropTypes.string.isRequired,
  combineAddress: PropTypes.string.isRequired,
};

const mapStateToProps = (state) => state['compound-package/shipment-scan'];
export default connect(mapStateToProps)(i18n(Container));
