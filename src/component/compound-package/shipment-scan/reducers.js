import assign from 'object-assign';
import { select } from 'redux-saga/effects';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { shipmentScanCheckCarNumber, shipmentScanCheckBoxNumber } from './server';
import Modal from '../../common/modal';
import aaoo from '../../../source/audio/aaoo.mp3';
import { classFocus, getHeaderTitle } from '../../../lib/util';

const defaultState = {
  boxCount: 0,
  carNumber: '',
  carNumberDataLoading: 1,
  boxNumber: '',
  boxNumberDataLoading: 1,
  ableBoxNumberInput: false,
  palletCode: '', // 托盘号
  headerTitle: '',
  combineAddress: '', // 合包地址
};

const aaooEle = new Audio(aaoo);
aaooEle.load();

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  // 检查车牌号
  * checkCarNumber(action, ctx) {
    markStatus('carNumberDataLoading');
    const {
      code,
      info,
      msg,
    } = yield shipmentScanCheckCarNumber(action.data);
    if (code === '0') {
      yield ctx.changeData({
        data: {
          ableBoxNumberInput: true,
          boxCount: info.hasLoadingBoxNum,
          combineAddress: info.combineAddress,
        },
      });
      // 解决输入框双聚焦样式显示问题
      classFocus('carNumberInput');
      classFocus('boxNumberInput');
    } else {
      Modal.error({
        content: msg,
        onOk: () => {
          classFocus('carNumberInput');
        },
        autoFocusButton: null,
      });
    }
  },
  // 检查箱号
  * checkBoxNumber(action, ctx) {
    markStatus('boxNumberDataLoading');
    const { code, msg, info } = yield shipmentScanCheckBoxNumber(action.data);
    const { boxCount } = yield select((v) => v['compound-package/shipment-scan']);
    if (code === '0') {
      if (info.isAlertTip) {
        const alertStatus = yield new Promise((r) => (
          Modal.confirm({
            content: info.topMessage,
            onOk: () => r(1),
            onCancel: () => r(0),
            autoFocusButton: null,
          })
        ));
        if (alertStatus === 1) {
          // 在这里重新调用接口传参 isConfirmed：true，
          yield ctx.checkBoxNumber({
            data: Object.assign(action.data, {
              isConfirmed: true,
            }),
          });
        } else if (alertStatus === 0) {
          yield ctx.changeData({
            data: {
              boxNumber: '',
            },
          });
          classFocus('boxNumberInput');
        }
        return;
      }

      if (info.isRepeatPallet) {
        const status = yield new Promise((r) => (
          Modal.info({
            content: t('不允许重复使用托盘'),
            onOk: () => r(1),
            autoFocusButton: null,
          })
        ));
        if (status === 1) {
          yield ctx.changeData({
            data: {
              palletCode: '',
              boxNumber: '',
            },
          });
          classFocus('palletCodeInput');
        }
        return;
      }
      yield ctx.changeData({
        data: {
          boxNumber: '',
          boxCount: boxCount + 1,
          combineAddress: info.combineAddress,
        },
      });
      classFocus('boxNumberInput');
    } else {
      aaooEle.play();
      Modal.error({
        content: msg,
        onOk: () => {
          classFocus('boxNumberInput');
        },
        autoFocusButton: null,
      });
      yield ctx.changeData({
        data: {
          boxNumber: '',
        },
      });
    }
  },
};
