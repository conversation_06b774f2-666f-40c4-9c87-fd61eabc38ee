import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { push } from 'react-router-redux';
import { i18n, t } from '@shein-bbl/react';
import { classFocus } from '../../../lib/util';
import store from './reducers';
import style from '../../style.css';
import {
  Header,
  FocusInput,
  Footer,
  FooterBtn,
} from '../../common';

class Container extends Component {
  componentDidMount() {
    store.init();
    this.checkCarNumberFn = () => {
      if (this.props.carNumber) {
        store.checkCarNumber({
          data: {
            plate_number: this.props.carNumber.toUpperCase(),
          },
        });
        setTimeout(() => {
          classFocus('carNumberInput');
        });
      }
    };
    this.keydownFn = (event) => {
      if (event.keyCode === 112) {
        this.checkCarNumberFn();
      }
    };
    window.addEventListener('keydown', this.keydownFn);
  }

  componentWillUnmount() {
    window.removeEventListener('keydown', this.keydownFn);
  }

  render() {
    const {
      dispatch,
      carNumber,
      dataLoading,
      headerTitle,
    } = this.props;
    return (
      <div className={style.flexColContainer}>
        <Header title={headerTitle || t('发运扫描')} />
        <Form>
          <FocusInput
            data-bind="carNumber"
            className="carNumberInput"
            maxLength="16"
            autoFocus
            disabled={dataLoading === 0}
            placeholder={t('请扫描')}
            onPressEnter={(e) => {
              if (!e.target.value.trim()) {
                return;
              }
              this.checkCarNumberFn();
            }}
          >
            <label>{t('车牌号/装车单号')}：</label>
          </FocusInput>
        </Form>
        <Footer>
          <FooterBtn
            disabled={!carNumber || dataLoading === 0}
            onClick={() => {
              this.checkCarNumberFn();
            }}
          >
            {t('确定')}(F1)
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  carNumber: PropTypes.string.isRequired,
  dataLoading: PropTypes.number.isRequired,
  headerTitle: PropTypes.string.isRequired,
};

export default i18n(Container);
