import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { t } from '@shein-bbl/react';
import { deliveryScanCheckCarNumber } from './server';
import message from '../../common/message';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle } from '../../../lib/util';

const defaultState = {
  dataLoading: 1,
  carNumber: '',
  headerTitle: '',
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  // 扫描车牌号/装车单号
  * checkCarNumber(action, ctx) {
    markStatus('dataLoading');
    const { code, msg } = yield deliveryScanCheckCarNumber(action.data);
    yield ctx.changeData({
      data: {
        carNumber: '',
      },
    });
    if (code === '0') {
      message.success(t('发运成功'));
      classFocus('carNumberInput');
    } else {
      Modal.error({
        content: msg,
        onOk: () => {
          classFocus('carNumberInput');
        },
        autoFocusButton: null,
      });
    }
  },
};
