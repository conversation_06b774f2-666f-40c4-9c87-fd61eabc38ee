import { sendPostRequest } from '../../../lib/public-request';

// 收货扫描 - 扫描 装车单号/大箱号
export const receivingScanCheckBoxNumber = (param) => sendPostRequest({
  url: '/receive/manager/scan_box_no',
  param,
}, process.env.WPOC_URI);

// 收货扫描 - 确认收货（正常情况）
export const receivingScanCheckActuallyBoxCount = (param) => sendPostRequest({
  url: '/receive/manager/confirm_receive',
  param,
}, process.env.WPOC_URI);

// 收货扫描 -  再次确认收货（有差异的情况下，二次确认）
export const receivingScanDifferenceConfirm = (param) => sendPostRequest({
  url: '/receive/manager/difference/confirm_receive',
  param,
}, process.env.WPOC_URI);

// 收货扫描 -  扫描箱号
export const scanBoxNoApi = (param) => sendPostRequest({
  url: '/receive/manager/delivery_by_case/scan_box_no',
  param,
}, process.env.WPOC_URI);

export const confirmBindRfidAPI = (param) => sendPostRequest({
  url: '/change_transfer/bind_rfid',
  param,
}, process.env.WPOC_URI);
