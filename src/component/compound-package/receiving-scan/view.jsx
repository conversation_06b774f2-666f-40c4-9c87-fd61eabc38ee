import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form, Dialog } from 'react-weui/build/packages';
import { push } from 'react-router-redux';
import { i18n, t } from '@shein-bbl/react';
import MotRadio from 'common/mot-radio';
import Header from '../../common/header';
import Footer from '../../common/footer';
import FocusInput from '../../common/focus-input';
import FooterBtn from '../../common/footer-btn';
import store from './reducers';
import style from '../../style.css';
import { classFocus, getWarehouseId, limitInputNumber } from '../../../lib/util';

class Container extends Component {
  componentWillMount() {
    store.init();
  }

  render() {
    const {
      dispatch,
      dataLoading,
      boxNumber,
      fillCarOrderCode,
      actuallyBoxCount,
      isDisabled,
      headerTitle,
      mode,
      expContainerCode,
      rfid,
      showDialog,
    } = this.props;
    const handleSubmit = () => {
      if (!actuallyBoxCount || !boxNumber) {
        return;
      }
      store.checkConfirm({
        param: {
          fillCarOrderCode,
          boxNumber: actuallyBoxCount,
        },
      });
    };
    const radioBoxStyle = {
      display: 'flex',
      justifyContent: 'space-around',
      padding: '10px 0',
      backgroundColor: '#fff',
      marginBottom: 10,
    };
    return (
      <div className={style.flexColContainer}>
        <Header title={headerTitle || t('收货扫描')} />
        <div style={radioBoxStyle}>
          <MotRadio
            name={t('按车收货')}
            checked={mode === 1}
            onChange={(checked) => {
              if (checked) {
                store.changeData({
                  data: {
                    mode: 1,
                    boxNumber: '',
                    boxNo: '',
                  },
                });
                classFocus('boxNumber');
              }
            }}
          />
          <MotRadio
            name={t('按箱收货')}
            checked={mode === 2}
            onChange={(checked) => {
              if (checked) {
                store.changeData({
                  data: {
                    mode: 2,
                    boxNumber: '',
                    boxNo: '',
                  },
                });
                classFocus('boxNo');
              }
            }}
          />
        </div>
        <Form>
          {mode === 1 && (
            <>
              <FocusInput
                data-bind="boxNumber"
                className="boxNumber"
                disabled={dataLoading === 0}
                onPressEnter={(e) => {
                  if (!e.target.value.trim()) {
                    return;
                  }
                  store.receivingScanCheckBoxNumber({ param: { boxNumber } });
                }}
              >
                <label>{t('装车单号/箱号')}</label>
              </FocusInput>
              <FocusInput
                data-bind="actuallyBoxCount"
                className="actuallyBoxCount"
                disabled={isDisabled || dataLoading === 0}
                onPressEnter={() => handleSubmit()}
                onChange={({ target: { value } }) => {
                  if (limitInputNumber(value).length > 5) {
                    store.changeData({
                      data: {
                        actuallyBoxCount: limitInputNumber(value)
                          .slice(0, 5),
                      },
                    });
                    return;
                  }
                  store.changeData({
                    data: {
                      actuallyBoxCount: limitInputNumber(value),
                    },
                  });
                }}

              >
                <label>{t('实际箱数')}</label>
              </FocusInput>
            </>
          )}
          {mode === 2 && (
            <>
              <FocusInput
                data-bind="boxNo"
                className="boxNo"
                disabled={dataLoading === 0}
                onPressEnter={(e) => {
                  if (!e.target.value.trim()) {
                    return;
                  }
                  store.scanBox({
                    param: {
                      boxNo: e.target.value,
                      combineAddress: getWarehouseId(),
                    },
                  });
                }}
              >
                <label>{t('箱号')}</label>
              </FocusInput>
              <FocusInput
                data-bind="fillCarOrderCodeRes"
                disabled
              >
                <label>{t('装车单号')}</label>
              </FocusInput>
              <FocusInput
                data-bind="boxNum"
                disabled
              >
                <label>{t('待收货箱数')}</label>
              </FocusInput>
            </>
          )}
          {expContainerCode ? (
            <FocusInput
              data-bind="expContainerCode"
              disabled
              style={{ color: 'red', fontWeight: 'bold', fontSize: '20px' }}
            >
              <label>{t('异常上架箱号')}</label>
            </FocusInput>
          ) : ''}
        </Form>
        <Footer>
          <FooterBtn
            disabled={!actuallyBoxCount || !boxNumber || dataLoading === 0}
            onClick={() => handleSubmit()}
          >
            {t('收货完成')}
          </FooterBtn>
        </Footer>
        <Dialog
          title={t('绑定RFID')}
          show={showDialog}
          buttons={[{
            label: t('取消'),
            type: 'default',
            onClick: () => {
              store.changeData({
                data: {
                  showDialog: false,
                  rfid: '',
                  boxNo: '',
                },
              });
              classFocus('boxNo');
            },
          }, {
            label: t('确定'),
            type: 'primary',
            onClick: () => {
              store.handleBindRfid(rfid);
            },
          }]}
        >
          <div style={{ display: 'flex' }}>
            <div>
              <FocusInput
                style={{ backgroundColor: 'rgba(189, 166, 166, 0.08)', borderRadius: '5px', padding: '5px 10px' }}
                className="rfid"
                placeholder={t('请输入')}
                value={rfid}
                onChange={(e) => {
                  store.changeData({
                    data: {
                      rfid: e.target.value,
                    },
                  });
                }}
                onPressEnter={(e) => {
                  store.handleBindRfid(e.target.value);
                }}
              />
            </div>
          </div>
        </Dialog>
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  dataLoading: PropTypes.number.isRequired,
  boxNumber: PropTypes.string.isRequired,
  fillCarOrderCode: PropTypes.string.isRequired,
  actuallyBoxCount: PropTypes.string.isRequired,
  isDisabled: PropTypes.bool.isRequired,
  headerTitle: PropTypes.string.isRequired,
  mode: PropTypes.number,
  rfid: PropTypes.string,
  showDialog: PropTypes.bool,
};

export default i18n(Container);
