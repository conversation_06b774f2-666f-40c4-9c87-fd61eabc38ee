import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { select } from 'redux-saga/effects';
import { t } from '@shein-bbl/react';
import {
  receivingScanCheckBoxNumber,
  receivingScanCheckActuallyBoxCount,
  receivingScanDifferenceConfirm, scanBoxNoApi,
  confirmBindRfidAPI,
} from './server';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import message from '../../common/message';
import tipAudio from '../../../source/audio/dingdong.mp3';

const audio = new Audio(tipAudio);
audio.load();

const defaultState = {
  dataLoading: 1,
  boxNumber: '', // 装车单号
  actuallyBoxCount: '', //
  fillCarOrderCode: '',
  isDisabled: true, // 箱数输入框是否禁止
  headerTitle: '',
  mode: 1, // 收货模式 1 按车收货 2 按箱收货
  fillCarOrderCodeRes: '', // 接口返回的装车单号
  boxNum: '', // 接口返回的待收箱数量
  expContainerCode: '', // 异常上架箱号
  rfid: '',
  showDialog: false,
};

export default {
  defaultState,
  $init(draft) {
    assign(draft, defaultState);
    classFocus('boxNumber');
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  // 扫描装车单号/箱号
  * receivingScanCheckBoxNumber(action, ctx) {
    markStatus('dataLoading');
    const res = yield receivingScanCheckBoxNumber({
      boxNo: action.param.boxNumber,
    });
    if (res.code === '0') {
      const { resultType, fillCarOrderCode } = res.info;
      if (resultType === 2) {
        yield ctx.changeData({
          data: {
            isDisabled: false,
            fillCarOrderCode,
          },
        });
        classFocus('boxNumber');
        classFocus('actuallyBoxCount');
      } else if (resultType === 1) {
        // 弹窗确认
        const status = yield new Promise((r) => Modal.confirm({
          content: t('该批次已签收，是否重新签收？'),
          onOk: () => r(1),
          onCancel: () => r(2),
        }));
        // 点击确认，重新签收
        if (status === 1) {
          yield ctx.changeData({
            data: {
              isDisabled: false,
              fillCarOrderCode,
            },
          });
          classFocus('boxNumber');
          classFocus('actuallyBoxCount');
        }
        // 点击取消，初始化
        if (status === 2) {
          yield ctx.init();
        }
      } else if (resultType === 3) {
        Modal.error({
          className: 'boxNumber',
          content: t('这一车转运箱只允许按箱收货'),
        });
        yield ctx.changeData({ data: { boxNumber: '' } });
      }
    } else {
      Modal.error({
        className: 'boxNumber',
        content: res.msg,
      });
      yield ctx.changeData({ data: { boxNumber: '' } });
    }
  },
  // 确认收货
  * checkConfirm(action, ctx) {
    markStatus('dataLoading');
    const { fillCarOrderCode } = yield select((v) => v['compound-package/receiving-scan']);
    const res = yield receivingScanCheckActuallyBoxCount(action.param);
    if (res.code === '0') {
      const {
        resultType,
        trueBoxNumber,
      } = res.info;
      if (resultType === 1) {
        message.success(t('操作成功'), 2000);
        yield ctx.init();
        classFocus('boxNumber');
      } else if (resultType === 2) {
        const status = yield new Promise((r) => {
          Modal.confirm({
            content: `${t('系统箱数为{}, 与实际收货数量不一致，确定保存吗？', trueBoxNumber)}`,
            onOk: () => r(1),
            onCancel: () => r(2),
          });
        });
        if (status === 1) {
          const res2 = yield receivingScanDifferenceConfirm({
            boxNo: fillCarOrderCode,
          });
          if (res2.code === '0') {
            message.success(t('操作成功'), 2000);
            yield ctx.init();
          } else {
            Modal.error({
              className: 'boxNumber',
              content: res2.msg,
            });
          }
        } else if (status === 2) {
          yield ctx.changeData({
            data: {
              actuallyBoxCount: '',
            },
          });
          classFocus('actuallyBoxCount');
        }
      }
    } else {
      yield ctx.init();
      Modal.error({
        className: 'boxNumber',
        content: res.msg,
      });
    }
  },
  * scanBox(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanBoxNoApi(action.param);
    if (res.code === '0') {
      const {
        resultType, errorMessage, fillCarOrderCode, boxNum, expContainerCode, needBindRfid,
      } = res.info;
      if (expContainerCode) {
        audio.play();
      }
      yield ctx.changeData({
        data: {
          expContainerCode,
        },
      });
      if (resultType === 0) {
        message.success(t('操作成功'), 2000);
        // 成功
        // 弹RFID绑定弹框
        if (needBindRfid) {
          yield ctx.changeData({
            data: {
              fillCarOrderCodeRes: fillCarOrderCode,
              boxNum,
              showDialog: true,
              rfid: '',
            },
          });
          classFocus('rfid');
        } else {
          yield ctx.changeData({
            data: {
              fillCarOrderCodeRes: fillCarOrderCode,
              boxNum,
              boxNo: '',
            },
          });
          classFocus('boxNo');
        }
      } else if (resultType === 1) {
        Modal.error({
          className: 'boxNo',
          content: errorMessage,
        });
        yield ctx.changeData({ data: { boxNo: '' } });
      }
    } else {
      Modal.error({
        className: 'boxNo',
        content: res.msg,
      });
      yield ctx.changeData({ data: { boxNo: '' } });
    }
  },
  /**
   * 绑定RFID
   */
  * handleBindRfid(rfid) {
    markStatus('bindModalLoading');
    const { boxNo } = yield '';
    const res = yield confirmBindRfidAPI({
      containerCode: boxNo,
      rfid,
      scene: 2,
    });
    if (res.code === '0') {
      // 页面保留选项“按箱收货”，清空箱号输入并重新获取光标
      yield this.changeData({
        data: {
          showDialog: false,
          boxNo: '',
          rfid: '',
        },
      });
      message.success(t('绑定RFID成功'));
      classFocus('boxNo');
    } else {
      yield this.changeData({
        data: {
          rfid: '',
        },
      });
      Modal.error({
        className: 'rfid',
        content: res.msg,
      });
    }
  },
};
