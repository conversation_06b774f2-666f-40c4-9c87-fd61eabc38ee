import { sendPostRequest } from '../../../lib/public-request';

// 分包扫描-箱号
export const subpackageScanCheckBoxNumber = param => sendPostRequest({
  url: '/combine_package_scanning/new_scan_box_no',
  param,
}, process.env.WPOC_URI);

// 分包扫描-箱号--新增接口
export const subpackageScanCheckBoxNumberNew = param => sendPostRequest({
  url: '/combine_package_scanning/judge_scan_box',
  param,
}, process.env.WPOC_URI);

// 根据协作批次号获取未分包的转运箱号
export const getUnScanTransferboxes = param => sendPostRequest({
  url: '/combine_package_scanning/get_un_scan_transferboxes',
  param,
}, process.env.WPOC_URI);

// 分包扫描-空箱操作
export const subpackageScanEmptyPackage = param => sendPostRequest({
  url: '/combine_package_scanning/empty_box',
  param,
}, process.env.WPOC_URI);

// 分包扫描-更换库位
export const replaceLocationApi = param => sendPostRequest({
  url: '/combine_package_scanning/replace_location',
  param,
}, process.env.WPOC_URI);

// 分包扫描-初始化数据
export const initScanBoxNoApi = param => sendPostRequest({
  url: '/combine_package_scanning/init_scan_box_no',
  param,
});

// 分包扫描-获取推荐库位
export const getLocationApi = param => sendPostRequest({
  url: '/combine_package_scanning/get_location',
  param,
});
