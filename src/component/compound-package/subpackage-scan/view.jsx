import React from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import SpScan from './jsx/sp-scan';
import SpScanConfirm from './jsx/sp-scan-confirm';

class Container extends React.Component {
  componentDidMount() {
    // store.init();
  }

  render() {
    const {
      type,
    } = this.props;
    switch (type) {
      case 1:
        return (
          <div>
            <SpScanConfirm {...this.props} />
          </div>
        );
      default:
        return (
          <div>
            <SpScan {...this.props} />
          </div>
        );
    }
  }
}

Container.propTypes = {
  type: PropTypes.number.isRequired,
};

export default i18n(Container);
