import React from 'react';
import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { classFocus, getHeaderTitle } from 'lib/util';
import {
  subpackageScanCheckBoxNumberNew,
  subpackageScanCheckBoxNumber,
  subpackageScanEmptyPackage,
  getUnScanTransferboxes, initScanBoxNoApi, replaceLocationApi, getLocationApi,
} from './server';
import Modal from '../../common/modal';
import message from '../../common/message';

const defaultRecommendWay = 1;

// 根据code获取对应的名称
export const getRecommendWayName = (value) => {
  const list = [{
    value: 1,
    name: t('最早优先'),
  }, {
    value: 2,
    name: t('最近优先'),
  }];
  const item = list.find((i) => i.value === value);
  return item ? item.name : '';
};

export const defaultState = {
  dataLoading: 1,
  type: 0, // 0 默认页面 1 确认页面
  boxNumber: '', // 转运箱号
  packageNumber: '',
  isCommit: 1, // 是否提交
  scanBoxNoRsp: {
    coBatchNo: '',
    hasScanNumber: 0,
    unScanNumber: 0,
  },
  coBatchNo: '', // 协作批次号
  transferContainerCodes: [], // 确认页面的转运箱号列表
  selectedTransferContainerCode: '', // 确认页面勾选的转运箱号
  showSelect: false,
  headerTitle: '',
  recommendWays: defaultRecommendWay, // 推荐方式, 1-最早优先 2-最近优先
  canSpitLocationNum: 0, // 可分库位数
  recommendLocation: '', // 推荐库位
  modalVisible: false,
  locationNotExist: false,
};

export default {
  defaultState,
  $init(draft) {
    assign(draft, defaultState);
    classFocus('boxNumber');
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * getInfoByRecommendWays(action, ctx) {
    const res = yield initScanBoxNoApi(action.params);
    if (res.code === '0') {
      const { canSpitLocationNum, location } = res.info;
      yield ctx.changeData({
        data: {
          canSpitLocationNum,
          recommendLocation: location,
        },
      });
    } else {
      console.log(res.msg);
    }
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    // 页面初始化根据推荐方式获取信息
    yield ctx.getInfoByRecommendWays({ params: { recommendWays: defaultRecommendWay } });
  },
  // 最初扫描箱号
  * firstCheckBoxNumber(action, ctx) {
    const {
      code,
      msg,
      info,
    } = yield subpackageScanCheckBoxNumberNew(action.param);
    if (code === '0') {
      // needConfirm 为 true的话，需要弹窗，并且将转运箱数和集货货位显示出来
      const {
        needConfirm,
        location,
        fillBoxNum,
      } = info;
      if (needConfirm) {
        const status = yield new Promise((r) => Modal.confirm({
          content: `${t('库位')}${location}${t('共')}${fillBoxNum}${t('箱')},${t('请确定库位上货物数量是否正确')}`,
          onOk: () => (r('ok')),
          onCancel: () => (r('cancel')),
        }));
        if (status === 'ok') {
          yield ctx.checkBoxNumber({
            param: action.param,
          });
        }
        if (status === 'cancel') {
          classFocus('boxNumber');
          yield ctx.changeData({
            data: {
              boxNumber: '',
              scanBoxNoRsp: {
                coBatchNo: '',
                hasScanNumber: 0,
                unScanNumber: 0,
              },
            },
          });
        }
      } else {
        // 若 needConfirm为 false或者点击了弹窗的确定的按钮, 调用checkBoxNumber
        yield ctx.checkBoxNumber({
          param: action.param,
        });
      }
    } else {
      Modal.error({
        content: msg,
        onOk: () => classFocus('boxNumber'),
      });

      // 获取推荐库位
      yield ctx.getLocation({
        param: {
          location: action.param.location,
          recommendWays: action.param.recommendWays,
          boxCode: action.param.transferContainerCode,
        },
      });

      yield ctx.changeData({
        data: {
          boxNumber: '',
        },
      });
    }
  },
  // 再次扫描箱号
  * checkBoxNumber(action, ctx) {
    const {
      code,
      msg,
      info,
    } = yield subpackageScanCheckBoxNumber(action.param);
    if (code === '0') {
      yield ctx.changeData({
        data: {
          boxNumber: '', // 清空转运箱号
          scanBoxNoRsp: info,
        },
      });
      // 获取推荐库位
      yield ctx.getLocation({
        param: {
          location: action.param.location,
          recommendWays: action.param.recommendWays,
          boxCode: action.param.transferContainerCode,
        },
      });
      const { needConfirmSpitBatch, location } = info;
      if (needConfirmSpitBatch) {
        const status = yield new Promise((r) => Modal.confirm({
          content: (
            <div>
              <div>{t('你上一批次未分包完成，是否确认分包新的批次')}</div>
              {location && <div style={{ color: 'red' }}>{location}</div>}
            </div>
          ),
          onOk: () => (r('ok')),
          onCancel: () => (r('cancel')),
        }));
        if (status === 'ok') {
          yield ctx.checkBoxNumber({
            param: {
              ...action.param,
              isSpitNewBatch: 1,
            },
          });
        }
        return;
      }

      if (+info.unScanNumber === 0) {
        Modal.info({
          content: t('该协作批次已分包完毕'),
        });
        classFocus('boxNumber');
        yield ctx.changeData({
          data: {
            boxNumber: '',
            scanBoxNoRsp: {
              coBatchNo: '',
              hasScanNumber: 0,
              unScanNumber: 0,
            },
          },
        });
      }
    } else {
      Modal.error({
        content: msg,
        onOk: () => classFocus('boxNumber'),
      });

      // 获取推荐库位
      yield ctx.getLocation({
        param: {
          location: action.param.location,
          recommendWays: action.param.recommendWays,
          boxCode: action.param.transferContainerCode,
        },
      });

      yield ctx.changeData({
        data: {
          boxNumber: '',
        },
      });
    }
  },
  // 确认清空箱
  * confirmEmptyPackage(action, ctx) {
    const {
      code,
      msg,
      info,
    } = yield subpackageScanEmptyPackage(assign({
      isCommit: 1,
    }, action.param));

    if (code === '0') {
      const {
        result,
        coBatchCode,
      } = info;
      switch (result) {
        case '2': {
          Modal.info({
            content: `${t('该协作批次已分包完毕,协作批次为{}', coBatchCode)}`,
            onOk: () => classFocus('coBatchNo'),
          });
          break;
        }
        case '3': {
          Modal.info({
            content: `${t('异常批次已完成分包，协作批次为{}', coBatchCode)}`,
            onOk: () => classFocus('coBatchNo'),
          });
          break;
        }
        default: {
          message.success(t('操作成功'), 2000);
          classFocus('coBatchNo');
        }
      }
      // 清空数据
      yield ctx.changeData({
        data: {
          coBatchNo: '', // 协作批次号
          transferContainerCodes: [], // 确认页面的转运箱号列表
          selectedTransferContainerCode: '', // 确认页面勾选的转运箱号
        },
      });
      classFocus('coBatchNo');
    } else {
      Modal.error({
        content: msg,
        onOk: () => classFocus('coBatchNo'),
      });
    }
  },
  // 根据协作批次号获取未分包的转运箱号
  * getOtherBoxNumber(action, ctx) {
    const result = yield getUnScanTransferboxes(action.param);
    if (result.code === '0') {
      yield ctx.changeData({
        data: {
          transferContainerCodes: result.info.transferContainerCodes,
        },
      });
      classFocus('selectedTransferContainerCode');
    } else {
      yield ctx.changeData({
        data: {
          coBatchNo: '',
        },
      });
      Modal.error({
        content: result.msg,
        onOk: () => classFocus('coBatchNo'),
      });
    }
  },
  // 箱空操作
  * emptyPackage(action, ctx) {
    const res = yield subpackageScanEmptyPackage(assign({
      isCommit: '2',
    }, action.param));
    if (res.code === '0') {
      const status = yield new Promise((r) => Modal.confirm({
        content: `${t('箱号还有{}个包裹未分包，确定要清空箱？', res.info.emptyPackageNos.length)}`,
        onOk: () => r(1),
        onCancel: () => r(2),
      }));
      if (status === 1) {
        yield ctx.confirmEmptyPackage(action);
      }
    } else {
      Modal.error({
        content: res.msg,
      });
    }
  },
  // 更换库位
  * replaceLocation(action, ctx) {
    const res = yield replaceLocationApi(action.params);
    if (res.code === '0') {
      const { locationNotExist, location, canSpitLocationNum } = res.info;
      if (!locationNotExist) {
        yield ctx.changeData({
          data: {
            recommendLocation: location,
            canSpitLocationNum,
            locationNotExist: false,
            modalVisible: false,
          },
        });
        classFocus('boxNumber');
      } else {
        // 合包库位不存在
        yield ctx.changeData({
          data: {
            locationNotExist: true,
            changeLocation: '',
          },
        });
      }
    } else {
      Modal.error({
        content: res.msg,
        className: 'changeLocation',
      });
      yield ctx.changeData({ data: { changeLocation: '' } });
    }
  },
  // 获取推荐库位
  * getLocation(action, ctx) {
    const res = yield getLocationApi(action.param);
    if (res.code === '0') {
      const { canSpitLocationNum, location } = res.info;
      // 设置推荐库位
      yield ctx.changeData({
        data: {
          canSpitLocationNum,
          recommendLocation: location,
        },
      });
    } else {
      console.log(res.msg);
    }
  },
};
