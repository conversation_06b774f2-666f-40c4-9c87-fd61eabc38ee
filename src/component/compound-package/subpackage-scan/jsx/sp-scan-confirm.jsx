import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import { classFocus } from '../../../../lib/util';
import FooterBtn from '../../../common/footer-btn';
import store from '../reducers';
import Footer from '../../../common/footer';
import Header from '../../../common/header';
import FocusInput from '../../../common/focus-input';
import Pickers from '../../../common/pickers';

class SpScanConfirm extends React.Component {
  componentDidMount() {
    this.bNumberFocusEvent = () => {
      setTimeout(() => this.bNumberInputRef.focus());
    };
    this.tcCodeFocusEvent = () => {
      setTimeout(() => this.tcCodeInputRef.focus());
    };
    store.changeData({
      data: {
        coBatchNo: '',
        transferContainerCodes: [],
        selectedTransferContainerCode: '',
      },
    });
  }

  render() {
    const {
      coBatchNo,
      transferContainerCodes,
      selectedTransferContainerCode,
      dataLoading,
      showSelect,
      headerTitle,
    } = this.props;
    // picker组件接受空数组，点确定会报错
    const transferContainerCodesData = [{
      items: ((transferContainerCodes && transferContainerCodes.length > 0) ? transferContainerCodes : ['']).map(v => ({
        label: v,
        value: v,
      })),
    }];
    return (
      <div>
        <Header title={headerTitle || t('分包扫描')} />
        <Form>
          <FocusInput
            data-bind="coBatchNo"
            className="coBatchNo"
            disabled={dataLoading === 0}
            onChange={e => store.changeData({ data: { coBatchNo: e.target.value } })}
            onPressEnter={() => coBatchNo && store.getOtherBoxNumber({
              param: {
                coBatchNo,
              },
            })}
            ref={(node) => {
              this.bNumberInputRef = node;
            }}
          >
            <label>{t('协作批次号')}</label>
          </FocusInput>
          <Pickers
            value={selectedTransferContainerCode}
            className="selectedTransferContainerCode"
            label={t('箱号')}
            placeholder={t('请选择')}
            onClick={() => store.changeData({ data: { showSelect: true } })}
            onChange={(select) => {
              if (!select) {
                return;
              }
              store.changeData({ data: { showSelect: false, selectedTransferContainerCode: `${select.value}` || '' } });
              classFocus('containerCode');
            }}
            show={showSelect}
            pickerData={transferContainerCodesData}
            onCancel={() => store.changeData({ data: { showSelect: false } })}
          />

        </Form>
        <Footer
          beforeBack={() => {
            store.changeData({
              data: {
                type: 0,
              },
            });
            classFocus('boxNumber');
          }}
        >
          <FooterBtn
            disabled={!coBatchNo || !selectedTransferContainerCode || dataLoading === 0}
            onClick={() => store.emptyPackage({
              param: {
                transferContainerCode: selectedTransferContainerCode,
                recommendWays: 1,
                isSpitNewBatch: 2,
              },
            })}
          >
            {t('确认箱空')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

SpScanConfirm.propTypes = {
  coBatchNo: PropTypes.string.isRequired,
  transferContainerCodes: PropTypes.arrayOf(PropTypes.string),
  selectedTransferContainerCode: PropTypes.string.isRequired,
  dataLoading: PropTypes.number.isRequired,
  showSelect: PropTypes.bool.isRequired,
  headerTitle: PropTypes.string.isRequired,
};
export default SpScanConfirm;
