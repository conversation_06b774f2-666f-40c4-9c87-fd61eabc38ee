import React from 'react';
import PropTypes from 'prop-types';
import { Dialog, Form } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import { classFocus } from 'lib/util';

const ChangeLocationModal = (props) => {
  const {
    modalVisible,
    locationNotExist,
  } = props;

  return (
    <Dialog
      title={`${t('更换推荐的可分库位')}`}
      show={modalVisible}
      buttons={[{
        label: t('关闭'),
        type: 'primary',
        onClick: () => {
          store.changeData({ data: { modalVisible: false } });
          classFocus('boxNumber');
        },
      }]}
    >
      <Form
        style={{
          boxShadow: 'none',
          textAlign: 'left',
        }}
      >
        <FocusInput
          data-bind="changeLocation"
          className="changeLocation"
          onPressEnter={(e) => {
            const { value } = e.target;
            if (!value) {
              return;
            }
            store.replaceLocation({
              params: {
                locationNo: value
              },
            });
          }}
        >
          <label>{t('库位号')}</label>
        </FocusInput>
      </Form>
      {locationNotExist && (
        <div style={{ color: '#F85555',marginTop:5 }}>
          {t('此合包集货库位不存在')}
        </div>
      )}
    </Dialog>
  );
};

ChangeLocationModal.propTypes = {
  modalVisible: PropTypes.bool,
  locationNotExist: PropTypes.bool,
};

export default ChangeLocationModal;
