import { sendPostRequest } from '../../../lib/public-request';

// 拆包投线-扫描分拣机
export const scanSorterCodeAPI = (param) => sendPostRequest({
  url: '/collect_check/scan_sorter_code',
  param,
}, process.env.WPOC_URI);

// 拆包投线-扫描箱号
export const scanBoxNoAPI = (param) => sendPostRequest({
  url: '/collection_check/split/scan_box_no',
  param,
}, process.env.WPOC_URI);

// 强制退出
export const exitAPI = (param) => sendPostRequest({
  url: '/collection_check/exit',
  param,
}, process.env.WPOC_URI);
