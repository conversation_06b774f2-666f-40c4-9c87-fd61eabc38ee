import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { classFocus } from 'lib/util';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, Footer, FooterBtn, View, modal,
} from 'common';
import style from './style.less';
import store from './reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      initLoading,
      dataLoading,
      scanSorterObj,
      transferContainerCode,
      showDetailVisible,
      infoContent,
      successVisible,
      sorterCode,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle} />
        <View
          diff={100} // 默认56; 页面内容高度：window.innerHeight - diff 中的 diff 值【必填: 100是头部标题44+底部按钮56之和】
          flex={false} // flex布局，默认为true，当需要固定单个输入框时，不启用【选填: 根据实际情况决定】
          initLoading={initLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面【选填: 根据实际情况决定】
        >
          {(!showDetailVisible || successVisible) && (
            <Form>
              <FocusInput
                autoFocus
                value={sorterCode}
                className="sorterCode"
                placeholder={t('请扫描')}
                disabled={dataLoading === 0}
                onChange={(e) => {
                  const { value } = e.target;
                  store.changeData({
                    sorterCode: value,
                    scanSorterObj: {
                      ...scanSorterObj,
                      firstSorterCode: !successVisible ? value : scanSorterObj.firstSorterCode,
                      sorterCode: value,
                    },
                  });
                }}
                onPressEnter={(e) => {
                  const { value } = e.target;
                  // 空值不触发请求【是否在FocusInput统一处理】
                  if (!value && value !== 0) {
                    return;
                  }
                  store.scanSorterCode({
                    ...scanSorterObj,
                    isComplete: successVisible,
                    firstSorterCode: !successVisible ? value : scanSorterObj.firstSorterCode,
                    sorterCode: value,
                  });
                }}
              >
                <label>{t('分拣机号')}</label>
              </FocusInput>
            </Form>
          )}
          { !successVisible && (
            <Form>
              <FocusInput
                value={transferContainerCode}
                className="transferContainerCode"
                placeholder={t('请扫描')}
                disabled={dataLoading === 0}
                onChange={(e) => {
                  const { value } = e.target;
                  store.changeData({
                    transferContainerCode: value,
                  });
                }}
                onPressEnter={(e) => {
                  const { value } = e.target;
                  // 空值不触发请求
                  if (!value && value !== 0) {
                    return;
                  }
                  store.scanBoxNo({
                    sorterCode,
                    transferContainerCode: value,
                  });
                }}
              >
                <label>{t('箱号')}</label>
              </FocusInput>
            </Form>
          )}
          {showDetailVisible && (
            <div>
              <p className={style.batchNum}>{t('关联批次')}：{infoContent.coBatchCode}</p>
              <p className={style.batchNum}>{t('关联批次下大箱')}：{infoContent.boxTotalNum}</p>
              <div className={style.boxList}>
                {(infoContent.boxList || []).map((item, index) => (
                  <span style={{ whiteSpace: 'nowrap' }}>
                    <span
                      style={{
                        color: (infoContent.checkedBoxList || []).indexOf(item) !== -1 ? '#02A7F0' : '',
                      }}
                    >
                      {item}
                    </span>
                    {index === (infoContent.boxList.length - 1) ? '' : '、'}
                  </span>
                ))}
              </div>
            </div>
          )}
          {successVisible && (
            <div>
              <p className={style.batchNum} style={{ color: '#027DB4' }}>
                {t('关联批次下大箱已复核完成，如确认拆包投线，请扫描分拣机号！')}
              </p>
              <p className={style.batchNum}>{t('关联批次')}：{infoContent.coBatchCode}</p>
              <p className={style.batchNum}>{t('关联批次下大箱')}：{infoContent.boxTotalNum}</p>
              <p className={style.batchNum}>{t('分拣机号')}：{infoContent.sorterCode}</p>
            </div>
          )}
        </View>
        <Footer
          hideBackBtn={showDetailVisible}
          beforeBack={(back) => {
            if (successVisible) {
              store.exit({ exitType: 2 });
            } else {
              back();
            }
          }}
        >
          {showDetailVisible && (
            <FooterBtn
              onClick={() => {
                modal.confirm({
                  content: t('该批次还有{}个大箱未进行集箱复核，是否要强制退出集箱复核？', infoContent.uncheckedBoxNum),
                  onOk: () => {
                    store.exit({ exitType: 1 });
                  },
                  onCancel: () => {
                    store.changeData({
                      transferContainerCode: '',
                    });
                    classFocus('transferContainerCode');
                  },
                });
              }}
            >
              {t('强制退出集箱复核')}
            </FooterBtn>
          )}
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  initLoading: PropTypes.bool,
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  scanSorterObj: PropTypes.shape(),
  transferContainerCode: PropTypes.string,
  boxList: PropTypes.arrayOf(),
  showDetailVisible: PropTypes.bool,
  infoContent: PropTypes.shape(),
  successVisible: PropTypes.bool,
  sorterCode: PropTypes.string,
};

export default i18n(Container);
