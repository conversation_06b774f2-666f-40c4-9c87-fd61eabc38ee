import { markStatus } from 'rrc-loader-helper';
import { getHeaderTitle, classFocus } from 'lib/util';
import { t } from '@shein-bbl/react';
import err from 'source/audio/aaoo.mp3';
import ok from 'source/audio/ok.mp3';
import Modal from '../../common/modal';
import { exitAPI, scanBoxNoAPI, scanSorterCodeAPI } from './server';

const errAudio = new Audio(err);
const okAudio = new Audio(ok);
errAudio.load();
okAudio.load();

const defaultState = {
  initLoading: false, // 初始化loading
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  scanSorterObj: { // 拆包投线-扫描分拣机
    coBatchCode: '', // 关联批次号
    firstSorterCode: '', // 初始化进入绑定的分拣机号
    isComplete: '', // 是否集箱复核完成
    sorterCode: '', // 分拣机号
  },
  sorterCode: '', // 分拣机号
  transferContainerCode: '', // 转运箱号
  boxList: [], // 关联批次下大箱
  infoContent: {}, // 展示的信息
  showDetailVisible: false, // 扫描的详情展示
  successVisible: false, // 扫描成功后的展示
};

export default {
  state: defaultState,
  $init: () => defaultState,
  * init() {
    yield this.changeData({ headerTitle: getHeaderTitle() || t('拆包投线集箱复核') });
    // 部分页面进页面会请求获取数据
    yield this.changeData({ initLoading: false });
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  /**
   * 拆包投线-扫描分拣机
   */
  * scanSorterCode(action) {
    const { infoContent } = yield '';
    markStatus('dataLoading');
    const { code, msg } = yield scanSorterCodeAPI({
      coBatchCode: infoContent.coBatchCode,
      firstSorterCode: action.firstSorterCode,
      isComplete: action.isComplete,
      sorterCode: action.sorterCode,
    });
    if (code === '0') {
      okAudio.play();
      if (action.isComplete) {
        yield this.init();
        classFocus('sorterCode');
      } else {
        yield this.changeData({
          transferContainerCode: '',
          showDetailVisible: false,
        });
        classFocus('transferContainerCode');
      }
    } else {
      errAudio.play();
      yield this.changeData({
        sorterCode: '',
        showDetailVisible: false,
      });
      Modal.error({
        content: msg || t('请求数据出错'),
        modalBlurInput: true, // 出现弹窗失焦
        className: 'sorterCode',
      });
    }
  },
  /**
   * 拆包投线-扫描箱号
   */
  * scanBoxNo(action) {
    const { sorterCode } = yield '';
    markStatus('dataLoading');
    const { code, info, msg } = yield scanBoxNoAPI(action);
    if (code === '0') {
      okAudio.play();
      if (info.uncheckedBoxNum !== 0) {
        yield this.changeData({
          transferContainerCode: '',
          infoContent: {
            ...info,
            sorterCode,
          },
          showDetailVisible: true,
        });
        classFocus('transferContainerCode');
      } else {
        yield this.changeData({
          infoContent: {
            ...info,
            sorterCode,
          },
          sorterCode: '',
          showDetailVisible: false,
          successVisible: true,
          headerTitle: t('复核完成'),
        });
        classFocus('sorterCode');
      }
    } else if (code === '500801') {
      Modal.error({ content: msg || t('请求数据出错') });
      // 两秒后返回初始化页面，光标定位到分拣机号
      const status = yield new Promise((r) => {
        setTimeout(() => r(1), 2000);
      });
      if (status === 1) {
        yield this.init();
        classFocus('sorterCode');
      }
    } else {
      errAudio.play();
      yield this.changeData({
        transferContainerCode: '',
      });
      Modal.error({
        content: msg || t('请求数据出错'),
        modalBlurInput: true, // 出现弹窗失焦
        className: 'transferContainerCode',
      });
    }
  },
  /**
   * 拆包投线-强制退出
   */
  * exit(action) {
    const { infoContent } = yield '';
    markStatus('dataLoading');
    const { code, msg } = yield exitAPI({
      checkType: 2, // 集箱复核类型（1-下架投线，2-拆包投线）
      coBatchCode: infoContent.coBatchCode, // 关联批次号
      exitType: action.exitType, // 退出类型（1-强制退出，2-复核完成返回）
    });
    if (code === '0') {
      yield this.init();
      classFocus('sorterCode');
    } else {
      yield this.changeData({
        transferContainerCode: '',
      });
      Modal.error({ content: msg || t('请求数据出错') });
      classFocus('transferContainerCode');
    }
  },
};
