import { t } from '@shein-bbl/react';
import { getHeaderTitle, classFocus } from 'lib/util';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { modal, message } from 'common';
import errAudioSrc from 'source/audio/aaoo.mp3';
import sucAudioSrc from 'source/audio/dingdong.mp3';
import {
  scanBoxAPI,
  scanLocationAPI,
  completeAPI,
  getDetailAPI,
  getUserTaskAPI,
} from './server';

const errorAudio = new Audio(errAudioSrc);
errorAudio.load();

const tipAudio = new Audio(sucAudioSrc);
tipAudio.load();

const defaultState = {
  initLoading: false, // 初始化loading
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  pageType: 1, // 集箱扫描-1，明细-2
  boxNo: '', // 箱号
  locationNo: '', // 库位
  boxCodeDisabled: false, // 箱号禁用状态
  locationDisabled: true, // 库位禁用状态
  version: 0,
  collectBoxId: 0,
  statusInfo: {}, // 状态信息
  successInfo: {}, // 集箱成功信息
  detailInfo: {},
};

export default {
  state: defaultState,
  $init: () => defaultState,
  * init() {
    yield this.changeData({
      headerTitle: getHeaderTitle() || t('合包集箱扫描'),
    });
    yield this.getUserTask();
    classFocus('boxNo');
  },
  changeData(state, data) {
    Object.assign(state, data);
  },

  // 清空数据
  * reset() {
    yield this.changeData({
      boxNo: '',
      locationNo: '',
      version: 0,
      collectBoxId: 0,
      statusInfo: {},
      successInfo: {},
      boxCodeDisabled: false,
      locationDisabled: true,
    });
    classFocus('boxNo');
  },

  // 扫描箱号
  * scanBox() {
    const { boxNo } = yield '';
    markStatus('dataLoading');
    const { code, msg, info } = yield scanBoxAPI({ boxNo });
    if (code === '0') {
      if (info?.successCode === '3102') {
        errorAudio.play();
        const statusRes = yield new Promise((r) => modal.error({
          title: info?.successMsg || '',
          onOk: () => (r(1)),
        }));
        if (statusRes === 1) {
          window.location.hash = '/collection/sub-collection';
        }
        return;
      }
      tipAudio.play();
      yield this.changeData({
        boxCodeDisabled: true,
        locationDisabled: false,
      });
      classFocus('locationNo');
    } else {
      if (code === '503102') {
        errorAudio.play();
        const statusRes = yield new Promise((r) => modal.error({
          title: msg || '',
          onOk: () => (r(1)),
        }));
        if (statusRes === 1) {
          window.location.hash = '/collection/sub-collection';
        }
        return;
      }
      errorAudio.play();
      yield this.changeData({ boxNo: '' });
      modal.error({ content: msg, className: 'boxNo' });
    }
  },

  // 扫描库位
  * scanLocation() {
    const {
      boxNo, locationNo, version, collectBoxId,
    } = yield '';
    markStatus('dataLoading');
    const { code, msg, info } = yield scanLocationAPI({
      boxNo,
      locationNo,
      version,
      collectBoxId,
    });
    if (code === '0') {
      // 自动集满完成
      if (info?.successCode === '3122') {
        tipAudio.play();
        const statusRes = yield new Promise((r) => modal.success({
          title: info?.successMsg || '',
          onOk: () => (r(1)),
        }));
        if (statusRes === 1) {
          message.success(t('该库位集满成功！'), 2000);
          yield this.reset();
        }
        return;
      }
      message.success(t('转运箱集箱成功，请放置到集货库位'), 2000);
      yield this.changeData({
        successInfo: info || {},
        statusInfo: info?.queryUserCollectBoxRsq || {},
        version: info?.version || 0,
        collectBoxId: info?.collectBoxId || 0,
        boxNo: '',
        locationNo: '',
        boxCodeDisabled: false,
        locationDisabled: true,
      });
      classFocus('boxNo');
    } else {
      yield this.locationTips({ code, msg, locationNo });
    }
  },
  // 集满成功操作
  * handleComplete(action) {
    const { version, collectBoxId } = yield '';
    const { locationNo } = action;
    markStatus('dataLoading');
    const res = yield completeAPI({
      version,
      collectBoxId,
      locationNo,
    });
    if (res.code === '0') {
      message.success(t('该库位集满成功！'), 2000);
      yield this.reset();
    } else {
      modal.error({ content: res.msg, className: '' });
    }
  },

  // 点击集满完成
  * clickComplete() {
    const { statusInfo } = yield '';
    const { remainPackageTotalCount } = statusInfo;
    const { locationNo } = statusInfo;
    if (remainPackageTotalCount > 0) {
      const statusRes = yield new Promise((r) => modal.confirm({
        content: t('当前集货位少{}个包裹未达到波次最大包裹数，是否确认集满完成！', remainPackageTotalCount),
        onOk: () => { r(1); },
        onCancel: () => { r(0); },
      }));
      if (statusRes === 1) {
        yield this.handleComplete({ locationNo });
      }
    } else {
      yield this.handleComplete({ locationNo });
    }
  },

  // 查看明细
  * getDetails() {
    const { statusInfo } = yield '';
    const { locationNo } = statusInfo;
    markStatus('dataLoading');

    const res = yield getDetailAPI({
      combineCollectLocation: locationNo,
      // transferContainerCode,
    });
    if (res.code === '0') {
      yield this.changeData({ detailInfo: res.info || {} });
    } else {
      modal.error({ content: res.msg, className: '' });
    }
  },

  // 查看当前用户待集满的数据
  * getUserTask() {
    markStatus('dataLoading');
    const res = yield getUserTaskAPI({});
    if (res.code === '0') {
      if (res.info?.collectLocationStatus === 1) {
        yield this.changeData({
          statusInfo: res.info || {},
          version: res.info?.version || 0,
          collectBoxId: res.info?.collectBoxId || 0,
        });
      }
    } else {
      modal.error({ content: res.msg, className: '' });
    }
  },

  // 提示信息
  * locationTips({ code, msg, locationNo }) {
    // 包裹超出最大限制
    if (code === '503121') {
      errorAudio.play();
      const statusRes = yield new Promise((r) => modal.confirm({
        content: msg || '',
        onOk: () => { r(1); },
        onCancel: () => { r(0); },
      }));
      if (statusRes === 1) {
        yield this.handleComplete({ locationNo });
      } else {
        classFocus('locationNo');
      }
      return;
    }

    errorAudio.play();
    const statusRes = yield new Promise((r) => modal.error({
      title: msg || '',
      onOk: () => (r(1)),
    }));
    if (statusRes === 1) {
      yield this.changeData({ locationNo: '' });
      classFocus('locationNo');
    }
  },
};
