.infoContent {
  padding: 15px;
  box-sizing: border-box;

  .scanInfo {
    .infoItem {
      display: flex;
      margin-bottom: 8px;
    }

    .infoLabel {
      font-size: 14px;
      line-height: 20px;
    }

    .infoValue {
      font-size: 16px;
      color: red;
      line-height: 20px;
      font-weight: bold;
    }
  }

  .successInfo {
    margin-top: 20px;
    background: rgb(3 185 21);
    padding: 5px 8px;

    .infoRow {
      display: flex;
      justify-content: space-between;
      color: #ffffff;
      font-size: 14px;
    }
  }
}

.footerBtn {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 56px;
  padding: 10px 15px;
  background: #ffffff;
  box-shadow: 0 -2px 4px 0 rgba(25, 122, 250, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;

  .btnWrap {
    width: 40%;
    margin: 0 10px;
  }
}

.detailList {
  .detailItem {
    margin-top: 20px;
    padding: 5px 8px;
    border-bottom: 1px solid #DDDDDD;
    .infoRow {
      display: flex;
      justify-content: space-between;
      color: #333333;
      font-size: 14px;
    }
  }
}