import { i18n } from '@shein-bbl/react';
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import { Header } from 'common';
import store from './reducers';
import NormalPage from './jsx/normal-page';
import Detail from './jsx/detail';

function Container(props) {
  useEffect(() => {
    store.init();
  }, []);

  const {
    headerTitle,
    pageType,
    statusInfo,
  } = props;

  return (
    <div>
      <Header title={headerTitle} style={{ width: '100%' }}>
        {statusInfo?.collectLocationStatus === 1 && (
          <div onClick={() => store.changeData({ pageType: 2 })}>
            <Icon name="order-query" style={{ fontSize: 20 }} />
          </div>
        )}
      </Header>
      {pageType === 2 ? (
        <Detail {...props} />
      ) : (
        <NormalPage {...props} />
      )}
    </div>
  );
}

Container.propTypes = {
  initLoading: PropTypes.bool,
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  pageType: PropTypes.number,
  location: PropTypes.string,
  barCode: PropTypes.string,
  statusInfo: PropTypes.shape(),
};

export default i18n(Container);
