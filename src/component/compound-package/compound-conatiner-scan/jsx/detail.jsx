import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { View, Footer } from 'common';
import store from '../reducers';
import styles from '../style.less';

function Details(props) {
  const { detailInfo, statusInfo } = props;
  const {
    combineWellenTypeName, collectLocationStatusName, collectedPackageTotalCount,
  } = detailInfo;
  useEffect(() => {
    store.getDetails();
    return () => {
      store.changeData({ detailInfo: {} });
    };
  }, []);

  return (
    <View diff={100} flex={false}>
      <div className={styles.infoContent}>
        <div className={styles.scanInfo}>
          <div className={styles.infoItem}>
            <div className={styles.infoLabel}>{t('集货库位：')}</div>
            <div className={styles.infoLabel}>{statusInfo?.locationNo}</div>
          </div>
          <div className={styles.infoItem}>
            <div className={styles.infoLabel}>{t('集货库位合包类型：')}</div>
            <div className={styles.infoValue}>{combineWellenTypeName}</div>
          </div>
          <div className={styles.infoItem}>
            <div className={styles.infoLabel}>{t('当前集货位状态：')}</div>
            <div className={styles.infoValue}>{`${collectLocationStatusName}/${t('已集{}个包裹', collectedPackageTotalCount)}`}</div>
          </div>
        </div>
        <div className={styles.detailList}>
          {(detailInfo.detailList || []).map((item, index) => (
            <div className={styles.detailItem} key={item.id}>
              <div className={styles.infoRow}>
                <div className={styles.info}>{`NO.${index + 1}`}</div>
                <div className={styles.info}>{t('集货库位:{}', item.combineCollectLocation)}</div>
              </div>
              <div className={styles.infoRow}>
                <div className={styles.info}>{t('箱号:{}', item.transferContainerCode)}</div>
                <div className={styles.info}>{t('包裹数:{}', item.packageCount)}</div>
              </div>
              <div className={styles.infoRow}>
                <div className={styles.info}>{item.collectBoxEndTime}</div>
                <div className={styles.info}>{item.collectBoxUser}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <Footer
        beforeBack={() => {
          store.changeData({ pageType: 1 });
        }}
      />
    </View>
  );
}

Details.propTypes = {
  // dataLoading: PropTypes.number,
  detailInfo: PropTypes.shape(),
  statusInfo: PropTypes.shape(),
};

export default Details;
