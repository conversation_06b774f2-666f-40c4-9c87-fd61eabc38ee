import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  FocusInput, FooterBtn, Footer, View,
} from 'common';
import store from '../reducers';
import styles from '../style.less';

function NormalPage(props) {
  const {
    initLoading,
    dataLoading,
    locationNo,
    boxNo,
    statusInfo,
    successInfo,
    boxCodeDisabled,
    locationDisabled,
  } = props;
  const {
    combineWellenTypeName,
    collectLocationStatusName,
    remainPackageTotalCount,
    collectedPackageTotalCount,
  } = statusInfo;
  return (
    <View diff={100} flex={false} initLoading={initLoading}>
      <div className={styles.pageContent}>
        <Form>
          <FocusInput
            autoFocus
            value={boxNo}
            className="boxNo"
            placeholder={t('请扫描')}
            disabled={dataLoading === 0 || boxCodeDisabled}
            onChange={(e) => {
              const { value } = e.target;
              store.changeData({ boxNo: value?.trim() });
            }}
            onPressEnter={(e) => {
            // 空值不触发请求
              if (!e.target.value) {
                return;
              }
              store.scanBox();
            }}
          >
            <label>{t('箱号')}</label>
          </FocusInput>
        </Form>
        <Form>
          <FocusInput
            value={locationNo}
            className="locationNo"
            placeholder={t('请扫描')}
            disabled={dataLoading === 0 || locationDisabled}
            onChange={(e) => {
              const { value } = e.target;
              store.changeData({
                locationNo: value?.trim(),
              });
            }}
            onPressEnter={(e) => {
            // 空值不触发请求【是否在FocusInput统一处理】
              if (!e.target.value) {
                return;
              }
              store.scanLocation();
            }}
          >
            <label>{t('集货库位')}</label>
          </FocusInput>
        </Form>
        <div className={styles.infoContent}>
          {statusInfo.collectLocationStatus === 1 && (
          <div className={styles.scanInfo}>
            <div className={styles.infoItem}>
              <div className={styles.infoLabel}>{t('集货库位：')}</div>
              <div className={styles.infoValue}>{statusInfo.locationNo}</div>
            </div>
            <div className={styles.infoItem}>
              <div className={styles.infoLabel}>{t('集货库位合包类型：')}</div>
              <div className={styles.infoValue}>{combineWellenTypeName}</div>
            </div>
            <div className={styles.infoItem}>
              <div className={styles.infoLabel}>{t('当前集货位状态：')}</div>
              <div className={styles.infoValue}>{`${collectLocationStatusName}/${t('已集{}个包裹', collectedPackageTotalCount)}`}</div>
            </div>
            <div className={styles.infoItem}>
              <div className={styles.infoLabel}>{t('自动集满完成仍需：')}</div>
              <div className={styles.infoValue}>{t('{}个包裹', remainPackageTotalCount)}</div>
            </div>
          </div>
          )}
          {!!successInfo?.combineCollectLocation && (
            <div className={styles.successInfo}>
              <div className={styles.infoRow}>
                <div className={styles.info}>{t('集箱成功')}</div>
                <div className={styles.info}>{t('集货库位：{}', successInfo.combineCollectLocation)}</div>
              </div>
              <div className={styles.infoRow}>
                <div className={styles.info}>{t('箱号：{}', successInfo.transferContainerCode)}</div>
                <div className={styles.info}>{t('包裹数：{}', successInfo.packageCount)}</div>
              </div>
              <div className={styles.infoRow}>
                <div className={styles.info}>{successInfo.collectBoxEndTime}</div>
                <div className={styles.info}>{successInfo.collectBoxUser}</div>
              </div>
            </div>
          )}
        </div>
      </div>
      <Footer
        beforeBack={(back) => {
          back();
        }}
      >
        {statusInfo?.collectLocationStatus === 1 && (
          <FooterBtn disabled={dataLoading === 0} onClick={() => store.clickComplete()}>{t('集满完成')}</FooterBtn>
        )}
      </Footer>
    </View>
  );
}

NormalPage.propTypes = {
  initLoading: PropTypes.bool,
  dataLoading: PropTypes.number,
  locationNo: PropTypes.string,
  boxNo: PropTypes.string,
  boxCodeDisabled: PropTypes.bool,
  locationDisabled: PropTypes.bool,
  statusInfo: PropTypes.shape(),
  successInfo: PropTypes.shape(),
};

export default NormalPage;
