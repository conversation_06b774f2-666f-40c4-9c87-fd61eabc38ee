import { sendPostRequest } from 'lib/public-request';

// 扫描箱号
export const scanBoxAPI = (param) => sendPostRequest({
  url: '/combine_wellen_collect_box/scan_box_no',
  param,
}, process.env.WPOC_URI);

// 扫描集货库位
export const scanLocationAPI = (param) => sendPostRequest({
  url: '/combine_wellen_collect_box/scan_location_no',
  param,
}, process.env.WPOC_URI);

// 查询是否存在待集满的数据
export const getUserTaskAPI = (param) => sendPostRequest({
  url: '/combine_wellen_collect_box/query_user_collect_box',
  param,
}, process.env.WPOC_URI);

// 查询明细
export const getDetailAPI = (param) => sendPostRequest({
  url: '/combine_wellen_collect_box/query_collect_box_detail',
  param,
}, process.env.WPOC_URI);

// 集满完成
export const completeAPI = (param) => sendPostRequest({
  url: '/combine_wellen_collect_box/collection_complete',
  param,
}, process.env.WPOC_URI);
