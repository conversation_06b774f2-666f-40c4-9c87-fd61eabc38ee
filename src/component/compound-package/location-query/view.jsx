import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import store from './reducers';
import Header from '../../common/header';
import FocusInput from '../../common/focus-input';
import Footer from '../../common/footer';
import style from '../../style.css';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      collectLocationCode,
      seedContainerCode,
      isContainerCodeDisabled,
      headerTitle,
    } = this.props;

    const height = window.innerHeight - 56;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <Header title={headerTitle || t('集箱货位扫描')} />
        <Form>
          <FocusInput
            disabled={isContainerCodeDisabled}
            placeholder={t('请扫描')}
            autoFocus
            className="seedContainerCode"
            data-bind="seedContainerCode"
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }

              store.changeData({
                data: {
                  isContainerCodeDisabled: true,
                },
              });

              store.scanContainer({
                params: {
                  containerCode: seedContainerCode,
                },
              });
            }}
          >
            <label>{t('转运箱号')}</label>
          </FocusInput>
        </Form>

        <div
          style={{
            textAlign: 'center',
            marginTop: 10,
          }}
        >
          <b>{collectLocationCode}</b>
        </div>

        <Footer
          beforeBack={(back) => {
            store.init();
            back();
          }}
        />

      </div>
    );
  }
}

Container.propTypes = {
  collectLocationCode: PropTypes.string.isRequired,
  seedContainerCode: PropTypes.string.isRequired,
  isContainerCodeDisabled: PropTypes.bool.isRequired,
  headerTitle: PropTypes.string.isRequired,
};

const mapStateToProps = state => state['compound-package/location-query'];
export default connect(mapStateToProps)(i18n(Container));
