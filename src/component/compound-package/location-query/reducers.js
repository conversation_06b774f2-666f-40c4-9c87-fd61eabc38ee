import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { scanPickContainer } from './server';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import { modal } from '../../common';

const defaultState = {
  seedContainerCode: '', // 转运箱
  collectLocationCode: '',
  isContainerCodeDisabled: false,
  headerTitle: '',
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  * scanContainer(action, ctx) {
    const result = yield scanPickContainer({
      ...action.params,
      pageNum: 1,
      pageSize: 10,
    });
    yield ctx.changeData({
      data: {
        isContainerCodeDisabled: false,
      },
    });
    if (result.code === '0') {
      const list = result.info.data || [];
      yield ctx.changeData({
        data: {
          collectLocationCode: list.length ? list[0].collectLocationCode : t('无集箱库位'),
          seedContainerCode: '',
        },
      });
    } else {
      yield ctx.changeData({
        data: {
          collectLocationCode: '',
          seedContainerCode: '',
        },
      });
      modal.error({
        modalBlurInput: true,
        content: result.msg,
        className: 'seedContainerCode',
      });
    }
  },
};
