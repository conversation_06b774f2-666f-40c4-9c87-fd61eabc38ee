import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import {
  containerScanCheckBoxNumber,
  containerScanCheckContainerPosition,
} from './server';

export const defaultState = {
  dataLoading: 1,
  boxNumber: '',
  containerPosition: '',
  wellenCollectLocation: '',
  ableContainerPositionInput: false,
  collectLocationStatus: false,
  firstWellenPackage: false,
  combineGoodsType: '', // 订单类型
  headerTitle: '',
  fillBoxNum: 0,
  hasCollectedNum: 0,
  boxInfoList: [],
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  /**
   * [*checkBoxNumber 扫描箱号]
   * <AUTHOR>
   * @DateTime 2019-11-05
   * @param    {[type]}                 options.value   [description]
   * @param    {[type]}                 options.focusFn [description]
   * @yield    {[type]}                 [description]
   */
  * checkBoxNumber(action, ctx) {
    markStatus('dataLoading');
    const {
      code,
      info,
      msg,
    } = yield containerScanCheckBoxNumber(action.data.param);

    if (code === '0') {
      const {
        wellenCollectLocation,
        collectLocationStatus,
        firstWellenPackage, // 是否第一个协作批次包裹
        combineGoodsType,
        fillBoxNum,
        hasCollectedNum,
        boxInfoList,
        coWellenCode,
      } = info;
      switch (info.status) {
        case 3: {
          const status = yield new Promise((r) => Modal.confirm({
            title: `${t('该箱被强制集货，对应的协作波次号：{}；共{}箱', coWellenCode, fillBoxNum)}`,
            buttons: [{
              type: 'primary',
              label: t('确定'),
              onClick: () => (r('ok')),
            }],
          }));
          if (status === 'ok') {
            yield ctx.init();
            classFocus('boxNumber');
          }
          break;
        }
        case 4: {
          const status = yield new Promise((r) => Modal.error({
            title: t('箱号的状态不是已收货，不允许操作集货'),
            onOk: () => (r('ok')),
          }));
          if (status === 'ok') {
            yield ctx.init();
            classFocus('boxNumber');
          }
          break;
        }
        case 5: {
          // 弹窗
          const status = yield new Promise((r) => Modal.error({
            title: t('没有空置的集货库位'),
            onOk: () => (r('ok')),
          }));
          if (status === 'ok') {
            yield ctx.init();
            classFocus('boxNumber');
          }
          break;
        }
        default: {
          yield ctx.changeData({
            data: {
              ableContainerPositionInput: true,
              wellenCollectLocation,
              collectLocationStatus,
              firstWellenPackage,
              combineGoodsType,
              fillBoxNum,
              hasCollectedNum,
              boxInfoList: boxInfoList || [],
            },
          });
          action.data.focusFn();
        }
      }
    } else if (code === '500829') {
      const status = yield new Promise((r) => Modal.confirm({
        content: t('此箱号已协作批次集箱，请到集箱扫描功能中集箱'),
        onOk: () => {
          window.location.hash = 'compound-package/container-scan';
        },
        onCancel: () => (r('cancel')),
      }));
      if (status === 'cancel') {
        yield ctx.changeData({
          data: {
            boxNumber: '',
            combineGoodsType: '',
          },
        });
        classFocus('boxNumber');
      }
    } else {
      const status = yield new Promise((r) => Modal.error({
        title: msg,
        onOk: () => (r('ok')),
      }));
      if (status === 'ok') {
        yield ctx.changeData({
          data: {
            boxNumber: '',
            combineGoodsType: '',
          },
        });
        classFocus('boxNumber');
      }
    }
  },
  /**
   * [*checkContainerPosition 扫描集货货位]
   * <AUTHOR>
   * @DateTime 2019-11-05
   * @param    {[type]}                 options.value [description]
   * @yield    {[type]}                 [description]
   */
  * checkContainerPosition(action, ctx) {
    markStatus('dataLoading');
    const {
      code,
      info,
      msg,
    } = yield containerScanCheckContainerPosition(action.data.param);
    if (code === '0') {
      const statusVal = info.status;
      const {
        scanLocation,
        fillBoxNum,
        hasCollectedNum,
        boxInfoList,
      } = info;
      switch (statusVal) {
        case 0: {
          const status = yield new Promise((r) => Modal.success({
            title: `${t('该协作波次已集箱完成,波次集货库位{},共{}箱', scanLocation, fillBoxNum)}`,
            onOk: () => (r('ok')),
          }));
          if (status === 'ok') {
            yield ctx.init();
            action.data.focusFn();
          }
          break;
        }
        case 1: {
          const status = yield new Promise((r) => Modal.success({
            title: t('操作成功'),
            onOk: () => (r('ok')),
          }));
          if (status === 'ok') {
            yield ctx.init();
            yield ctx.changeData({
              data: {
                fillBoxNum,
                hasCollectedNum,
                boxInfoList: boxInfoList || [],
              },
            });
            action.data.focusFn();
          }
          break;
        }
        case 2: {
          const status = yield new Promise((r) => Modal.error({
            title: t('请扫描正确的集货库位'),
            onOk: () => (r('ok')),
          }));
          if (status === 'ok') {
            yield ctx.changeData({
              data: {
                containerPosition: '',
              },
            });
            classFocus('containerPosition');
          }
          break;
        }
        default:
      }
    } else {
      const status = yield new Promise((r) => Modal.error({
        title: msg,
        onOk: () => (r('ok')),
      }));
      if (status === 'ok') {
        yield ctx.changeData({
          data: {
            containerPosition: '',
          },
        });
        classFocus('containerPosition');
      }
    }
  },
};
