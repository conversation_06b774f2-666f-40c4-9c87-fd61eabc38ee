import { sendPostRequest } from '../../../lib/public-request';

/**
 * [波次集箱扫描-箱号]
 * <AUTHOR>
 * @DateTime 2019-11-05
 * @param    {[type]}                 param [description]
 * @return   {[type]}                       [description]
 */
export const containerScanCheckBoxNumber = param => sendPostRequest({
  url: '/wellen_collect_split/scan_box_no',
  param,
}, process.env.WPOC_URI);

/**
 * [波次集箱扫描-集货货位]
 * <AUTHOR>
 * @DateTime 2019-11-05
 * @param    {[type]}                 param [description]
 * @return   {[type]}                       [description]
 */
export const containerScanCheckContainerPosition = param => sendPostRequest({
  url: '/wellen_collect_split/scan_location',
  param,
}, process.env.WPOC_URI);
