import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import { Form } from 'react-weui/build/packages';
import FocusInput from '../../common/focus-input';
import {
  Header,
  Footer,
  List,
  SplitBar,
} from '../../common';
import { classFocus } from '../../../lib/util';
import store from './reducers';
import styles from './style.css';

const rows = [
  [
    {
      title: '',
      /* eslint-disable */
      render: v => v.collectedTime ? v.collectedTime : '--',
      /* eslint-enable */
    },
    {
      title: '',
      render: 'boxNo',
    },
  ],
];

class Container extends Component {
  componentDidMount() {
    store.init();
    this.firstInputFocusEvent = () => {
      setTimeout(() => {
        classFocus('boxNumber');
      });
    };
    this.secondInputFocusEvent = () => {
      setTimeout(() => {
        classFocus('containerPosition');
      });
    };
  }

  render() {
    const {
      dataLoading,
      dispatch,
      boxNumber,
      containerPosition,
      wellenCollectLocation,
      ableContainerPositionInput,
      collectLocationStatus,
      firstWellenPackage,
      combineGoodsType,
      headerTitle,
      fillBoxNum,
      hasCollectedNum,
      boxInfoList,
    } = this.props;

    return (
      <div style={{ marginBottom: '56px' }}>
        <Header title={headerTitle || t('波次集箱扫描')} />
        <List
          header={(
            <div style={{ marginLeft: -15 }}>
              <Form style={{ position: 'relative' }}>
                <FocusInput
                  disabled={!dataLoading}
                  autoFocus
                  placeholder={t('请扫描')}
                  data-bind="boxNumber"
                  className="boxNumber"
                  onPressEnter={() => {
                    store.checkBoxNumber({
                      data: {
                        param: {
                          box_no: boxNumber,
                        },
                        focusFn: this.secondInputFocusEvent,
                      },
                    });
                  }}
                >
                  <label>{t('大箱号')}</label>
                </FocusInput>
                <div className={styles.topRightNum}>
                  <span style={{ color: 'red' }}>{hasCollectedNum}</span>
                  <span>/{fillBoxNum}</span>
                </div>
              </Form>
              <Form>
                <FocusInput
                  placeholder={t('请扫描')}
                  disabled={!ableContainerPositionInput || !dataLoading}
                  data-bind="containerPosition"
                  keepFocus={false}
                  className="containerPosition"
                  onPressEnter={() => {
                    store.checkContainerPosition({
                      data: {
                        param: {
                          box_no: boxNumber,
                          scan_collect_location: containerPosition,
                          collect_location: wellenCollectLocation,
                          collectLocationStatus,
                        },
                        focusFn: this.firstInputFocusEvent,
                      },
                    });
                  }}
                >
                  <label>{t('集货库位')}</label>
                </FocusInput>
              </Form>
              <SplitBar>
                { (boxNumber && ableContainerPositionInput) ? (
                  <div style={{ textAlign: 'center', color: 'red' }}>{(firstWellenPackage ? `${t('无上架记录，推荐上架至')}${wellenCollectLocation}` : `${t('已有协作波次上架至')}${wellenCollectLocation}`)}</div>
                ) : '' }
                <div style={{ textAlign: 'center', color: '#616161' }}>{ combineGoodsType || ''}</div>
              </SplitBar>
            </div>
          )}
          data={boxInfoList}
          rows={rows}
          rowStyleOrClass={(r) => {
            if (r.hasCollected) {
              return styles.hasScaned;
            }
            return styles.notScaned;
          }}
          style={{ height: 'calc(100vh - 110px)', overflowY: 'auto' }}
        />
        <Footer />
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  headerTitle: PropTypes.string,
  boxNumber: PropTypes.string,
  containerPosition: PropTypes.string,
  wellenCollectLocation: PropTypes.string,
  combineGoodsType: PropTypes.string,
  ableContainerPositionInput: PropTypes.bool,
  collectLocationStatus: PropTypes.bool,
  firstWellenPackage: PropTypes.bool,
  fillBoxNum: PropTypes.number,
  hasCollectedNum: PropTypes.number,
  boxInfoList: PropTypes.arrayOf(PropTypes.shape()),
  dataLoading: PropTypes.number,
};

export default i18n(Container);
