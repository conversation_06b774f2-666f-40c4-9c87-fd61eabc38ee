import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, Footer, FooterBtn, View,
} from 'common';
import style from './style.css';
import store from './reducers';
import RowInfo from '../../common/row-info';
import Modal from '../../common/modal';
import { classFocus } from '../../../lib/util';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      initLoading,
      dataLoading,
      transferContainerCode,
      coBatchCode,
      boxTotalNum,
      boxList,
      checkedBoxList,
      checkType,
      uncheckedBoxNum,
      forceQuitDisabled,
    } = this.props;
    return (
      <div>
        <Header title={headerTitle} />
        <View
          diff={100} // 默认56; 页面内容高度：window.innerHeight - diff 中的 diff 值【必填: 100是头部标题44+底部按钮56之和】
          flex={false} // flex布局，默认为true，当需要固定单个输入框时，不启用【选填: 根据实际情况决定】
          initLoading={initLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面【选填: 根据实际情况决定】
        >
          <Form>
            <FocusInput
              autoFocus
              value={transferContainerCode}
              className="location"
              placeholder={t('请扫描')}
              disabled={dataLoading === 0}
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  transferContainerCode: value,
                });
              }}
              onPressEnter={(e) => {
                // 空值不触发请求【是否在FocusInput统一处理】
                if (!e.target.value) {
                  return;
                }
                store.scanLocation({
                  transferContainerCode,
                });
              }}
            >
              <label>{t('箱号')}</label>
            </FocusInput>
          </Form>
          {
            coBatchCode && (
              <RowInfo
                type="info"
                label={t('关联批次')}
                content={coBatchCode}
              />
            )
          }
          {
            (boxTotalNum || String(boxTotalNum) === '0') && (
              <RowInfo
                type="info"
                label={t('关联批次下大箱')}
                content={boxTotalNum}
              />
            )
          }
          <div className={style.boxList} style={{ height: 'calc(100vh - 270px)', overflowY: 'auto' }}>
            {
              boxList.map((b) => (
                <div className={[style.boxListItem, checkedBoxList.indexOf(b) > -1 ? style.scannedBox : ''].join(' ')}>{b}</div>
              ))
            }
          </div>
        </View>
        <Footer
          beforeBack={(back) => {
            back();
          }}
        >
          <FooterBtn
            disabled={forceQuitDisabled}
            onClick={() => {
              Modal.confirm({
                content: t('该批次还有{}个大箱未进行集箱复核，是否要强制退出集箱复核？', uncheckedBoxNum),
                modalBlurInput: true,
                onOk: () => {
                  store.forceQuit({
                    exitType: 1,
                    checkType,
                    coBatchCode,
                  });
                },
                onCancel: () => {
                  classFocus('location');
                },
              });
            }}
          >{t('强退集箱复核')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  initLoading: PropTypes.bool,
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  transferContainerCode: PropTypes.string,
  coBatchCode: PropTypes.string,
  boxTotalNum: PropTypes.number,
  boxList: PropTypes.arrayOf(PropTypes.shape()),
  checkedBoxList: PropTypes.arrayOf(PropTypes.shape()),
  checkType: PropTypes.string,
  uncheckedBoxNum: PropTypes.number,
  forceQuitDisabled: PropTypes.bool,
};

export default i18n(Container);
