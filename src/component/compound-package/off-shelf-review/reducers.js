import { classFocus } from 'lib/util';
import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { scanLocationAPI, forceQuitAPI } from './server';
import Modal from '../../common/modal';
import message from '../../common/message';

const defaultState = {
  initLoading: false, // 初始化loading
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  checkType: 1, // 集箱复核类型（1-下架投线，2-拆包投线）
  transferContainerCode: '', // 箱号
  boxList: [], // 关联批次下大箱
  coBatchCode: '', // 关联批次
  boxTotalNum: '', // 关联批次下大箱数量
  checkedBoxList: [], // 已经复核的箱子
  uncheckedBoxNum: 0, // 未复核的箱子
  forceQuitDisabled: true, // 是否禁用强制退出
};

export default {
  state: defaultState,
  $init(draft) {
    assign(draft, defaultState);
  },
  * init() {
    yield this.changeData({ headerTitle: t('下架投线集箱复核') });
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  // 扫描库位
  * scanLocation(param, ctx) {
    const res = yield scanLocationAPI(param);
    if (res.code === '0') {
      const {
        boxList = [],
        coBatchCode = '',
        boxTotalNum = 0,
        checkedBoxList = [],
        uncheckedBoxNum = 0,
      } = res.info || {};
      yield ctx.changeData({
        showDetails: true,
        boxList: boxList.filter((element) => !checkedBoxList.includes(element)).concat(checkedBoxList),
        checkedBoxList,
        uncheckedBoxNum,
        coBatchCode,
        boxTotalNum,
        forceQuitDisabled: false,
      });
      if (uncheckedBoxNum > 0) {
        classFocus('location');
        message.success(t('操作成功'), 2000);
        yield ctx.changeData({ transferContainerCode: '' });
      } else {
        const status = yield new Promise((r) => Modal.success({
          modalBlurInput: true,
          content: t('复核完成'),
          okText: t('确定'),
          onOk: () => r(1),
        }));
        if (status === 1) {
          yield ctx.init();
        }
      }
    } else {
      yield ctx.changeData({ transferContainerCode: '' });
      Modal.error({
        modalBlurInput: true,
        content: res.msg,
      });
    }
  },
  // 强制退出
  * forceQuit(param, ctx) {
    const res = yield forceQuitAPI(param);
    if (res.code === '0') {
      yield ctx.init();
    } else {
      Modal.error({
        modalBlurInput: true,
        content: res.msg,
      });
    }
  },
};
