import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import { Footer, Table, FooterBtn } from 'common';
import { classFocus } from 'lib/util';
import store from '../reducers';

function CompoundList(props) {
  const {
    packageDetails,
    selectedRowIndex,
    dispatch,
    selectedRow,
    combineSubNo,
  } = props;
  const columns = [
    {
      title: t('合包包裹号'),
      dataIndex: 'combineNo',
      default: '',
    },
    {
      title: t('格口'),
      dataIndex: 'chequer',
      default: '',
    },
  ];
  // // 监听键盘事件
  // const handleKeyDown = (event) => {
  //   if (event.key === 'ArrowUp') {
  //     if (selectedRowIndex > 0) {
  //       const nextIndex = selectedRowIndex - 1;
  //       store.changeData({
  //         data: {
  //           selectedRowIndex: nextIndex,
  //           selectedRow: packageDetails[nextIndex],
  //         },
  //       });
  //     }
  //   }
  //   if (event.key === 'ArrowDown') {
  //     if (selectedRowIndex < packageDetails.length - 2) {
  //       const nextIndex = selectedRowIndex + 1;
  //       store.changeData({
  //         data: {
  //           selectedRowIndex: nextIndex,
  //           selectedRow: packageDetails[nextIndex],
  //         },
  //       });
  //     }
  //   }
  // };

  // useEffect(() => {
  //   document.addEventListener('keydown', handleKeyDown);
  //   return () => document.removeEventListener('keydown', handleKeyDown);
  // }, [selectedRowIndex]);

  return (
    <div>
      <div
        style={{
          textAlign: 'center',
          fontSize: 16,
          margin: '15px 0',
        }}
      >
        {t('播种墙异常包裹')}
      </div>
      <Table
        columns={columns}
        dataSource={packageDetails || []}
        maxHeight={300}
        selectedRowIndex={selectedRowIndex}
        onRowClick={(row, rowIndex) => {
          store.changeData({
            data: {
              selectedRowIndex: rowIndex,
              selectedRow: row,
            },
          });
        }}
      />
      <Footer
        dispatch={dispatch}
        footerText={t('取消')}
        beforeBack={() => {
          store.init();
          classFocus('combineSubNo');
        }}
      >
        <FooterBtn
          disabled={selectedRowIndex < 0}
          onClick={() => {
            store.scanPackage({
              combineExpShelfChequerList: [selectedRow],
              combineSubNo,
            });
            store.changeData({ data: { showExceptionPackages: false } });
          }}
        >
          {t('确认')}
        </FooterBtn>
      </Footer>
    </div>
  );
}

CompoundList.propTypes = {
  combineSubNo: PropTypes.string,
  packageDetails: PropTypes.arrayOf(PropTypes.shape),
  selectedRowIndex: PropTypes.number,
  dispatch: PropTypes.func,
  selectedRow: PropTypes.shape(),
};

export default CompoundList;
