import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import * as servers from './server';
import { message } from '../../common';
import { showToast } from '../../common/common';

const defaultState = {
  packageDisabled: 1,
  locationDisabled: 0,
  headerTitle: '',
  combineSubNo: '', // 包裹号
  location: '', // 合包异常库位
  combineSubInfos: [], // 返回的列表
  combineNo: '', // 返回的合包包裹号
  locationNo: '', // 返回的推荐库位
  secondContainerDisabled: 0,
  showSecondContainer: false, // 二分周转箱默认不展示
  secondContainer: '', // 如果不展示，需要拼接到包裹列表中
  showExceptionPackages: false, // 显示播种墙异常页面
  packageDetails: [], // 播种墙异常列表
  selectedRowIndex: 0, // 选中的合包包裹索引
  // 选中的合包包裹
  selectedRow: {
    combineNo: '',
    chequer: '',
  },
};

export default {
  defaultState,
  $init: () => {
    classFocus('combineSubNo');
    return defaultState;
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    classFocus('combineSubNo');
  },
  * errorClear(action, ctx) {
    yield ctx.changeData({
      data: {
        [action.data]: '',
        [action.disabled]: 1,
      },
    });
    Modal.error({
      content: action.msg,
      onOk: () => {
        classFocus(action.data);
      },
    });
  },
  * scanPackage(action, ctx) {
    yield ctx.changeData({
      data: {
        packageDisabled: 0,
      },
    });
    const res = yield servers.exceptionShelfCheckPackageNo(action);
    if (res.code !== '0') {
      yield ctx.errorClear({
        data: 'combineSubNo',
        msg: res.msg,
        disabled: 'packageDisabled',
      });
    } else {
      // 弹出播种墙异常页面
      if (res.info?.combineExpShelfChequerList?.length) {
        yield ctx.changeData({
          data: {
            // combineSubNo: action.combineSubNo,
            showExceptionPackages: true,
            packageDetails: res.info.combineExpShelfChequerList || [],
            selectedRow: res.info.combineExpShelfChequerList[0] || {},
            selectedRowIndex: 0,
          },
        });
        return;
      }
      if (res.info.resultType === 1) {
        showToast(t('包裹已异常下架'));
        classFocus('combineSubNo');
        yield ctx.changeData({
          data: {
            packageDisabled: 1,
            showSecondContainer: false,
            combineSubNo: '',
          },
        });
        return;
      } else if (res.info.resultType === 4) {
        yield ctx.changeData({
          data: {
            packageDisabled: 0,
            locationDisabled: 0,
            showSecondContainer: true,
            secondContainerDisabled: 1,
            secondContainer: '',
            combineSubNo: action.combineSubNo,
            combineSubInfos: res.info.combineSubInfos || [], // 返回的列表
            combineNo: res.info.combineNo, // 返回的合包包裹号
            locationNo: res.info.locationNo, // 返回的推荐库位
          },
        });
        classFocus('secondContainer');
        return;
      }
      yield ctx.changeData({
        data: {
          combineSubNo: action.combineSubNo,
          showSecondContainer: false,
          secondContainer: res.info.secondContainerCode || '', // 返回的二分周转箱
          packageDisabled: 0,
          locationDisabled: 1,
          combineSubInfos: res.info.combineSubInfos || [], // 返回的列表
          combineNo: res.info.combineNo, // 返回的合包包裹号
          locationNo: res.info.locationNo, // 返回的推荐库位
        },
      });
      classFocus('location');
    }
  },
  * scanSecondContainer(action, ctx) {
    const { selectedRow } = yield '';
    yield ctx.changeData({
      data: {
        secondContainerDisabled: 0,
      },
    });
    const res = yield servers.scanSecondContainerAPI({
      ...action,
      combineExpShelfChequerList: selectedRow?.combineNo ? [selectedRow] : undefined,
    });
    if (res.code !== '0') {
      yield ctx.errorClear({
        data: 'secondContainer',
        msg: res.msg,
        disabled: 'secondContainerDisabled',
      });
    } else {
      yield ctx.changeData({
        data: {
          secondContainer: action.secondContainer,
          location: '',
          locationDisabled: 1,
        },
      });
      classFocus('location');
    }
  },
  * scanLocation(action, ctx) {
    yield ctx.changeData({
      data: {
        locationDisabled: 0,
      },
    });
    const { selectedRow } = yield '';
    const res = yield servers.exceptionShelfPost({
      ...action,
      combineExpShelfChequerList: selectedRow?.combineNo ? [selectedRow] : undefined,
    });
    if (res.code !== '0') {
      yield ctx.errorClear({
        data: 'location',
        msg: res.msg,
        disabled: 'locationDisabled',
      });
    } else {
      yield ctx.changeData({
        data: {
          showSecondContainer: false,
          packageDisabled: 1,
          locationDisabled: 0,
          combineSubNo: '', // 包裹号
          location: '', // 合包异常库位
          secondContainer: res.info.secondContainerCode || '', // 返回的二分周转箱
          combineSubInfos: res.info.combineSubInfos || [], // 返回的列表
          combineNo: res.info.combineNo, // 返回的合包包裹号
          locationNo: res.info.locationNo, // 返回的推荐库位
          packageDetails: [],
          selectedRow: {},
          selectedRowIndex: 0,
        },
      });
      classFocus('combineSubNo');
      if (res.info.shelfed) {
        yield ctx.changeData({
          data: {
            locationNo: '', // 上架完成清空推荐
          },
        });
        message.success(t('异常上架成功，该合包包裹已全部上架，请合包'));
      } else {
        message.success(t('异常上架成功'));
      }
    }
  },
};
