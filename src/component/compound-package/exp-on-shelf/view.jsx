import React, { Component } from 'react';
import { connect } from 'react-redux';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { CellBody } from 'react-weui/build/packages/components/cell';
import Icon from '@shein-components/Icon';
import store from './reducers';
import {
  Header,
  Footer,
  FocusInput,
  Table,
} from '../../common';
import PackageList from './jsx/package-list';
import style from './style.css';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      packageDisabled,
      locationDisabled,
      combineSubNo,
      combineNo,
      locationNo,
      combineSubInfos,
      secondContainer,
      secondContainerDisabled,
      showSecondContainer,
      showExceptionPackages,
    } = this.props;
    return (
      <div>
        <Header title={t('合包异常上架')} style={{ width: '100%' }}>
          <Icon
            name="order-query"
            className={style.headerQueryIcon}
            onClick={() => {
              // 跳转到【合包异常上架查询】
              window.location.hash = 'compound-package/exp-on-shelf-query';
            }}
          />

        </Header>
        {showExceptionPackages ? (
          <PackageList {...this.props} />
        ) : (
          <div>
            <Form>
              <FocusInput
                placeholder={t('请扫描')}
                className="combineSubNo"
                data-bind="combineSubNo"
                autoFocus
                allowClear
                disabled={packageDisabled === 0}
                onPressEnter={(e) => {
                  if (e.target.value) {
                    store.scanPackage({
                      combineSubNo: e.target.value,
                    });
                  }
                }}
              >
                <label>{t('包裹号/容器号')}</label>
              </FocusInput>
              {
                showSecondContainer && (
                  <FocusInput
                    placeholder={t('请扫描')}
                    className="secondContainer"
                    data-bind="secondContainer"
                    autoFocus
                    allowClear
                    disabled={secondContainerDisabled === 0}
                    onPressEnter={(e) => {
                      if (e.target.value) {
                        store.scanSecondContainer({
                          combineSubNo,
                          secondContainer: e.target.value,
                        });
                      }
                    }}
                  >
                    <label>{t('二分周转箱')}</label>
                  </FocusInput>
                )
              }
              <FocusInput
                placeholder={t('请扫描')}
                className="location"
                data-bind="location"
                disabled={locationDisabled === 0}
                onPressEnter={(e) => {
                  if (e.target.value) {
                    store.scanLocation({
                      combineSubNo,
                      secondContainer,
                      location: e.target.value,
                    });
                  }
                }}
              >
                <label>{t('合包异常库位')}</label>
              </FocusInput>
            </Form>
            {
              locationNo && (
                <div
                  style={{ padding: '10px 0 0 15px', fontSize: 18 }}
                >
                  {t('推荐库位')}：<b style={{ color: 'red' }}>{locationNo}</b>
                </div>
              )
            }
            {
              combineNo && (
                <div
                  style={{ padding: '5px 0 0 15px', fontSize: 16 }}
                >
                  {t('合包包裹号')}：<span>{combineNo}</span>
                </div>
              )
            }
            {
              combineSubInfos.length ? (
                <CellBody>
                  <Table
                    maxHeight={200}
                    columns={[
                      {
                        title: t('子包裹号'),
                        render: (d) => (
                          <p>
                            {d.combineSubNo}
                            <br />
                            {!!d.secondContainerCode && `(${d.secondContainerCode})`}
                          </p>
                        ),
                      },
                      {
                        title: t('状态'),
                        dataIndex: 'statusStr',
                      },
                    ]}
                    rowClassName={(v) => (v.index === 0 ? style.mark : '')}
                    dataSource={combineSubInfos.map((v, i) => ({ ...v, index: i }))}
                  />
                </CellBody>
              ) : ''
            }
            <Footer />
          </div>
        )}
      </div>
    );
  }
}

Container.propTypes = {

};

const mapStateToProps = (state) => state['compound-package/exp-on-shelf'];
export default connect(mapStateToProps)(i18n(Container));
