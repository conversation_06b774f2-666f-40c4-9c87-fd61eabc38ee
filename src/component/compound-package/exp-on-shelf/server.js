import { sendPostRequest } from '../../../lib/public-request';

export const exceptionShelfCheckPackageNo = param => sendPostRequest({
  url: '/combine_exception/scanPackageNo',
  param,
}, process.env.WPOC_URI);

export const scanSecondContainerAPI = param => sendPostRequest({
  url: '/combine_exception/scanSecondContainer',
  param,
}, process.env.WPOC_URI);

export const exceptionShelfPost = param => sendPostRequest({
  url: '/combine_exception/scanLocation',
  param,
}, process.env.WPOC_URI);
