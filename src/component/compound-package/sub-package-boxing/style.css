.loader {
    animation: loader-effect 1s infinite linear;
}

@keyframes loader-effect {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.list {
    padding: 15px;
}

.item {
    display: flex;
    padding-bottom: 10px;
    justify-content: flex-start;
}

.itemTitle {
    display: flex;
    padding-right: 5px;
    white-space: nowrap;
}
.itemContent {
    text-align: left;
}

.detailItem {
    margin-right: 0px;
    display: block;
    width: 130px;
    line-height: 1.2;
    word-break: break-all;
}

.alterTip{
    text-align: left;
}
.splitPackageNo{
    margin-top: 10px;
    max-height: 100px;
    overflow: auto;
}
.splitPackageNoItem{
    line-height: 25px;
}