import React, { Component } from 'react';
import { i18n, t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import ScanPackage from './jsx/scan-package';
import ScanBox from './jsx/scan-box';
import DetailList from './jsx/detail';
import { Header } from '../../common';
import store from './reducers';
import style from './style.css';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      type,
      dataLoading,
    } = this.props;
    return (
      <div>
        {type === 1 && (
          <>
            <Header title={headerTitle || t('子包裹装箱')}>
              <Icon className={style.loader} name="loading" data-if={dataLoading === 0} />
            </Header>
            <ScanPackage {...this.props} />
          </>
        )}
        {
          type === 2 && (
            <>
              <Header title={headerTitle || t('已装箱列表')} />
              <DetailList {...this.props} />
            </>
          )
        }
        {
          type === 3 && (
            <>
              <Header title={headerTitle || t('扫描转运箱号')}>
                <Icon className={style.loader} name="loading" data-if={dataLoading === 0} />
              </Header>
              <ScanBox {...this.props} />
            </>
          )
        }
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  type: PropTypes.number,
  containerCode: PropTypes.string,
  dataLoading: PropTypes.number,
  initLoading: PropTypes.bool,
};

export default i18n(Container);
