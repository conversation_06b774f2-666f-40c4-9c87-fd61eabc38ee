import React from 'react';
import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { LOCAL_USER } from 'lib/storage';
import { message, modal } from '../../common';
import {
  scanSubPackageNoAPI,
  closeBoxAPI,
  queryLoadedPackagesAPI,
  queryBindFillBoxAPI,
  cloudMessagePublishAPI,
  scanFillBoxCodeAPI,
  removeLoadDetailAPI,
  confirmScanSubPackageNoAPI,
} from './server';
import { getHeaderTitle, classFocus as utilClassFocus } from '../../../lib/util';
import Modal from '../../common/modal';
import DisplayInfo from './jsx/display-info';
import error from '../../../source/audio/delete.mp3';
import styles from './style.css';

const audio = new Audio(error);
audio.load();

const defaultState = {
  headerTitle: '',
  initLoading: false, // 初始化loading
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  packageNo: '', // 包裹号
  userName: (JSON.parse(localStorage.getItem(LOCAL_USER)) || {}).username || '',
  dataInfo: null, // 扫子包裹返回的具体信息
  detailList: [], // 明细
  type: 1, // 1 扫描页面 2 明细页面 3 扫转运箱页面
  isCloseBoxDisabled: true, // 关箱按钮是否禁用
  bindUser: '', // 绑定的用户
  bindUserDisabled: false, // 绑定用户输入框是否禁用
  scanPackageNoDisabled: true, // 扫子包裹是否禁用
  transferContainerCode: '', // 转运箱
  bindCodeDisplay: `${Math.random().toString(36).slice(2)}-${Date.now().toString()}`, // 显示编码 仅仅为了不显示人名
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * classFocus(className, ctx) {
    utilClassFocus(className);
    yield ctx.changeData({
      data: {
        focusPosition: className,
      },
    });
  },
  * init(action, ctx) {
    const { userName } = yield '';
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    yield ctx.changeData({ data: { initLoading: false } });
    yield ctx.queryBindFillBox({ params: { userName } });
  },
  /**
   * 扫描合包子包裹号
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * scanSubPackageNo(action, ctx) {
    markStatus('dataLoading');
    const data = yield scanSubPackageNoAPI(action.params);
    if (data.code === '0') {
      const { dataInfo } = yield '';
      yield ctx.changeData({
        data: {
          dataInfo: { ...dataInfo, ...(data.info || {}) },
        },
      });
      // 自动关箱 resultType = 2
      if (data.info?.isNeedPrintTransferContainerCode) {
        const status = yield new Promise((r) => {
          Modal.success({
            content: <DisplayInfo dataInfo={data.info || {}} isModal />,
            onOk: () => {
              r('ok');
            },
          });
        });
        if (status === 'ok') {
          // 推送打印
          const { bindUser } = yield '';
          yield ctx.publish({
            params: {
              title: 'sub-package-boxing',
              content: JSON.stringify(data.info || {}),
              targetList: [bindUser],
              module: 4,
              publishType: 'unicast',
            },
          });
          // 切换页面到扫转运箱页面
          yield ctx.changeData({
            data: {
              type: 3,
            },
          });
          // 聚焦转运箱
          yield ctx.classFocus('transferContainerCode');
        }
        return;
      }
      yield ctx.changeData({
        data: {
          packageNo: '',
          isCloseBoxDisabled: false,
        },
      });
      yield ctx.classFocus('packageNo');
      const { resultType, transferContainerCode } = data.info || {};
      if (resultType === 3) {
        message.success(t('包裹已装箱'));
      } else if (resultType === 4) {
        const confirmStatus = yield new Promise((r) => {
          Modal.confirm2({
            title: t('批量装箱提示'),
            content: (
              <div>
                {t('该大箱可批量装箱，点击确定为你自动扫描大箱内包裹，且转运箱已达到最大装箱包裹数，自动关箱')}
              </div>),
            buttons: [{
              label: t('确定'),
              type: 'primary',
              onClick: () => r('ok'),
            }],
          });
        });
        if (confirmStatus === 'ok') {
          yield this.confirmScanSubPackageNo({
            params: {
              ...action.params,
              resultType,
              transferContainerCode,
            },
          });
        }
      } else if (resultType === 5) {
        const confirmStatus = yield new Promise((r) => {
          Modal.confirm2({
            title: t('批量装箱提示'),
            content: (
              <div>
                {t('该大箱可批量装箱，点击确定为你自动扫描大箱内包裹')}
              </div>),
            buttons: [{
              label: t('确定'),
              type: 'primary',
              onClick: () => r('ok'),
            }],
          });
        });
        if (confirmStatus === 'ok') {
          yield this.confirmScanSubPackageNo({
            params: {
              ...action.params,
              resultType,
              transferContainerCode,
            },
          });
        }
      } else if (resultType === 7) {
        const confirmStatus = yield new Promise((r) => {
          Modal.confirm2({
            title: t('批量装箱提示'),
            content: (
              <div>
                {t('超过转运箱最大装箱数限制，包裹号{}已扫描装箱，请再逐个扫描其他包裹装箱', action.params.packageNo)}
              </div>),
            buttons: [{
              label: t('确定'),
              type: 'primary',
              onClick: () => r('ok'),
            }],
          });
        });
        if (confirmStatus === 'ok') {
          yield this.confirmScanSubPackageNo({
            params: {
              ...action.params,
              resultType,
              transferContainerCode,
            },
          });
        }
      } else {
        message.success(t('装箱成功'));
      }
    } else {
      yield ctx.changeData({
        data: {
          packageNo: '',
        },
      });
      modal.error({ content: data.msg, className: 'packageNo' });
      audio.play();
    }
  },
  /**
   * 扫描合包子包裹号-二次确认
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * confirmScanSubPackageNo(action, ctx) {
    markStatus('dataLoading');
    const data = yield confirmScanSubPackageNoAPI(action.params);
    if (data.code === '0') {
      // 自动关箱触发推送打印并跳转到扫转运箱页面
      if (action.params?.resultType === 4) {
        // 推送打印
        const { bindUser, dataInfo } = yield '';
        yield ctx.publish({
          params: {
            title: 'sub-package-boxing',
            content: JSON.stringify(data.info || {}),
            targetList: [bindUser],
            module: 4,
            publishType: 'unicast',
          },
        });
        // 切换页面到扫转运箱页面
        yield ctx.changeData({
          data: {
            dataInfo: { ...dataInfo, ...(data.info || {}) },
            type: 3,
          },
        });
        // 聚焦转运箱
        yield ctx.classFocus('transferContainerCode');
      } else {
        const { dataInfo } = yield '';
        yield ctx.changeData({
          data: {
            dataInfo: { ...dataInfo, ...(data.info || {}) },
          },
        });
        yield ctx.classFocus('packageNo');
      }
    } else {
      yield ctx.changeData({
        data: {
          packageNo: '',
        },
      });
      modal.error({ content: data.msg, className: 'packageNo' });
      audio.play();
    }
  },
  /**
   * 关箱
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * closeBox(action, ctx) {
    markStatus('dataLoading');
    const data = yield closeBoxAPI(action.params);
    if (data.code === '0') {
      message.success(t('装箱成功'));
      // 推送打印
      const { bindUser, dataInfo } = yield '';
      yield ctx.publish({
        params: {
          title: 'sub-package-boxing',
          content: JSON.stringify(data.info || {}),
          targetList: [bindUser],
          module: 4,
          publishType: 'unicast',
        },
      });
      // 切换页面到扫转运箱页面
      yield ctx.changeData({
        data: {
          dataInfo: { ...dataInfo, ...(data.info || {}) },
          type: 3,
        },
      });
      // 聚焦转运箱
      yield ctx.classFocus('transferContainerCode');
    } else {
      modal.error({
        content: data.msg,
      });
      audio.play();
    }
  },
  /**
   * 查询已装箱包裹
   * @param action
   * @param ctx
   * @returns {Generator<*, void, *>}
   */
  * queryLoadedPackages(action, ctx) {
    markStatus('dataLoading');
    const data = yield queryLoadedPackagesAPI(action.params);
    if (data.code === '0') {
      yield ctx.changeData({
        data: {
          type: 2,
          detailList: (data.info?.packages || []).map((v) => (
            {
              subPackage: v,
              transferContainerCode: data.info?.transferContainerCode,
            }
          )),
        },
      });
    } else {
      modal.error({
        content: data.msg,
      });
    }
  },
  /**
   * 查询用户已绑定转运箱
   * @param action
   * @param ctx
   * @returns {Generator<*, void, *>}
   */
  * queryBindFillBox(action, ctx) {
    markStatus('dataLoading');
    const data = yield queryBindFillBoxAPI(action.params);
    if (data.code === '0') {
      yield ctx.changeData({
        data: {
          dataInfo: data.info || {},
        },
      });
      // returnFlag 0没有占用信息 1有占用信息 2进入扫转运箱页面
      if (data.info.returnFlag === 1 || data.info.returnFlag === 2) {
        yield ctx.changeData({
          data: {
            isCloseBoxDisabled: false,
          },
        });
        if (data.info.returnFlag === 2) {
          yield ctx.changeData({
            data: {
              type: 3,
            },
          });
        }
      }
      // 光标定位
      const { type, bindUser } = yield '';
      if (bindUser) {
        if (type === 1) {
          yield this.classFocus('packageNo');
        } else {
          yield this.classFocus('transferContainerCode');
        }
      } else {
        yield this.classFocus('bindUser');
      }
      if (action.type === 'detail') {
        yield this.classFocus(action.focusPosition);
      }
    } else {
      modal.error({
        content: data.msg,
      });
    }
  },
  /**
   * 推送到pc进行打印
   * @param action
   * @param ctx
   * @returns {Generator<*, void, *>}
   */
  * publish(action, ctx) {
    markStatus('dataLoading');
    const { type } = yield '';
    const params = {
      ...action.params,
      messageType: 'message',
    };
    const data = yield cloudMessagePublishAPI(params);
    if (data.code === '0') {
      // 操作的是绑定
      if (action.params?.operateBind) {
        yield ctx.changeData({
          data: {
            bindUserDisabled: true,
            scanPackageNoDisabled: false,
          },
        });
        message.success(t('绑定成功！'));
        if (type === 1) {
          yield this.classFocus('packageNo');
        } else {
          yield this.classFocus('transferContainerCode');
        }
      }
    } else {
      modal.error({
        content: data.msg,
        className: 'bindUser',
      });
      // 操作的是绑定
      if (action.params?.operateBind) {
        yield ctx.changeData({
          data: {
            bindUser: '',
          },
        });
        yield this.classFocus('bindUser');
      }
    }
  },
  /**
   * 扫描转运箱
   * @param action
   * @param ctx
   * @returns {Generator<*, void, *>}
   */
  * scanFillBoxCode(action, ctx) {
    markStatus('dataLoading');
    const data = yield scanFillBoxCodeAPI(action.params);
    if (data.code === '0') {
      const { bindUser } = yield '';
      yield this.init();
      if (bindUser) {
        yield ctx.changeData({
          data: {
            bindUser,
            bindUserDisabled: true,
            scanPackageNoDisabled: false,
          },
        });
        yield ctx.classFocus('packageNo');
      } else {
        yield ctx.classFocus('bindUser');
      }
      message.success(t('扫描成功'));
    } else {
      modal.error({
        content: data.msg,
      });
      audio.play();
    }
  },
  /**
   * 查询已装箱包裹
   * @param action
   * @param ctx
   * @returns {Generator<*, void, *>}
   */
  * removeLoadDetail(action, ctx) {
    const { dataInfo } = yield '';
    markStatus('dataLoading');
    const data = yield removeLoadDetailAPI(action);
    if (data.code === '0') {
      if (data.info && data.info.needAlert) {
        const alterTipDiv = (
          <div className={styles.alterTip}>
            <div>{t('请拿出包裹【{}】已装箱的拆包子包裹:', data.info.subPackageNo)}</div>
            <div className={styles.splitPackageNo}>
              {data.info.splitPackageNoList.map((item) => (
                <div className={styles.splitPackageNoItem}>
                  {item}
                </div>
              ))}
            </div>
          </div>
        );
        const status = yield new Promise((r) => modal.info({
          content: alterTipDiv,
          onOk: () => r('ok'),
        }));
        if (status === 'ok') {
          yield ctx.queryLoadedPackages({
            params: {
              transferContainerCode: dataInfo?.transferContainerCode,
            },
          });
        }
      } else {
        yield ctx.queryLoadedPackages({
          params: {
            transferContainerCode: dataInfo?.transferContainerCode,
          },
        });
      }
    } else {
      modal.error({
        content: data.msg,
      });
    }
  },
};
