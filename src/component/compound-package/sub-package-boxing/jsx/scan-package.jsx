import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import {
  Form,
} from 'react-weui/build/packages';
import { push } from 'react-router-redux';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import Footer from '../../../common/footer';
import RowInfo from '../../../common/row-info';
import { View } from '../../../common';
import FooterBtn from '../../../common/footer-btn';
import Modal from '../../../common/modal';
import DisplayInfo from './display-info';
import BindUser from './bind-user';

class ScanPackage extends Component {
  render() {
    const {
      dataLoading,
      packageNo,
      userName,
      dataInfo,
      initLoading,
      isCloseBoxDisabled,
      bindUser,
      scanPackageNoDisabled,
      type,
      dispatch,
    } = this.props;

    return (
      <div>
        <View
          flex={false} // flex布局，默认为true，当需要固定单个输入框是，不启用
          diff={110} // 页面高度：window.innerHeight - diff 中的 diff 值
          initLoading={initLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面
        >
          <Form>
            <BindUser {...this.props} />
            <FocusInput
              placeholder={t('请扫描')}
              disabled={dataLoading === 0 || scanPackageNoDisabled}
              className="packageNo"
              value={packageNo}
              onChange={(e) => {
                store.changeData({
                  data: {
                    packageNo: e.target.value.trim(),
                  },
                });
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanSubPackageNo({
                  params: {
                    packageNo: e.target.value.trim(),
                    userName,
                    bindPcId: bindUser,
                  },
                });
              }}
            >
              <label>{t('子包裹号')}</label>
            </FocusInput>
          </Form>
          {dataInfo?.packageNum > 0 && (
            <DisplayInfo dataInfo={dataInfo} type={type} />
          )}
          {dataInfo?.packageNum > 0 && (
            <Form>
              <RowInfo
                label={t('已装箱包裹数')}
                content={
                  <span
                    style={{ color: '#0059ce' }}
                    onClick={() => {
                      store.queryLoadedPackages({
                        params: {
                          transferContainerCode: dataInfo?.transferContainerCode,
                        },
                      });
                    }}
                  >
                    {dataInfo?.packageNum}
                    <Icon name="arr-right" />
                  </span>
                }
              />
            </Form>
          )}
          {dataInfo?.packageNum > 0 && (
            <Form>
              <RowInfo
                label={t('已装箱包裹系重')}
                content={(
                  <span>
                    {dataInfo?.packageWeight}KG
                  </span>
                )}
              />
            </Form>
          )}
        </View>
        <Footer
          beforeBack={() => {
            store.init();
            dispatch(push('/sub-menu'));
          }}
        >
          <FooterBtn
            disabled={isCloseBoxDisabled}
            onClick={() => {
              Modal.confirm({
                content: (
                  <div>
                    {t('转运箱')}: <b style={{ color: 'red' }}>{dataInfo.transferContainerCode}</b> {t('是否关箱？')}
                  </div>
                ),
                onOk: () => {
                  store.closeBox({
                    params: {
                      transferContainerCode: dataInfo?.transferContainerCode,
                      userName,
                      bindPcId: bindUser,
                    },
                  });
                },
              });
            }}
          >
            关箱
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

ScanPackage.propTypes = {
  packageNo: PropTypes.string,
  userName: PropTypes.string,
  dataLoading: PropTypes.number,
  dataInfo: PropTypes.shape(),
  initLoading: PropTypes.bool,
  isCloseBoxDisabled: PropTypes.bool,
  scanPackageNoDisabled: PropTypes.bool,
  bindUser: PropTypes.string,
  type: PropTypes.number,
  dispatch: PropTypes.func,
};

export default ScanPackage;
