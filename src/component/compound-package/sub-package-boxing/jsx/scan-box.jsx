import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import {
  Form,
} from 'react-weui/build/packages';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import { modal, View } from '../../../common';
import DisplayInfo from './display-info';
import BindUser from './bind-user';

class ScanBox extends Component {
  render() {
    const {
      dataLoading,
      userName,
      dataInfo,
      initLoading,
      transferContainerCode,
      type,
    } = this.props;

    return (
      <div>
        <View
          flex={false} // flex布局，默认为true，当需要固定单个输入框是，不启用
          diff={110} // 页面高度：window.innerHeight - diff 中的 diff 值
          initLoading={initLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面
        >
          <Form>
            <BindUser {...this.props} />
            <FocusInput
              placeholder={t('请扫描')}
              disabled={dataLoading === 0}
              className="transferContainerCode"
              value={transferContainerCode}
              onChange={(e) => {
                store.changeData({
                  data: {
                    transferContainerCode: e.target.value.trim(),
                  },
                });
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                if (e.target.value.trim() !== dataInfo.transferContainerCode) {
                  store.changeData({
                    data: {
                      transferContainerCode: '',
                    },
                  });
                  modal.error({ content: t('箱号错误，请重新扫描！'), className: 'transferContainerCode' });
                } else {
                  store.scanFillBoxCode({
                    params: {
                      userName,
                      transferContainerCode,
                    },
                  });
                }
              }}
            >
              <label>{t('转运箱号')}</label>
            </FocusInput>
          </Form>
          {dataInfo?.packageNum > 0 && (
            <DisplayInfo dataInfo={dataInfo} type={type} />
          )}
        </View>
      </div>
    );
  }
}

ScanBox.propTypes = {
  transferContainerCode: PropTypes.string,
  userName: PropTypes.string,
  dataLoading: PropTypes.number,
  dataInfo: PropTypes.shape(),
  initLoading: PropTypes.bool,
  type: PropTypes.number,
};

export default ScanBox;
