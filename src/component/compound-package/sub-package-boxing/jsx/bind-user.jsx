import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';

class BindUser extends Component {
  render() {
    const {
      dataLoading,
      bindUserDisabled,
      bindUser,
      bindCodeDisplay,
    } = this.props;

    return (
      <>
        {bindUserDisabled ? (
          <FocusInput
            placeholder={t('请扫描')}
            disabled={dataLoading === 0 || bindUserDisabled}
            value={bindCodeDisplay}
          >
            <label>{t('电脑二维码')}</label>
          </FocusInput>
        ) : (
          <FocusInput
            placeholder={t('请扫描')}
            disabled={dataLoading === 0 || bindUserDisabled}
            className="bindUser"
            value={bindUser}
            autoFocus
            onChange={(e) => {
              store.changeData({
                data: {
                  bindUser: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.publish({
                params: {
                  title: t('绑定成功'),
                  content: t('绑定成功!'),
                  targetList: [bindUser],
                  module: 1,
                  publishType: 'unicast',
                  operateBind: true,
                },
              });
            }}
          >
            <label>{t('电脑二维码')}</label>
          </FocusInput>
        )}

        <p style={{ padding: '0 15px 5px 15px' }}>{t('请扫描电脑二维码绑定，关箱后自动打印转运箱条码')}</p>
      </>
    );
  }
}

BindUser.propTypes = {
  dataLoading: PropTypes.number,
  bindUserDisabled: PropTypes.bool,
  bindUser: PropTypes.string,
  bindCodeDisplay: PropTypes.string,
};

export default BindUser;
