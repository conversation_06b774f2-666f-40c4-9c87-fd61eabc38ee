import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import style from '../style.css';

class DisplayInfo extends Component {
  render() {
    const {
      dataInfo,
      isModal,
      type,
    } = this.props;

    // const declareMarkMap = {
    //   0: t('非外综服'),
    //   1: t('9610-简易申报'),
    //   2: t('9610-汇总申报'),
    // };
    //
    // const shippingProductionTypeMap = {
    //   0: t('非敏感'),
    //   1: t('敏感带电'),
    //   2: t('敏感普货'),
    //   3: t('干电产品'),
    // };

    return (
      <div className={isModal ? null : style.list}>
        <div className={style.item}>
          <div className={style.itemTitle}>{t('转运箱号')}:</div>
          <div className={style.itemContent}>
            <b style={{ color: 'red' }}>
              {dataInfo.transferContainerCode}
            </b>
            {/* eslint-disable-next-line no-nested-ternary */}
            {(dataInfo.isNeedPrintTransferContainerCode && isModal) ? t('已到达最大装箱数量，自动关箱') : (type !== 1 ? t('已关箱') : '')}
          </div>
        </div>
        <div className={style.item}>
          <div className={style.itemTitle}>{t('合包类型')}:</div>
          <div className={style.itemContent}>
            {dataInfo.combineWellenType}
          </div>
        </div>
        {/* <div className={style.item}> */}
        {/*  <div className={style.itemTitle}>{t('报关方式')}:</div> */}
        {/*  <div className={style.itemContent}> */}
        {/*    {declareMarkMap[dataInfo.declareMark] || ''} */}
        {/*  </div> */}
        {/* </div> */}
        {/* <div className={style.item}> */}
        {/*  <div className={style.itemTitle}>{t('敏感类别')}:</div> */}
        {/*  <div className={style.itemContent}> */}
        {/*    {shippingProductionTypeMap[dataInfo.shippingProductionType] || ''} */}
        {/*  </div> */}
        {/* </div> */}
        <div className={style.item}>
          <div className={style.itemTitle}>{t('合包地址')}:</div>
          <div className={style.itemContent}>
            {dataInfo.combineAddress}
          </div>
        </div>
      </div>
    );
  }
}

DisplayInfo.propTypes = {
  dataInfo: PropTypes.shape(),
  isModal: PropTypes.bool,
  type: PropTypes.number,
};

export default DisplayInfo;
