import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import { LOCAL_USER } from 'lib/storage';
import store from '../reducers';
import Footer from '../../../common/footer';
import List from '../../../common/list';
import style from '../../../style.css';
import styles from '../style.css';
import Modal from '../../../common/modal';

const rows = [
  [
    {
      title: '',
      render: (record) => <span className={styles.detailItem}>{record.subPackage}</span>,
    },
    {
      title: '',
      render: (record) => <span className={styles.detailItem}>{record.transferContainerCode}</span>,
    },
    {
      title: '',
      render: (record) => (
        <Icon
          name="delete"
          color="red"
          fontSize={20}
          onClick={() => {
            Modal.confirm({
              content: t('是否确定将该【{}】子包裹移出箱子？', record.subPackage),
              okText: t('确定'),
              cancelText: t('取消'),
              onOk: () => {
                store.removeLoadDetail({
                  transferContainerCode: record?.transferContainerCode,
                  packageNo: record?.subPackage,
                });
              },
            });
          }}
        />
      ),
    },
  ],
];

class DetailList extends Component {
  render() {
    const {
      dispatch,
      detailList,
      focusPosition,
    } = this.props;

    const height = window.innerHeight - 44 - 56;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <List
          header={(
            <div style={{
              fontWeight: 'bold',
              color: '#000',
              display: 'flex',
              paddingRight: '15px',
              justifyContent: 'space-between',
            }}
            >
              <div>{t('子包裹号')}</div>
              <div>{t('转运箱号')}</div>
              <div>{t('操作')}</div>
            </div>
          )}
          rows={rows}
          data={detailList}
          rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0' }}
        />
        <Footer
          dispatch={dispatch}
          beforeBack={() => {
            store.changeData({
              data: {
                type: 1,
              },
            });
            store.queryBindFillBox({
              params: {
                userName: (JSON.parse(localStorage.getItem(LOCAL_USER)) || {}).username || '',
              },
              type: 'detail',
              focusPosition,
            });
          }}
        />
      </div>
    );
  }
}

DetailList.propTypes = {
  dispatch: PropTypes.func,
  detailList: PropTypes.arrayOf(PropTypes.shape()),
  focusPosition: PropTypes.string,
};

export default DetailList;
