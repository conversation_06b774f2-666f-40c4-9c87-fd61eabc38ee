import { sendPostRequest } from '../../../lib/public-request';

/**
 * 查询用户已绑定转运箱
 * @param param
 * @returns {*}
 */
export const queryBindFillBoxAPI = (param) => sendPostRequest({
  url: '/wpoc/pda/combine/declare/query_bound_fill_box_of_user',
  param,
}, '');

/**
 * 扫描合包子包裹号
 * @param param
 * @returns {*}
 */
export const scanSubPackageNoAPI = (param) => sendPostRequest({
  url: '/wpoc/pda/combine/declare/scan_sub_package_no',
  param,
}, '');

/**
 * 关箱
 * @param param
 * @returns {*}
 */
export const closeBoxAPI = (param) => sendPostRequest({
  url: '/wpoc/pda/combine/declare/close_fill_box',
  param,
}, '');

/**
 * 查询已装箱包裹
 * @param param
 * @returns {*}
 */
export const queryLoadedPackagesAPI = (param) => sendPostRequest({
  url: '/wpoc/pda/combine/declare/query_loaded_packages',
  param,
}, '');

/**
 * 扫描转运箱
 * @param param
 * @returns {*}
 */
export const scanFillBoxCodeAPI = (param) => sendPostRequest({
  url: '/wpoc/pda/combine/declare/scan_fill_box_code',
  param,
}, '');

/**
 * 发布消息
 * @param param
 * @returns {*}
 */
export const cloudMessagePublishAPI = (param) => sendPostRequest({
  url: '/cloud_message/publish',
  param,
}, process.env.WGS_FRONT_BRIDGE);

/**
 * 取消
 * @param param
 * @returns {*}
 */
export const removeLoadDetailAPI = (param) => sendPostRequest({
  url: '/wpoc/pda/combine/declare/remove_load_detail',
  param,
}, '');

/**
 * 扫子包裹号二次确认
 * @param param
 * @returns {*}
 */
export const confirmScanSubPackageNoAPI = (param) => sendPostRequest({
  url: '/wpoc/pda/combine/declare/confirm_scan_sub_package_no',
  param,
}, '');
