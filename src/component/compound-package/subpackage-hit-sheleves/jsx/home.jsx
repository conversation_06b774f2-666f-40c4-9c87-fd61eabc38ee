import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
// import style from '../../../style.css';
import store from '../reducers';
import Footer from '../../../common/footer';
import Header from '../../../common/header';
import FocusInput from '../../../common/focus-input';
import Serial from './serial';

class Home extends Component {
  render() {
    const {
      loading,
      containerCode,
      containerCodeDisabled,
      workLocation,
      recommendWorkLocation,
      coBatchInfoList,
    } = this.props;
    return (
      <div>
        <Header title={t('分包上架')} />
        <Form>
          <FocusInput
            autoFocus
            placeholder={t('请扫描')}
            className="containerCode"
            disabled={!loading || containerCodeDisabled}
            value={containerCode}
            onChange={(e) => {
              store.changeData({
                containerCode: e.target.value,
              });
            }}
            onPressEnter={(e) => {
              if (e.target.value) {
                store.scanContainerCode({
                  containerCode: e.target.value,
                });
              }
            }}
          >
            <label>{t('箱号')}</label>
          </FocusInput>
        </Form>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            className="workLocation"
            disabled={!loading}
            value={workLocation}
            onChange={(e) => {
              store.changeData({
                workLocation: e.target.value,
              });
            }}
            onPressEnter={(e) => {
              if (e.target.value) {
                store.scanWorkLocation({
                  containerCode,
                  workLocation: e.target.value,
                });
              }
            }}
          >
            <label>{t('二分工位')}</label>
          </FocusInput>
        </Form>
        {recommendWorkLocation ? (
          <div style={{ color: '#FF4D50', padding: '5px 15px' }}>
            <p>{t('请扫描二分工位')}</p>
            <p>{recommendWorkLocation}</p>
          </div>
        ) : (
          <div
            style={{
              color: '#FF4D50',
              padding: '5px 15px',
              wordBreak: 'break-all',
              wordWrap: 'break-word',
            }}
          >
            {
              coBatchInfoList?.map((item, index) => (
                <span key={item.coBatchCode}>
                  {`${t('协作批次{}', item.coBatchCode)}${item.workLocation ? t('对应工位{}', item.workLocation) : t('无对应工位')}`}
                  {index < coBatchInfoList.length - 1 ? '，' : ''}
                </span>
              ))
            }
          </div>
        )}
        <Serial {...this.props} />
        <Footer />
      </div>
    );
  }
}

Home.propTypes = {
  loading: PropTypes.number,
  containerCode: PropTypes.string,
  containerCodeDisabled: PropTypes.bool,
  workLocation: PropTypes.string,
  recommendWorkLocation: PropTypes.string,
  coBatchInfoList: PropTypes.arrayOf(),
};
export default Home;
