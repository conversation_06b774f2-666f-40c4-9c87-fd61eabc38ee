import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Footer from '../../../common/footer';
import FocusInput from '../../../common/focus-input';
import RowInfo from '../../../common/row-info';
import Header from '../../../common/header';
import Table from '../../../common/table';
import store from '../reducers';
import { classFocus } from '../../../../lib/util';

class SubDetails extends Component {
  render() {
    const columns = [
      {
        title: t('序号'),
        dataIndex: 'serialNo',
        width: '15',
      },
      {
        title: t('包裹号'),
        dataIndex: 'packageNo',
        width: '70',
      },
      {
        title: t('状态'),
        dataIndex: 'status',
        render: row => (
          row.status === 1 ? t('已扫描') : t('未扫描')
        ),
        width: '15',
      },
    ];
    const {
      containerCode,
      hasScanPackages,
      unScanPackages,
      scanNum,
      leftNum,
    } = this.props;
    return (
      <div>
        <Header title={t('转运箱明细')} />
        <FocusInput
          disabled
          value={containerCode}
        >
          <label>{t('转运箱')}</label>
        </FocusInput>
        <RowInfo
          label={t('扫描/待扫描')}
          content={(
            <div>{scanNum}/<span style={{ color: 'red' }}>{leftNum}</span></div>)}
          type="info"
        />
        <Table
          maxHeight="calc(100vh - 200px)"
          columns={columns}
          dataSource={unScanPackages.concat(hasScanPackages)}
        />
        <Footer
          beforeBack={() => {
            store.changeData({
              containerType: 2,
            });
            classFocus('packageNo');
          }}
        />
      </div>
    );
  }
}

SubDetails.propTypes = {
  containerCode: PropTypes.string,
  hasScanPackages: PropTypes.arrayOf(PropTypes.shape),
  unScanPackages: PropTypes.arrayOf(PropTypes.shape),
  scanNum: PropTypes.number,
  leftNum: PropTypes.number,
};
export default SubDetails;
