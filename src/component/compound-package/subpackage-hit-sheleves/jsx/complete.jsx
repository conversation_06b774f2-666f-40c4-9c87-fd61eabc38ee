import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import { Icon } from 'react-weui/build/packages';
import Footer from '../../../common/footer';
import Header from '../../../common/header';
import Serial from './serial';
import style from '../style.css';
import store from '../reducers';

class SubDetails extends Component {
  render() {
    const {
      expList,
      expPackages,
      serialInfo,
      cancelList,
      canclePackages,
    } = this.props;
    return (
      <div>
        <Header title={t('分包上架完成')} />
        <div style={{ textAlign: 'center', paddingTop: '10px' }}>
          <Icon value="success" style={{ fontSize: 30, marginBottom: 10 }} />
          <span style={{ fontWeight: '700', fontSize: '18px' }}>{t('分包上架完成')}</span>
          <p>{t('此协作批次下的所有箱子已完成分包')}</p>
        </div>
        {!!serialInfo && (
          <div style={{ borderTop: '1px solid #e5e5e5', marginTop: '10px' }}>
            {/* 序号展示 */}
            <Serial {...this.props} />
          </div>
        )}
        <div style={{ padding: '20px 0 0 40px' }}>
          <div style={{ textAlign: 'left', fontSize: '12px', marginTop: '-10px' }}><label className={style.label}>{t('异常包裹序号/容器号')}</label></div>
          <div style={{ maxHeight: '150px', overflowY: 'scroll' }}>
            <table style={{ fontSize: '20px', color: 'red' }}>
              <tbody>
                {/* 二选一 */}
                {expList && !!expList.length && expList.map((v) => (
                  <tr>
                    <td align="right"><b>{v.locationOrder}-{v.containerOrder}</b></td>
                    <td>/</td>
                    <td align="left"><b>{v.containerCode}</b></td>
                  </tr>
                ))}
                {expPackages && !!expPackages.length && expPackages.map(v => (
                  <tr>
                    <td align="right"><b>{v.locationSequence}-{v.secondContainerSequence}</b></td>
                    <td>/</td>
                    <td align="left">
                      <b>
                        { Array.isArray(v.seedContainerCode) ?
                          v.seedContainerCode.join(', ') :
                          v.seedContainerCode
                        }
                      </b>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        <div style={{ padding: '20px 0 0 40px' }}>
          <div style={{ textAlign: 'left', fontSize: '12px', marginTop: '-10px' }}><label className={style.label}>{t('取消包裹序号/容器号')}</label></div>
          <div style={{ maxHeight: '150px', overflowY: 'scroll' }}>
            <table style={{ fontSize: '20px', color: 'red' }}>
              <tbody>
                {/* 二选一 */}
                {cancelList && !!cancelList.length && cancelList.map((v) => (
                  <tr>
                    <td align="right"><b>{v.locationOrder}-{v.containerOrder}</b></td>
                    <td>/</td>
                    <td align="left"><b>{v.containerCode}</b></td>
                  </tr>
                ))}
                {canclePackages && !!canclePackages.length && canclePackages.map((v) => (
                  <tr>
                    <td align="right"><b>{v.locationSequence}-{v.secondContainerSequence}</b></td>
                    <td>/</td>
                    <td align="left">
                      <b>
                        { Array.isArray(v.seedContainerCode) ?
                          v.seedContainerCode.join(', ') :
                          v.seedContainerCode
                        }
                      </b>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        <Footer
          footerText={t('确定')}
          beforeBack={() => {
            store.reset();
          }}
        />
      </div>
    );
  }
}

SubDetails.propTypes = {
  expList: PropTypes.arrayOf(PropTypes.shape()),
  expPackages: PropTypes.arrayOf(PropTypes.shape()),
  serialInfo: PropTypes.shape(),
  cancelList: PropTypes.arrayOf(PropTypes.shape()),
  canclePackages: PropTypes.arrayOf(PropTypes.shape()),
};
export default SubDetails;
