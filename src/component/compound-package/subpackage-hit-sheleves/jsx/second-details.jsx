import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import { NavDetail, pages, Footer } from '../../../common';
import { classFocus } from '../../../../lib/util';
import store from '../reducers';

function Serial(props) {
  const {
    // eslint-disable-next-line react/prop-types
    shelfSequence,
    // eslint-disable-next-line react/prop-types
    containerSequence,
  } = props;
  let fontStyle = {};
  let backgroundStyle = {};
  switch (Number(shelfSequence)) {
    case 1:
      fontStyle = { color: '#FF4d50' };
      backgroundStyle = { background: 'rgba(255,77,80,0.1)' };
      break;
    case 2:
      fontStyle = { color: '#52C41A' };
      backgroundStyle = { background: 'rgba(82,196,26,0.1)' };
      break;
    case 3:
      fontStyle = { color: '#FF8C00' };
      backgroundStyle = { background: 'rgba(255,140,0,0.1)' };
      break;
    case 4:
      fontStyle = { color: '#197AFA' };
      backgroundStyle = { background: 'rgba(25,122,250,0.1)' };
      break;
    default:
      fontStyle = {};
      backgroundStyle = {};
  }
  return (
    <div>
      <span style={{ marginRight: '4px', color: '#666c7c' }}>{t('包裹序号')}: </span>
      {
        shelfSequence !== null ? (
          <span
            style={{
              ...fontStyle, display: 'inline-block', fontSize: '12px', fontWeight: 700,
            }}
          >
            <span style={{ ...backgroundStyle, padding: '0 4px' }}>{shelfSequence}{t('货架')}</span>
            <span style={{ padding: '0px 4px' }}>-</span>
            <span style={{ display: 'inline-block' }}>{containerSequence}</span>
          </span>
        ) : t('无')
      }
    </div>
  );
}

function SecondDetails(props) {
  const {
    secondDetailList,
    secondNotBind,
  } = props;

  const rowsList = [
    [
      [
        {
          title: `${t('SKC')}: `,
          render: 'goodsSn',
        },
        {
          title: t('图片'),
          render: 'null',
        },
      ],
      [
        {
          title: `${t('尺码')}: `,
          render: 'size',
        },
        {
          title: `${t('包裹序号')}: `,
          render: (d) => {
            const { shelfSequence, containerSequence } = d;
            return (
              <Serial shelfSequence={shelfSequence} containerSequence={containerSequence} />
            );
          },
        },
      ],
    ],
    [
      [
        {
          title: `${t('SKC')}: `,
          render: 'goodsSn',
        },
        {
          title: t('图片'),
          render: 'null',
        },
      ],
      [
        {
          title: `${t('尺码')}: `,
          render: 'size',
        },
        {
          title: `${t('包裹序号')}: `,
          render: (d) => {
            const { shelfSequence, containerSequence } = d;
            return (
              <Serial shelfSequence={shelfSequence} containerSequence={containerSequence} />
            );
          },
        },
      ],
    ],
  ];
  const totalNumList = [(secondDetailList[0] || []).length, (secondDetailList[1] || []).length];
  const navList = [t('已分播'), t('未分播')];
  return (
    <pages.View diff={100}>
      <NavDetail
        data={secondDetailList}
        rowsList={rowsList}
        navList={navList}
        imgUrlFieldName="imageUrl"
        totalNumList={totalNumList}
      />
      <Footer
        beforeBack={() => {
          store.changeData({
            containerType: 1,
          });
          if (secondNotBind) {
            classFocus('sku');
          } else {
            classFocus('seedContainerCode');
          }
        }}
      />
    </pages.View>
  );
}

SecondDetails.propTypes = {
  secondDetailList: PropTypes.arrayOf(),
  secondNotBind: PropTypes.bool,
};

export default SecondDetails;
