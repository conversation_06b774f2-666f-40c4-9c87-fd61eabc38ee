import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import style from '../../../style.css';
import store from '../reducers';
import FooterBtn from '../../../common/footer-btn';
import Footer from '../../../common/footer';
import Header from '../../../common/header';
import FocusInput from '../../../common/focus-input';
import { RowInfo } from '../../../common';
import Serial from './serial';

class SubScan extends Component {
  render() {
    const {
      containerCode,
      workLocation,
      scanNum,
      leftNum,
      loading,
      coBatchInfoList,
    } = this.props;
    return (
      <div className={style.flexColContainer}>
        <Header title={t('分包扫描')} />
        <Serial {...this.props} />
        <Form>
          <FocusInput
            data-bind="containerCode"
            className="containerCode"
            disabled
          >
            <label>
              {t('箱号')}
              <Icon
                onClick={() => {
                  store.getDetails({
                    containerCode,
                  });
                }}
                name="chaxunyewu"
                style={{ color: '#197afa', marginLeft: '5px' }}
              />
            </label>
          </FocusInput>
        </Form>
        <Form>
          <FocusInput
            data-bind="packageNo"
            className="packageNo"
            autoFocus
            disabled={!loading}
            onPressEnter={(e) => {
              if (e.target.value) {
                store.scanPackageNo({
                  containerCode,
                  workLocation,
                  packageNo: e.target.value,
                });
              }
            }}
          >
            <label>{t('包裹号')}</label>
          </FocusInput>
        </Form>
        <RowInfo
          label={t('扫描/待扫描')}
          content={(
            <div>
              {scanNum}/<span style={{ color: 'red' }}>{leftNum}</span>
            </div>
          )}
          type="info"
        />
        {!!coBatchInfoList?.length && (
          <div
            style={{
              color: '#FF4D50',
              padding: '5px 15px',
              wordBreak: 'break-all',
              wordWrap: 'break-word',
            }}
          >
            {
              coBatchInfoList?.map((item, index) => (
                <span key={item.coBatchCode}>
                  {`${t('协作批次{}', item.coBatchCode)}${item.workLocation ? t('对应工位{}', item.workLocation) : t('无对应工位')}`}
                  {index < coBatchInfoList.length - 1 ? '，' : ''}
                </span>
              ))
            }
          </div>
        )}
        <Footer>
          <FooterBtn
            disabled={!loading}
            onClick={() => {
              store.emptyBox({
                containerCode,
              });
            }}
          >
            {t('箱空')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

SubScan.propTypes = {
  containerCode: PropTypes.string,
  workLocation: PropTypes.string,
  scanNum: PropTypes.number,
  leftNum: PropTypes.number,
  loading: PropTypes.number,
  coBatchInfoList: PropTypes.arrayOf(),
};
export default SubScan;
