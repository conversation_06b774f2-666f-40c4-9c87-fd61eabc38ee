import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';

class Home extends Component {
  render() {
    const {
      serialInfo,
    } = this.props;
    if (!serialInfo) {
      return <div />;
    }
    const {
      location,
      container,
      plus,
      packageNo,
    } = serialInfo;
    let fontStyle = {};
    let backgroundStyle = {};
    switch (Number(location)) {
      case 1:
        fontStyle = { color: '#FF4d50' };
        backgroundStyle = { background: 'rgba(255,77,80,0.1)' };
        break;
      case 2:
        fontStyle = { color: '#52C41A' };
        backgroundStyle = { background: 'rgba(82,196,26,0.1)' };
        break;
      case 3:
        fontStyle = { color: '#FF8C00' };
        backgroundStyle = { background: 'rgba(255,140,0,0.1)' };
        break;
      case 4:
        fontStyle = { color: '#197AFA' };
        backgroundStyle = { background: 'rgba(25,122,250,0.1)' };
        break;
      default:
        fontStyle = {};
        backgroundStyle = {};
    }
    return (
      <div style={{ padding: '5px 15px' }}>
        <div>
          <span style={{ display: 'inline-block', paddingRight: '10px' }}>{t('序号')}</span>
          <span style={{
            ...fontStyle, display: 'inline-block', fontSize: '20px', fontWeight: 700,
          }}
          >
            <span style={{ ...backgroundStyle, padding: '0 8px' }}>{location}{t('货架')}</span>
            <span style={{ padding: '0px 40px' }}>-</span>
            <span style={{ display: 'inline-block', position: 'relative', fontSize: '28px' }}>
              {container}
              {!!plus && <span style={{ position: 'absolute', top: '2px', fontSize: '16px' }}>+{plus}</span>}
            </span>
          </span>

        </div>
        {!!packageNo && <p>{packageNo}</p>}
      </div>
    );
  }
}

Home.propTypes = {
  serialInfo: PropTypes.shape(),
};
export default Home;
