import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import WellenTips from 'common/wellen-tips';
import { Form, Button } from 'react-weui/build/packages';
import Icon from '@shein-components/Icon';
import Serial from './serial';
import store from '../reducers';
import {
  Header,
  Footer,
  FooterBtn,
  modal,
  RowInfo,
  FocusInput,
} from '../../../common';
import style from '../style.css';

class ScanGoods extends React.Component {
  render() {
    const {
      loading,
      containerCode,
      batchContainerInfo,
      sku,
      goodsSn,
      seedContainerCode,
      secondNotBind,
      workLocation,
      no,
      size,
      skuCode,
    } = this.props;
    const {
      hasSowingBatchContainerNum,
      waitingSowingBatchContainerNum,
      secondSowingNum,
      watingSecondSowingNum,
      firstSowingNum,
      wellenMark,
      batchCode,
      batchContainerCode,
    } = batchContainerInfo;
    // 多货
    const validMoreGoodsCb = (param) => {
      const {
        waitingSowingNum,
        waitingSowingSkuList,
        wellenCode,
      } = param;
      modal.confirm({
        content: (
          <div className={style.modalStyle}>
            <span style={{ color: 'blue' }}>{wellenCode}</span>{t('波次存在')}
            <span style={{ color: 'red' }}>{waitingSowingNum}</span>
            {t('件多货未分播,包括')}{waitingSowingSkuList.map(v => (`${v.goodsSn}/${v.size}`)).slice(0, 2).join(',')}{waitingSowingSkuList.length > 2 ? '...' : ''}{t('请确认实物是否准确')}
          </div>
        ),
        onOk: () => new Promise(() => {
          store.secondHangUp({
            batchCode,
            batchContainerCode,
          });
        }),
        autoFocusButton: null,
      });
    };
    return (
      <div>
        <Header title={t('二分操作')}>
          <div
            onClick={() => {
              if (!loading) {
                return;
              }
              modal.confirm({
                content: t('是否确认箱空'),
                onOk: () => {
                  store.emptyContainer({
                    batchContainerCode,
                    batchCode,
                    isCommit: 1,
                  });
                },
                autoFocusButton: null,
              });
            }}
          >
            <Icon name="xiangkong" style={{ marginRight: '5px' }} />
            {t('箱空')}
          </div>
        </Header>
        <RowInfo
          data={[
            {
              label: t('待分箱'),
              content: waitingSowingBatchContainerNum,
            },
            {
              label: t('已分箱'),
              content: hasSowingBatchContainerNum,
              type: 'warn',
            },
          ]}
        />
        <div
          style={{
            marginTop: '10px',
            borderTop: '1px solid #e5e5e5',
            borderBottom: '1px solid #e5e5e5',
          }}
        >
          <div style={{ borderBottom: '1px solid #e5e5e5' }}>
            {/* 序号展示 */}
            <Serial {...this.props} />
          </div>
          <div>
            <RowInfo
              extraStyle={{ height: 40, borderBottomColor: 'transparent', marginTop: -8 }}
              label={t('二分/待二分')}
              content={(<span><span style={{ color: '#ff9636' }}>{secondSowingNum}</span>/{watingSecondSowingNum}</span>)}
              type="warn"
            />
            <RowInfo
              extraStyle={{ height: 40, borderBottomColor: 'transparent', marginBottom: 18 }}
              label={t('一分')}
              content={firstSowingNum}
              type="info"
            />
          </div>
        </div>
        <Form>
          <FocusInput
            importance
            value={containerCode}
            disabled
            footer={(
              <Button
                style={{ marginRight: 5 }}
                disabled={!loading}
                size="small"
                onClick={() => {
                  modal.confirm({
                    content: (
                      <React.Fragment>
                        <div>
                          {t('是否将')}
                          <span className={style.getTaskNumLabel}> {containerCode} </span>
                          {t('挂起')}?
                        </div>
                        <WellenTips arr={wellenMark || []} />
                      </React.Fragment>
                    ),
                    onOk: () => {
                      store.validMultipleGoodsInfo({
                        param: {
                          operateType: 4,
                          containerCode: batchContainerCode,
                          batchCode,
                        },
                        cb: () => {
                          store.secondHangUp({
                            batchCode,
                            batchContainerCode,
                          });
                        },
                        cb2: validMoreGoodsCb,
                      });
                    },
                    autoFocusButton: null,
                  });
                }}
              >
                {t('挂起')}
              </Button>
            )}
          >
            <label>{t('周转箱')}</label>
          </FocusInput>
          <FocusInput
            placeholder={t('请扫描')}
            autoFocus
            disabled={!loading}
            className="sku"
            value={sku}
            onChange={(e) => {
              store.changeData({ sku: e.target.value });
            }}
            onPressEnter={(e) => {
              store.scanGoodsSn({
                sku: e.target.value, // 字段用的sku
                batchCode,
                batchContainerCode,
                workLocation,
              });
            }}
            ftstyle={{
              fontSize: '18px', color: 'red', fontWeight: '700',
            }}
          >
            <label>{t('商品条码')}</label>
          </FocusInput>
          {secondNotBind && (
          <FocusInput
            placeholder={t('请扫描')}
            autoFocus
            disabled={!loading}
            className="seedContainerCode"
            value={seedContainerCode}
            onChange={(e) => {
              store.changeData({ seedContainerCode: e.target.value });
            }}
            onPressEnter={(e) => {
              store.scanSeedContainerCode({
                goodsSn,
                seedContainerCode: e.target.value,
                batchCode,
                batchContainerCode,
                workLocation,
                no,
                size,
                skuCode,
              });
            }}
            ftstyle={{
              fontSize: '18px', color: 'red', fontWeight: '700',
            }}
          >
            <label>{t('二分周转箱')}</label>
          </FocusInput>
          )}
        </Form>
        <Footer
          beforeBack={() => {
            store.init();
          }}
        >
          <FooterBtn
            disabled={!loading}
            onClick={() => {
              store.getDetail({ batchContainerCode });
            }}
          >
            {t('明细')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

ScanGoods.propTypes = {
  loading: PropTypes.number,
  containerCode: PropTypes.string,
  workLocation: PropTypes.string,
  batchContainerInfo: PropTypes.shape(),
  goodsSn: PropTypes.string,
  sku: PropTypes.string.isRequired,
  seedContainerCode: PropTypes.string.isRequired,
  secondNotBind: PropTypes.bool,
  no: PropTypes.string,
  size: PropTypes.string,
  skuCode: PropTypes.string,
};

export default ScanGoods;
