import { sendPostRequest } from '../../../lib/public-request';

// 首页/二分操作-扫描周转箱
export const scanContainerCodeAPI = (param) => sendPostRequest({
  url: '/split_up/scan_box',
  param,
}, process.env.WPOC_URI);

// 首页-扫描工号
export const scanWorkLocationAPI = (param) => sendPostRequest({
  url: '/split_up/scan_work_location',
  param,
}, process.env.WPOC_URI);

// 首页-更换二分人
export const changeSecondSowingUserAPI = (param) => sendPostRequest({
  url: '/second_sowing/change_second_sowing_user',
  param,
}, process.env.WOS_URI);

// 分包扫描-包裹明细
export const listSplitDetailAPI = (param) => sendPostRequest({
  url: '/subcontract_scan/list_split_detail',
  param,
}, process.env.WPOC_URI);

// 分包扫描-扫描包裹号
export const scanPackageNoAPI = (param) => sendPostRequest({
  url: '/split_up/scan_package',
  param,
}, process.env.WPOC_URI);

// 分包扫描-空箱操作
export const emptyBoxAPI = (param) => sendPostRequest({
  url: '/split_up/empty_box',
  param,
}, process.env.WPOC_URI);

// 分包扫描-空箱操作确认
export const confirmEmptyBoxAPI = (param) => sendPostRequest({
  url: '/split_up/comfirm_empty',
  param,
}, process.env.WPOC_URI);

// 二分操作-多播
export const getMultipleGoodsInfoAPI = (param) => sendPostRequest({
  url: '/sowing_multiple_goods/get_sowing_multiple_goods',
  param,
}, process.env.WOS_URI);

// 二分操作-挂起
export const secondHangUpAPI = (param) => sendPostRequest({
  url: '/second_sowing/hang_up',
  param,
}, process.env.WOS_URI);

// 二分操作-箱空
export const emptyBatchContainerAPI = (param) => sendPostRequest({
  url: '/second_sowing/manual_empty_batch_container',
  param,
}, process.env.WOS_URI);

// 二分操作-明细
export const secondSowingDetailAPI = (param) => sendPostRequest({
  url: '/second_sowing/view_spilt_upper',
  param,
}, process.env.WOS_URI);

// 二分操作-扫描商品条码
export const scanBarcodeAPI = (param) => sendPostRequest({
  url: '/split_up/scan_barcode',
  param,
}, process.env.WOS_URI);

// 二分操作-扫描二分周转箱
export const scanSeedContainerAPI = (param) => sendPostRequest({
  url: '/split_up/scan_second_container',
  param,
}, process.env.WOS_URI);

// 二分操作-关闭二分周转箱
export const closeSeedContainerAPI = (param) => sendPostRequest({
  url: '/second_sowing/close_seed_container',
  param,
}, process.env.WOS_URI);

// 多货登记
export const sowingMultipleGoodsAPI = (param) => sendPostRequest({
  url: '/sowing_multiple_goods/scan_barcode',
  param,
}, process.env.WOS_URI);
