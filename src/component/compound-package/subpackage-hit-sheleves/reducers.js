import React from 'react';
import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import WellenTips from 'common/wellen-tips';
import { LOCAL_USER } from 'lib/storage';
import { classFocus, getWarehouseId } from '../../../lib/util';
import {
  scanContainerCodeAPI, scanWorkLocationAPI, listSplitDetailAPI, scanPackageNoAPI,
  emptyBoxAPI, confirmEmptyBoxAPI, changeSecondSowingUserAPI, secondHangUpAPI,
  getMultipleGoodsInfoAPI, emptyBatchContainerAPI, secondSowingDetailAPI,
  scanBarcodeAPI, scanSeedContainerAPI, closeSeedContainerAPI, sowingMultipleGoodsAPI,
} from './server';
// import FocusInput from '../../common/focus-input';
import Modal from '../../common/modal';
import dingdong from '../../../source/audio/dingdong.mp3';
import aaoo from '../../../source/audio/aaoo.mp3';
import message from '../../common/message';
import styles from './style.css';

const dingdongEle = new Audio(dingdong);
const aaooEle = new Audio(aaoo);
dingdongEle.load();
aaooEle.load();

export const defaultState = {
  loading: 1,
  // 0 首页，1一分周转箱-二分操作，2 转运箱-分包扫描, 3 二分操作的详情，
  // 4 分包扫描的详情，5 协作批次分包上架完成页面
  containerType: 0,
  containerCode: '', // 周转箱
  containerCodeDisabled: false, // 周转箱不可输入
  workLocation: '', // 工位
  recommendWorkLocation: '', // 推荐工位
  scanNum: '', // 分包扫描-已扫描
  leftNum: '', // 分包扫描-待扫描
  hasScanPackages: [], // 分包扫描箱号详情-已扫描包裹
  unScanPackages: [], // 分包扫描箱号详情-未扫描包裹
  packageNo: '', // 分包扫描-包裹号
  /**
   * @description 数据结构
   *   serialInfo: { // 包裹序号展示
          location: '', // 二分货架序号
          container: '', // 周转箱序号
          plus: 0, // +1,+2,+3, +0 不展示
          packageNo: '', // 只有‘分包扫描’的操作才会带过来显示，二分的显示序号即可
       },
   */
  serialInfo: null, // 默认不展示
  expList: [], // 分包上架完成展示1
  expPackages: [], // 分包上架完成展示2
  canclePackages: [], // 展示该‘协作批次’下‘取消的包裹序号及对应的二分周转箱’
  batchContainerInfo: {
    hasSowingBatchContainerNum: 0,
    waitingSowingBatchContainerNum: 0,
    secondSowingNum: 0,
    watingSecondSowingNum: 0,
    firstSowingNum: 0,
    wellenMark: '',
    batchCode: '',
    batchContainerCode: '',
  }, // 二分操作-数据展示
  secondDetailList: [], // 二分操作详情
  sku: '', // 二分操作-商品条码
  goodsSn: '', // 二分操作扫描完商品条码后返回的goodsSn
  no: '', // 二分操作扫描完商品条码后返回的no
  size: '', // 二分操作扫描完商品条码后返回的size
  skuCode: '', // 二分操作扫描完商品条码后返回的skuCode
  secondNotBind: false, // 二分操作-是否绑定了二分周转箱
  seedContainerCode: '', // 二分操作-二分周转箱
  newSecondSowingUser: (JSON.parse(localStorage.getItem(LOCAL_USER)) || {}).username || '',
  newSecondSowingUserEn: (JSON.parse(localStorage.getItem(LOCAL_USER)) || {}).formatName || '',
  coBatchInfoList: [], // 协作批次号
};

export default {
  state: defaultState,
  $init(draft) {
    assign(draft, defaultState);
    classFocus('containerCode');
  },
  reset: () => defaultState,
  changeData(draft, action) {
    assign(draft, action);
  },
  // eslint-disable-next-line no-unused-vars
  * init(action, ctx) {
    yield true;
  },
  // 首页-扫描周转箱
  // eslint-disable-next-line default-param-last
  * scanContainerCode(args = {}, ctx) {
    markStatus('loading');
    const { code, info, msg } = yield scanContainerCodeAPI({
      ...args,
      warehouseId: getWarehouseId(),
    });
    if (code === '0') {
      // 如果是批次箱，需要先赋值
      if (info.batchContainerInfo) {
        yield ctx.changeData({
          batchContainerInfo: info.batchContainerInfo,
        });
        // 判断是否要更换二分人
        const { secondSowingUser, batchCode, secondSowingUserEn } = info.batchContainerInfo;
        if (secondSowingUser) {
          const status = yield new Promise((r) => Modal.confirm2({
            title: t('更换二分人'),
            styleType: 'android',
            content: (
              (
                // eslint-disable-next-line react/jsx-filename-extension
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <div className={styles.secondList}>
                    <div className={styles.secondTitle}>{t('原二分人')}</div>
                    <div className={styles.secondValue}>{secondSowingUserEn}</div>
                  </div>
                  <div className={styles.secondList}>
                    <div className={styles.secondTitle}>{t('新二分人')}</div>
                    <div className={styles.secondValue}>{this.state.newSecondSowingUserEn}</div>
                  </div>
                </div>
              )
            ),
            onOk: () => { r(true); },
            onCancel: () => { r(false); },
          }));
          if (status) {
            const cResult = yield changeSecondSowingUserAPI({
              batchCode,
              oldSecondSowingUser: secondSowingUser,
              newSecondSowingUser: this.state.newSecondSowingUser,
            });
            if (cResult.code === '0') {
              yield new Promise((r) => {
                Modal.success({
                  content: t('更换二分人成功！'),
                  onOk: () => {
                    r(true);
                  },
                });
              });
              yield ctx.reset(); // 重置页面
            } else {
              Modal.error({
                content: cResult.msg,
                modalBlurInput: true,
                className: 'containerCode',
              });
              yield ctx.changeData({
                containerCode: '',
                containerCodeDisabled: false,
              });
              return;
            }
          }
          yield ctx.changeData({
            containerCode: '',
            containerCodeDisabled: false,
          });
          classFocus('containerCode');
          return;
        }
        // 更换二分人-end
        // 重复确认-start
        const {
          seedRecordNum, allBatchContainerNum, batchContainerCode,
          packageNum, wellenTypeName, nationalLine, orderSource, wellenMark,
        } = info.batchContainerInfo;
        if (seedRecordNum === 0) {
          /* eslint-disable */
          const confirmStatus = yield new Promise(r => Modal.confirm({
            content: (
              <div>
                <div>{t('该周转箱')}{batchContainerCode},</div>
                <div>{batchCode}</div>
                <div>
                  {t('包含')}
                  <span style={{color: 'red', fontWeight: 'bold'}}>{allBatchContainerNum}</span>
                  {t('个周转箱')}
                </div>
                <div>{t('包裹数')}：{packageNum}</div>
                <div>{t('波次类型')}：{wellenTypeName}</div>
                <WellenTips arr={wellenMark || []}/>
                <div>
                  {t('请确认实物箱数是否准确')}
                </div>
                <div style={{textAlign: 'center'}}>
                  {nationalLine ? (
                    <span>{t('国家线')}：<b style={{fontSize: '24px', color: 'red'}}>{nationalLine}</b></span>
                  ) : ''}
                </div>
                <div style={{textAlign: 'center'}}>
                  {orderSource ? (
                    <span>{t('订单来源')}：<b style={{fontSize: '24px', color: 'red'}}>{orderSource}</b></span>
                  ) : ''}
                </div>
              </div>
            ),
            onOk: () => {r(true);},
            onCancel: () => {r(false);},
          }));
          /* eslint-enable */
          if (confirmStatus) {
            yield ctx.scanContainerCode({
              ...args,
              confirm: 1,
            });
          } else {
            yield ctx.changeData({
              containerCode: '',
              containerCodeDisabled: false,
            });
            classFocus('containerCode');
          }
          return;
        }
        // 重复确认-end
      }
      yield ctx.changeData({
        containerCodeDisabled: true, // 周转箱不可输入
        recommendWorkLocation: info.workLocation || '', // 推荐工位
        coBatchInfoList: info?.coBatchInfoList || [], // 协作批次
      });
      // 需要判断二分工位是否已经有值, 否则清空二分工位
      const { workLocation, containerCode } = this.state;
      if (workLocation) {
        // 有推荐工位且不等于已存在的二分工位
        if (info.workLocation && workLocation !== info.workLocation) {
          yield ctx.changeData({
            workLocation: '',
          });
        } else {
          yield ctx.scanWorkLocation({
            containerCode,
            workLocation,
          });
          return;
        }
      }
      classFocus('workLocation');
    } else {
      yield ctx.changeData({
        containerCode: '',
      });
      Modal.error({
        content: msg,
        className: 'containerCode',
        audioErr: true,
      });
    }
  },
  // 扫描工位
  // eslint-disable-next-line default-param-last
  * scanWorkLocation(args = {}, ctx) {
    markStatus('loading');
    const { code, info, msg } = yield scanWorkLocationAPI({
      ...args,
      warehouseId: getWarehouseId(),
    });
    if (code === '0') {
      yield ctx.changeData(
        assign(
          {},
          {
            containerType: info.type, // 1 二分操作，2 分包扫描
            serialInfo: null,
            // 清空数据
            packageNo: '',
            sku: '', // 二分操作-商品条码
            secondNotBind: false, // 二分操作-是否绑定了二分周转箱
            seedContainerCode: '', // 二分操作-二分周转箱
          },
          info.type === 2 && {
            scanNum: info.scanNum, // 分包扫描-已扫描
            leftNum: info.leftNum, // 分包扫描-待扫描
          },
        ),
      );
      classFocus(info.type === 1 ? 'sku' : 'packageNo');
    } else {
      yield ctx.changeData({
        workLocation: '',
      });
      Modal.error({
        content: msg,
        className: 'workLocation',
        audioErr: true,
      });
    }
  },
  // 分包扫描-获取包裹明细
  // eslint-disable-next-line default-param-last
  * getDetails(args = {}, ctx) {
    markStatus('loading');
    const { containerCode } = args;
    const { code, info, msg } = yield listSplitDetailAPI({
      transferContainerCode: containerCode,
    });
    if (code === '0') {
      yield ctx.changeData({
        containerType: 4, // 详情页
        hasScanPackages: info.hasScanPackages,
        unScanPackages: info.unScanPackages,
      });
    } else {
      Modal.error({
        content: msg,
        className: 'packageNo',
        audioErr: true,
      });
    }
  },
  // 分包扫描-扫描包裹号
  // eslint-disable-next-line default-param-last
  * scanPackageNo(args = {}, ctx) {
    markStatus('loading');
    const oldSerialMark = this.state.serialInfo
      ? `${this.state.serialInfo.location}${this.state.serialInfo.container}`
      : null;
    const { code, info, msg } = yield scanPackageNoAPI({
      ...args,
      warehouseId: getWarehouseId(),
    });
    if (code === '0') {
      // 1.操作成功，更新扫描/待扫描数 2.该箱分包完成 3.进入协作批次分包上架完成页面
      // 4.二分工位已满，请扫描新的二分工位5.该箱分包完成且二分工位已满
      switch (info.type) {
        case 1: {
          message.success(t('操作成功!'), 1000);
          yield this.changeData({
            packageNo: '',
            scanNum: info.scanNum,
            leftNum: info.leftNum,
            serialInfo: {
              container: info.containerOrder,
              location: info.locationOrder,
              packageNo: args.packageNo,
              plus: oldSerialMark === `${info.locationOrder}${info.containerOrder}` ?
                this.state.serialInfo.plus + 1 : 0, // 相同显示加1
            },
          });
          classFocus('packageNo');
          // 如果是已取消的包裹，需要啊～哦～
          if (info.canceled) {
            aaooEle.play();
            Modal.info({
              content: t('包裹已取消，不需要上架'),
            });
          }
          break;
        }
        case 2: {
          // 回到首页
          message.success(t('该箱分包完成!'), 1000);
          yield this.changeData({
            containerType: 0,
            containerCode: '',
            containerCodeDisabled: false,
            recommendWorkLocation: '',
            serialInfo: {
              container: info.containerOrder,
              location: info.locationOrder,
              packageNo: args.packageNo,
              plus: oldSerialMark === `${info.locationOrder}${info.containerOrder}` ?
                this.state.serialInfo.plus + 1 : 0, // 相同显示加1
            },
          });
          classFocus('containerCode');
          // 如果是已取消的包裹，需要啊～哦～
          if (info.canceled) {
            aaooEle.play();
            Modal.info({
              content: t('包裹已取消，不需要上架'),
            });
          }
          break;
        }
        case 3: {
          // 协作批次分包上架完成页面
          yield this.changeData({
            containerType: 5,
            expList: info.expList || [],
            cancelList: info.cancelList || [],
            expPackages: [],
            canclePackages: [],
            serialInfo: {
              container: info.containerOrder,
              location: info.locationOrder,
              packageNo: args.packageNo,
              plus: oldSerialMark === `${info.locationOrder}${info.containerOrder}` ?
                this.state.serialInfo.plus + 1 : 0, // 相同显示加1
            },
          });
          break;
        }
        case 4: {
          message.warning(t('二分工位已满，请扫描新的工位!'), 1000);
          yield this.changeData({
            containerType: 0,
            containerCode: args.containerCode,
            containerCodeDisabled: true,
            workLocation: '',
            recommendWorkLocation: '',
            serialInfo: {
              container: info.containerOrder,
              location: info.locationOrder,
              packageNo: args.packageNo,
              plus: oldSerialMark === `${info.locationOrder}${info.containerOrder}` ?
                this.state.serialInfo.plus + 1 : 0, // 相同显示加1
            },
          });
          classFocus('workLocation');
          break;
        }
        case 5: {
          // 回到首页
          message.success(t('该箱分包完成且二分工位已满!'), 1000);
          yield this.changeData({
            containerType: 0,
            containerCode: '',
            containerCodeDisabled: false,
            workLocation: '',
            recommendWorkLocation: '',
            serialInfo: {
              container: info.containerOrder,
              location: info.locationOrder,
              packageNo: args.packageNo,
              plus: oldSerialMark === `${info.locationOrder}${info.containerOrder}` ?
                this.state.serialInfo.plus + 1 : 0, // 相同显示加1
            },
          });
          classFocus('containerCode');
          break;
        }
        default:
          Modal.error({
            content: msg,
            audioErr: true,
          });
      }
    } else if (code === '500399') {
      // 二分工位上满
      yield new Promise((r) => Modal.error({
        content: msg,
        onOk: () => { r(true); },
        autoFocusButton: null,
        audioErr: true,
      }));
      yield this.changeData({
        containerType: 0,
        containerCode: args.containerCode,
        containerCodeDisabled: true,
        workLocation: '',
        recommendWorkLocation: '',
      });
      classFocus('workLocation');
    } else {
      yield ctx.changeData({
        packageNo: '',
      });
      Modal.error({
        content: msg,
        className: 'packageNo',
        audioErr: true,
      });
    }
  },
  // 分包扫描-箱空操作
  // eslint-disable-next-line default-param-last
  * emptyBox(args = {}, ctx) {
    markStatus('loading');
    const { code, info, msg } = yield emptyBoxAPI({
      ...args,
      warehouseId: getWarehouseId(),
    });
    if (code === '0') {
      const status = yield new Promise((r) => Modal.confirm({
        content: (
          // eslint-disable-next-line react/jsx-filename-extension
          <div>
            <div style={{ textAlign: 'left' }}>{t('箱号还有')}
              {info}{t('个包裹未分包，确定要清空箱？')}
            </div>
          </div>
        ),
        onOk: () => r(true),
        onCancel: () => r(false),
      }));
      if (status) {
        yield ctx.confirmEmptyBox(args);
      } else {
        classFocus('packageNo');
      }
    } else {
      Modal.error({
        content: msg,
        audioErr: true,
      });
    }
  },
  // 分包扫描-确认清空箱
  * confirmEmptyBox(args) {
    markStatus('loading');
    const { code, info, msg } = yield confirmEmptyBoxAPI({
      ...args,
      warehouseId: getWarehouseId(),
    });
    if (code === '0') {
      switch (info.type) {
        case 1: {
          // 协作批次分包上架完成页面
          yield this.changeData({
            containerType: 5,
            expList: info.expList || [],
            cancelList: info.cancelList || [],
            serialInfo: null,
          });
          break;
        }
        case 2: {
          // 回到首页
          message.success(t('该箱箱空完成!'), 1000);
          yield this.changeData({
            containerType: 0,
            containerCode: '',
            containerCodeDisabled: false,
            recommendWorkLocation: '',
            serialInfo: null,
          });
          classFocus('containerCode');
          break;
        }
        default: {
          message.success(t('该箱箱空完成'));
        }
      }
    } else {
      Modal.error({
        content: msg,
        audioErr: true,
      });
    }
  },
  // 二分操作-多货校验
  * validMultipleGoodsInfo(args = {}) {
    markStatus('loading');
    const { param, cb, cb2 } = args;
    const { code, info, msg } = yield getMultipleGoodsInfoAPI(param);
    if (code === '0') {
      if (cb2 && info.waitingSowingNum > 0) {
        cb2(info);
      } else {
        cb();
      }
    } else {
      Modal.error({
        content: msg,
        autoFocusButton: null,
        audioErr: true,
      });
    }
  },
  // 二分操作-挂起
  // eslint-disable-next-line default-param-last
  * secondHangUp(args = {}, ctx) {
    markStatus('loading');
    const { code, msg } = yield secondHangUpAPI(args);
    if (code === '0') {
      // 回到首页
      yield this.changeData({
        containerType: 0,
        containerCode: '',
        containerCodeDisabled: false,
        recommendWorkLocation: '',
        serialInfo: null,
      });
      classFocus('containerCode');
      yield ctx.reset();
    } else {
      Modal.error({
        content: msg,
        onOk: () => { },
        autoFocusButton: null,
        audioErr: true,
      });
    }
  },
  // 二分操作-手动箱空
  * emptyContainer(args = {}) {
    markStatus('loading');
    const { code, info, msg } = yield emptyBatchContainerAPI(args);
    if (code === '0') {
      switch (info.type) {
        // 协作批次分包上架完成页面
        case 7:
          yield this.changeData({
            containerType: 5,
            expList: [],
            cancelList: [],
            expPackages: info.expPackages || [],
            canclePackages: info.canclePackages || [],
            serialInfo: null,
          });
          break;
        default:
          message.success(t('该箱分包完成!'), 1000);
          yield this.changeData({
            containerType: 0,
            containerCode: '',
            containerCodeDisabled: false,
            recommendWorkLocation: '',
            serialInfo: null,
          });
          classFocus('containerCode');
          break;
      }
    } else {
      Modal.error({
        content: msg,
        onOk: () => {},
        autoFocusButton: null,
        audioErr: true,
      });
    }
  },
  // 二分操作-明细
  // eslint-disable-next-line default-param-last
  * getDetail(args = {}, ctx) {
    markStatus('loading');
    const { secondNotBind } = this.state;
    const { code, info, msg } = yield secondSowingDetailAPI(args);
    if (code === '0') {
      const { hasSowingInfoList, unSowingInfoList } = info;
      yield ctx.changeData({
        containerType: 3,
        secondDetailList: [hasSowingInfoList, unSowingInfoList],
      });
    } else {
      Modal.error({
        content: msg,
        onOk: () => {
          if (secondNotBind) {
            classFocus('sku');
          } else {
            classFocus('seedContainerCode');
          }
        },
        autoFocusButton: null,
        audioErr: true,
      });
    }
  },
  // 二分操作-扫描商品条码
  // eslint-disable-next-line default-param-last
  * scanGoodsSn(args = {}, ctx) {
    markStatus('loading');
    const oldSerialMark = this.state.serialInfo
      ? `${this.state.serialInfo.location}${this.state.serialInfo.container}`
      : null;
    const { code, info, msg } = yield scanBarcodeAPI(args);
    if (code === '0') {
      // 1未绑定二分箱，3 当前箱子分完，协作批次没分完 4全部二分完成，5 关箱 6 多货登记 7分包上架的‘协作批次’已分完
      switch (info.type) {
        case 1: {
          dingdongEle.play();
          yield ctx.changeData({
            secondNotBind: true,
            seedContainerCode: '',
            serialInfo: {
              container: info.secondContainerSequence, // 二分货架序号
              location: info.locationSequence, // 二分周转箱序号
              packageNo: '',
              plus: oldSerialMark === `${info.locationSequence}${info.secondContainerSequence}` ?
                this.state.serialInfo.plus + 1 : 0, // 相同显示加1
            },
            goodsSn: info.goodsSn, // 给二分周转箱用
            no: info.no, // 给二分周转箱用
            size: info.size, // 给二分周转箱用
            skuCode: info.skuCode, // 给二分周转箱用
          });
          classFocus('seedContainerCode');
          break;
        }
        case 3:
        case 4: {
          // 回到首页
          message.success(t('该箱分包完成!'), 1000);
          yield this.changeData({
            containerType: 0,
            containerCode: '',
            containerCodeDisabled: false,
            recommendWorkLocation: '',
            sku: '', // 二分操作-商品条码
            seedContainerCode: '',
            secondNotBind: false, // 二分操作-是否绑定了二分周转箱
            serialInfo: {
              container: info.secondContainerSequence, // 二分货架序号
              location: info.locationSequence, // 二分周转箱序号
              packageNo: '',
              plus: oldSerialMark === `${info.locationSequence}${info.secondContainerSequence}` ?
                this.state.serialInfo.plus + 1 : 0, // 相同显示加1
            },
          });
          classFocus('containerCode');
          break;
        }
        case 5: {
          // 如果在商品条码 扫描周转箱就会触发 关箱
          const status = yield new Promise((r) => Modal.confirm({
            content: (
              <div>
                {t('是否将')}<span style={{ color: 'red' }}> {args.sku} </span>{t('换箱')}？
              </div>
            ),
            onOk: () => r(true),
            onCancel: () => r(false),
          }));
          if (status) {
            yield ctx.close({
              seedContainerCode: args.sku, // 这时sku是二分周转箱
              batchContainerCode: args.batchContainerCode,
              batchCode: args.batchCode,
            });
          } else {
            yield this.changeData({
              sku: '', // 二分操作-商品条码
            });
            classFocus('sku');
          }
          break;
        }
        case 6: {
          const status = yield new Promise((r) => {
            Modal.confirm({
              content: t('商品条码不存在该批次周转箱的包裹明细，是否多件登记？'),
              onOk: () => r(true),
              onCancel: () => r(false),
              autoFocusButton: null,
            });
          });
          if (status) {
            const multiRes = yield sowingMultipleGoodsAPI({
              ...info.sowingMultipleGoodsScanBarcodeReq,
            });
            if (multiRes.code !== '0') {
              Modal.error({
                content: multiRes.msg,
                autoFocusButton: null,
                audioErr: true,
              });
            }
          }
          yield ctx.changeData({
            sku: '',
            secondNotBind: false,
            seedContainerCode: '',
            serialInfo: {
              container: info.secondContainerSequence, // 二分货架序号
              location: info.locationSequence, // 二分周转箱序号
              packageNo: '',
              plus: oldSerialMark === `${info.locationSequence}${info.secondContainerSequence}` ?
                this.state.serialInfo.plus + 1 : 0, // 相同显示加1
            },
            // 更新二分/待二分
            batchContainerInfo: {
              ...this.state.batchContainerInfo,
              secondSowingNum: info.secondSowingNum,
              watingSecondSowingNum: info.watingSecondSowingNum,
            },
          });
          classFocus('sku');
          break;
        }
        case 7: {
          // 协作批次分包上架完成页面
          yield this.changeData({
            containerType: 5,
            expList: [],
            cancelList: [],
            expPackages: info.expPackages || [],
            canclePackages: info.canclePackages || [],
            serialInfo: {
              container: info.secondContainerSequence, // 二分货架序号
              location: info.locationSequence, // 二分周转箱序号
              packageNo: '',
              plus: oldSerialMark === `${info.locationSequence}${info.secondContainerSequence}` ?
                this.state.serialInfo.plus + 1 : 0, // 相同显示加1
            },
          });
          break;
        }
        default:
          yield ctx.changeData({
            sku: '',
            secondNotBind: false,
            seedContainerCode: '',
            serialInfo: {
              container: info.secondContainerSequence, // 二分货架序号
              location: info.locationSequence, // 二分周转箱序号
              packageNo: '',
              plus: oldSerialMark === `${info.locationSequence}${info.secondContainerSequence}` ?
                this.state.serialInfo.plus + 1 : 0, // 相同显示加1
            },
            // 更新二分/待二分
            batchContainerInfo: {
              ...this.state.batchContainerInfo,
              secondSowingNum: info.secondSowingNum,
              watingSecondSowingNum: info.watingSecondSowingNum,
            },
          });
          classFocus('sku');
      }
    } else {
      yield ctx.changeData({
        sku: '',
        seedContainerCode: '',
        secondNotBind: false,
      });
      Modal.error({
        content: msg,
        autoFocusButton: null,
        onOk: () => {
          classFocus('sku');
        },
        audioErr: true,
      });
    }
  },
  // 二分操作-扫描二分周转箱
  // eslint-disable-next-line default-param-last
  * scanSeedContainerCode(args = {}, ctx) {
    markStatus('loading');
    const oldSerialMark = this.state.serialInfo
      ? `${this.state.serialInfo.location}${this.state.serialInfo.container}`
      : null;
    const { code, info, msg } = yield scanSeedContainerAPI(args);
    if (code === '0') {
      // 3 当前箱子分完，协作批次没分完 4全部二分完成，6二分工位已上满，7分包上架的‘协作批次’已分完
      switch (info.type) {
        case 3:
        case 4: {
          // 回到首页
          message.success(t('该箱分包完成!'), 1000);
          yield this.changeData({
            containerType: 0,
            containerCode: '',
            containerCodeDisabled: false,
            recommendWorkLocation: '',
            sku: '', // 二分操作-商品条码
            seedContainerCode: '',
            secondNotBind: false, // 二分操作-是否绑定了二分周转箱
          });
          classFocus('containerCode');
          break;
        }
        case 6: {
          message.warning(t('二分工位已满，请扫描新的工位!'), 1000);
          // 回到首页
          yield ctx.changeData({
            containerType: 0,
            containerCodeDisabled: true,
            workLocation: '',
            recommendWorkLocation: '',
            sku: '', // 二分操作-商品条码
            seedContainerCode: '',
            secondNotBind: false, // 二分操作-是否绑定了二分周转箱
          });
          classFocus('workLocation');
          break;
        }
        case 7: {
          // 协作批次分包上架完成页面
          yield this.changeData({
            containerType: 5,
            expList: [],
            cancelList: [],
            expPackages: info.expPackages || [],
            canclePackages: info.canclePackages || [],
            serialInfo: {
              container: info.secondContainerSequence, // 二分货架序号
              location: info.locationSequence, // 二分周转箱序号
              packageNo: '',
              plus: oldSerialMark === `${info.locationSequence}${info.secondContainerSequence}` ?
                this.state.serialInfo.plus + 1 : 0, // 相同显示加1
            },
          });
          break;
        }
        default:
          yield ctx.changeData({
            sku: '',
            secondNotBind: false,
            seedContainerCode: '',
            // 更新二分/待二分
            batchContainerInfo: {
              ...this.state.batchContainerInfo,
              secondSowingNum: info.secondSowingNum,
              watingSecondSowingNum: info.watingSecondSowingNum,
            },
          });
          classFocus('sku');
      }
    } else {
      yield ctx.changeData({
        seedContainerCode: '',
      });
      Modal.error({
        content: msg,
        autoFocusButton: null,
        onOk: () => {
          classFocus('seedContainerCode');
        },
        audioErr: true,
      });
    }
  },
  // 二分操作-关闭二分箱
  * close(args, ctx) {
    markStatus('loading');
    const { code, info, msg } = yield closeSeedContainerAPI(args);
    if (code === '0') {
      const { openWindow, openWindowMsg } = info;
      if (openWindow) {
        Modal.error({
          content: openWindowMsg,
          onOk: () => {
            classFocus('sku');
          },
          autoFocusButton: null,
          audioErr: true,
        });
        return;
      }
      yield ctx.changeData({
        sku: '',
        secondNotBind: false,
        seedContainerCode: '',
        // 更新二分/待二分
        batchContainerInfo: {
          ...this.state.batchContainerInfo,
          secondSowingNum: info.secondSowingNum,
          watingSecondSowingNum: info.watingSecondSowingNum,
        },
      });
      message.success(`${t('{} 二分周转箱已成功换箱', args.seedContainerCode)}`);
      classFocus('sku');
    } else {
      Modal.error({
        content: msg,
        onOk: () => {
          classFocus('sku');
        },
        autoFocusButton: null,
        audioErr: true,
      });
    }
  },
};
