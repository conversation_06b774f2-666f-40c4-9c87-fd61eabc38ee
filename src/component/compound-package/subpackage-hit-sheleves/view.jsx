import React from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import Home from './jsx/home';
import SubScan from './jsx/sub-scan';
import SubDetails from './jsx/sub-details';
import Second from './jsx/second';
import SecondDetails from './jsx/second-details';
import Complete from './jsx/complete';
import store from './reducers';

class Container extends React.Component {
  componentWillMount() {
    store.init();
  }

  render() {
    const {
      containerType,
    } = this.props;
    return (
      <div>
        {(() => {
          switch (Number(containerType)) {
            case 1: // 二分操作
              return <Second {...this.props} />;
            case 2: // 分包扫描
              return <SubScan {...this.props} />;
            case 3: // 二分详情
              return <SecondDetails {...this.props} />;
            case 4: // 分包扫描详情
              return <SubDetails {...this.props} />;
            case 5: // 协作批次分包上架完成页面
              return <Complete {...this.props} />;
            default: // 首页
              return <Home {...this.props} />;
          }
        })()}
      </div>
    );
  }
}

Container.propTypes = {
  containerType: PropTypes.number.isRequired,
};

export default i18n(Container);
