import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import {
  Table, PopSheet,
} from '../../../common';
import styles from '../style.css';

function QueryResultPage(props) {
  const {
    showPopSheet,
    queryType, // 1-合包异常库位  2-二分周转箱  3-子包裹号
    queryContent,
    infoData,
    secondContainerList,
    queryContentNoClear,
  } = props;
  const {
    locationNo, combineNo, combineStatus, subList,
    secondContainerCode, combineSubNo, secondContainerStatus,
  } = infoData;
  const columns = [
    {
      title: t('子包裹号'),
      dataIndex: 'combineSubNo',
      width: 20,
      render: (r) => (
        // eslint-disable-next-line jsx-a11y/anchor-is-valid
        <a
          onClick={() => {
            // 没有周转箱就不跳转了
            if (!r.secondContainerCodeList || r.secondContainerCodeList.length === 0) {
              return;
            }
            const list = r.secondContainerCodeList?.map((e) => ({
              label: e,
              name: e,
            }));
            // 如果只有一条 直接跳到明细 否则弹框让用户选择
            if (list.length === 1) {
              store.changeContainerCodeAndToDtl({
                containerCode: list[0].name,
                combineSubNo: r.combineSubNo,
              });
            } else {
              store.changeData({
                showPopSheet: true,
                combineSubNo: r.combineSubNo,
                secondContainerList: list,
              });
            }
          }}
          className={styles.dichotomyStationA}
        >
          <div>
            <div
               // 【维度-二分周转箱 2】对应二分周转箱对应的子包裹行数据，置为红色。
               // 【维度-子包裹 3】对应的子包裹行数据，置为红色。
              className={
                ((queryType === '2' && r.secondContainerCodeList?.includes(queryContentNoClear)) || (queryType === '3' && r.combineSubNo === queryContentNoClear)) ?
                  styles.redTag :
                  styles.normal
              }
            >
              {r.combineSubNo}
            </div>
            {(r.secondContainerCodeList && r.secondContainerCodeList.length > 0) && <div>({r.secondContainerCodeList?.join(',')})</div>}
          </div>
        </a>
      ),
    },
    {
      title: t('状态'),
      dataIndex: 'combineSubStatus',
      width: 20,
      render: (r) => (
        // 【维度-二分周转箱 2】对应二分周转箱对应的子包裹行数据，置为红色。
        // 【维度-子包裹 3】对应的子包裹行数据，置为红色。
        <span
          className={
            ((queryType === '2' && r.secondContainerCodeList?.includes(queryContentNoClear)) || (queryType === '3' && r.combineSubNo === queryContentNoClear)) ?
              styles.redTag :
              ''
          }
        >
          {r.combineSubStatus}
        </span>
      ),
    },
  ];
  return (
    <div className={styles.dichotomyStationForm} style={{ maxHeight: 'calc(100vh - 208px)', overflowY: 'scroll' }}>
      {/* 合包异常库位start  */}
      {
        ['1'].includes(queryType) && (
          <div>
            <div className={styles.dichotomyStationline}>
              <div>{t('合包异常库位')}:</div>
              <div>{locationNo}</div>
            </div>
            <div className={styles.dichotomyStationline}>
              <div>{t('合包包裹号&状态')}:</div>
              <div>
                {combineNo ? (
                  <div className={styles.dichotomyStationItem}>
                    <span>{combineNo}</span>
                    <span>{combineStatus}</span>
                  </div>
                ) :
                  t('无')}
              </div>
            </div>
          </div>
        )
      }
      {/* 合包异常库位end  */}
      {/* 二分周转箱start */}
      {
        ['2'].includes(queryType) && (
          <div>
            <div className={styles.dichotomyStationline}>
              <div>{t('二分周转箱&使用状态')}:</div>
              <div className={styles.dichotomyStationItem}>
                <span>{secondContainerCode}</span>
                <span>{secondContainerStatus}</span>
              </div>
            </div>
            {locationNo && (
            <div className={styles.dichotomyStationline}>
              <div>{t('合包异常库位')}:</div>
              <div>{locationNo}</div>
            </div>
            )}
            <div className={styles.dichotomyStationline}>
              <div>{t('合包包裹号&状态')}:</div>
              <div>
                {combineNo ? (
                  <div className={styles.dichotomyStationItem}>
                    <span>{combineNo}</span>
                    <span>{combineStatus}</span>
                  </div>
                ) :
                  t('无')}
              </div>
            </div>
          </div>
        )
      }
      {/* 二分周转箱end */}
      {/* 子包裹start */}
      {
        ['3'].includes(queryType) && (
          <div>
            <div className={styles.dichotomyStationline}>
              <div>{t('子包裹')}:</div>
              <div>{combineSubNo}</div>
            </div>
            <div className={styles.dichotomyStationline}>
              <div>{t('合包异常库位')}:</div>
              <div>{locationNo || t('无')}</div>
            </div>
            <div className={styles.dichotomyStationline}>
              <div>{t('合包包裹号&状态')}:</div>
              <div>
                {combineNo ? (
                  <div className={styles.dichotomyStationItem}>
                    <span>{combineNo}</span>
                    <span>{combineStatus}</span>
                  </div>
                ) :
                  t('无')}
              </div>
            </div>
          </div>
        )
      }
      {/* 子包裹end */}

      {subList && subList.length && (
      <Table
        columns={columns}
        dataSource={subList || []}
      />
      )}
      <PopSheet
        onClick={(v) => {
          store.changeData({
            showPopSheet: false,
            containerCode: v.name,
            showDetail: true, // 展示二分箱明细
          });
        }}
        onClose={() => {
          store.changeData({ showPopSheet: false });
        }}
        cancelBtn
        menus={secondContainerList}
        show={showPopSheet}
      />
    </div>
  );
}

QueryResultPage.propTypes = {
  showPopSheet: PropTypes.bool,
  queryType: PropTypes.string,
  queryContent: PropTypes.string,
  infoData: PropTypes.shape(),
  secondContainerList: PropTypes.arrayOf(PropTypes.shape()),
  queryContentNoClear: PropTypes.string,
};

export default QueryResultPage;
