import { sendPostRequest } from '../../../lib/public-request';

/**
 * 分包上架-多维度查询
 * @param {object} param
 * @returns
 */
export const multiTypeQueryAPI = (param) => sendPostRequest({
  url: '/combine_exception/exception_query',
  param,
}, process.env.WPOC_URI);

/**
 * 合包异常上架查询-二分箱明细查询
 * @param {object} param
 * @returns
 */
export const getDetailListAPI = (param) => sendPostRequest({
  url: '/pda/combine_exception/second_detail_query',
  param,
}, process.env.WOS_URI);

/**
 *  扫描商品条码
 * @param {object} param
 * @returns
 */
export const scanGoodsSnAPI = (param) => sendPostRequest({
  url: '/inventory/get_batch_container_info_scan',
  param,
});
