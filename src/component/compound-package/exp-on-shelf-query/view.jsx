import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { CellsTitle } from 'react-weui/build/packages/components/cell';
import store from './reducers';
import { Header, Footer, pages } from '../../common';
import Entry from './jsx/entry';
import QueryResultPage from './jsx/query-result-page';
import DetailsPage from './jsx/details-page';
import styles from './style.css';

const { View } = pages;
class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      showDetail,
      isQueried,
      initLoading,
    } = this.props;
    return (
      <View initLoading={initLoading}>
        {
          showDetail ? (
            <div>
              <Header title={t('异常上架查询-二分箱明细')} />
              <DetailsPage {...this.props} />
              <Footer
                beforeBack={() => {
                  store.changeData({
                    showDetail: false,
                    showPopSheet: false,
                  });
                }}
              />
            </div>
          )
            : (
              <div>
                <Header title={t('异常上架查询')} />
                <Entry {...this.props} />
                <CellsTitle className={styles.infoStyle}>{t('信息')}</CellsTitle>
                {isQueried && <QueryResultPage {...this.props} />}
                <Footer
                  beforeBack={() => {
                    // 点击则返回到路径上一页[合包业务-合包异常上架]
                    window.location.hash = 'compound-package/exp-on-shelf';
                  }}
                />
              </div>
            )
         }
      </View>
    );
  }
}

Container.propTypes = {
  totalContainerNum: PropTypes.number,
  totalPalletNum: PropTypes.number,
  showDetail: PropTypes.bool,
  isQueried: PropTypes.bool,
  initLoading: PropTypes.bool,
};

export default i18n(Container);
