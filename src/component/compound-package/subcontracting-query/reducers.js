import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { markStatus } from 'rrc-loader-helper';
import {
  classFocus, getHeaderTitle,
} from 'lib/util';
import {
  multiTypeQueryAPI, scanContainerCodeAPI, scanGoodsSnAPI, getDetailListAPI,
} from './server';
import Modal from '../../common/modal';

/**
 * 根据id找到对应的列
 * @param list
 * @param id
 * @returns {*|number}
 */
const getCurrentItemIndex = (list, id, skuCode) => {
  // 首先根据id找到对应列，且复核数不等于数量
  // 能满足条件则返回
  let targetItemIndex;
  if (skuCode && list.find((v) => (v.skuCode))) {
    targetItemIndex = list.findIndex((v) => `${v.skuCode}` === skuCode && Number(v.reviewNum) < Number(v.num));
  } else {
    targetItemIndex = list.findIndex((v) => `${v.goodsSn}-${v.size}` === id && Number(v.reviewNum) < Number(v.num));
  }
  if (targetItemIndex > -1) {
    return targetItemIndex;
  }
  // 找不到满足条件的，则只返回匹配id的
  if (skuCode && list.find((v) => (v.skuCode))) {
    return list.findIndex((v) => `${v.skuCode}` === skuCode);
  } else {
    return list.findIndex((v) => `${v.goodsSn}-${v.size}` === id);
  }
};

const handleRowCurrent = (jumpType, id, list, containerType, skuCode) => {
  list = JSON.parse(JSON.stringify(list));
  const result = {};
  switch (jumpType) {
    case 1:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
      // eslint-disable-next-line no-case-declarations
      const itemIndex = getCurrentItemIndex(list, id, skuCode);
      result.current = list[itemIndex];
      result.index = itemIndex;
      break;
    default:
      result.current = list.find((v, i) => {
        if (list[i].containerCode === id) {
          result.index = i;
          return true;
        }
        return false;
      });
      break;
  }
  if (!result.current) {
    throw new Error(t('容器号不存在'));
  }
  list.splice(result.index, 1);
  result.current.reviewNum = String(parseInt(result.current.reviewNum || '0', 10) + 1);
  if (jumpType === 2 && containerType && containerType === 4) {
    // eslint-disable-next-line max-len
    const isSecondCompleteList = list.filter((item) => item.isSecondComplete).sort((a, b) => a.packageSequence - b.packageSequence);
    // eslint-disable-next-line max-len
    const notSecondCompleteList = list.filter((item) => !item.isSecondComplete).sort((a, b) => a.packageSequence - b.packageSequence);
    list = [...notSecondCompleteList, ...isSecondCompleteList];
  }
  list.unshift(result.current);
  return list;
};

const defaultState = {
  queryTypeName: t('二分工位'),
  queryTypeList: [
    {
      items: [
        {
          label: t('二分工位'),
          value: '1',
        },
        {
          label: t('二分周转箱'),
          value: '2',
        },
        {
          label: t('子包裹'),
          value: '3',
        },
      ],
    },
  ],
  queryType: '1', // 查询维度 1-二分工位  2-二分周转箱  3-子包裹号查询
  queryContent: '', // 查询内容
  queryContentDisabled: 1,
  showPopSheet: false, // 选择弹框flag
  detailList: [], // 明细列表
  showDetail: false,
  isQueried: false,
  infoData: {
    workLocationCode: '', // 二分工位
    splitBatchCode: '', // 分包上架批次
    splitUpStatus: '', // 分包上架批次状态
    splitUpStatusDesc: '', // 分包上架批次状态描述
    coBatchCode: '', // 协作批次号
    secondContainerCode: '', // 二分周转箱
    secondContainerStatus: '', // 二分周转箱状态
    secondContainerStatusDesc: '', // 二分周转箱状态描述
    subPackageNo: '', // 子包裹号
    workLocationDetails: [], // 工位上架序号详情列表
  },
  secondContainerList: [], // 二分周转箱列表
  containerCode: '', // 点击表格选择的周转箱
  goodsSnDisabled: 1,
  initLoading: false,
  showPickers: false,
};

export default {
  state: defaultState,

  $init: () => defaultState,

  * init() {
    yield this.changeData({ headerTitle: getHeaderTitle(), initLoading: false });
  },

  changeData(state, data) {
    assign(state, data);
  },
  /**
   * 查询维度
   */
  * multiTypeQuery() {
    markStatus('queryContentDisabled');
    const { queryType, queryContent } = yield '';
    const res = yield multiTypeQueryAPI({
      queryType, // 查询维度 1-二分工位  2-二分周转箱  3-子包裹号查询
      queryContent, // 查询内容
    });
    if (res.code === '0') {
      yield this.changeData({
        infoData: res.info || [],
        isQueried: true,
      });
      // 清空查询内容
      yield this.changeData({
        queryContent: '',
      });
      classFocus('queryContent');
    } else {
      // 清空查询内容
      yield this.changeData({
        queryContent: '',
      });
      Modal.error({
        content: res.msg,
        className: 'queryContent',
      });
    }
  },
  * changeContainerCodeAndToDtl(param) {
    const { containerCode } = param;
    yield this.changeData({
      containerCode,
      showDetail: true,
    });
  },
  /**
   * 容器查询
   */
  * containerQuery() {
    // 扫描容器号获取包裹号 然后再通过包裹号+容器号 获取明细数据
    const { containerCode } = yield '';
    const res = yield scanContainerCodeAPI({
      containerCode,
    });
    if (res.code === '0') {
      const { packageNo } = res.info || {};
      if (packageNo) {
        // 获取明细数据
        const detailRes = yield getDetailListAPI({
          containerCode,
          packageNo,
        });
        if (detailRes.code === '0') {
          yield this.changeData({
            detailList: detailRes.info.list || [],
            containerCode,
          });
        } else {
          Modal.error({
            content: detailRes.msg,
          });
        }
      }
    } else {
      yield this.changeData({

        showDetail: false,
      });
      Modal.error({
        content: res.msg,
      });
    }
  },
  /**
   * 扫描条码
   */
  * scanGoodsSn(action, ctx) {
    markStatus('goodsSnDisabled');
    const res = yield scanGoodsSnAPI(action.param);
    if (res.code === '0') {
      const { detailList } = yield '';
      const { goodsSn, size, skuCode } = res.info;
      const id = `${goodsSn}-${size}`;
      let targetRow = null;
      if (skuCode && detailList.find((v) => v.skuCode)) {
        targetRow = detailList.find((v) => v.skuCode === skuCode);
      } else {
        targetRow = detailList.find((v) => `${v.goodsSn}-${v.size}` === id);
      }
      if (targetRow) {
        // 参考【库内查询-容器号查询界面】周转箱上架详情 jumpType 9
        const resultList = handleRowCurrent(9, id, detailList, null, skuCode);
        yield ctx.changeData({
          detailList: [...resultList],
          goodsSn: '',
        });
        classFocus('goodsSn');
      } else {
        yield ctx.changeData({
          goodsSn: '',
        });
        Modal.error({
          content: t('无此商品，请重新扫描'),
          className: 'goodsSn',
        });
      }
    } else {
      yield ctx.changeData({
        goodsSn: '',
      });
      Modal.error({
        className: 'goodsSn',
        content: res.msg,
      });
    }
  },
};
