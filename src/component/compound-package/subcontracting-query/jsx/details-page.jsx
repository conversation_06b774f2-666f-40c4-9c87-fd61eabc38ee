import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import {
  List, FocusInput,
} from '../../../common';
import styles from '../style.css';

function Entry(props) {
  const {
    detailList,
    goodsSn,
    containerCode,
    goodsSnDisabled,
  } = props;
  useEffect(() => {
    store.containerQuery();
  }, []);
  const rows = [
    [
      {
        title: t('SKC'),
        render: 'goodsSn',
      },
    ],
    [
      {
        title: t('尺码'),
        render: 'size',
        width: 33,
      },
      {
        title: t('数量'),
        render: 'goodsNum',
        width: 33,
      },
      {
        title: t('复核'),
        width: 33,
        default: 0,
        render: 'reviewNum',
      },
    ],
  ];
  return (
    <div>
      <div className={styles.detialPageline}>
        <span>{t('二分箱')}：</span>
        <span>{containerCode}</span>
      </div>
      <FocusInput
        placeholder={t('请扫描')}
        autoFocus
        value={goodsSn}
        disabled={goodsSnDisabled === 0}
        className="goodsSn"
        allowClear
        onChange={(e) => {
          store.changeData({
            goodsSn: e.target.value.trim(),
          });
        }}
        onPressEnter={() => {
          if (goodsSn) {
            store.scanGoodsSn({
              param: {
                goodsSn,
                containerCode,
              },
            });
          }
        }}
      >
        <label>{t('条码')}:</label>
      </FocusInput>
      <List
        rows={rows}
        data={detailList}
        style={{ height: 'calc(100vh - 197px)', overflowY: 'auto' }}
      />
    </div>
  );
}

Entry.propTypes = {
  detailList: PropTypes.arrayOf(PropTypes.shape()),
  goodsSn: PropTypes.string,
  containerCode: PropTypes.string,
  goodsSnDisabled: PropTypes.number,
};

export default Entry;
