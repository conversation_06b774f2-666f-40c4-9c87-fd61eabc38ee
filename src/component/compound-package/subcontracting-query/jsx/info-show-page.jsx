import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import {
  Table, PopSheet,
} from '../../../common';
import styles from '../style.css';

function Entry(props) {
  const {
    showPopSheet,
    queryType, // 1-二分工位  2-二分周转箱  3-子包裹号
    infoData,
    secondContainerList,
  } = props;
  const columns = [
    {
      title: t('货架序号'),
      dataIndex: 'sequence',
      width: 10,
    },
    {
      title: t('子包裹号'),
      dataIndex: 'subPackageNos',
      width: 20,
    },
    {
      title: t('二分周转箱'),
      dataIndex: 'secondContainerCodes',
      width: 20,
      render: (r) => (
        // eslint-disable-next-line jsx-a11y/anchor-is-valid
        <a
          onClick={() => {
            const list = (r.secondContainerCodes || []).split(',').map((e) => ({
              label: e,
              name: e,
            }));
            // 如果只有一条 直接跳到明细 否则弹框让用户选择
            if (list.length === 1) {
              store.changeContainerCodeAndToDtl({
                containerCode: list[0].name,
              });
            } else {
              store.changeData({
                showPopSheet: true,
                secondContainerList: list,
              });
            }
          }}
          className={styles.dichotomyStationA}
        >
          {r.secondContainerCodes}
        </a>
      ),
    },
  ];
  const {
    workLocationCode, splitBatchCode, splitUpStatusDesc, workLocationDetails,
    secondContainerCode, subPackageNo, coBatchCode, secondContainerStatusDesc,
  } = infoData;
  return (
    <div className={styles.dichotomyStationForm} style={{ maxHeight: 'calc(100vh - 242px)', overflow: 'scroll' }}>
      {
        ['1'].includes(queryType) && (
        <div className={styles.dichotomyStationline}>
          <div>{t('二分工位')}:</div>
          <div>{workLocationCode}</div>
        </div>
        )
      }
      {
        ['2'].includes(queryType) && (
        <div className={styles.dichotomyStationline}>
          <div>{t('二分周转箱')}:</div>
          <div className={styles.dichotomyStationItem}>
            <span>{secondContainerCode}</span>
            <span>{secondContainerStatusDesc}</span>
          </div>
        </div>
        )
      }
      {
        ['3'].includes(queryType) && (
        <div className={styles.dichotomyStationline}>
          <div>{t('子包裹')}:</div>
          <div>{subPackageNo}</div>
        </div>
        )
      }
      <div className={styles.dichotomyStationline}>
        <div>{t('分包上架批次&状态')}:</div>
        <div>
          {splitBatchCode ? (
            <div className={styles.dichotomyStationItem}>
              <span>{splitBatchCode}</span>
              <span>{splitUpStatusDesc}</span>
            </div>
          ) :
            t('无')}
        </div>
      </div>
      {coBatchCode && (
      <div className={styles.dichotomyStationline}>
        <div>{t('协作批次')}:</div>
        <div>{coBatchCode}</div>
      </div>
      )}
      {
      ['2', '3'].includes(queryType) && workLocationCode &&
      (
      <div className={styles.dichotomyStationline}>
        <div>{t('二分工位')}:</div>
        <div>{workLocationCode}</div>
      </div>
      )
      }
      {workLocationDetails && workLocationDetails.length && (
      <Table
        columns={columns}
        dataSource={workLocationDetails || []}
      />
      )}
      <PopSheet
        onClick={(v) => {
          store.changeData({
            showPopSheet: false,
            containerCode: v.name,
            showDetail: true, // 展示二分箱明细
          });
        }}
        onClose={() => {
          store.changeData({ showPopSheet: false });
        }}
        cancelBtn
        menus={secondContainerList}
        show={showPopSheet}
      />
    </div>
  );
}

Entry.propTypes = {
  showPopSheet: PropTypes.bool,
  queryType: PropTypes.string,
  infoData: PropTypes.shape(),
  secondContainerList: PropTypes.arrayOf(PropTypes.shape()),
};

export default Entry;
