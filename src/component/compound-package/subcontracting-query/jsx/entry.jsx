import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import {
  classFocus,
} from 'lib/util';
import store from '../reducers';
import {
  FocusInput, Pickers,
} from '../../../common';

function Entry(props) {
  const {
    queryContent,
    queryContentDisabled,
    queryTypeList,
    queryTypeName,
    showPickers,
  } = props;
  return (
    <div>
      <Form>
        {/* <SelectInput
          value={queryTypeName}
          selectValue={queryType}
          lineBreak={false}
          label={`${t('查询维度')}:`}
          enterShow
          disabled
          selectList={queryTypeList}
          onSelect={(value) => {
            const item = queryTypeList.find((e) => e.value === value);
            store.changeData({
              queryType: value,
              queryTypeName: item.name,
              infoData: [],
              queryContent: '',
              isQueried: false,
            });
            classFocus('queryContent');
          }}
        /> */}
        <Pickers
          value={queryTypeName}
          label={t('查询维度')}
          onClick={() => store.changeData({ showPickers: true })}
          onChange={(select) => {
            store.changeData({
              queryType: select.value,
              queryTypeName: select.label,
              infoData: [],
              queryContent: '',
              isQueried: false,
              showPickers: false,
            });
            classFocus('queryContent');
          }}
          show={showPickers}
          pickerData={queryTypeList}
          onCancel={() => store.changeData({ showPickers: false })}
        />
        <FocusInput
          placeholder={t('请扫描')}
          autoFocus
          value={queryContent}
          disabled={queryContentDisabled === 0}
          className="queryContent"
          allowClear
          onChange={(e) => {
            store.changeData({
              queryContent: e.target.value.trim(),
            });
            // 清空时聚焦查询内容
            if (!e.target.value) {
              classFocus('queryContent');
            }
          }}
          onPressEnter={() => {
            if (queryContent) {
              store.multiTypeQuery();
            }
          }}
        >
          <label>{t('查询内容')}:</label>
        </FocusInput>
      </Form>
    </div>
  );
}

Entry.propTypes = {
  queryContent: PropTypes.string,
  queryContentDisabled: PropTypes.number,
  queryTypeName: PropTypes.string,
  queryTypeList: PropTypes.arrayOf(PropTypes.shape()),
  showPickers: PropTypes.bool,
};

export default Entry;
