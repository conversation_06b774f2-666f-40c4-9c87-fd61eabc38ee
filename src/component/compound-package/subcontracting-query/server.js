import { sendPostRequest } from '../../../lib/public-request';

/**
 * 分包上架-多维度查询
 * @param {object} param
 * @returns
 */
// eslint-disable-next-line import/prefer-default-export
export const multiTypeQueryAPI = (param) => sendPostRequest({
  url: '/split_up/multi_type_query',
  param,
}, process.env.WPOC_URI);

/**
 *  扫描容器号
 * @param {object} param
 * @returns
 */
export const scanContainerCodeAPI = (param) => sendPostRequest({
  url: '/inventory/container/query',
  param,
}, process.env.BASE_URI_WMD);

/**
 * 库内查询-播种容器号明细查询
 * @param {object} param
 * @returns
 */
export const getDetailListAPI = (param) => sendPostRequest({
  url: '/inventory/seed_container_detail/query',
  param,
}, process.env.WOS_URI);

/**
 *  扫描商品条码
 * @param {object} param
 * @returns
 */
export const scanGoodsSnAPI = (param) => sendPostRequest({
  url: '/inventory/get_batch_container_info_scan',
  param,
});
