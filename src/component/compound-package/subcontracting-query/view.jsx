import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { CellsTitle } from 'react-weui/build/packages/components/cell';
import store from './reducers';
import { Header, Footer, pages } from '../../common';
import Entry from './jsx/entry';
import DichotomyStationPage from './jsx/info-show-page';
import DetailsPage from './jsx/details-page';
import styles from './style.css';

const { View } = pages;
class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      showDetail,
      isQueried,
      initLoading,
    } = this.props;
    return (
      <View initLoading={initLoading}>
        {
          showDetail ? (
            <div>
              <Header title={t('分包上架查询-二分箱明细')} />
              <DetailsPage {...this.props} />
              <Footer
                beforeBack={() => {
                  store.changeData({
                    showDetail: false,
                    showPopSheet: false,
                  });
                }}
              />
            </div>
          )
            : (
              <div>
                <Header title={headerTitle} />
                <Entry {...this.props} />
                <CellsTitle className={styles.infoStyle}>{t('信息')}</CellsTitle>
                {isQueried && <DichotomyStationPage {...this.props} />}
                <Footer />
              </div>
            )
         }
      </View>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  totalContainerNum: PropTypes.number,
  totalPalletNum: PropTypes.number,
  showDetail: PropTypes.bool,
  isQueried: PropTypes.bool,
  initLoading: PropTypes.bool,
};

export default i18n(Container);
