import React from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import SpScan from './jsx/sp-scan';

class Container extends React.Component {
  componentDidMount() {
    // store.init();
  }

  render() {
    const {
      type,
    } = this.props;
    return (
      <div>
        <SpScan {...this.props} />
      </div>
    );
  }
}

Container.propTypes = {
  type: PropTypes.number.isRequired,
};

export default i18n(Container);
