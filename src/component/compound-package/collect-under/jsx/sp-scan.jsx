import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form, Button } from 'react-weui/build/packages';
import Icon from '@shein-components/Icon';
import { push } from 'react-router-redux';
import { t } from '@shein-bbl/react';
import style from '../../../style.css';
import store, { getRecommendWayName } from '../reducers';
import FooterBtn from '../../../common/footer-btn';
import Footer from '../../../common/footer';
import Header from '../../../common/header';
import FocusInput from '../../../common/focus-input';
import { classFocus } from '../../../../lib/util';
import privateStyle from '../style.css';
import RowInfo from '../../../common/row-info';
import ChangeLocationModal from './modal-change-location';

class SpScan extends Component {
  componentWillMount() {
    store.init();
  }

  render() {
    const {
      transferContainerCode,
      scanBoxNoRsp,
      dataLoading,
      headerTitle,
      recommendWays,
      canSplitLocationNum,
      recommendLocation,
    } = this.props;
    return (
      <div className={style.flexColContainer}>
        <Header title={headerTitle || t('集货下架')} />
        <Form className={privateStyle.scanBoxNoRspWrap}>
          <div style={{ marginLeft: '15px', marginRight: '15px' }}>
            <div className={privateStyle.coBatch}>{t('协作批次')}</div>
            <div className={privateStyle.coBatchNo}>{scanBoxNoRsp.coBatchCode}</div>
          </div>
          <RowInfo
            data={[
              {
                label: t('总数'),
                content: parseInt(scanBoxNoRsp.fillBoxTotalNum, 10),
                type: 'warn',
              },
              {
                label: t('已扫描'),
                content: scanBoxNoRsp.hasScanNumber,
              },
            ]}
          />
        </Form>
        <Form>
          <FocusInput
            data-bind="transferContainerCode"
            className="transferContainerCode"
            disabled={dataLoading === 0}
            onPressEnter={() => {
              if (!transferContainerCode) {
                return;
              }
              store.firstCheckBoxNumber({
                param: {
                  transferContainerCode,
                  location: recommendLocation,
                },
              });
            }}
          >
            <label>{t('箱号')}</label>
          </FocusInput>
        </Form>
        <div style={{ padding: 10 }}>
          <div className={privateStyle.infoBox}>
            <span>{t('推荐方式')}：</span>
            <span style={{ fontSize: 18 }}>{getRecommendWayName(recommendWays)}</span>
            &nbsp;
            <Icon
              name="switch"
              style={{ color: '#0059CE' }}
              onClick={() => {
                const temp = recommendWays === 1 ? 2 : 1;
                store.changeData({ data: { recommendWays: temp } });
                store.getInfoByRecommendWays({ params: { recommendWays: temp } });
              }}
            />
          </div>
          <div className={privateStyle.infoBox}>
            {t('可分库位')}
            <span style={{ color: '#F85555' }}>&nbsp;{canSplitLocationNum}个&nbsp;</span>
          </div>
          <div className={privateStyle.locationBox}>
            <div className={privateStyle.bigRed}>
              {recommendLocation}
            </div>
            {recommendWays === 2 && canSplitLocationNum > 1 && (
              <div style={{ display: 'inline-flex' }}>
                <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    store.changeData({
                      data: {
                        modalVisible: true,
                        changeLocation: '',
                        locationNotExist: false,
                      },
                    });
                    classFocus('changeLocation');
                  }}
                >
                  {t('更换')}
                </Button>
              </div>
            )}
          </div>
        </div>
        <Footer
          beforeBack={(back) => {
            store.reset();
            back();
          }}
        >
        </Footer>
        <ChangeLocationModal {...this.props} />
      </div>
    );
  }
}

SpScan.propTypes = {
  dispatch: PropTypes.func.isRequired,
  transferContainerCode: PropTypes.string,
  scanBoxNoRsp: PropTypes.shape(),
  dataLoading: PropTypes.number.isRequired,
  headerTitle: PropTypes.string.isRequired,
  recommendWays: PropTypes.number.isRequired,
  canSpitLocationNum: PropTypes.number,
  recommendLocation: PropTypes.string,
  modalVisible: PropTypes.bool,
};
export default SpScan;
