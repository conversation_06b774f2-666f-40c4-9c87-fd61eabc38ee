import React from 'react';
import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { select } from 'redux-saga/effects';
import { markStatus } from 'rrc-loader-helper';
import { classFocus, getHeaderTitle } from 'lib/util';
import {
  initScanBoxNoApi, replaceLocationA<PERSON>,
  firstCheckBoxNumberApi,
  secondCheckBoxNumberApi,
  thirdCheckBoxNumberApi,
  getLocationApi,
} from './server';
import Modal from '../../common/modal';
import message from '../../common/message';

const defaultRecommendWay = 1;

// 根据code获取对应的名称
export const getRecommendWayName = (value) => {
  const list = [{
    value: 1,
    name: t('最早优先'),
  }, {
    value: 2,
    name: t('最近优先'),
  }];
  const item = list.find((i) => i.value === value);
  return item ? item.name : '';
};

export const defaultState = {
  dataLoading: 1,
  type: 0, // 0 默认页面 1 确认页面
  boxNumber: '', // 转运箱号
  packageNumber: '',
  isCommit: 1, // 是否提交
  scanBoxNoRsp: {
    coBatchCode: '', // 协作批次号
    fillBoxTotalNum: 0,
    hasScanNumber: 0,
    finish: false,
    canSplitLocationNum: 0, // 可分库位
    recommendLocation: '', // 推荐的可分库位
  },
  coBatchNo: '', // 协作批次号
  transferContainerCodes: [], // 确认页面的转运箱号列表
  selectedTransferContainerCode: '', // 确认页面勾选的转运箱号
  showSelect: false,
  headerTitle: '',
  recommendWays: defaultRecommendWay, // 推荐方式, 1-最早优先 2-最近优先
  modalVisible: false,
  locationNotExist: false,
  canSplitLocationNum: 0, // 可分库位数 换库位和初始化的该字段少个l
  recommendLocation: '', // 推荐库位
};

export default {
  defaultState,
  $init(draft) {
    assign(draft, defaultState);
    classFocus('transferContainerCode');
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  reset(draft) {
    assign(draft, defaultState);
  },
  * getInfoByRecommendWays(action, ctx) {
    const res = yield initScanBoxNoApi(action.params);
    if (res.code === '0') {
      const { canSpitLocationNum, location } = res.info;
      yield ctx.changeData({
        data: {
          canSplitLocationNum: canSpitLocationNum,
          recommendLocation: location,
        },
      });
    } else {
      console.log(res.msg);
    }
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    // 页面初始化根据推荐方式获取信息
    yield ctx.getInfoByRecommendWays({ params: { recommendWays: defaultRecommendWay } });
  },
  // 最初扫描箱号
  * firstCheckBoxNumber(action, ctx) {
    markStatus('dataLoading');
    const {
      code,
      msg,
      info,
    } = yield firstCheckBoxNumberApi(action.param);
    // const {
    //   code,
    //   msg,
    //   info,
    // } = {
    //   code: '0',
    //   info: {
    //     coBatchCode: 'ABC11111', // 协作批次号
    //     fillBoxTotalNum: 10,
    //     hasScanNumber: 6,
    //     finish: false,
    //     canSplitLocationNum: 0, // 可分库位
    //     recommendLocation: 'HAHAHA', // 推荐的可分库位
    //     needConfirm: true,
    //   }
    // };
    if (code === '0') {
      const {
        needConfirm,
        recommendLocation,
        fillBoxTotalNum,
      } = info;
      yield ctx.changeData({
        data: {
          transferContainerCode: '',
          scanBoxNoRsp: info,
          canSplitLocationNum: info.canSplitLocationNum, // 可分库位
          recommendLocation: info.recommendLocation, // 推荐的可分库位
        },
      });
      if (needConfirm) {
        if (info.canSplitLocationNum === 0) {
          const status = yield new Promise((r) => Modal.confirm({
            content: (
              <div>
                <div>{t('你上一批次未集货下架完成，是否确认开始新的批次')}</div>
                {info.recommendLocation && <div style={{ textAlign: 'center' }}>{info.recommendLocation}</div>}
              </div>
            ),
            onOk: () => (r('ok')),
            onCancel: () => (r('cancel')),
          }));
          if (status === 'ok') {
            yield ctx.thirdCheckBoxNumber({
              param: action.param,
            });
          }
          if (status === 'cancel') {
            const { recommendWays } = yield select((v) => v['compound-package/collect-under']);
            // 获取推荐库位
            yield ctx.getLocation({
              param: {
                location: action.param.location,
                recommendWays,
                boxCode: action.param.transferContainerCode,
              },
            });
            yield ctx.changeData({
              data: {
                transferContainerCode: '', // 清空转运箱号
                transferContainerCodeTmp: '',
              },
            });
            classFocus('transferContainerCode');
          }
          return;
        }
        const status = yield new Promise((r) => Modal.confirm({
          content: `${t('库位')}${recommendLocation}${t('共')}${fillBoxTotalNum}${t('箱')},${t('请确定库位上货物数量是否正确')}`,
          onOk: () => (r('ok')),
          onCancel: () => (r('cancel')),
        }));
        if (status === 'ok') {
          yield ctx.secondCheckBoxNumber({
            param: action.param,
          });
        }
        if (status === 'cancel') {
          classFocus('transferContainerCode');
          yield ctx.changeData({
            data: {
              transferContainerCode: '',
              transferContainerCodeTmp: '',
            },
          });
        }
      } else {
        const { recommendWays } = yield select((v) => v['compound-package/collect-under']);
        // 获取推荐库位
        yield ctx.getLocation({
          param: {
            location: action.param.location,
            recommendWays,
            boxCode: action.param.transferContainerCode,
          },
        });
        if (info.finish) {
          Modal.info({
            content: t('该协作批次已集货下架完成'),
            className: 'transferContainerCode',
          });
        }
        message.success(t('操作成功'));
        classFocus('transferContainerCode');
        // yield ctx.secondCheckBoxNumber({
        //   param: action.param,
        // });
      }
    } else {
      Modal.error({
        content: msg,
        onOk: () => classFocus('transferContainerCode'),
      });
      yield ctx.changeData({
        data: {
          transferContainerCode: '',
          transferContainerCodeTmp: '',
        },
      });
    }
  },
  // 再次扫描箱号
  * secondCheckBoxNumber(action, ctx) {
    const {
      code,
      msg,
      info,
    } = yield secondCheckBoxNumberApi(action.param);
    if (code === '0') {
      yield ctx.changeData({
        data: {
          transferContainerCode: '',
          scanBoxNoRsp: info,
          canSplitLocationNum: info.canSplitLocationNum, // 可分库位
          recommendLocation: info.recommendLocation, // 推荐的可分库位
        },
      });
      const { coBatchCode, needConfirm } = info;
      const { recommendWays } = yield select((v) => v['compound-package/collect-under']);
      if (needConfirm) {
        const status = yield new Promise((r) => Modal.confirm({
          content: (
            <div>
              <div>{t('你上一批次未集货下架完成，是否确认开始新的批次')}</div>
              {coBatchCode && <div style={{ textAlign: 'center' }}>{coBatchCode}</div>}
            </div>
          ),
          onOk: () => (r('ok')),
          onCancel: () => (r('cancel')),
        }));
        if (status === 'ok') {
          yield ctx.thirdCheckBoxNumber({
            param: action.param,
          });
        }
        if (status === 'cancel') {
          // 获取推荐库位
          yield ctx.getLocation({
            param: {
              location: action.param.location,
              recommendWays,
              boxCode: action.param.transferContainerCode,
            },
          });
          yield ctx.changeData({
            data: {
              transferContainerCode: '', // 清空转运箱号
              transferContainerCodeTmp: '',
            },
          });
          classFocus('transferContainerCode');
        }
      } else {
        // 获取推荐库位
        yield ctx.getLocation({
          param: {
            location: action.param.location,
            recommendWays,
            boxCode: action.param.transferContainerCode,
          },
        });
        if (info.finish) {
          Modal.info({
            content: t('该协作批次已集货下架完成'),
            className: 'transferContainerCode',
          });
        }
        message.success(t('操作成功'));
        classFocus('transferContainerCode');
        // yield ctx.thirdCheckBoxNumber({
        //   param: action.param,
        // });
      }
    } else {
      Modal.error({
        content: msg,
        onOk: () => classFocus('transferContainerCode'),
      });
      yield ctx.changeData({
        data: {
          transferContainerCode: '',
          transferContainerCodeTmp: '',
        },
      });
    }
  },
  // 第三次扫描箱号
  * thirdCheckBoxNumber(action, ctx) {
    const {
      code,
      msg,
      info,
    } = yield thirdCheckBoxNumberApi(action.param);
    if (code === '0') {
      yield ctx.changeData({
        data: {
          transferContainerCode: '',
          scanBoxNoRsp: info,
          canSplitLocationNum: info.canSplitLocationNum, // 可分库位
          recommendLocation: info.recommendLocation, // 推荐的可分库位
        },
      });
      message.success(t('操作成功'));
      classFocus('transferContainerCode');
      yield ctx.changeData({
        data: {
          transferContainerCodeTmp: action.param.transferContainerCode,
        },
      });
      const { recommendWays } = yield select((v) => v['compound-package/collect-under']);
      // 获取推荐库位
      yield ctx.getLocation({
        param: {
          location: action.param.location,
          recommendWays,
          boxCode: action.param.transferContainerCode,
        },
      });
      if (info.finish) {
        Modal.info({
          content: t('该协作批次已集货下架完成'),
        });
      }
    } else {
      Modal.error({
        content: msg,
        onOk: () => classFocus('transferContainerCode'),
      });
      yield ctx.changeData({
        data: {
          transferContainerCode: '',
          transferContainerCodeTmp: '',
        },
      });
    }
  },
  // 更换库位
  * replaceLocation(action, ctx) {
    const res = yield replaceLocationApi(action.params);
    if (res.code === '0') {
      const { locationNotExist, location, canSpitLocationNum } = res.info;
      if (!locationNotExist) {
        yield ctx.changeData({
          data: {
            recommendLocation: location,
            canSplitLocationNum: canSpitLocationNum,
            locationNotExist: false,
            modalVisible: false,
          },
        });
        classFocus('transferContainerCode');
      } else {
        // 合包库位不存在
        yield ctx.changeData({
          data: {
            locationNotExist: true,
            changeLocation: '',
          },
        });
      }
    } else {
      Modal.error({
        content: res.msg,
        className: 'changeLocation',
      });
      yield ctx.changeData({ data: { changeLocation: '' } });
    }
  },
  // 获取推荐库位
  * getLocation(action, ctx) {
    const res = yield getLocationApi(action.param);
    if (res.code === '0') {
      const { canSpitLocationNum, location } = res.info;
      // 设置推荐库位
      yield ctx.changeData({
        data: {
          canSplitLocationNum: canSpitLocationNum, // 细微差别
          recommendLocation: location,
        },
      });
    } else {
      console.log(res.msg);
    }
  },
};
