import { sendPostRequest } from '../../../lib/public-request';

// 集货下架-初始化数据
export const initScanBoxNoApi = param => sendPostRequest({
  url: '/collect_under/init_scan_transfer_container_code',
  param,
}, process.env.WPOC_URI);

// 集货下架-扫描箱号步骤一
export const firstCheckBoxNumberApi = param => sendPostRequest({
  url: '/collect_under/scan_transfer_container_code_step_one',
  param,
}, process.env.WPOC_URI);

// 集货下架-扫描箱号步骤二
export const secondCheckBoxNumberApi = param => sendPostRequest({
  url: '/collect_under/scan_transfer_container_code_step_two',
  param,
}, process.env.WPOC_URI);

// 集货下架-扫描箱号步骤三
export const thirdCheckBoxNumberApi = param => sendPostRequest({
  url: '/collect_under/scan_transfer_container_code_step_three',
  param,
}, process.env.WPOC_URI);

// 集货下架-更换库位
export const replaceLocationApi = param => sendPostRequest({
  url: '/combine_package_scanning/replace_location',
  param,
}, process.env.WPOC_URI);

// 集货下架-获取推荐库位
export const getLocationApi = param => sendPostRequest({
  url: '/collect_under/get_location',
  param,
}, process.env.WPOC_URI);
