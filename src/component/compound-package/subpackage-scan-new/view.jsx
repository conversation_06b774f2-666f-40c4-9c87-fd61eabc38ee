import React from 'react';
import PropTypes from 'prop-types';
import { i18n } from '@shein-bbl/react';
import SpScan from './jsx/sp-scan';
import Details from './jsx/details';

class Container extends React.Component {
  componentDidMount() {
    // store.init();
  }

  render() {
    const {
      showDetails,
    } = this.props;
    return (
      <div>
        <SpScan {...this.props} />
        {
          showDetails && <Details {...this.props} />
        }
      </div>
    );
  }
}

Container.propTypes = {
  showDetails: PropTypes.bool.isRequired,

};

export default i18n(Container);
