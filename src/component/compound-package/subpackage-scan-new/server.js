import { sendPostRequest } from '../../../lib/public-request';

// 分包扫描-箱号
export const scanTransferContainerCodeApi = (param) => sendPostRequest({
  url: '/subcontract_scan/subcontract_scan_box_no',
  param,
}, process.env.WPOC_URI);

// 分包扫描-扫描包裹号
export const scanPackageNoApi = (param) => sendPostRequest({
  url: '/subcontract_scan/subcontract_scan_package_no',
  param,

}, process.env.WPOC_URI);

// 分包扫描-空箱操作
export const emptyBoxApi = (param) => sendPostRequest({
  url: '/combine_package_scanning/empty_box',
  param,

}, process.env.WPOC_URI);

// 分包扫描-空箱操作确认
export const confirmEmptyBoxApi = (param) => sendPostRequest({
  url: '/combine_package_scanning/empty_box',
  param,

}, process.env.WPOC_URI);

// 获取明细
export const getDetailsApi = (param) => sendPostRequest({
  url: '/subcontract_scan/list_split_detail',
  param,

}, process.env.WPOC_URI);
