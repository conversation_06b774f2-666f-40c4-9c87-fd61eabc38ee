import React from 'react';
import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { classFocus, getHeaderTitle } from 'lib/util';
import { markStatus } from 'rrc-loader-helper';
import {
  scanTransferContainer<PERSON>odeApi, scanPackageNoApi, emptyBoxApi, confirmEmptyBoxApi, getDetailsApi,
} from './server';
import Modal from '../../common/modal';
import { showToast } from '../../common/common';
import tipAudio from '../../../source/audio/delete.mp3';
import dingdong from '../../../source/audio/dingdong.mp3';
import { subpackageScanEmptyPackage } from '../subpackage-scan/server';
import message from '../../common/message';
import privateStyle from './style.css';

const audio = new Audio(tipAudio);
audio.load();
const dingdongEle = new Audio(dingdong);
dingdongEle.load();

export const defaultState = {
  headerTitle: '',
  dataLoading: 1,
  isCommit: 1, // 是否提交
  transferContainerCode: '', // 转运箱号
  coBatchNo: '', // 协作批次号
  batchPackageNum: '', // 批次包裹数
  hasScanPackageNum: 0, // 已扫描包裹数
  unScanPackageNum: 0, // 未扫描包裹数
  fillBoxNum: 0, // 转运箱总数
  hasScanBoxNum: 0, // 已扫描箱数
  transferContainerCodeDisabled: false,
  transferContainerCodeScaned: false,
  packageNo: '', // 包裹号
  packageNoTmp: '', // 临时保存包裹号 相当于扫包裹号成功后返回的
  boxFinish: false, // 该箱是否分包结束
  coBatchFinish: false, // 批次是否分包结束
  coBatchSequence: [], // 协作的包裹所在批次序号
  coBatchCancelSequence: [], // 协作批次下取消的合包包裹号
  sequence: '', // 合包包裹号对应的序号
  showDetails: false,
  containerCode: '',
  hasScanPackages: [],
  unScanPackages: [],
  disabledEmptyBoxBtn: true,
  isOnBlur: false,
};

export default {
  state: defaultState,
  $init(draft) {
    assign(draft, defaultState);
    classFocus('transferContainerCode');
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  // 扫描转运箱
  * scanTransferContainerCode(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanTransferContainerCodeApi(action.param);
    const {
      coBatchNo, fillBoxNum, hasScanBoxNum, hasScanPackageNum, unScanPackageNum, batchPackageNum,
    } = res.info || {};
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          coBatchNo,
          batchPackageNum,
          fillBoxNum,
          hasScanBoxNum,
          hasScanPackageNum,
          unScanPackageNum,
          transferContainerCodeDisabled: true,
          transferContainerCodeScaned: true,
          coBatchSequence: [],
          coBatchCancelSequence: [],
          packageNoTmp: '',
          coBatchFinish: false,
          disabledEmptyBoxBtn: false,
        },
      });
      classFocus('packageNo');
    } else {
      audio.play();
      yield ctx.changeData({
        data: {
          transferContainerCode: '',
        },
      });
      Modal.error({
        content: res.msg,
        className: 'transferContainerCode',
      });
    }
  },
  // 扫描包裹号
  * scanPackageNo(action, ctx) {
    markStatus('dataLoading');
    const res = yield scanPackageNoApi(action.param);
    const { packageNo } = action.param;
    if (res.code === '0') {
      const {
        boxFinish, // 该箱是否分包结束
        coBatchFinish, // 批次是否分包结束
        coBatchNo, // 协作批次号
        coBatchSequence, // 异常非取消包裹序列
        coBatchCancelSequence, // 取消包裹序列
        hasScanPackageNum, // 已扫描包裹数
        unScanPackageNum, // 未扫描包裹数
        sequence, // 合包包裹号对应的序号
        alertType, // 弹框类型
      } = res.info;
      yield ctx.changeData({
        data: {
          boxFinish,
          coBatchFinish,
          coBatchNo,
          coBatchSequence,
          coBatchCancelSequence,
          hasScanPackageNum,
          unScanPackageNum,
          sequence,
          packageNoTmp: action.param.packageNo,
        },
      });
      // 取消弹框
      if (alertType === 2) {
        dingdongEle.play();
        // 阻断操作，输入框不可输
        yield ctx.changeData({
          data: {
            isOnBlur: true,
          },
        });
        const status = yield new Promise((r) => Modal.info({
          content: (
            <div style={{ position: 'relative' }}>
              <span
                style={{
                  color: 'red',
                }}
              >
                {`${t('取消')}：${packageNo}`}
              </span>
              <div className={privateStyle.boldNum} style={{ color: 'red' }}>
                {sequence}
              </div>
            </div>
          ),
          onOk: () => r(1),
        }));
        if (status === 1) {
          yield this.changeData({
            data: {
              isOnBlur: false,
            },
          });
        }
      } else if (alertType === 3) {
        dingdongEle.play();
        // 阻断操作，输入框不可输
        yield ctx.changeData({
          data: {
            isOnBlur: true,
          },
        });
        // 异常弹框
        const status = yield new Promise((r) => Modal.info({
          content: (
            <div style={{ position: 'relative' }}>
              <span>
                {`${t('异常')}：${packageNo}`}
              </span>
              <div className={privateStyle.boldNum}>
                {sequence}
              </div>
            </div>
          ),
          onOk: () => r(1),
        }));
        if (status === 1) {
          yield this.changeData({
            data: {
              isOnBlur: false,
            },
          });
        }
      }
      if (boxFinish) {
        message.success(t('该箱分包完成'));
        yield ctx.changeData({
          data: {
            transferContainerCode: '',
            packageNo: '',
            transferContainerCodeDisabled: false,
            transferContainerCodeScaned: false,
            hasScanBoxNum: action.hasScanBoxNum + 1,
            disabledEmptyBoxBtn: true,
          },
        });
        classFocus('transferContainerCode');
      } else {
        message.success(t('操作成功'));
        yield ctx.changeData({
          data: {
            packageNo: '',
          },
        });
        classFocus('packageNo');
      }
      if (coBatchFinish) {
        Modal.info({
          content: t('批次已完成分包，协作批次为') + coBatchNo,
        });
      }
    } else {
      audio.play();
      Modal.error({
        content: res.msg,
        className: 'packageNo',
      });
      yield ctx.changeData({ data: { packageNo: '' } });
    }
  },
  // 箱空操作
  * emptyBox(action, ctx) {
    const res = yield emptyBoxApi(assign({
      isCommit: '2',
    }, action.param));
    if (res.code === '0') {
      const status = yield new Promise((r) => Modal.confirm({
        content: (<div>
          <div style={{ textAlign: 'left' }}>{t('箱号还有')}{res.info.emptyPackageNos.length}{t('个包裹未分包，确定要清空箱？')}</div>
          {/* <div style={{ maxHeight: '120px', overflowY: 'scroll'}}> */}
          {/* {res.info.emptyPackageNos.map(v => ( */}
          {/* <div>{v}</div> */}
          {/* ))} */}
          {/* </div> */}
                  </div>),
        onOk: () => r(1),
        onCancel: () => r(2),
      }));
      if (status === 1) {
        yield ctx.confirmEmptyBox(action);
      }
      if (status === 2) {
        classFocus('packageNo');
      }
    } else {
      Modal.error({
        content: res.msg,
      });
    }
  },
  // 确认清空箱
  * confirmEmptyBox(action, ctx) {
    const {
      code,
      msg,
      info,
    } = yield confirmEmptyBoxApi(assign({
      isCommit: 1,
    }, action.param));
    if (code === '0') {
      const {
        result,
        coBatchCode,
        expSerialNos,
        cancelSerialNos,
        splitBatchUser,
      } = info;
      if (result === '4') {
        Modal.info({
          content: `${t('该协作批次已由{}分包', splitBatchUser)}`,
        });
        audio.play();
        return;
      }
      switch (result) {
        case '2': {
          Modal.info({
            content: `${t('批次已完成分包,协作批次为{}', coBatchCode)}`,
          });
          break;
        }
        case '3': {
          Modal.info({
            content: `${t('异常批次已完成分包，协作批次为{}', coBatchCode)}`,
          });
          break;
        }
        default: {
          message.success(t('该箱箱空完成'));
        }
      }
      // 更新协作批次及箱数
      yield ctx.changeData({
        data: {
          coBatchCode,
          coBatchFinish: (result === '2' || result === '3'),
          coBatchSequence: expSerialNos, // 异常合包包裹号对应的序号
          coBatchCancelSequence: cancelSerialNos, // 取消合包包裹号对应的序号
          hasScanBoxNum: action.hasScanBoxNum + 1,
        },
      });
      // 清空数据
      yield ctx.changeData({
        data: {
          packageNo: '',
          packageNoTmp: '',
          transferContainerCode: '',
          transferContainerCodeScaned: false,
          transferContainerCodeDisabled: false,
          disabledEmptyBoxBtn: true,
        },
      });
      classFocus('transferContainerCode');
    } else {
      Modal.error({
        content: msg,
      });
    }
  },
  // 获取明细
  * getDetails(action, ctx) {
    const res = yield getDetailsApi(action.param);
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          showDetails: true,
          containerCode: res.info.containerCode,
          hasScanPackages: res.info.hasScanPackages || [],
          unScanPackages: res.info.unScanPackages || [],
        },
      });
    } else {
      Modal.error({
        content: res.msg,
      });
    }
  },
};
