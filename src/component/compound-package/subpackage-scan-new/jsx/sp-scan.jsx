import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form, Button } from 'react-weui/build/packages';
import Icon from '@shein-components/Icon';
import { push } from 'react-router-redux';
import { t } from '@shein-bbl/react';
import style from '../../../style.css';
import store, { getRecommendWayName, getDetails } from '../reducers';
import FooterBtn from '../../../common/footer-btn';
import Footer from '../../../common/footer';
import Header from '../../../common/header';
import FocusInput from '../../../common/focus-input';
import { classFocus } from '../../../../lib/util';
import privateStyle from '../style.css';
import RowInfo from '../../../common/row-info';
import Modal from '../../../common/modal';

class SpScan extends Component {
  componentWillMount() {
    store.init();
  }

  render() {
    const {
      dataLoading,
      headerTitle,
      coBatchNo,
      batchPackageNum,
      hasScanPackageNum,
      unScanPackageNum,
      packageNo,
      transferContainerCode,
      transferContainerCodeDisabled,
      transferContainerCodeScaned,
      fillBoxNum,
      hasScanBoxNum,
      coBatchFinish,
      packageNoTmp,
      coBatchSequence,
      coBatchCancelSequence,
      sequence,
      disabledEmptyBoxBtn,
      isOnBlur,
    } = this.props;
    return (
      <div
        className={style.flexColContainer}
        style={this.props.showDetails ? { display: 'none' } : { display: 'block' }}
      >
        <Header title={headerTitle || t('分包扫描')} />
        <Form className={privateStyle.scanBoxNoRspWrap}>
          <div className={privateStyle.coBatchAndBatchPkg}>
            <div className={privateStyle.flexItem}>
              <div className={privateStyle.coBatch}>{t('协作批次')}</div>
              <div className={privateStyle.coBatchNo}>{coBatchNo}</div>
            </div>
            <div className={privateStyle.flexItem}>
              <div className={privateStyle.batchPkg}>{t('批次包裹数')}</div>
              <div className={privateStyle.batchPkgNum}>{batchPackageNum}</div>
            </div>
          </div>
          <RowInfo
            data={[
              {
                label: t('总数'),
                content:
                  parseInt(fillBoxNum, 10),
                type: 'warn',
              },
              {
                label: t('已扫描'),
                content: hasScanBoxNum,
              },
            ]}
          />
        </Form>
        {packageNoTmp !== '' ? (
          <div style={{
            padding: '10px 15px 0 15px',
            display: 'flex',
            background: '#c3dcfd',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
          >
            <div style={{ textAlign: 'left', fontSize: '14px' }}>
              <div>{t('包裹序号')}</div>
              <div>{packageNoTmp}</div>
            </div>
            <div style={{
              textAlign: 'center', fontSize: '36px', fontWeight: 'bold', color: 'red',
            }}
            >{sequence}
            </div>
            <div />
          </div>
        ) : ''}
        <Form>
          <FocusInput
            autoFocus
            data-bind="transferContainerCode"
            className="transferContainerCode"
            disabled={dataLoading === 0 || transferContainerCodeDisabled || isOnBlur}
            onPressEnter={() => {
              if (!transferContainerCode) {
                return;
              }
              store.scanTransferContainerCode({
                param: {
                  transferContainerCode,
                },
              });
            }}
          >
            <label>{t('箱号')} <Icon
              onClick={() => {
                store.getDetails({
                  param: {
                    transferContainerCode,
                  },
                });
              }} name="chaxunyewu" style={{ color: '#197afa', marginLeft: '5px' }}
            />
            </label>
          </FocusInput>
        </Form>
        <Form>
          <FocusInput
            data-bind="packageNo"
            className="packageNo"
            disabled={!transferContainerCodeScaned || isOnBlur || !dataLoading}
            onPressEnter={() => {
              if (!packageNo) {
                return;
              }
              store.scanPackageNo({
                param: {
                  packageNo,
                  transferContainerCode,
                },
                hasScanBoxNum,
              });
            }}
          >
            <label>{t('包裹号')}</label>
          </FocusInput>
        </Form>
        {transferContainerCodeScaned ? (
          <RowInfo
            label={t('扫描/待扫描')}
            content={(
              <div>{parseInt(hasScanPackageNum, 10)}/
                <span
                  style={{ color: 'red' }}
                >
                  {parseInt(unScanPackageNum, 10)}
                </span>
              </div>
)}
            type="info"
          />
        ) : ''}
        {coBatchFinish ? (
          <div style={{
            fontSize: '21px', color: 'red', fontWeight: 'bold', padding: '2px 10px',
          }}
          >
            { coBatchSequence?.length > 0 ? (
              <div>
                <div>{t('异常')}:</div>
                {coBatchSequence.join('、')}
              </div>
            ) : '' }
            { coBatchCancelSequence.length > 0 ? (
              <div>
                <div>{t('取消')}:</div>
                {coBatchCancelSequence.join('、')}
              </div>
            ) : ''}
          </div>
        ) : ''}
        <Footer>
          <FooterBtn
            disabled={!transferContainerCode || disabledEmptyBoxBtn}
            onClick={() => {
              store.emptyBox({
                param: {
                  transferContainerCode,
                },
                hasScanBoxNum,
              });
            }}
          >
            {t('箱空')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

SpScan.propTypes = {
  // dispatch: PropTypes.func.isRequired,
  // boxNumber: PropTypes.string.isRequired,
  // scanBoxNoRsp: PropTypes.shape(),
  coBatchNo: PropTypes.string,
  batchPackageNum: PropTypes.string,
  hasScanPackageNum: PropTypes.number,
  unScanPackageNum: PropTypes.number,
  packageNo: PropTypes.string,
  transferContainerCode: PropTypes.string,
  transferContainerCodeDisabled: PropTypes.bool,
  transferContainerCodeScaned: PropTypes.bool,
  fillBoxNum: PropTypes.number,
  hasScanBoxNum: PropTypes.number,
  coBatchFinish: PropTypes.bool,
  isOnBlur: PropTypes.bool,
  packageNoTmp: PropTypes.string,
  disabledEmptyBoxBtn: PropTypes.bool,
  dataLoading: PropTypes.number.isRequired,
  headerTitle: PropTypes.string.isRequired,
  // recommendWays: PropTypes.number.isRequired,
  // canSpitLocationNum: PropTypes.number.isRequired,
  // recommendLocation: PropTypes.string,
  // modalVisible: PropTypes.bool,
  coBatchSequence: PropTypes.arrayOf(PropTypes.number),
  coBatchCancelSequence: PropTypes.arrayOf(PropTypes.number),
  sequence: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
};
export default SpScan;
