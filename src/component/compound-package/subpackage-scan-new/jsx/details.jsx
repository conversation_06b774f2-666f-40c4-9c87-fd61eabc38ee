import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Footer from '../../../common/footer';
import pages from '../../../common/pages';
import FocusInput from '../../../common/focus-input';
import { Form } from './sp-scan';
import RowInfo from '../../../common/row-info';
import Header from '../../../common/header';
import Table from '../../../common/table';
import store from '../reducers';
import { classFocus } from '../../../../lib/util';

const { View } = pages;
class Details extends Component {
  componentWillMount() {
  }

  render() {
    const columns = [
      {
        title: t('序号'),
        dataIndex: 'serialNo',
        width: '15',
      },
      {
        title: t('包裹号'),
        dataIndex: 'packageNo',
        width: '70',
      },
      {
        title: t('状态'),
        dataIndex: 'status',
        render: (row) => (
          row.status === 1 ? t('已扫描') : t('未扫描')
        ),
        width: '15',
      },
    ];
    const {
      containerCode,
      hasScanPackages,
      unScanPackages,
      packageInfo,
      transferContainerCode,
      packageNo,
    } = this.props;
    return (
      <div>
        <Header title={t('转运箱明细')} />
        <View flex={false} diff={110}>
          <FocusInput
            value={containerCode}
            allowClear
            autoFocus
            data-bind="containerCode"
            onPressEnter={(e) => {
              if (e.target.value) {
                store.getDetails({
                  param: {
                    transferContainerCode: e.target.value,
                  },
                });
              }
            }}
          >
            <label>{t('转运箱')}</label>
          </FocusInput>
          <RowInfo
            label={t('扫描/待扫描')}
            content={(
              <div>{hasScanPackages.length}/<span style={{ color: 'red' }}>{unScanPackages.length}</span></div>)}
            type="info"
          />
          <Table
            columns={columns}
            dataSource={unScanPackages.concat(hasScanPackages)}
          />
        </View>
        <Footer
          beforeBack={() => {
            store.changeData({
              data: {
                showDetails: false,
              },
            });
            if (transferContainerCode) {
              classFocus('packageNo');
            } else {
              classFocus('transferContainerCode');
            }
          }}
        />
      </div>
    );
  }
}

Details.propTypes = {
  containerCode: PropTypes.string,
  hasScanPackages: PropTypes.arrayOf(PropTypes.number),
  unScanPackages: PropTypes.arrayOf(PropTypes.number),
  packageInfo: PropTypes.shape(),
  packageNo: PropTypes.string,
  transferContainerCode: PropTypes.string,
};
export default Details;
