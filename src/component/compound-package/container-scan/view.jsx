import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Input, Picker, Form, Cell, CellHeader, CellBody, CellFooter, FormCell, Button, Label, CellsTitle,
} from 'react-weui/build/packages';
import { i18n, t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import store, { defaultState } from './reducers';
import Header from '../../common/header';
import Footer from '../../common/footer';
import FooterBtn from '../../common/footer-btn';
import FocusInput from '../../common/focus-input';
import Modal from '../../common/modal';
import {
  List,
  SplitBar,
} from '../../common';
import { classFocus } from '../../../lib/util';

import RowInfo from '../../common/row-info';
import styles from './style.css';

const trimLeft = (str) => {
  try {
    return str.replace(/^\s+/g, '');
  } catch (e) {
    return '';
  }
};

const rows = [
  [
    {
      title: '',
      render: 'collectedTime',
    },
    {
      title: '',
      render: 'boxNo',
    },
  ],
];

// 除推荐货区外空闲集货区信息
const areaRows = [
  [
    {
      title: '',
      render: (r) => (`${t('库区：')}${r.areaName || ''}`),
    },
    {
      title: '',
      render: (r) => `${t('空闲集货位数量：')}${r.freeCollectLocationNum}`,
    },
  ],
];

class Container extends Component {
  componentDidMount() {
    store.init();
    this.firstInputFocusEvent = () => {
      setTimeout(() => {
        classFocus('boxNumber');
      });
    };
    this.secondInputFocusEvent = () => {
      setTimeout(() => {
        classFocus('containerPosition');
      });
    };
  }

  render() {
    const {
      dataLoading,
      dispatch,
      boxNumber,
      containerPosition,
      recommendPosition,
      ableContainerPositionInput,
      collectLocationStatus,
      batchFirstPackage,
      combineGoodsType,
      headerTitle,
      fillBoxNum,
      hasCollectedNum,
      boxInfoList,
      disbaledBoxNumber,
      collectAreaInfoList,
      showArea,
    } = this.props;
    return (
      <div style={{ marginBottom: '56px' }}>
        <Header title={headerTitle || t('集箱扫描')} />
        <List
          header={(
            <div style={{ marginLeft: -15 }}>
              <Form style={{ position: 'relative' }}>
                <FocusInput
                  autoFocus
                  placeholder={t('请扫描')}
                  data-bind="boxNumber"
                  className="boxNumber"
                  disabled={disbaledBoxNumber || !dataLoading}
                  onPressEnter={() => {
                    store.checkBoxNumber({
                      data: {
                        param: {
                          box_no: boxNumber,
                        },
                        focusFn: this.secondInputFocusEvent,
                      },
                    });
                  }}
                >
                  <label>{t('大箱号')}</label>
                </FocusInput>
                <div className={styles.topRightNum}>
                  <span style={{ color: 'red' }}>{hasCollectedNum}</span>
                  <span>/{fillBoxNum}</span>
                </div>
              </Form>
              <Form>
                <FocusInput
                  placeholder={t('请扫描')}
                  disabled={!ableContainerPositionInput || !dataLoading}
                  data-bind="containerPosition"
                  keepFocus={false}
                  className="containerPosition"
                  onPressEnter={() => {
                    store.checkContainerPosition({
                      data: {
                        param: {
                          box_no: boxNumber,
                          scan_collect_location: containerPosition,
                          collect_location: recommendPosition,
                          collectLocationStatus,
                          confirmSure: false,
                          areaFlag: showArea,
                        },
                        focusFn: this.firstInputFocusEvent,
                      },
                    });
                  }}
                >
                  <label>{t('集货库位')}</label>
                </FocusInput>
              </Form>
              {
                showArea ? (
                  <SplitBar>
                    <div style={{ textAlign: 'left', color: 'red' }}>{t('当前集货区没有空置的集货货位，请寻找临近的集货区进行集箱')}</div>
                    <div style={{ textAlign: 'left', color: '#616161' }}>{t('库区空闲集货货位数量')}：</div>
                  </SplitBar>
                )
                  : (
                    <SplitBar>
                      { (boxNumber && ableContainerPositionInput) ? (
                        <div style={{ textAlign: 'center', color: 'red' }}>{(batchFirstPackage ? `${t('无上架记录，推荐上架至')}${recommendPosition}` : `${t('已有协作批次上架至')}${recommendPosition}`)}</div>
                      ) : '' }
                      <div style={{ textAlign: 'center' }}>{ combineGoodsType || ''}</div>
                    </SplitBar>
                  )
              }
            </div>
            )}
          data={showArea ? collectAreaInfoList : boxInfoList}
          rows={showArea ? areaRows : rows}
          rowStyleOrClass={(r) => {
            if (r.hasCollected) {
              return styles.hasScaned;
            }
            return styles.notScaned;
          }}
          style={{ height: 'calc(100vh - 110px)', overflowY: 'auto' }}
        />
        <Footer dispatch={dispatch} />
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  dataLoading: PropTypes.number.isRequired,
  boxNumber: PropTypes.string.isRequired,
  headerTitle: PropTypes.string.isRequired,
  collectAreaInfoList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  showArea: PropTypes.bool.isRequired,
};

export default i18n(Container);
