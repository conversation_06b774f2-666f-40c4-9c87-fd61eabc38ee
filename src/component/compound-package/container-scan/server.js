import { sendPostRequest } from '../../../lib/public-request';

/**
 * [集箱扫描-箱号]
 * <AUTHOR>
 * @DateTime 2018-11-23T14:53:31+0800
 * @param    {[type]}                 param [description]
 * @return   {[type]}                       [description]
 */
export const containerScanCheckBoxNumber = param => sendPostRequest({
  url: '/collectionbox/scan_box_no',
  param,
}, process.env.WPOC_URI);

/**
 * [集箱扫描-集货货位]
 * <AUTHOR>
 * @DateTime 2018-11-23T14:54:28+0800
 * @param    {[type]}                 param [description]
 * @return   {[type]}                       [description]
 */
export const containerScanCheckContainerPosition = param => sendPostRequest({
  url: '/collectionbox/scan_box_number',
  param,
}, process.env.WPOC_URI);
