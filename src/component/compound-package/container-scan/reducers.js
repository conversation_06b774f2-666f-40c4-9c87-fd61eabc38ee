import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import {
  containerScanCheckBoxNumber,
  containerScanCheckContainerPosition,
} from './server';
import tipAudio from '../../../source/audio/dingdong.mp3';

const audio = new Audio(tipAudio);
audio.load();

export const defaultState = {
  dataLoading: 1,
  boxNumber: '',
  containerPosition: '',
  recommendPosition: '',
  ableContainerPositionInput: false,
  collectLocationStatus: false,
  batchFirstPackage: false,
  combineGoodsType: '', // 订单类型
  headerTitle: '',
  fillBoxNum: 0,
  hasCollectedNum: 0,
  boxInfoList: [],
  disbaledBoxNumber: false,
  collectAreaInfoList: [], // 除推荐货区外空闲集货区信息
  showArea: false, // 显示 除推荐货区外空闲集货区信息
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  /**
   * [*checkBoxNumber 扫描箱号]
   * <AUTHOR>
   * @DateTime 2018-11-28T15:29:06+0800
   * @param    {[type]}                 options.value   [description]
   * @param    {[type]}                 options.focusFn [description]
   * @yield    {[type]}                 [description]
   */
  * checkBoxNumber(action, ctx, put) {
    markStatus('dataLoading');
    const {
      code,
      info,
      msg,
    } = yield containerScanCheckBoxNumber(action.data.param);

    if (code === '0') {
      yield ctx.changeData({
        data: {
          disbaledBoxNumber: true,
        },
      });
      const {
        status,
        collectLocation,
        batchCode,
        collectLocationStatus,
        batchFirstPackage, // 是否第一个协作批次包裹
        combineGoodsType,
        combineWareHouse,
        fillBoxNum,
        hasCollectedNum,
        boxInfoList,
        currentStatusName,
        collectAreaInfoList,
      } = info;
      switch (status) {
        case 3: {
          const status = yield new Promise((r) => Modal.confirm({
            title: `${t('该箱被强制集货，对应的协作批次号：{}；共{}箱', batchCode, fillBoxNum)}`,
            buttons: [{
              type: 'primary',
              label: t('确定'),
              onClick: () => (r('ok')),
            }],
          }));
          if (status === 'ok') {
            yield ctx.init();
            classFocus('boxNumber');
          }
          break;
        }
        case 4: {
          const status = yield new Promise((r) => Modal.error({
            title: `${combineWareHouse ? t('该箱状态不是已关箱或已称重或已收货，不允许操作集箱') : t('箱号的状态不是已收货，不允许操作集货')}`,
            onOk: () => (r('ok')),
          }));
          if (status === 'ok') {
            yield ctx.init();
            classFocus('boxNumber');
          }
          break;
        }
        case 5: {
          // 弹窗
          const status = yield new Promise((r) => Modal.error({
            title: `${t('没有空置的集货库位')}`,
            onOk: () => (r('ok')),
          }));
          if (status === 'ok') {
            yield ctx.init();
            classFocus('boxNumber');
          }
          break;
        }
        case 6: {
          // 弹窗
          Modal.info({
            title: `${currentStatusName}`,
            className: 'boxNumber',
          });
          yield ctx.init();
          break;
        }
        default: {
          yield ctx.changeData({
            data: {
              ableContainerPositionInput: true,
              recommendPosition: collectLocation,
              collectLocationStatus,
              batchFirstPackage,
              combineGoodsType,
              fillBoxNum,
              hasCollectedNum,
              boxInfoList: boxInfoList || [],
              collectAreaInfoList: collectAreaInfoList && collectAreaInfoList.length ? collectAreaInfoList : [],
              // 状态是8而且collectAreaInfoList里面不是空数组就显示【库区空闲集货货位】页面
              showArea: status === 8 && collectAreaInfoList && collectAreaInfoList.length > 0,
            },
          });
          action.data.focusFn();
        }
      }
    } else if (code === '500829') {
      const status = yield new Promise((r) => Modal.confirm({
        content: t('请把协作波次全部集货完成再进行协作批次集箱'),
        onOk: () => {
          window.location.hash = 'compound-package/wave-box';
        },
        onCancel: () => (r('cancel')),
      }));
      if (status === 'cancel') {
        yield ctx.changeData({
          data: {
            boxNumber: '',
            combineGoodsType: '',
            fillBoxNum: 0,
            hasCollectedNum: 0,
            boxInfoList: [],
            disbaledBoxNumber: false,
          },
        });
        classFocus('boxNumber');
      }
    } else if (code === '500840') {
      audio.play();
      yield ctx.changeData({
        data: {
          boxNumber: '',
          combineGoodsType: '',
          fillBoxNum: 0,
          hasCollectedNum: 0,
          boxInfoList: [],
          disbaledBoxNumber: false,
        },
      });
      classFocus('boxNumber');
      Modal.error({ content: t('补拣包裹的转运箱，请进行异常上架') });
    } else {
      const status = yield new Promise((r) => Modal.error({
        title: msg,
        onOk: () => (r('ok')),
      }));
      if (status === 'ok') {
        yield ctx.changeData({
          data: {
            boxNumber: '',
            combineGoodsType: '',
            fillBoxNum: 0,
            hasCollectedNum: 0,
            boxInfoList: [],
            disbaledBoxNumber: false,
          },
        });
        classFocus('boxNumber');
      }
    }
  },
  /**
   * [*checkContainerPosition 扫描集货货位]
   * <AUTHOR>
   * @DateTime 2018-11-28T15:29:19+0800
   * @param    {[type]}                 options.value [description]
   * @yield    {[type]}                 [description]
   */
  * checkContainerPosition(action, ctx, put) {
    markStatus('dataLoading');
    const {
      code,
      info,
      msg,
    } = yield containerScanCheckContainerPosition(action.data.param);
    if (code === '0') {
      const {
        status,
        boxInfoList,
        fillBoxNum,
        hasCollectedNum,
        coBatchCode, // 协作批次号
        boxNums,		// 转运箱数量
        needConfirm, // 是否需要弹窗，默认否
      } = info;
      if (needConfirm) {
        const status = yield new Promise((r) => Modal.success({
          title: t('该箱被强制集货，对应的协作批次号:{};共{}箱', coBatchCode, boxNums),
          onOk: () => (r('ok')),
        }));
        if (status === 'ok') {
          yield ctx.init();
        }
        return;
      }
      switch (status) {
        case 0: {
          const status = yield new Promise((r) => Modal.success({
            title: `${t('该批次已集箱完成,共{}箱', fillBoxNum)}`,
            onOk: () => (r('ok')),
          }));
          if (status === 'ok') {
            yield ctx.init();
            action.data.focusFn();
          }
          break;
        }
        case 1: {
          const status = yield new Promise((r) => Modal.success({
            title: `${t('操作成功')}`,
            onOk: () => (r('ok')),
          }));
          if (status === 'ok') {
            yield ctx.init();
            yield ctx.changeData({
              data: {
                fillBoxNum,
                hasCollectedNum,
                boxInfoList: boxInfoList || [],
              },
            });
            action.data.focusFn();
          }
          break;
        }
        case 2: {
          const status = yield new Promise((r) => Modal.error({
            title: `${t('请扫描正确的集货库位')}`,
            onOk: () => (r('ok')),
          }));
          if (status === 'ok') {
            yield ctx.changeData({
              data: {
                containerPosition: '',
              },
            });
            classFocus('containerPosition');
          }
          break;
        }
        case 7: {
          const confirmStatus = yield new Promise((r) => Modal.confirm({
            title: `${t('推荐库区内有空闲的集货位，是否强制上架到其他库区')}`,
            onOk: () => (r('ok')),
            onCancel: () => (r('cancel')),
          }));
          if (confirmStatus === 'ok') {
            // 继续调用扫描集货货位接口
            yield ctx.checkContainerPosition({
              data: {
                param: {
                  ...action.data.param,
                  confirmSure: true,
                  areaFlag: true,
                },
                focusFn: action.data.focusFn,
              },
            });
          } else {
            // 清空集货货位扫描框，光标重新定位到扫描集货货位
            yield ctx.changeData({
              data: {
                containerPosition: '',
              },
            });
            classFocus('containerPosition');
          }
          break;
        }
        default:
      }
    } else {
      const status = yield new Promise((r) => Modal.error({
        title: msg,
        onOk: () => (r('ok')),
      }));
      if (status === 'ok') {
        yield ctx.changeData({
          data: {
            containerPosition: '',
          },
        });
        classFocus('containerPosition');
      }
    }
  },
};
