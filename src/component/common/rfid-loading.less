.outDiv {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
}

.tipsDiv {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  padding: 10px 8px;
  background: #d7d7d7;
}

.tipsP {
  text-align: center;
  color: #ffffff;
  font-size: 14px;
}
.loadingDiv{
  margin: 8px 0 2px 0;
  display: flex;
  justify-content: space-between;
}

.loading{
  width: 16px;
  height: 16px;
  background: #ffffff;
}

.rfidLoading,
.rfidLoading > div {
  position: relative;
  box-sizing: border-box;
}

.rfidLoading {
  display: block;
  font-size: 0;
  color: #000;
}

.rfidLoading.la-dark {
  color: #333;
}

.rfidLoading > div {
  display: inline-block;
  float: none;
  background-color: currentColor;
  border: 0 solid currentColor;
}

.rfidLoading {
  margin: auto;
  height: 18px;
}

.rfidLoading > div {
  width: 10px;
  height: 10px;
  margin: 4px;
  border-radius: 100%;
  opacity: 0;
  animation: rfid-loading__ball-fall--yanIzVgr 1s ease-in-out infinite;
}

.rfidLoading > div:nth-child(1) {
  animation-delay: -1000ms;
}

.rfidLoading > div:nth-child(2) {
  animation-delay: -857ms;
}

.rfidLoading > div:nth-child(3) {
  animation-delay: -714ms;
}

.rfidLoading > div:nth-child(4) {
  animation-delay: -571ms;
}

.rfidLoading > div:nth-child(5) {
  animation-delay: -428ms;
}

.rfidLoading > div:nth-child(6) {
  animation-delay: -285ms;
}

.rfidLoading > div:nth-child(7) {
  animation-delay: -142ms;
}

.rfidLoading > div:nth-child(8) {
  animation-delay: 0ms;
}

@keyframes rfid-loading__ball-fall--yanIzVgr {
  0% {
    opacity: 0;
    color: #01b616;
  }

  10% {
    opacity: 0.1;
    color: #01b616;
  }

  20% {
    opacity: 0.2;
    color: #01b616;
  }

  80% {
    opacity: 0.8;
    color: #01b616;
  }

  90% {
    opacity: 0.9;
    color: #01b616;
  }

  100% {
    opacity: 1;
    color: #01b616;
  }
}