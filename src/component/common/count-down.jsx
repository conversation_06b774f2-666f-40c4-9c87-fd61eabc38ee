import React from 'react';
import PropTypes from 'prop-types';
import style from './count-down.css';
import worker from './worker-utils';

/**
 * 计算相对时间字符串
 * @param {number} time 13位时间戳
 */
function relativeTime(time) {
  const sign = time < 0 ? '-' : '';
  time = time < 0 ? -time : time;
  const hour = Number.parseInt(time / 60 / 60, 10);
  const minute = Number.parseInt((time / 60) % 60, 10);
  const second = Number.parseInt(time % 60, 10);
  return `${sign}${hour > 9 ? hour : `0${hour}`}:${minute > 9 ? minute : `0${minute}`}:${
    second > 9 ? second : `0${second}`
  }`;
}

const changeCalss = () => {
  document.getElementById('countDownContainer').setAttribute('class', style.containerWarn);
};

function CountDown(props) {
  const {
    timeoutSeconds,
    soonOvertimeSeconds,
    onSoon,
    onEnd,
    countLabelText,
  } = props;
  const initTime = relativeTime(timeoutSeconds);
  const [time, setTime] = React.useState(initTime);
  worker.onmessage = (e) => {
    setTime(relativeTime(e.data));
  };
  React.useEffect(() => {
    worker.postMessage({
      state: 'start',
      timeoutSeconds: Number.parseInt(timeoutSeconds, 10),
    });
    return function () {
      worker.postMessage({ state: 'stop' });
    };
  }, [timeoutSeconds]);
  React.useEffect(() => {
    if (time.indexOf('-') > -1) {
      changeCalss();
      onEnd();
    }
    const bool = timeoutSeconds > 0 && timeoutSeconds < soonOvertimeSeconds;
    if (bool) {
      changeCalss();
      onSoon();
    }
  }, []);
  React.useEffect(() => {
    if (time === relativeTime(soonOvertimeSeconds + 1)) {
      return function () {
        onSoon();
        changeCalss();
      };
    }
    if (time === relativeTime(1)) {
      return function () {
        onEnd();
        changeCalss();
      };
    }
  }, [time]);
  return (
    <div id="countDownContainer" className={style.containerNormal}>
      <span>{countLabelText}</span>
      <b>{time}</b>
    </div>
  );
}

CountDown.propTypes = {
  timeoutSeconds: PropTypes.number,
  soonOvertimeSeconds: PropTypes.number,
  onSoon: PropTypes.func,
  onEnd: PropTypes.func,
};

export default CountDown;
