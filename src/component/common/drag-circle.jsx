/* eslint-disable react/prop-types */
import React from 'react';
import { LOCAL_UPLOAD_ERROR_COUNT } from 'lib/storage';
import { DragCircle } from '@shein-components/mot-components';
import { useAppFn } from '@alita/react';
import navStore from '../nav/reducers';

const DragCircleContainer = (props) => {
  const { onClick, ifOpenMotStandardApp, ...rest } = props;
  const motStandardApp = useAppFn('mot-standard');

  return (
    <DragCircle
      onClick={() => {
        navStore.changeData({ data: { firstTimeIntoAndonPage: false } });
        if (ifOpenMotStandardApp) {
          motStandardApp?.changeAndonVisible(true);
        }
        if (onClick) {
          onClick();
        }
      }}
      getUploadErrorCount={() => localStorage.getItem(LOCAL_UPLOAD_ERROR_COUNT) || 0}
      {...rest}
    />
  );
};

export default DragCircleContainer;
