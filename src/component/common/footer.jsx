import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import FooterBtn from './footer-btn';
import { getParentHref } from '../../lib/util';

export const footerStyle = {
  position: 'fixed',
  bottom: '0px',
  width: '100%',
  textAlign: 'center',
  boxSizing: 'border-box',
  height: '56px',
  padding: '10px 15px',
  background: '#ffffff',
  boxShadow: '0px -2px 4px 0px rgba(25,122,250,0.15)',
  // zIndex: '99',
};

function Footer(props) {
  const {
    beforeBack,
    dispatch,
    disabled,
    footerText,
    style,
    footerBtnClass,
    hideBackBtn,
  } = props;
  const back = () => {
    const parentHref = getParentHref();
    dispatch(push(parentHref));
  };
  let slot;
  if (props.children) {
    slot = (
      <div
        style={{
          width: '40%',
          display: 'inline-block',
          marginLeft: '20px',
          verticalAlign: 'middle',
        }}
      >
        {props.children}
      </div>
    );
  }
  return (
    <div style={({ ...footerStyle, ...style })}>
      { !hideBackBtn && (
        <div
          style={{
            width: '40%',
            display: 'inline-block',
            verticalAlign: 'middle',
          }}
        >
          <FooterBtn
            type="primary"
            className={footerBtnClass}
            plain
            disabled={disabled}
            onClick={() => {
              if (beforeBack) {
                beforeBack(back);
              } else {
                back();
              }
            }}
          >
            {footerText || t('返回')}
          </FooterBtn>
        </div>
      )}
      { !hideBackBtn && slot }
      { hideBackBtn && props.children }
    </div>
  );
}

Footer.defaultProps = {
  style: {},
};

Footer.propTypes = {
  beforeBack: PropTypes.func,
  disabled: PropTypes.bool,
  footerText: PropTypes.string,
  style: PropTypes.shape(),
  footerBtnClass: PropTypes.string,
  hideBackBtn: PropTypes.bool,
};

const stateToProps = (state) => state;
export default connect(stateToProps)(Footer);
