import React from 'react';
import PropTypes from 'prop-types';
import assign from 'object-assign';
import ActionSheet from 'react-weui/build/packages/components/actionsheet';
import { t } from '@shein-bbl/react';

const PopSheet = (props) => {
  const {
    onClick,
    cancelBtn,
    onClose,
    show,
  } = props;
  let { menus } = props;
  menus = menus.map(v => assign({}, v, {
    onClick: () => onClick(v),
  }));
  let actions = [];
  if (cancelBtn) {
    actions = [
      {
        label: t('取消'),
        onClick: onClose,
      },
    ];
  }

  return (
    <ActionSheet
      menus={menus}
      actions={actions}
      onRequestClose={onClose}
      type="ios"
      show={show}
    />
  );
};

PopSheet.defaultProps = {
  onClick: () => {},
  onClose: () => {},
  menus: [],
  cancelBtn: false,
  show: false,
};

PopSheet.propTypes = {
  onClick: PropTypes.func,
  onClose: PropTypes.func,
  menus: PropTypes.arrayOf(PropTypes.shape),
  cancelBtn: PropTypes.bool,
  show: PropTypes.bool,
};

export default PopSheet;
