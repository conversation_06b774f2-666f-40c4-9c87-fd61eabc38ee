import React from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import style from './style.css';

class ImgLIst extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      picList: this.props.picList || '',
      index: 0,
      src: this.props.picList.length > 0 ? this.props.picList[0].path : '',
      scale: 1,
      min: 0.5,
      max: 4,
      rotate: 0,
      showZoom: false,
    };
    this.base = 200;
    this.changeIndex = (num) => {
      const picLength = this.state.picList.length - 1;
      const result = this.state.index + num;
      if (result > picLength || result < 0) return;
      this.setState({
        index: result,
        src: this.state.picList[result].path,
        rotate: 0,
      });
    };
    this.changeImg = (num) => {
      this.setState({ index: num, src: this.state.picList[num].path, rotate: 0 });
    };
    this.handle = this.props.onClick;
    this.maxWidth = Math.floor(window.innerWidth * 0.8);
    this.maxHeight = window.innerHeight - 240;
  }

  bigger() {
    if (this.state.scale < this.state.max) {
      this.setState({
        scale: this.state.scale * 1.4,
      });
    }
  }

  smaller() {
    if (this.state.scale > this.state.min) {
      this.setState({
        scale: this.state.scale / 1.4,
      });
    }
  }

  rotate() {
    const val = +this.state.rotate + 1;
    this.setState({
      rotate: val % 4,
    });
  }

  render() {
    const flag = (this.state.index === (this.state.picList.length - 1));
    return (
      <div
        className={style.imgModal}
        onClick={this.handle}
        style={this.props.style}
        onMouseUp={(e) => {
          this.setState({
            dragging: false,
          });
          e.preventDefault();
        }}
      >
        <div
          onClick={e => e.stopPropagation()}
        >
          <div className={style.head}>
            <Icon
              name="arr-left"
              style={{ visibility: this.state.index ? 'visible' : 'hidden' }}
              className={style.icon}
              onClick={() => this.changeIndex(-1)}
            />
            <div
              className={style.bigImg}
              ref={(ref) => {
                this.ref = ref;
              }}
              style={{
                maxWidth: this.maxWidth,
                maxHeight: this.maxHeight,
              }}
              onMouseOver={() => {
                this.setState({
                  showZoom: true,
                });
              }}
              onMouseLeave={() => {
                this.setState({
                  showZoom: false,
                });
              }}
            >
              <img
                className={style.zoomImg}
                src={this.state.src}
                style={{
                  width: this.state.scale * this.base,
                  transform: `rotate(${this.state.rotate * 90}deg)`,
                  transition: 'transform 0.5s, width 0.5s',
                }}
                onMouseDown={(e) => {
                  this.setState({
                    dragging: true,
                    currentX: e.nativeEvent.clientX,
                    currentY: e.nativeEvent.clientY,
                  });
                  e.preventDefault();
                }}
                onMouseMove={(e) => {
                  if (this.state.dragging) {
                    const newPosition = {
                      currentX: e.nativeEvent.clientX,
                      currentY: e.nativeEvent.clientY,
                    };
                    const delta = {
                      x: -newPosition.currentX + this.state.currentX,
                      y: -newPosition.currentY + this.state.currentY,
                    };
                    this.setState(newPosition);
                    const max = [this.ref.scrollWidth, this.ref.scrollHeight];
                    const newScrollTemp = [this.ref.scrollLeft + delta.x,
                      this.ref.scrollTop + delta.y];
                    const res = newScrollTemp.map((value, index) => (
                      Math.max(Math.min(max[index], value), Math.max(0, value))
                    ));
                    this.ref.scrollLeft = res[0];
                    this.ref.scrollTop = res[1];
                  }
                }}
                alt=""
              />
              <div className={style.zoom} style={{ display: this.state.showZoom ? '' : 'none' }} >
                <span onClick={() => this.bigger()}>
                  <Icon name="plus" style={{ color: '#fff' }} />
                </span>
                <span onClick={() => this.smaller()}>
                  <Icon name="minus" style={{ color: '#fff' }} />
                </span>
                <span onClick={() => this.rotate()}>
                  <Icon name="sync" style={{ color: '#fff' }} />
                </span>
              </div>
            </div>
            <Icon
              name="arr-right"
              style={{ visibility: flag ? 'hidden' : 'visible' }}
              className={style.icon}
              onClick={() => this.changeIndex(1)}
            />
          </div>
          <div className={style.pic}>
            {
              this.state.picList.map((val, index) => (
                <span key={val.path}>
                  <img
                    className={index === this.state.index ? style.on : null}
                    src={val.path}
                    onClick={() => this.changeImg(index)}
                    alt=""
                  />
                </span>
              ))
            }
          </div>
        </div>
      </div>
    );
  }
}

ImgLIst.propTypes = {
  picList: PropTypes.arrayOf(PropTypes.object).isRequired,
  onClick: PropTypes.func.isRequired,
  style: PropTypes.shape(),
};

export default ImgLIst;
