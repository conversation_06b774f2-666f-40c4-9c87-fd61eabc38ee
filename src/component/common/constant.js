/**
 * 用于存放一些多处重复使用的公关静态变量，方便统一维护
 */

const imgIcon = {
  wuliuguanli: 'wuliuguanli',
  scan: 'jiao<PERSON><PERSON><PERSON><PERSON>',
  collection: 'jihuo',
  sowing: 'fenbo',
  query: 'kuneichaxun',
  'put-shelves': 'shangjia',
  'order-picking': 'jianhuo',
  'take-account': 'pandian',
  'compound-package': 'hebaoyewu',
  'allot-out': 'tiaobochuku',
  'back-warehouse': 'fancang',
  'inbound-manage': 'kuneiguanli',
  'standard-receive-goods': 'biaozhunhuaruku',
  'receive-goods': 'shouhuo',
  'sample-manage': 'yangyiguanli',
  'refund-scan': 'shouhuoruku',
  'group-manager': 'user-group',
  return: 'return',
  goods_collect: 'shangpinxinxicaiji',
  'change-box': 'huanxiangcaozuo',
  'group-management': 'user-group',
  'special-out': 'teshuchuku',
  '/rfid-manage/container-query': 'container-query',
  '/rfid-manage/rfid-bind': 'rfid-bound',
  '/rfid-manage/rfid-query': 'rfid-query',
  '/rfid-manage/empty-frame-loading': 'loadingscanning',
  '/rfid-manage/empty-frame-unloading': 'discharge',
  '/oversea/oversea-relay-group': 'overseas-group',
  '/oversea/forward': 'overseas-received',
  '/oversea/forwarding-collection': 'overseas-shipping',
  '/oversea/sowing-first': 'overseas-sow',
  '/oversea/sowing-second': 'overseas-binary',
  'return-new': 'tuigong',
  'refund-scan/abnormal': 'storage-anomaly',
  'defective-scrap': 'defective-scrap',
  'transit-operating-warehouse': 'interim-positions',
  put_in_box: 'zhuangxiang',
  'common-function': 'commodity',
  'container-management': 'container-management',
  'exception-manage': 'chukuyichang',
  'allot-out-temporary': 'transfers-staging',
  '/work-order': 'm-create-multic',
};

// 根据菜单地址获取对应的icon图标名
const getIconName = (urlStr) => imgIcon[urlStr] || 'menu';

// 控制页面的pull-to-refresh 行为，需要禁用下拉刷新功能的页面url放在数组里，
const prohibitPageUrlArr = [
  '/take-account/list',
  '/take-account/take/1',
  '/take-account/take/2',
  '/take-account/take/3',
  '/take-account/take/4',
  '/inbound-manage/location-info-manage/3',
  '/inbound-manage/location-info-manage/4',
  '/group-management/group-tasks',
  '/group-management/crew-arrangements',
  '/refund-scan/new-point-binning',
  '/back-warehouse/new-binning',
  '/refund-scan/goods-receipt',
];

const getIconStyle = (rule) => {
  const specialIcon = [
    '/new-pda/oversea',
    '/new-pda/work-manager',
  ];
  const bgMap = {
    '/new-pda/goods-in-manager': 'linear-gradient(142deg,rgba(61,228,226,1) 0%,rgba(4,199,196,1) 100%)',
    '/new-pda/goods-inbound-manager': 'linear-gradient(137deg,rgba(53,182,246,1) 0%,rgba(63,142,253,1) 100%)',
    '/new-pda/goods-out-manager': 'linear-gradient(142deg,rgba(7,200,197,1) 0%,rgba(0,164,225,1) 100%)',
    '/new-pda/goods-transer-manager': 'linear-gradient(137deg,rgba(136,159,255,1) 0%,rgba(38,85,239,1) 100%)',
    '/gtms-pda': 'linear-gradient(142deg,rgba(255,204,5,1) 0%,rgba(249,108,0,1) 100%)',
    '/tms-pda': '#dbdbde',
    '/new-pda/group-management': 'linear-gradient(137deg,rgba(47,52,54,1) 0%,rgba(49,67,88,1) 100%)',
    '/new-pda/oversea': 'linear-gradient(137deg,rgba(147,152,246,1) 0%,rgba(149,167,246,1) 100%)',
    '/new-pda/rfid-project': 'linear-gradient(137deg,rgba(147,152,246,1) 0%,rgba(149,167,246,1) 100%)',
    '/new-pda/rfid-manage': 'linear-gradient(137deg,rgba(255,152,0,1) 0%,rgba(246,191,111,1) 100%)',
    '/new-pda/work-manager': 'linear-gradient(137deg,rgba(53,182,246,1) 0%,rgba(63,142,253,1) 100%)',
  };
  return {
    background: bgMap[rule],
    position: 'relative',
    width: 32,
    height: 32,
    lineHeight: specialIcon.some((v) => v === rule) ? '40px' : '35px',
    borderRadius: 8,
    color: '#fff',
    textAlign: 'center',
    fontSize: specialIcon.some((v) => v === rule) && '24px',
  };
};

/**
 * 主功能：进菜单页先统一选择作业子仓
 * 涉及功能模块：收货入库，次品报废
 * 涉及页面功能点：
 * 1）主菜单进二级菜单前，判断是否要先进入作业子仓页面
 * 2）子菜单页面点返回，判断回主菜单还是作业子仓页面
 * 3）
 */
const homeworkSubWarehouseUrls = [
  '/new-pda/refund-scan', // 收货入库
  '/new-pda/return-new', // 次品退供
  '/new-pda/defective-scrap', // 次品报废
  '/new-pda/container-management', // 容器管理
  '/new-pda/transit-operating-warehouse', // 中转仓作业
  '/new-pda/allot-out', // 调拨管理
  '/new-pda/standard-receive-goods', // 特殊入库收货
  '/new-pda/scan', // 交接扫描
  '/new-pda/put-shelves', // 入库管理-上架
];

// 跳过二级页面
const skipSecondLevelPages = [
  '/oversea/sowing-first',
  '/oversea/sowing-second',
  '/oversea/forwarding-collection',
  '/oversea/forward',
  '/oversea/oversea-relay-group',
  '/rfid-manage/container-query',
  '/rfid-manage/rfid-query',
  '/rfid-manage/rfid-bind',
  '/rfid-manage/empty-frame-loading',
  '/rfid-manage/empty-frame-unloading',
  '/work-order',
];

// 存放常用的公共变量
export {
  imgIcon,
  getIconName,
  prohibitPageUrlArr,
  getIconStyle,
  homeworkSubWarehouseUrls,
  skipSecondLevelPages,
};
