import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { push } from 'react-router-redux';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import { Grids } from 'react-weui/build/packages/components/grid';
import { getWarehouseId } from 'lib/util';
import {
  LOCAL_SUB_MENU_URL, LOCAL_USER, LOCAL_LANG, LOCAL_GTMS_TOKEN,
} from 'lib/storage';
import style from '../style.css';
import {
  getIconName, getIconStyle, homeworkSubWarehouseUrls, skipSecondLevelPages,
} from './constant';

const getIcon = (name, rule, showRedDotted = false) => {
  if (!showRedDotted) {
    return <Icon style={getIconStyle(rule)} name={name} />;
  }
  return (
    <span className={style.redDottedWrap}>
      <Icon style={getIconStyle(rule)} name={name} />
    </span>
  );
};

class Menus extends React.Component {
  render() {
    const {
      redDottedList,
      dispatch,
      menuList,
    } = this.props;
    let menuArr = [];
    for (let i = 0; i < menuList.length; i++) {
      const menuItem = menuList[i];
      menuArr.push({
        title: menuItem.title,
        key: menuItem.rule,
        grids: menuItem.children.map((item) => ({
          label: item.title,
          href: `#${item.rule.split('/new-pda')
            .pop()}`,
          icon: getIcon(getIconName(item.rule.split('/new-pda/')
            .pop()), menuItem.rule, redDottedList.includes(item.rule)),
          rule: item.rule,
          onClick: (e) => {
            e.preventDefault();
            localStorage.setItem(LOCAL_SUB_MENU_URL, item.rule);
            if (item.rule === '/new-pda/return-new') {
              dispatch(push('/homework-subwarehouse'));
              return;
            }
            // 判断是否要先选'作业子仓' 需要先进入作业子仓的页面
            if (homeworkSubWarehouseUrls.includes(item.rule)) {
              dispatch(push('/homework-subwarehouse'));
              return;
            }
            // 海外转发管理统一处理 RFID管理统一处理 跳过二级菜单的页面
            if (skipSecondLevelPages.includes(item.rule)) {
              dispatch(push(item.rule));
              return;
            }

            dispatch(push('/sub-menu'));
          },
        })),
      });
    }

    if (localStorage.getItem(LOCAL_USER)) {
      const user = JSON.parse(localStorage.getItem(LOCAL_USER));
      if ((user.system || []).includes('tms')) {
        const warehouse = getWarehouseId();
        let tmotURL = `${process.env.GMOT_URI}?token=${localStorage.getItem('GTMS_TOKEN')}`;
        tmotURL += `&source=${window.location.origin}`;
        tmotURL += `&warehouse=${warehouse}`;
        tmotURL += `&lang=${localStorage.getItem('lang')}`;

        menuArr.push({
          title: t('物流'),
          key: '/tms-pda',
          grids: [
            {
              label: <i style={{ color: '#666' }}>{t('即将下线/物流')}</i>,
              href: '#/tms-pda/tms-menus',
              icon: getIcon('wuliuguanli', '/tms-pda'),
              rule: '/tms-pda',
            },
            ...((user.system || []).includes('gtms') ? [{
              label: 'GTMS',
              href: tmotURL,
              icon: getIcon('wuliuguanli', '/gtms-pda'),
              rule: '/gtms-pda',
            }] : []),
          ],
        });
      }
    }

    // 二级菜单数量
    const menuCount = menuArr.reduce((prev, next) => prev + next.grids.length, 0);

    // 二级菜单数量小于等于5，则不作分级展示
    if (menuCount <= 5) {
      menuArr = [{
        title: '',
        key: 'menu',
        grids: menuArr.reduce((prev, next) => [...prev, ...next.grids], []),
      }];
    }

    return (
      <div style={{ overflow: 'hidden' }}>
        {
            menuArr.length ? menuArr.map((i) => (
              <div key={i.key}>
                {i.title && (<div className={style.menuTitle}>{i.title}</div>)}
                <Grids
                  data={i.grids}
                  className={style.menuBg}
                />
              </div>
            )) : (
              <div
                style={{ fontSize: '15px', textAlign: 'center' }}
              >{t('当前菜单无可用权限，请联系管理员处理！')}
              </div>
            )
          }
      </div>
    );
  }
}

Menus.defaultProps = {
  redDottedList: [],
};

Menus.propTypes = {
  dispatch: PropTypes.func.isRequired,
  redDottedList: PropTypes.arrayOf(PropTypes.string),
  menuList: PropTypes.arrayOf(PropTypes.shape()),
};

const NavProps = (state) => state.nav;

export default connect(NavProps)(Menus);
