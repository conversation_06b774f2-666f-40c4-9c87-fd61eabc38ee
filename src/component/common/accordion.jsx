import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import modal from './modal';
import styles from '../style.css';

// 计算对象数组某字段为true的长度：如当前子集菜单选中的个数，一级菜单展开个数
const calcCheckedNum = (subList, keyName = 'isChecked') => {
  if (!subList.length || !subList.filter) {
    return 0;
  }
  return subList.filter(sub => sub[keyName]).length;
};
// 汇总所有选中子集成一个数组
const getSubCheckedList = (parentList) => {
  const checkedArr = [];
  parentList.forEach((parent) => {
    if (parent.children && parent.children.length) {
      parent.children.forEach((sub) => {
        if (sub.isChecked) {
          checkedArr.push(sub);
        }
      });
    }
  });
  return checkedArr;
};
// 汇总所有选中主菜单成一个数组
const getParentCheckedList = (parentList) => {
  const checkedArr = [];
  parentList.forEach((parent) => {
    if (parent.isChecked) {
      checkedArr.push(parent);
    }
  });
  return checkedArr;
};
// 处理props中的dataSource数据
const handleDataSource = (dataSource, props) => {
  const { expendOne, multiple } = props;
  // 深度拷贝props传进来的accordionList，并存于当前state数据中
  const accordionListData = JSON.parse(JSON.stringify(dataSource));
  // 处理expendOne和dataSource的isExpand冲突情况，以expendOne为主
  if (expendOne && calcCheckedNum(accordionListData, 'isExpand') > 1) {
    // 保留最前面的那个展开值
    let isExpandOne = false;
    accordionListData.forEach(((v) => {
      if (v.isExpand) {
        v.isExpand = !isExpandOne;
        isExpandOne = true;
      }
    }));
  }
  // 处理multiple和dataSource的isChecked冲突情况，以multiple为主
  if (!multiple && getSubCheckedList(accordionListData).length > 1) {
    accordionListData.forEach((obj) => {
      if (calcCheckedNum(obj.children) > 1) {
        // 保留最前面的选中
        let isCheckedOne = false;
        obj.children.forEach(((v) => {
          if (v.isChecked) {
            v.isChecked = !isCheckedOne;
            isCheckedOne = true;
          }
        }));
      }
    });
  }
  return accordionListData;
};
// 处理props中的dataSource数据选中状态
const handleDataSourceChecked = (accordionListData, dataSource) => {
  const checkedParentArr = getParentCheckedList(dataSource);
  const checkedArr = getSubCheckedList(dataSource);
  accordionListData.forEach((obj) => {
    if (checkedParentArr.some((i) => obj.title && i.title === obj.title)) {
      obj.isChecked = true;
    } else {
      obj.isChecked = false;
    }
    obj.children.forEach((sub) => {
      if (checkedArr.some(v => sub.title && v.title === sub.title)) {
        sub.isChecked = true;
      } else {
        sub.isChecked = false;
      }
    });
  });
  return [...accordionListData];
};

class Accordion extends Component {
  constructor(props) {
    super(props);
    const { dataSource } = props;
    this.state = {
      accordionListData: handleDataSource(dataSource, props),
      originDataSourceStr: JSON.stringify(dataSource),
    };
  }

  // 只监听props变化【和constructor初始化时dataSource值对比】
  static getDerivedStateFromProps(props, state) {
    if (JSON.stringify(props.dataSource) !== state.originDataSourceStr) {
      return {
        accordionListData: handleDataSourceChecked(state.accordionListData, props.dataSource),
      };
    }
    return null;
  }

  render() {
    const {
      onSelected,
      expendOne,
      multiple,
      badge,
      limitNum,
      limitNumTip,
    } = this.props;
    // checkedOne代表limitNum是否为1的情况，即整个手风琴只能选一个
    const checkedOne = limitNum === 1;
    // 当checkedOne为true时，multiple则为false【只能当选】
    const nowMultiple = checkedOne ? false : multiple;
    const {
      accordionListData,
    } = this.state;
    // 手风琴折叠展开
    const handleExpand = (idx, isExpand, isChecked) => {
      if (isExpand) {
        accordionListData[idx].isExpand = false;
      } else {
        // 是否需要把其它展开的折叠起来,同一时间只能展开一个
        if (expendOne) {
          accordionListData.forEach((obj) => {
            obj.isExpand = false;
          });
        }
        accordionListData[idx].isExpand = true;
      }
      // 点击主菜单 只有没有子级的才会勾选与否
      const nowObj = accordionListData[idx];
      if (!nowObj.children.length) {
        // 当前全部选中的菜单级数组
        let allParentCheckedArr = [];
        const allSubCheckedArr = getSubCheckedList(accordionListData);
        if (isChecked) {
          // 只有多选时，才能取消当前自身勾选
          if (nowMultiple) {
            nowObj.isChecked = false;
          }
          allParentCheckedArr = getParentCheckedList(accordionListData);
        } else {
          allParentCheckedArr = getParentCheckedList(accordionListData);
          // 限制最多选中总个数；limitNum为1的情况不用弹窗提示
          if (limitNum && limitNum <= [...allParentCheckedArr, ...allSubCheckedArr].length && limitNum !== 1) {
            modal.error({ content: limitNumTip });
            return;
          }
          nowObj.isChecked = true;
          if (checkedOne) {
            allParentCheckedArr = [nowObj];
          } else {
            allParentCheckedArr.push(nowObj);
          }
        }
        // 父级回调, 将全部选中的传回父组件
        onSelected([...allParentCheckedArr, ...allSubCheckedArr]);
      }
      this.setState({ accordionListData: [...accordionListData] });
    };
    // 子级菜单选中切换
    const handleSubChecked = (idx, subIdx, isChecked) => {
      const nowObj = accordionListData[idx].children[subIdx];
      // 当前全部选中的子级数组
      let allSubCheckedArr = [];
      const allParentCheckedArr = getParentCheckedList(accordionListData);
      if (isChecked) {
        // 只有多选时，才能取消当前自身勾选
        if (nowMultiple) {
          nowObj.isChecked = false;
        }
        allSubCheckedArr = getSubCheckedList(accordionListData);
      } else {
        allSubCheckedArr = getSubCheckedList(accordionListData);
        // 限制最多选中总个数；limitNum为1的情况不用弹窗提示
        if (limitNum && limitNum <= [...allSubCheckedArr, ...allParentCheckedArr].length && limitNum !== 1) {
          modal.error({ content: limitNumTip });
          return;
        }
        if (checkedOne) {
          // checkedOne  为true时，整个手风琴只能选一个
          accordionListData.forEach((v) => {
            v.children.forEach((obj) => {
              obj.isChecked = false;
            });
          });
        } else if (!nowMultiple) {
          // 非多选时，同一子级最多选择一个
          accordionListData[idx].children.forEach((obj) => {
            obj.isChecked = false;
          });
          allSubCheckedArr = getSubCheckedList(accordionListData);
        }
        nowObj.isChecked = true;
        if (checkedOne) {
          allSubCheckedArr = [nowObj];
        } else {
          allSubCheckedArr.push(nowObj);
        }
      }
      this.setState({ accordionListData: [...accordionListData] });
      // 父级回调, 将全部选中的子级数组传回父组件
      onSelected([...allSubCheckedArr, ...allParentCheckedArr]);
    };
    // 渲染二级列表
    const renderSecondList = (children, idx) => children.map((sub, subIdx) => (
      <div
        className={styles.accordionSubItem}
        key={sub.title}
        onClick={handleSubChecked.bind(this, idx, subIdx, sub.isChecked)}
      >
        {
          sub.iconName && <Icon name={sub.iconName} className={styles.iconLeft} />
        }
        <div className={styles.accordionSubCont} style={{ color: sub.isChecked ? 'rgba(0,89,206,1)' : 'rgba(20,23,55,1)' }}>
          {sub.title}
        </div>
        {
          !nowMultiple && !sub.isChecked ? null
            : <Icon name="check" style={{ color: sub.isChecked ? 'rgba(0,89,206,1)' : '#727783' }} />
        }
      </div>
    ));

    return (
      <div style={{ background: '#fff' }}>
        {
          accordionListData.map((obj, idx) => (
            <div key={obj.title} className={styles.accordionItemWrap}>
              <div
                className={styles.accordionItem}
                onClick={handleExpand.bind(this, idx, obj.isExpand, obj.isChecked)}
              >
                {
                  obj.iconName && <Icon name={obj.iconName} className={styles.iconLeft} />
                }
                <div className={styles.contCenter}>{obj.title}</div>
                {
                  // badge 为true且选中数量不为0 则显示选中数量徽标
                  badge && calcCheckedNum(obj.children) ?
                    <span className={styles.countNum}>{calcCheckedNum(obj.children)}</span>
                    : null
                }
                {/* 菜单下面没有子级目录的-->badge 为true且选中数量不为0 则显示选中数量徽标 */}
                {
                  badge && obj.isChecked && !obj.children.length ?
                    <span className={styles.countNum}>1</span>
                    : null
                }
                {/* 菜单下面没有子级目录的-->添加选中 icon */}
                {
                  !nowMultiple && !obj.isChecked ? null
                    : (!obj.children.length ? <Icon name="check" style={{ fontSize: '12px', color: obj.isChecked ? 'rgba(0,89,206,1)' : '#727783' }} /> : null)
                }
                {/* 菜单下面没有子级目录的-->去掉展开icon */}
                {obj.children.length > 0 && (
                  <Icon name={obj.isExpand ? 'arr-down' : 'arr-right'} className={styles.iconRight} />
                )}
              </div>
              <div style={{ display: obj.isExpand ? 'block' : 'none' }}>
                { obj.children && renderSecondList(obj.children, idx) }
              </div>
            </div>
          ))
        }
      </div>
    );
  }
}

Accordion.defaultProps = {
  // dataSource: [{
  //   title: '交货扫描', // 一级菜单标题 【必传】
  //   iconName: 'jiaohuosaomiao', // 一级菜单左边icon图标
  //   isExpand: false, // 一级菜单是否展开
  //   children: [{ // 子级菜单数组
  //     title: '子交货扫描', // 子级菜单标题 【必传】
  //     // iconName: 'jiaohuosaomiao', // 子级菜单左边icon图标
  //     isChecked: false, // 子级是否选中
  //   }],
  // }],
  dataSource: [], // 手风琴渲染数据，一般是对象数组；具体字段要求看上面注释内容
  onSelected: (arr) => {
    // 父级回调, 将全部选中的子级数组传回父组件，将用户从dataSource传进来的数据传回去【包括id,value等其它字段】
    console.log('Accordion', arr);
  },
  expendOne: false, // 是否同一时刻，只能展开一个手风琴
  multiple: false, // 子级菜单是否单选
  badge: false, // 是否显示选中数量的徽标【multiple需为true】
  limitNum: 0, // 限制全部子级菜单的最多选中个数，0代表不限制个数；为1时__整个手风琴只能选一个，所以multiple 则为false【只能单选】
  limitNumTip: '', // 当limitCheckedNum达到上限时，弹窗提示话语。有设置limitNum且limitNum大于1时，需要加上对应提示话语
};

Accordion.propTypes = {
  dataSource: PropTypes.arrayOf(PropTypes.shape),
  onSelected: PropTypes.func,
  expendOne: PropTypes.bool,
  multiple: PropTypes.bool,
  badge: PropTypes.bool,
  limitNum: PropTypes.number,
  limitNumTip: PropTypes.string,
};
export default Accordion;
