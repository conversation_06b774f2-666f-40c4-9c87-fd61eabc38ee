import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import styles from '../style.css';

const defaultStyle = {
  height: 0,
  flex: '1 1 auto',
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
};

// 滚动到最底部时, scrollHeight - scrollTop 不一定等于 clientHeight，会存在误差
const tolerance = (num1, num2) => {
  const res = Math.abs(num1 - num2);
  return res < 1;
};

function ListItem(props) {
  const {
    rows,
    rowData,
    itemClass,
    itemStyle,
  } = props;

  if (rows.some((item) => !Array.isArray(item))) {
    throw new Error(t('rows中每一项必须为数组'));
  }

  return (
    <div className={`${styles.listItem} ${itemClass}`} style={itemStyle}>
      {
        rows.map((item, index) => (
          <div className={styles.row} key={index}>
            {
              item.map((v) => {
                let flex;
                const { width } = v;
                if (width && (typeof width === 'number')) {
                  flex = `1 1 ${width}%`;
                } else if (width && (typeof width === 'string')) {
                  flex = `1 1 ${width}`;
                }
                if (!v.render) {
                  return null;
                } else if (typeof v.render !== 'function') {
                  if (rowData[v.render] === 0) {
                    return (
                      <div style={{ flex }} key={v.render}>
                        <span className={styles.lable}>{v.title}</span>
                        <span className={styles.value} style={v.itemRenderStyle}>0</span>
                      </div>
                    );
                  }
                  return (
                    <div style={{ flex }} key={v.render}>
                      <span className={styles.lable}>{v.title}</span>
                      <span className={styles.value} style={v.itemRenderStyle}>{rowData[v.render] || v.default}</span>
                    </div>
                  );
                } else if (typeof v.render === 'function') {
                  return (
                    <div style={{ flex }} key={v.render}>
                      {v.render(rowData)}
                    </div>
                  );
                }
              })
            }
          </div>
        ))
      }
    </div>
  );
}

ListItem.propTypes = {
  rows: PropTypes.arrayOf(PropTypes.array),
  rowData: PropTypes.shape(),
  itemClass: PropTypes.string,
  itemStyle: PropTypes.shape(),
};

class PagesScrollList extends React.Component {
  constructor(props) {
    super(props);
    this.listRef = React.createRef();
  }

  componentDidMount() {
    const { scrollIntoBottom, scrollIntoTop } = this.props;
    const div = this.listRef.current;

    div.onscroll = () => {
      const { nowPage, showNum } = this.props;
      const { scrollTop, clientHeight, scrollHeight } = div;
      // 滚动到顶部
      if (scrollTop === 0) {
        if (scrollIntoTop && typeof scrollIntoTop === 'function') {
          scrollIntoTop(nowPage, showNum);
        }
      }
      // 滚动到底部
      if (tolerance(scrollHeight - scrollTop, clientHeight)) {
        if (scrollIntoBottom && typeof scrollIntoBottom === 'function') {
          scrollIntoBottom(nowPage, showNum);
        }
      }
    };
  }

  render() {
    const {
      rows,
      data,
      style,
      className,
      header,
      rowStyleOrClass,
      separate,
      showPagination,
      ItemOnclick,
      nowPage,
      showNum,
      total,
    } = this.props;

    if (!Array.isArray(data)) {
      throw new Error(t('data和rows必须为数组!'));
    }

    if (header && !React.isValidElement(header)) {
      throw new Error(t('header必须是react元素!'));
    }

    const type = rowStyleOrClass && (typeof rowStyleOrClass);
    if (type && type !== 'object' && type !== 'function' && type !== 'string') {
      throw new Error(t('itemStyle必须为object、function或string!'));
    }

    return (
      <div style={({ ...defaultStyle, ...style })} className={className}>
        <div className={styles.listHeader}>
          {header}
        </div>
        <div className={styles.listOverflow} ref={this.listRef}>
          {
            data.map((item, index) => {
              const evenBg = {
                backgroundColor: '#F9F9F9',
              };

              const oddBg = {
                backgroundColor: '#FFFFFF',
              };

              let itemStyle = {};

              if (separate) {
                itemStyle = index % 2 === 1 ? oddBg : evenBg;
              }

              let itemClass = '';
              if (type && type === 'function') {
                itemClass = rowStyleOrClass(item, index);
              } else {
                switch (type) {
                  case 'object':
                    itemStyle = rowStyleOrClass;
                    break;
                  case 'string':
                    itemClass = rowStyleOrClass;
                    break;
                  default:
                    break;
                }
              }
              return (
                <div
                  onClick={() => {
                    if (ItemOnclick && typeof ItemOnclick === 'function') {
                      ItemOnclick(item);
                    }
                  }}
                  key={index}
                >
                  <ListItem
                    key={index}
                    rows={rows}
                    rowData={item}
                    itemStyle={item.styleObj ? { ...itemStyle, ...item.styleObj } : itemStyle}
                    itemClass={itemClass}
                  />
                </div>
              );
            })
          }
        </div>
        {
          showPagination &&
          (
            <div className={styles.listPagination}>
              <div>{t('第')} {nowPage} / {Math.ceil(total / showNum)}{t('页')}</div>
              <div>{t('共')} {total} {t('条')}</div>
            </div>
          )
        }
      </div>
    );
  }
}

PagesScrollList.propTypes = {
  rows: PropTypes.arrayOf(PropTypes.array),
  data: PropTypes.arrayOf(PropTypes.object),
  style: PropTypes.shape(),
  className: PropTypes.string,
  header: PropTypes.element,
  rowStyleOrClass: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.func,
    PropTypes.object,
  ]),
  separate: PropTypes.bool,
  showPagination: PropTypes.bool,
  ItemOnclick: PropTypes.func,
  nowPage: PropTypes.number,
  showNum: PropTypes.number,
  total: PropTypes.number,
  scrollIntoBottom: PropTypes.func,
  scrollIntoTop: PropTypes.func,
};

PagesScrollList.defaultProps = {
  rows: [],
  data: [],
  style: {},
  separate: false,
  showPagination: false,
};

export default PagesScrollList;
