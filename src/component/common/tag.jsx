import React from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import styles from '../style.css';

const typeMap = {
  red: styles.red,
  error: styles.red,
  normal: styles.normal,
};

const Tag = (props) => {
  const {
    content,
    type,
    dataSource,
    style,
  } = props;
  const className = typeMap[type];
  if (dataSource && dataSource.length) {
    return (
      dataSource.map(text => (
        <div key={text} className={classnames(styles.tag, styles.tags)}>
          {text}
        </div>
      ))
    );
  }
  return (
    <div className={classnames(styles.tag, className)} style={style}>
      {content || props.children}
    </div>
  );
};

Tag.defaultProps = {
  dataSource: [], // 支持传数组，作为tags组件
  style: {},
};

Tag.propTypes = {
  type: PropTypes.string,
  content: PropTypes.string,
  dataSource: PropTypes.arrayOf(String),
  style: PropTypes.shape(),
};

export default Tag;
