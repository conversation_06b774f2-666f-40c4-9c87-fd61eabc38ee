# PopRadio
PopRadio组件为支持单选的弹框组件。

## Props
下表列出了 PopRadio 的所有属性及其类型和默认值。

| 属性 | 类型 | 默认值 | 描述 |
| ---- | ---- | ------ | ------ |
| `labelName` | `string` | `dictNameZh` | 展示内容对应的字段 |
| `valueName` | `string` | `dictCode` | 值对应的字段 |
| `leftBtn` | `string` | `取消` | 左侧按钮文本 |
| `rightBtn` | `string` | `确定` | 右侧按钮文本 |
| `selectValue` | `string、number` | `` |  |
| `show` | `boolean` | `false` | 是否需要显示 |
| `label` | `ReactNode` | `` | 标题名称 |
| `placeholder` | `string` | `请选择` | placeholder |
| `className` | `string` | `` | class名称 |
| `disabled` | `boolean` | `` | 是否禁用 |
| `lineBreak` | `boolean` | `true` | 是否换行 |
| `popupStyle` | `object` | `` | 弹框样式 |
| `hideInput` | `boolean` | `` | 是否隐藏input |
| `selectList` | `array` | `` | 数据源 |

### Event
| 属性 | 类型 | 默认值 | 描述 |
| ---- | ---- | ------ | ------ |
| `onCancel` | `event` |  | 取消事件 |
| `onOk` | `event` | `(val, data) => void` | 确认 |
| `onClick` | `event` |  | 点击事件 |

### 示例
```jsx
import { PopRadio } from 'common';

function App() {
  return (
    <div>
      <PopRadio
        label={t('label名称')}
        disabled={disabled}
        selectValue={1}
        selectList={[{ dictNameZh: 'label', dictCode: 'value' }]}
        show={true}
        lineBreak={false}
        className="loadType"
        valueName="dictCode"
        labelName="dictNameZh"
        placeholder={t('请选择')}
        onClick={() => {
          console.log('点击事件');
        }}
        onCancel={() => {
          console.log('点击取消');
        }}
        onOk={(val) => {
          console.log('选中');
        }}
      />
    </div>
  );
}
```

## 组件更新记录

| 更改内容 | 人 | 时间 |
| ---- | ---- | ------ |
| `改造为ts组件` | `Rebekah Hu` | `1 8th, 2024` |
