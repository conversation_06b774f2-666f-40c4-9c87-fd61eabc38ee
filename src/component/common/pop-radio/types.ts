export interface PopRadioProps {
  labelName?: string,
  valueName?: string,
  leftBtn?: string,
  rightBtn?: string,
  selectValue?: string | number,
  show: boolean,
  label?: React.ReactNode,
  placeholder?: string,
  className?: string,
  disabled?: boolean,
  lineBreak?: boolean,
  popupStyle?: React.CSSProperties,
  hideInput?: boolean,
  selectList?: Array<any>,
  onCancel?: () => void,
  onOk?: (val: number, selectItem: any) => void,
  onClick?: () => void,
}
