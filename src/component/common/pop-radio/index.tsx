import React, { Component } from 'react';
import {
  Popup,
  PopupHeader,
  Radio,
  CellBody,
  CellFooter,
  Form,
  FormCell,
} from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import FocusInput from 'common/focus-input';
import { PopRadioProps } from './types';

// pop单选组件
class PopRadio extends Component<PopRadioProps> {
  render() {
    const {
      labelName = 'dictNameZh',
      valueName = 'dictCode',
      leftBtn = t('取消'),
      rightBtn = t('确定'),
      selectList,
      selectValue,
      onCancel,
      onOk,
      show = false,
      onClick,
      label,
      placeholder = t('请选择'),
      className,
      disabled,
      lineBreak = true,
      popupStyle = {},
      hideInput,
    } = this.props;
    // 弹窗，最大高度限制，页面3/4高
    const height = window.innerHeight * 0.75 || 382;
    let selectValueData = '0';
    if (show) {
      if (selectValue) {
        selectValueData = selectValue as string;
      }
    }
    const selectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      selectValueData = e.target.value;
    };
    // 用户选确定，才生效
    const selectOk = () => {
      // selectItem为匹配的那条数据, 比如 {id: 1, name: '空闲'}
      const selectItem = (selectList || [])
        .find((item) => parseInt(selectValueData, 10) === item[valueName]);
      if (onOk) {
        onOk(parseInt(selectValueData, 10), selectItem || {});
      } else {
        throw new Error(t('onOk是必传属性！'));
      }
    };
    // 点击取消，要重新设置state
    const selectCancel = () => {
      selectValueData = selectValue as string;
      if (onCancel) {
        onCancel();
      } else {
        throw new Error(t('onCancel是必传属性,用于控制pop显示隐藏！'));
      }
    };

    // 根据selectValue获取input要显示的值
    const getInputValue = () => {
      if (selectValue && selectList && selectList.length) {
        return (
          selectList.find((item) => parseInt(`${selectValue}`, 10) === parseInt(item[valueName], 10))
            ? selectList
              .find((item) => parseInt(`${selectValue}`, 10) === parseInt(item[valueName], 10))[labelName]
            : ''
        ) || '';
      }
      return '';
    };

    return (
      <>
        {
          hideInput || (
            <FocusInput
              label={label}
              type="text"
              placeholder={placeholder}
              className={className || ''}
              readOnly
              disabled={disabled || false}
              onClick={onClick}
              value={getInputValue()}
              arrow
              keepFocus={false}
              lineBreak={lineBreak}
            >
              <label>{label}</label>
            </FocusInput>
          )
        }
        {
          show ? (
            <Popup
              show={show}
              style={{ ...popupStyle, maxHeight: height, overflowY: 'scroll' }}
            >
              <PopupHeader
                left={t('取消') || leftBtn}
                right={t('确定') || rightBtn}
                leftOnClick={selectCancel}
                rightOnClick={selectOk}
              />
              <div style={{ maxHeight: height - 33, overflowY: 'scroll' }}>
                <Form
                  style={{ marginTop: 0 }}
                  radio
                >
                  {
                      selectList && selectList.length ? (
                        selectList.map((item) => (
                          <FormCell
                            radio
                            key={item[valueName]}
                          >
                            <CellBody>{item[labelName]}</CellBody>
                            <CellFooter>
                              <Radio
                                name="radio"
                                value={item[valueName]}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                  selectChange(e);
                                }}
                                defaultChecked={
                                  (parseInt(selectValueData, 10) === parseInt(item[valueName], 10))
                                }
                              />
                            </CellFooter>
                          </FormCell>
                        ))
                      ) : <div style={{ height: 30, lineHeight: '30px', textAlign: 'center' }}>{t('暂无数据')}</div>
                    }
                </Form>
              </div>
            </Popup>
          ) : null
        }
      </>
    );
  }
}

export default PopRadio;
// 使用示例见 /example/test4
