import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Gallery, Button } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';

class ImgPreview extends Component {
  constructor(props) {
    super(props);
    this.state = {
      show: false,
    };
  }

  render() {
    const { src, children } = this.props;
    const { show } = this.state;
    const backButtonStyle = {
      display: 'inline-block',
      width: 120,
      color: 'white',
      border: 'none',
    };

    return (
      <div>
        <div
          onClick={() => this.setState({ show: true })}
        >
          {children}
        </div>
        <Gallery src={src} show={show}>
          <Button
            style={backButtonStyle}
            onClick={() => this.setState({ show: false })}
          >
            {t('返回')}
          </Button>
        </Gallery>
      </div>
    );
  }
}

ImgPreview.propTypes = {
  src: PropTypes.string,
  children: PropTypes.node,
};

export default ImgPreview;
