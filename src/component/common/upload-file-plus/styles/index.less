.imgSize {
  width: 78px;
  height: 78px;
}

.uploadContainer {
  display: flex;
  gap: 30px 12px;
  flex-flow: wrap;
}

// 选择文件方框-禁用情况下样式
.disabledBox .chooseFileBox {
  cursor: not-allowed;

  .add {
    &:hover,
    &:focus {
      &:before,
      &:after {
        background-color: #999;
      }
    }
  }

  &:hover {
    border-color: #999;

    .add {
      &:before,
      &:after {
        background-color: #999;
      }
    }
  }
}

.hoverFileBox {
  &:hover {
    border-color: #197afa;

    .add {
      &:before,
      &:after {
        background-color: #197afa;
      }
    }
  }
}

// 选择文件方框-通用样式
.chooseFileBox {
  display: flex;
  justify-content: center;
  align-items: center;
  .add {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px;

    &:before,
    &:after {
      background-color: #999;
      content: '';
      display: block;
      position: absolute;
    }

    &:before {
      left: 14px;
      width: 2px;
      height: 30px;
    }

    &:after {
      top: 14px;
      width: 30px;
      height: 2px;
    }

    &:hover,
    &:focus {
      &:before,
      &:after {
        background-color: #197afa;
      }
    }
  }
  position: relative;
  width: 80px;
  height: 80px;
}

.showFile {
  &:hover, &:focus {
    .imgCover {
      display: flex;
    }
  }

  .imgCover {
    .imgSize;
    display: none;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(20, 23, 55, 0.6);
    z-index: 1;
    align-items: center;
    justify-content: center;

    .coverIcon {
      pointer-events: auto;
      font-size: 18px;
      color: #b3b7c1;

      &:hover, &:active {
        color: #ffffff;
      }
    }
  }
}

// 校验未通过边框样式
.errorBorderStyle {
  border: 1px dashed #ff4d50;

  &:hover {
    border-color: #ff4d50;
  }
}

// 正常边框样式
.normalBorderStyle {
  border: 1px dashed #b3b7c1;
}

// 上传文件后 右上角红色删除按钮
.deleteFileIcon {
  position: absolute;
  z-index: 100;
  top: -7px;
  right: -7px;
  display: flex;
  width: 14px;
  height: 14px;
  background-color: #ff4d50;
  border-radius: 8px;

  &:before,
  &:after {
    position: absolute;
    left: 50%;
    top: 2px;
    content: '';
    display: inline-block;
    width: 1px;
    height: 10px;
    background-color: #fff;
  }

  &:before {
    transform: rotate(-45deg);
  }

  &:after {
    transform: rotate(45deg);
  }
}

// 上传文件后显示图标
.fileTypeIcon {
  height: 100%;
  width: 100%;
  font-size: 55px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 校验未通过底部提示语
.fileExceedMaxSize {
  position: absolute;
  word-break: break-all;
  //top: 100%;
  left: 50%;
  width: 100%;
  padding: 2px 8px;
  margin-top: 8px;
  background-color: #f4f5f8;
  border-radius: 4px;
  font-size: 12px;
  color: #ff4d50;
  transform: translateX(-50%);
  transform-origin: 0 0;
  z-index: 2;

  &:before {
    position: absolute;
    bottom: 100%;
    left: 50%;
    width: 6px;
    height: 6px;
    border: 1px solid rgba(255, 77, 80, 0.1);
    background: inherit;
    content: '';
    transform: rotate(45deg) translateY(3px);
  }
}

.fileNameStyle {
  width: 100%;
  text-align: center;
  margin-top: 8px;
  display: inline-block;
  max-width: 80px;
  word-break: break-all;
}

.thumbnail {
  width: 70px;
  height: 70px;
}

.errorStyle {
  color: #ff4d50;
}

.loader {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #555;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: inline-block;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}


.closeBtn {
  border: none;
  color: #ffffff;
}