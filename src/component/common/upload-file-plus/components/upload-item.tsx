import { t } from '@shein-bbl/react';
import React, { useState } from 'react';
import type { MouseEvent } from 'react';
import Icon from '@shein-components/Icon';
import { Button, Gallery } from 'react-weui/build/packages';
import style from '../styles/index.less';
import { isBlob, isObject, getFileSuffix } from '../util';
import { FileTypes } from '../type';

// 各格式文件后缀
const EXCEL_TYPE = ['xlsx', 'xls', 'xlt'];
const PDF_TYPE = ['pdf'];
const WORD_TYPE = ['doc', 'docm', 'docx', 'dot'];
const PPT_TYPE = ['pptx', 'pptm', 'ppt'];
const TEXT_TYPE = ['txt'];
const ZIP_TYPE = ['zip'];
const IMAGE_TYPE = ['png', 'jpg', 'jpeg', 'gif'];
const VIDEO_TYPE = ['avi', 'mp4', 'mov', 'rmvb', 'flv', '3gp'];

const fileTypeIcons = {
  excel: ['excel', '#2B704A'],
  ppt: ['ppt-1'],
  pdf: ['pdf-1'],
  word: ['word', '#187AFA'],
  text: ['text-t'],
  zip: ['odec-zip'],
  video: ['pc-video-fill', '#187AFA'],
  other: ['odec-unknown file-fill'],
};

// 根据文件类型，匹配对应的类型简称
const findTypeName = (name = ''): FileTypes => {
  // 获取文件后缀
  const suffix = getFileSuffix(name);
  let typeName = 'other';
  if (EXCEL_TYPE.find((item) => item === suffix)) typeName = 'excel';
  if (PDF_TYPE.find((item) => item === suffix)) typeName = 'pdf';
  if (WORD_TYPE.find((item) => item === suffix)) typeName = 'word';
  if (PPT_TYPE.find((item) => item === suffix)) typeName = 'ppt';
  if (TEXT_TYPE.find((item) => item === suffix)) typeName = 'text';
  if (ZIP_TYPE.find((item) => item === suffix)) typeName = 'zip';
  if (IMAGE_TYPE.find((item) => item === suffix)) typeName = 'img';
  if (VIDEO_TYPE.find((item) => item === suffix)) typeName = 'video';
  return typeName as FileTypes;
};

// 设定createObjectURL缓存，避免每次render都去create
const cacheList: { file: any; url: string; }[] = [];
let timeout: number | null | undefined = null;

function createObjectURL(file: Blob | MediaSource) {
  const cache = cacheList.find((c) => c.file === file);
  if (cache) return cache.url;
  // 判断传入格式是否正确
  if (!isBlob(file)) {
    throw new Error(t('文件格式不对！请检查fileLise与fileKeyPathName是否正确！'));
  }
  const url = URL.createObjectURL(file);
  cacheList.push({ file, url });

  if (timeout) clearTimeout(timeout);
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  timeout = setTimeout(() => {
    cacheList.splice(0, cacheList.length);
  }, 60 * 1000);

  return url;
}

export type UploadItemProps = {
  /**
   * 删除已上传的文件
   */
  onDelete: (e: MouseEvent) => void,
  file: any,
  filePathKeyName: string,
  renderResult: (data: any) => JSX.Element,
  successFileList: any[],
  index: number,
  disabled?: boolean,

}

export default function UploadItem({
  onDelete,
  file,
  filePathKeyName,
  renderResult,
  successFileList,
  index,
  disabled = false,
}: UploadItemProps) {
  // eslint-disable-next-line no-shadow
  const enhancerUploadItem = (file: any) => {
    // 如果是本地上传的文件会带有name属性，如果不存在name属性，则根据传入的文件名判断后缀
    // eslint-disable-next-line no-param-reassign
    file.typeName = findTypeName(file.name || file[filePathKeyName]);
    // 上传类型为图片 且 图片不存在url -> 本地预览
    if ((file.typeName === 'img' || file.typeName === 'video') && !file[filePathKeyName]) {
      // eslint-disable-next-line no-param-reassign
      file.src = createObjectURL(file);
    }
  };
  const [showErrorTip, setShowErrorTip] = useState(false);
  const [showGallery, setShowGallery] = useState(false);
  const [imgIndex, setImgIndex] = useState(0);
  const [imageList, setImageList] = useState<any>([]);
  enhancerUploadItem(file);

  return (
    <div style={{ position: 'relative' }}>
      <div
        className={`${style.chooseFileBox} ${
          file.errorMsg && !file.loading ? style.errorBorderStyle : [style.normalBorderStyle, style.showFile].join(' ')
        }`}
        onMouseMove={(e) => {
          setShowErrorTip(true);
        }}
        onMouseLeave={() => setShowErrorTip(false)}
      >
        {/* 非disabled情况下才显示删除文件x图标 */}
        {!disabled && !file.loading && (
          <span
            className={`${style.deleteFileIcon}`}
            onClick={(e) => {
              if (onDelete) {
                onDelete(e);
              }
            }}
          />
        )}
        {file.loading ? <div className={style.loader} /> : (
          <>
            {/* 文件回显展示 */}
            <div className={`${style.fileTypeIcon}`}>
              {file.typeName === 'img' ? (
                <img
                  className={style.thumbnail}
                  src={file.src || file[filePathKeyName]}
                  alt=""
                  onClick={() => {
                    if (!file.errorMsg && file.typeName && file.typeName === 'img') {
                      // 若类型为图片，则调用大图预览
                      const previewImages: any[] = [];
                      let currImgIndex = 0;
                      // 获取大图数据
                      if (successFileList && successFileList.length) {
                        successFileList.forEach((thumbnail, dataIndex) => {
                          if (thumbnail.typeName === 'img') previewImages.push(thumbnail.src || thumbnail[filePathKeyName]);
                          if (dataIndex === index) currImgIndex = previewImages.length - 1;
                        });
                      }
                      setImageList(previewImages);
                      setImgIndex(currImgIndex);
                      setShowGallery(true);
                    }
                  }}
                />
              ) : (
                <Icon
                  name={fileTypeIcons[findTypeName(file.name)][0]}
                  color={fileTypeIcons[findTypeName(file.name)][1]}
                />
              )}
            </div>
          </>
        )}
      </div>
      {/* 错误提示语 */}
      {file.errorMsg && showErrorTip && (
        <span className={style.fileExceedMaxSize}>
          {file.errorMsg}
        </span>
      )}
      {/* 文件名 */}
      {!file.loading && (
        <span className={`${style.fileNameStyle} ${file.errorMsg ? style.errorStyle : ''}`}>
          {file.errorMsg ? isObject(file) && file.name : renderResult(file)}
        </span>
      )}
      <div onTouchEnd={(e) => e.stopPropagation()}>
        <Gallery src={imageList} defaultIndex={imgIndex} show={showGallery}>
          <Button
            className={style.closeBtn}
            plain
            onClick={() => {
              setShowGallery(false);
            }}
          >
            {t('关闭')}
          </Button>
        </Gallery>
      </div>
    </div>

  );
}
