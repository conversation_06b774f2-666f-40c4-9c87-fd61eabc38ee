import { t } from '@shein-bbl/react';
import React, {
  useEffect, useImperativeHandle, useState, forwardRef,
} from 'react';
import { sendPostRequest } from '@wms/wms-public-tools';
import { Toast } from 'react-weui/build/packages';
import { message } from 'common/index';
import type { IUploadType } from './type';
import style from './styles/index.less';
import UploadItem from './components/upload-item';
import {
  isArrayValueEqual, isFunction, isObject, getFileSuffix,
} from './util';
import { UploadFile, Validator } from './type';

export interface UploadPlusProps {
  /**
   * 文件上传的接口，需配合autoUpload使用
   */
  action?: string;
  /**
   * 上传的文件列表,受控时必填（如数据回显）
   */
  fileList?: [] | UploadFile[];
  /**
   * 是否禁用上传按钮
   */
  disabled?: boolean;
  /**
   * 接收上传的文件类型
   */
  accept?: string;
  /**
   * 单个文件最大大小，单位MB
   */
  // 限制单个文件大小
  maxSize?: number;
  /**
   * 最多上传文件个数
   */
  // 限制上传文件个数
  limit?: number;
  /**
   * 上传文件时的回调，只返回当前上传成功的文件
   * @param data
   */
  onChange?: (data: object) => void;
  /**
   * 删除文件时的回调
   * @param file
   * @param newFileList
   */
  onDelete?: (file: [any], newFileList: [any]) => void;
  /**
   * 自动上传成功时的回调
   * @param info
   */
  onSuccessUpload?: (info: object) => void;
  /**
   * onFailUpload
   * @param file
   * @param msg
   */
  onFailUpload?: (file: [any], msg: string) => void;
  /**
   * 是否自动上传
   */
  autoUpload?: boolean;
  /**
   * 自动上传请求时请求参数名
   */
  autoUploadKeyName?: string;
  /**
   * 文件路径名-用于文件回显
   */
  filePathKeyName?: string;
  /**
   * 上传结果渲染展示
   * @param data
   */
  renderResult: (data: any) => JSX.Element;
  data: any;
  /**
   * 校验器
   */
  validator?: Validator;

  /**
   * 隐藏上传按钮
   */
  hideAddBtn?: boolean
}

type AddFileProps = {
  currentFile: any
}

/**
 * @cn上传组件
 * @desc 支持上传文本、图片
 * <AUTHOR> Huang
 * @date 2023-02-02
 */
const UploadPlus = forwardRef((
  {
    fileList,
    disabled,
    accept,
    maxSize = 50,
    onChange,
    onDelete,
    onSuccessUpload,
    onFailUpload,
    limit = 1,
    autoUpload = false,
    action = '',
    autoUploadKeyName = 'files',
    filePathKeyName = 'img',
    renderResult = (f) => <span>{f.name}</span>,
    data = {},
    validator,
    hideAddBtn = false,
  }: UploadPlusProps,
  ref,
) => {
  const [state, setState] = useState<Partial<IUploadType>>({
    file: fileList || [],
  });

  const [uploading, setUploading] = useState(false);

  const uploadContainerRef = React.useRef<HTMLDivElement>(null);
  if (fileList) {
    // eslint-disable-next-line max-len
    if (!isArrayValueEqual(fileList, state.file.filter((item: { errorMsg: string; loading?: boolean }) => !item.errorMsg && !item.loading))) {
      setState({
        file: [...fileList],
      });
    }
  }
  const addAttr = (file: any, key: string, val: any) => {
    // eslint-disable-next-line no-param-reassign
    file[key] = val;
    return file;
  };

  useEffect(() => {
    if (!state.file[state.file.length - 1]?.loading) {
      return;
    }
    const uploadAPI = async () => {
      const formData = new FormData();
      formData.append(autoUploadKeyName, state.file[state.file.length - 1]);
      // 添加参数
      if (typeof data === 'object' && Object.keys(data).length !== 0) {
        const keys = Object.keys(data);
        keys.forEach((key) => {
          formData.append(key, data[key]);
        });
      }
      return sendPostRequest({
        url: action,
        data: formData,
        transformRequest: false,
        transformResponse: false,
        formData: true,
      });
    };
    const currentForm = state.file[state.file.length - 1];
    setUploading(true);
    uploadAPI().then((res: any) => {
      if (res?.code === '0') {
        if (isFunction(onChange)) {
          onChange({
            file: currentForm, info: res?.info, autoUploadKeyName,
          });
        }
        // 上传成功触发函数
        if (isFunction(onSuccessUpload)) {
          onSuccessUpload({
            file: currentForm, info: res?.info, autoUploadKeyName,
          });
        }
      } else {
        // 若上传失败则错误提示
        addAttr(currentForm, 'errorMsg', res?.msg || t('上传错误'));
        if (isFunction(onFailUpload)) {
          onFailUpload([currentForm], res?.msg || t('上传错误'));
        }
      }
    }).catch(() => {
      addAttr(currentForm, 'errorMsg', t('上传错误'));
      if (isFunction(onFailUpload)) {
        onFailUpload([currentForm], t('上传错误'));
      }
      setUploading(false);
    }).finally(() => {
      addAttr(currentForm, 'loading', false);
      setState({ file: [...state.file.slice(0, -1), currentForm] });
      setUploading(false);
    });
  }, [state.file[state.file.length - 1]?.loading]);

  const inputRef = React.useRef<HTMLInputElement>(null);

  const startUpload = () => {
    if (!limit || state.file.length < limit) {
      inputRef.current?.click();
    } else {
      message.error(t('最多只能上传{}个文件', limit));
    }
  };

  useImperativeHandle(ref, () => ({
    startUpload,
  }));

  const addFile = ({ currentFile } : AddFileProps) => {
    if (currentFile.length > 0) {
      // 传入的文件超过限制大小
      if (currentFile && currentFile[0]?.size > maxSize * 1024 * 1024) {
        addAttr(currentFile[0], 'errorMsg', `${t('文件大小需小于')}${maxSize}MB`);
        setState({ file: [...state.file, currentFile[0]] });
        // 清掉input中当前的图片，以便能选择重复的图片
        if (inputRef.current) {
          inputRef.current.value = '';
        }
        return;
      }
      // 执行校验器
      if (validator && isObject(validator)) {
        let validatorFlag = false;
        const keys = Object.keys(validator);
        keys.forEach((key) => {
          if (key === 'ext' && validator[key] && isFunction(validator[key])) {
            // 获取当前文件的后缀
            const ext = getFileSuffix(currentFile[0].name);
            const error = validator[key]!(ext);
            if (error) {
              addAttr(currentFile[0], 'errorMsg', error.message || t('校验未通过'));
              setState({ file: [...state.file, currentFile[0]] });
              // 清掉input中当前的图片，以便能选择重复的图片
              if (inputRef.current) {
                inputRef.current.value = '';
              }
              validatorFlag = true;
              // return;
            }
          } else if (key === 'customValidator' && isFunction(validator[key])) {
            const error = validator[key]!(currentFile[0]);
            if (error) {
              addAttr(currentFile[0], 'errorMsg', error.message || t('校验未通过'));
              setState({ file: [...state.file, currentFile[0]] });
              // 清掉input中当前的图片，以便能选择重复的图片
              if (inputRef.current) {
                inputRef.current.value = '';
              }
              validatorFlag = true;

              // return;
            }
          }
        });
        if (validatorFlag) {
          return;
        }
      }
      // 走到此处表明已经通过校验，可回传currentFile
      // 类型校验，确保onChange为函数
      // 自动上传
      if (autoUpload) {
        const formData = new FormData();
        formData.append(autoUploadKeyName, currentFile[0]);
        // 复制一份同样的文件
        const currentForm = new File([currentFile[0]], currentFile[0].name);
        addAttr(currentForm, 'loading', true);
        setState({ file: [...state.file, currentForm] });
        // 文件上传开始
      } else {
        // 手动上传 - 返回所有通过校验的数组
        if (isFunction(onChange)) {
          onChange([...state.file.filter(
            (item: { errorMsg: string; }) => !item.errorMsg,
          ), currentFile[0]]);
        }
        setState({ file: [...state.file, currentFile[0]] });
      }
      // 清掉input中当前的图片，以便能选择重复的图片
      if (inputRef.current) {
        inputRef.current.value = '';
      }
    }
  };

  return (
    <div
      className={style.uploadContainer}
      ref={uploadContainerRef}
    >
      <Toast icon="loading" show={uploading}>Loading...</Toast>
      <input
        style={{ display: 'none' }}
            // 禁用情况下不允许点击 或请求期间不可点击
        disabled={disabled || state.file.some((item: { loading: any; }) => item?.loading)}
        type="file"
        id="delayUpload"
        ref={inputRef}
        name="upload"
        accept={accept || ''}
        onChange={async () => {
          // inputRef.current?.files是一个对象，只是它第一个key名为0
          const currentFile = inputRef.current?.files
            ? inputRef.current?.files : [];
          addFile({ currentFile });
        }}
      />
      {/* 已上传文件的展示 */}
      {state.file.map((item: any, index: number) => (
        <UploadItem
          // eslint-disable-next-line react/no-array-index-key
          key={index}
          index={index}
          disabled={disabled}
          successFileList={state.file.filter((file: { errorMsg: any; }) => !file.errorMsg)}
          renderResult={renderResult}
          filePathKeyName={filePathKeyName}
          onDelete={(e) => {
            const newFileList = state.file.filter((m: any, indey: number) => indey !== index);
            setState({ file: newFileList });
            if (isFunction(onDelete)) {
              onDelete(item, newFileList.filter((file: { errorMsg: any; }) => !file.errorMsg));
            }
            // 增加类型校验，确保onChange为函数
            if (isFunction(onChange)) {
              onChange(newFileList.filter((file: { errorMsg: any; }) => !file.errorMsg));
            }
          }}
          file={item}
        />
      ))}
      {/* 未达上传文件数量上限 或 未设置数量上限 未自定义隐藏 就显示上传文件图标 */}
      {!hideAddBtn && (!limit || state.file.length < limit) && (
        <div
          className={`${disabled || state.file.some((item: { loading: any; }) => item.loading) ? style.disabledBox : ''}`}
        >
          <div
            className={`${style.chooseFileBox} ${style.hoverFileBox} ${style.normalBorderStyle}`}
            onClick={() => inputRef.current?.click()}
          >
            <div className={`${style.add}`} />
          </div>
        </div>
      )}
    </div>
  );
});

export default UploadPlus;
