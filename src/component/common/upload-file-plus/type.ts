export type FileTypes = 'excel' | 'ppt' | 'pdf' | 'word' | 'text' | 'zip' | 'other'

export type UploadFile = {
  size: number;
  name: string;
  lastModified?: number;
  lastModifiedDate?: Date;
  url?: string;
  percent?: number;
  thumbUrl?: string;
  originFileObj?: File;
  response?: any;
  error?: any;
  linkProps?: any;
  type?: string;
  webkitRelativePath?: string;
  errorMsg?: string;
};

export interface IUploadType {
  file: any;
}

export type Validator = {
  ext?: ((ext: string) => void | Error);
  customValidator?: ((file: File) => void | Error)
}
