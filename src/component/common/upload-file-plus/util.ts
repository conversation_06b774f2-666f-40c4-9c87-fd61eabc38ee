export const objectToString = Object.prototype.toString;

export const toTypeString = (val: unknown) => objectToString.call(val);

export const isFunction = (val: unknown): val is typeof Function => toTypeString(val) === '[object Function]'
    || typeof val === 'function'
    || val instanceof Function;

export const isObject = (val: unknown): boolean => val !== null && typeof val === 'object';

export const isBlob = (val: unknown) => toTypeString(val) === '[object File]';

export const isArrayValueEqual = (pre: any[] = [], current: any[] = []) => {
  if (!pre || pre?.length !== current?.length) {
    return false;
  } if (pre?.length === 0 && current?.length === 0) {
    return true;
  }

  // 循环遍历数组的值进行比较
  for (let i = 0; i < pre?.length; i++) {
    if (current.indexOf(pre[i]) === -1) {
      return false;
    }
  }
  return true;
};

// 获取文件后缀
export const getFileSuffix = (name = '') => {
  const sName = name.split('.');
  // 获取文件后缀
  return sName[sName.length - 1];
};
