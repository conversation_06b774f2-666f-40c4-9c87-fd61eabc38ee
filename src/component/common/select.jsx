import React from 'react';
import PropTypes from 'prop-types';
import {
  Popup, Form, FormCell, CellFooter, Radio, CellBody, Checkbox,
} from 'react-weui/build/packages';

import { t } from '@shein-bbl/react';

function EmptyItem() {
  return (
    <div style={{ height: 30, lineHeight: '30px', textAlign: 'center' }}>
      {t('暂无数据')}
    </div>
  );
}

class Select extends React.Component {
  render() {
    const {
      show, onSelect, selectList, selectValue, onMaskClick, multiple, headerTitle,
      footerContent,
    } = this.props;
    const height = window.innerHeight * 0.75 || 382;
    const rightClick = (v, s) => {
      onSelect(v, s);
    };
    let SelectItem = (
      <Form radio style={{ marginTop: 7 }}>
        {!!headerTitle && (<h4 style={{ display: 'flex', justifyContent: 'center' }}>{headerTitle}</h4>)}
        {
          selectList && selectList.length
            ? selectList.map((v) => (
              <FormCell radio key={v.value}>
                <CellBody>
                  {v.name}
                </CellBody>
                <CellFooter>
                  <Radio
                    name="radio"
                    value={v.value}
                    onChange={() => rightClick(v.value, v)}
                    checked={selectValue === v.value}
                  />
                </CellFooter>
              </FormCell>
            ))
            : <EmptyItem />
        }
        {!!footerContent && (
          <FormCell>
            {footerContent}
          </FormCell>
        )}
      </Form>
    );
    if (multiple) {
      SelectItem = (
        <Form checkbox>
          {
            selectList && selectList.length
              ? selectList.map((v) => (
                <FormCell checkbox key={v.value}>
                  <CellBody>
                    {v.name}
                  </CellBody>
                  <CellFooter>
                    <Checkbox
                      name="checkbox"
                      value={v.value}
                      onChange={(...args) => console.log(...args, 111)}
                      // checked={selectValue.includes(v.value)}
                    />
                  </CellFooter>
                </FormCell>
              ))
              : <EmptyItem />
          }
        </Form>
      );
    }
    return (
      <Popup
        show={show}
        style={{ maxHeight: height, overflowY: 'scroll' }}
        onRequestClose={onMaskClick}
      >
        {SelectItem}
      </Popup>
    );
  }
}

Select.propTypes = {
  show: PropTypes.bool,
  onSelect: PropTypes.func,
  selectList: PropTypes.arrayOf(PropTypes.shape),
  selectValue: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
  ]),
  onMaskClick: PropTypes.func,
  multiple: PropTypes.bool,
  headerTitle: PropTypes.string,
  footerContent: PropTypes.string,
};

Select.defaultProps = {
  show: false,
  selectList: [],
  onSelect: () => {},
  onMaskClick: () => {},
  multiple: false,
};

export default Select;
