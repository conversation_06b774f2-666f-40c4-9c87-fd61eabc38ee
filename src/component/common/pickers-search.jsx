import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { Dialog, Input } from 'react-weui/build/packages';
import { CellsTitle } from 'react-weui/build/packages/components/cell';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import FocusInput from './focus-input';
import { classFocus } from '../../lib/util';
import { inputBlur, preFocus } from './common';


class PickersSearch extends Component {
  constructor(props) {
    super(props);
    this.state = {
      show: true,
      inputValue: '',
      selectValue: '',
      name: '',
    };
    this.onConfirm = this.onConfirm.bind(this);
    this.handleChange = this.handleChange.bind(this);
    this.handleClick = this.handleClick.bind(this);
    this.ref = React.createRef();
  }

  componentDidMount() {
    inputBlur();
  }

  onConfirm(type) {
    const {
      onCancel,
      onOk,
      className,
    } = this.props;
    this.setState({
      show: false, inputValue: '', selectValue: '', name: '',
    });
    if (type === 'cancel') {
      preFocus();
    }
    if (type === 'ok' && onOk) {
      onOk(this.state.selectValue ? String(this.state.selectValue) : '', this.state.name ? String(this.state.name) : '');
      if (className) {
        classFocus(className);
      }
    }
    if (type === 'cancel' && onCancel) {
      onCancel();
    }
  }

  handleChange(e) {
    const value = e.target.value.trim();
    this.setState({ inputValue: value, selectValue: '', name: '' });
  }

  handleClick(value, label) {
    this.setState({ selectValue: value, name: label });
  }

  render() {
    const {
      label,
      placeholder,
      value,
      onClick,
      show,
      only,
      disabled,
      cancelText,
      okText,
      pickerData,
      className,
      inputClassName,
    } = this.props;
    const btns = [{
      label: cancelText || t('取消'),
      type: 'default',
      onClick: () => { this.onConfirm('cancel'); },
    }, {
      label: okText || t('确定'),
      type: 'primary',
      onClick: () => { this.onConfirm('ok'); },
    }];
    const list = pickerData[0].items.filter(v => String(v.label).toLowerCase().includes(this.state.inputValue.toLowerCase()));

    return (
      <Fragment>
        {
          !only ? (
            <FocusInput
              label={label}
              type="text"
              placeholder={placeholder}
              readOnly
              disabled={disabled || false}
              onClick={onClick}
              value={value}
              arrow
              className={inputClassName}
            />
          ) : null
        }

        <Dialog
          className={className}
          title=""
          show={show}
          buttons={btns}
        >
          <div style={{ display: 'flex', paddingTop: '80px' }}>
            <div style={{ width: '100%' }}>
              <Input
                maxLength={20}
                style={{ backgroundColor: 'rgba(189, 166, 166, 0.08)', borderRadius: '5px', padding: '5px 10px' }}
                className="Input"
                placeholder={t('请输入')}
                value={this.state.inputValue}
                onChange={(e) => {
                  this.handleChange(e);
                }}
              />
            </div>
            <div style={{ paddingTop: '7px', color: '#197AFA' }}>
              <Icon name="search" />
            </div>
          </div>
          <div
            style={{ height: '200px', overflow: 'scroll' }}
          >
            {
              list.length ? list
                .map(key => (
                  <div
                    key={key.value}
                    onClick={() => {
                      this.handleClick(key.value, key.label);
                    }}
                  >
                    <CellsTitle
                      style={{ backgroundColor: this.state.selectValue === key.value ? '#eee' : '#fff', borderBottom: '1px solid #eee', fontSize: '18px' }}
                      key={key.value}
                    >{key.label}
                    </CellsTitle>
                  </div>
                )) : 'Not Found'
            }
          </div>
        </Dialog>
      </Fragment>
    );
  }
}

PickersSearch.propTypes = {
  placeholder: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
  onCancel: PropTypes.func,
  onClick: PropTypes.func,
  pickerData: PropTypes.arrayOf(PropTypes.shape),
  show: PropTypes.bool,
  disabled: PropTypes.bool,
  cancelText: PropTypes.string,
  okText: PropTypes.string,
  onOk: PropTypes.func,
};

export default PickersSearch;
