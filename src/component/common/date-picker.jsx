import React from 'react';
import PropTypes from 'prop-types';
import { Picker } from 'react-weui/build/packages/components/picker';
import { t } from '@shein-bbl/react';
import message from './message';

// 日期选择器，超简洁版

// 获取月份、天数的数组
const getArr = (num) => new Array(num).fill(1).map((v, i) => i + 1);
const mon = [4, 6, 9, 11];
// 天数需要特殊处理，例如闰年，30/31天等
const getDateArr = (y, m) => {
  // 1.闰年2月的情况，例如 2020-2
  if (!(y % (y % 100 ? 4 : 400)) && m === 2) {
    return getArr(29);
  }
  if (m === 2) {
    return getArr(28);
  }
  if (mon.includes(m)) {
    return getArr(30);
  }
  return getArr(31);
};
// 处理groups数据
const handleGroupData = (arr, k) => ({
  items: arr.map((v) => ({ key: k, name: v })),
  mapKeys: {
    label: 'name',
  },
});

const currentDate = `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()}`;
class DatePicker extends React.Component {
  constructor(props) {
    super(props);
    const data = props.selected ? props.selected.split('-').map(Number) : currentDate.split('-').map(Number);
    this.state = {
      yearData: handleGroupData(props.yearRange, 'year'),
      monthData: handleGroupData(getArr(12), 'month'),
      dateData: handleGroupData(getDateArr(data[0], data[1]), 'date'),
    };
  }

  render() {
    const {
      leftBtn,
      rightBtn,
      show,
      selected,
      onSelected,
      onCancel,
      infinite,
    } = this.props;
    const {
      yearData,
      monthData,
      dateData,
    } = this.state;
    const selectData = selected ? selected.split('-').map(Number) : currentDate.split('-').map(Number);
    const getSelectIdx = () => {
      const arr = [];
      arr[0] = (yearData.items || []).findIndex((v) => v.name === selectData[0]);
      arr[1] = (monthData.items || []).findIndex((v) => v.name === selectData[1]);
      arr[2] = (dateData.items || []).findIndex((v) => v.name === selectData[2]);
      return arr;
    };

    const handleChange = (i, j, x, y) => {
      const selectYear = yearData.items[(y[0])].name;
      const selectMonth = monthData.items[(y[1])].name;
      if (getDateArr(selectYear, selectMonth) && getDateArr(selectYear, selectMonth).length > 0) {
        this.setState({
          dateData: handleGroupData(getDateArr(selectYear, selectMonth), 'date'),
        });
      }
    };
    const handleOk = (arr) => {
      if (!arr || arr.length !== 3) {
        message.error(t('请选择完整的年月日！'));
        return;
      }
      if (!dateData.items[arr[2]]) {
        message.error(t('请选择日期！'));
        return;
      }
      const valArr = [];
      valArr[0] = yearData.items[arr[0]].name;
      valArr[1] = monthData.items[arr[1]].name < 10 ? `0${monthData.items[arr[1]].name}` : monthData.items[arr[1]].name;
      valArr[2] = dateData.items[arr[2]].name < 10 ? `0${dateData.items[arr[2]].name}` : dateData.items[arr[2]].name;
      if (onSelected) {
        onSelected(valArr);
      }
    };
    return (
      <div>
        {
          show ? (
            <Picker
              show={show}
              onChange={handleOk}
              onGroupChange={(i, j, x, y) => {
                console.info('i, j, x, y', i, j, x, y);
                if (!infinite) {
                  handleChange(i, j, x, y);
                } else {
                  if (x === 1) {
                    if (((j + 1) > monthData.items.length / 3 * 2)) {
                      this.setState({
                        monthData: { ...monthData, items: monthData.items.concat(getArr(12).map((v) => ({ key: 'month', name: v }))) },
                      });
                    }
                  }
                  if (x === 2) {
                    if (((j + 1) > dateData.items.length / 3 * 2)) {
                      const data = selected ? selected.split('-').map(Number) : currentDate.split('-').map(Number);
                      this.setState({
                        dateData: { ...dateData, items: dateData.items.concat(getDateArr(data[0], data[1]).map((v) => ({ key: 'date', name: v }))) },
                      });
                    }
                  }
                }
              }}
              defaultSelect={getSelectIdx()}
              groups={[yearData, monthData, dateData]}
              onCancel={onCancel}
              lang={{ leftBtn, rightBtn }}
            />
          ) : null
        }
      </div>
    );
  }
}

const currentYear = new Date().getFullYear();

DatePicker.defaultProps = {
  leftBtn: t('取消'),
  rightBtn: t('确定'),
  show: false,
  selected: `${currentYear}-${new Date().getMonth() + 1}-${new Date().getDate()}`,
  yearRange: [currentYear, currentYear - 1, currentYear - 2], // 年 的时间范围，默认为当前年到前两年
};

DatePicker.propTypes = {
  leftBtn: PropTypes.string,
  rightBtn: PropTypes.string,
  show: PropTypes.bool,
  selected: PropTypes.string,
  onSelected: PropTypes.func,
  onCancel: PropTypes.func,
  yearRange: PropTypes.arrayOf(PropTypes.number),
};

export default DatePicker;
