let disFocus = true;
let preEle;

export const inputFocus = (e) => {
  preEle = e.target;
  return new Promise(r => {
    setTimeout(() => {
      const focusEle = document.activeElement;
      // 当前聚焦的不是输入框的时候，依然报错聚焦之前的输入框
      if (focusEle.localName !== 'input' && preEle && disFocus) {
        if (!document.querySelectorAll('.modalBlurInput').length) {
          preEle.focus();
        }
        r(1);
        // 当前聚焦的是输入框的时候
      } else {
        disFocus = true;
        // 为了清除上一个输入框的active状态
        r(0);
      }
    }, 0);
  })

};

export const inputBlur = () => {
  const ele = document.activeElement;
  if (ele && ele.localName === 'input') {
    ele.blur();
    preEle = ele;
  }
};

export const preFocus = () => {
  if (preEle) {
    preEle.focus();
  }
};

// 增加水印
export const waterMark = (text, width, height) => {
  width = width || window.innerWidth;
  height = height || window.innerHeight;
  var canvas = document.createElement('canvas');
  var body = document.body;
  body.appendChild(canvas);
  canvas.width = width; //画布的宽
  canvas.height = height;//画布的高度
  canvas.style.display = 'none';

  var ct = canvas.getContext('2d');
  ct.rotate(-20 * Math.PI / 180);
  ct.fillStyle = '#dbdbdb';//画布里面文字的颜色
  ct.font = "13px Microsoft JhengHei"; //画布里面文字的字体
  const wLen = Math.ceil(width / 100);
  const hLen = Math.ceil(height / 60);
  function count(num) {
    for (let i = 3; i < 1000; i++) {
      num = num - i;
      if (num <= 0) {
        return i - 2;
      }
    }
  }

  let i = -count(hLen);

  for (; i <= wLen; i++) {
    for (let j = 1; j <= hLen; j++) {
      ct.fillText(text, i*100, j*60);
    }
  }
  canvas.textAlign = 'left'; //画布里面文字的水平位置
  canvas.textBaseline = 'Middle'; //画布里面文字的垂直位置
  var wmImg = canvas.toDataURL("image/png");
  return wmImg;
};

/**
 * 吐司提示：在页面底部提示相关信息
 * <AUTHOR>
 * @DateTime 2018-9-10
 * @param     msg |必传 | 成功提示话语
 * @param   duration | 3000 | 默认3秒后自动销毁隐藏；值为0时代表不销毁，一直显示
 * @param   bottomVal | 70px | 距离页面底部的高度，值可为px值，也可为百分比等
 */
export const showToast = function (msg, duration = 3000, bottomVal = '70px') {
  document.querySelectorAll('.globalToast').forEach(v => v.remove());
  const ele = document.createElement('div');
  ele.textContent = msg;
  ele.className = 'globalToast';
  ele.style.bottom = bottomVal;
  ele.fadeOut = function () {
    const e = this;
    setTimeout(() => {
      e.remove();
    }, duration);
  };
  document.body.appendChild(ele);
  // duration为0时，则表示不自动销毁关闭
  if (duration !== 0) {
    ele.fadeOut();
  }
};


const RefreshPage = {};

(function (r) {
  let _startY = 0;
  let container = null, ready = true;
  let refreshCb = () => {};
  const eventListenerOptions = { passive: true };
  function isReady() {
    return !document.body.classList.contains('refreshing') && ready;
  }

  function bindTouchstart(e) {
    _startY = e.touches[0].pageY;
  }

  function bindTouchmove(e) {
    const y = e.touches[0].pageY;
    if (container.scrollTop === 0 && y > (_startY + 30) && isReady()) {
      document.body.classList.add('refreshing');
      refreshCb();
      ready = false;
    }
  }

  const bindRefreshPage = (c = '#container', fn) => {
    container = document.querySelector(c);
    refreshCb = fn;
    const style = container.getAttribute('style');
    container.setAttribute('style', `${style}overscroll-behavior: contain;`);
    container.addEventListener('touchstart', bindTouchstart, eventListenerOptions);
    container.addEventListener('touchmove', bindTouchmove, eventListenerOptions);
  };
  const removeRefreshPage = (c = '#container') => {
    if (container) {
      container.removeEventListener('touchstart', bindTouchstart);
      container.removeEventListener('touchmove', bindTouchmove);
    }
  };

  const refreshEnd = () => {
    document.body.classList.remove('refreshing');
    setTimeout(() => {
      ready = true;
    }, 0);
  };

  r.bindRefreshPage = bindRefreshPage;
  r.removeRefreshPage = removeRefreshPage;
  r.refreshEnd = refreshEnd;
})(RefreshPage);

export const { bindRefreshPage, removeRefreshPage, refreshEnd } = RefreshPage;
