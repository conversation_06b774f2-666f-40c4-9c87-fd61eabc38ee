import React from 'react';
import PropTypes from 'prop-types';
import style from '../style.css';

const TableHeader = (props) => {
  const {
    columns,
    widthArr,
  } = props;
  const header = [];
  columns.forEach((v, index) => {
    const {
      width = 10,
      title,
      dataIndex,
    } = v;
    const th = (
      <th className={style.tableItem} key={dataIndex} style={{ width: widthArr[index] }}>
        {title}
      </th>
    );
    header.push(th);
  });
  return (
    <tr className={style.tableHeader}>
      {header}
    </tr>
  );
};

TableHeader.propTypes = {
  columns: PropTypes.arrayOf(PropTypes.shape),
  widthArr: PropTypes.arrayOf(PropTypes.string),
};

export default TableHeader;
