import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import Icon from '@shein-components/Icon';
import style from '../style.css';

class Check extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      checked: props.checked,
    };
  }

  // 触发条件：内部state变化或外部props变化都会触发
  static getDerivedStateFromProps(props, state) {
    if (props.checked !== state.checked) {
      return {
        checked: props.checked,
      };
    }
    return null;
  }

  render() {
    const { disabled, onChange } = this.props;
    const { checked } = this.state;
    return (
      <span
        className={classnames(style.check, checked ? style.checked : '', disabled ? style.checkDisable : '')}
        onClick={() => {
          if (disabled) {
            return;
          }
          this.setState({ checked: !checked });
          onChange(!checked);
        }}
      >
        <Icon name="check" />
      </span>
    );
  }
}

Check.defaultProps = {
  checked: false,
  disabled: false,
  onChange: () => {
    // 父级回调, 将当前value传出去
  },
};

Check.propTypes = {
  checked: PropTypes.bool,
  disabled: PropTypes.bool,
  onChange: PropTypes.func,
};

export default Check;
