import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { Picker } from 'react-weui/build/packages/components/picker';
import { t } from '@shein-bbl/react';
import FocusInput from './focus-input';

class Pickers extends Component {
  render() {
    const {
      label,
      placeholder,
      defaultValue,
      value,
      onChange,
      leftBtn,
      rightBtn,
      pickerData,
      onCancel,
      onClick,
      show,
      only,
      disabled,
      className,
      keepFocus,
      isRequired,
      allowClear,
      onClear,
    } = this.props;

    const pickersChange = (selected) => {
      const res = pickerData[0].items[selected[0]];
      onChange(res);
    };

    const defaultIndex = pickerData[0].items || [].findIndex((v) => v.value === defaultValue);

    return (
      <>
        {
          !only ? (
            <FocusInput
              label={label}
              type="text"
              placeholder={placeholder}
              className={className || ''}
              readOnly
              disabled={disabled || false}
              onClick={onClick}
              value={value}
              keepFocus={keepFocus}
              isRequired={isRequired}
              arrow
              allowClear={allowClear}
              onClear={onClear}
            />
          ) : null
        }

        <Picker
          defaultSelect={[defaultIndex]}
          key={defaultIndex}
          onChange={pickersChange}
          lang={{ leftBtn, rightBtn }}
          groups={pickerData}
          onCancel={onCancel}
          show={show}
        />
      </>
    );
  }
}

Pickers.defaultProps = {
  leftBtn: t('取消'),
  rightBtn: t('确定'),
  show: false,
  keepFocus: true,
};

Pickers.propTypes = {
  label: PropTypes.string,
  defaultValue: PropTypes.string,
  only: PropTypes.bool,
  placeholder: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
  onCancel: PropTypes.func,
  onClick: PropTypes.func,
  leftBtn: PropTypes.string,
  rightBtn: PropTypes.string,
  pickerData: PropTypes.arrayOf(PropTypes.shape),
  show: PropTypes.bool,
  disabled: PropTypes.bool,
  className: PropTypes.string,
  keepFocus: PropTypes.bool,
  isRequired: PropTypes.bool,
  allowClear: PropTypes.bool,
  onClear: PropTypes.func,
};

export default Pickers;
