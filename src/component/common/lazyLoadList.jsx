/**
 * LazyLoadList 是List的变种，增加了loadMore功能
 * 用法和List一样，只是多了一个loadMore属性
 * 只是List的场景太广，不好加入loadMore功能，所以单独写了一个组件
 */

import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import styles from '../style.css';
import list from './list.css';
import Check from './check';

const defaultStyle = {
  height: 0,
  flex: '1 1 auto',
  display: 'flex',
  flexDirection: 'column',
};

function ListItem(props) {
  const {
    rows,
    rowData,
    itemClass,
    itemStyle,
    onRowClick,
    isProgressShow,
  } = props;

  if (rows.some((item) => !Array.isArray(item))) {
    throw new Error(t('rows中每一项必须为数组'));
  }

  return (
    <div
      className={`${styles.listItem} ${itemClass} ${isProgressShow && (!(rowData.needUpperNum === 0 || rowData.actualNum === 0) || rowData?.showProgress) ? list.progressBg : ''}`}
      style={itemStyle}
      onClick={() => { onRowClick(rowData); }}
    >
      {
        // 进度条
        isProgressShow && (!(rowData.needUpperNum === 0 || rowData.actualNum === 0) || rowData?.showProgress) ? <div className={list.progressColor} style={{ width: `${(rowData.actualNum / (rowData.actualNum + rowData.needUpperNum)) * 100}%` }} /> : ''
      }
      {
        rows.map((item, index) => (
          <div className={styles.row} key={index}>
            {
              item.map((v) => {
                let flex;
                const { width } = v;
                if (width && (typeof width === 'number')) {
                  flex = `1 1 ${width}%`;
                } else if (width && (typeof width === 'string')) {
                  flex = `1 1 ${width}`;
                }
                if (!v.render) {
                  return null;
                } else if (typeof v.render !== 'function') {
                  if (rowData[v.render] === 0) {
                    return (
                      <div style={{ flex }} key={v.render}>
                        <span className={styles.lable}>{v.title}</span>
                        <span className={styles.value} style={v.itemRenderStyle}>0</span>
                      </div>
                    );
                  }
                  return (
                    <div style={{ flex }} key={v.render}>
                      <span className={styles.lable}>{v.title}</span>
                      <span className={styles.value} style={v.itemRenderStyle}>{rowData[v.render] || v.default}</span>
                    </div>
                  );
                } else if (typeof v.render === 'function') {
                  return (
                    <div style={{ flex }} key={v.render}>
                      {v.render(rowData)}
                    </div>
                  );
                }
              })
            }
          </div>
        ))
      }
    </div>
  );
}

ListItem.propTypes = {
  rows: PropTypes.arrayOf(PropTypes.array),
  rowData: PropTypes.shape(),
  itemClass: PropTypes.string,
  itemStyle: PropTypes.shape(),
  onRowClick: PropTypes.func,
  isProgressShow: PropTypes.bool,
};

class LazyLoadList extends React.Component {
  constructor(props) {
    super(props);
    this.srcollBoxDom = React.createRef();
    this.getCanScrollAndRowHeight = this.getCanScrollAndRowHeight.bind(this);
    this.handleAutoScroll = this.handleAutoScroll.bind(this);
  }

  componentDidMount() {
    this.handleAutoScroll();

    const { loadMore } = this.props;

    const { canScroll } = this.getCanScrollAndRowHeight();

    if (this.srcollBoxDom && canScroll) {
      this.srcollBoxDom.current.addEventListener('scroll', (e) => {
        const { clientHeight, scrollHeight, scrollTop } = e.target;
        const isBottom = scrollTop + clientHeight + 20 > scrollHeight;
        if (isBottom) {
          loadMore();
        }
      });
    }
  }

  componentDidUpdate() {
    this.handleAutoScroll();
  }

  getCanScrollAndRowHeight() {
    const scrollBoxDom = this.srcollBoxDom.current;
    const { data } = this.props;
    if (scrollBoxDom && scrollBoxDom.children.length > 0) {
      const srcollBoxHeight = scrollBoxDom.clientHeight;
      const rowHeight = scrollBoxDom.children[0].clientHeight;
      return { rowHeight, canScroll: srcollBoxHeight < rowHeight * data.length };
    }
    return { rowHeight: 0, canScroll: false };
  }

  handleAutoScroll() {
    const { autoScroll, scrollRowNums } = this.props;
    const { rowHeight, canScroll } = this.getCanScrollAndRowHeight();
    const scrollBoxDom = this.srcollBoxDom.current;
    if (autoScroll && canScroll && scrollRowNums > 0 && scrollBoxDom) {
      scrollBoxDom.scrollTop = rowHeight * scrollRowNums;
    }
  }

  render() {
    const {
      rows,
      data,
      style,
      className,
      header,
      rowStyleOrClass,
      checkListItemClass,
      separate,
      checkbox,
      onSelected,
      onRowClick,
    } = this.props;

    if (!Array.isArray(data)) {
      throw new Error(t('data和rows必须为数组!'));
    }
    if (header && !React.isValidElement(header)) {
      throw new Error(t('header必须是react元素!'));
    }
    const type = rowStyleOrClass && (typeof rowStyleOrClass);
    if (type && type !== 'object' && type !== 'function' && type !== 'string') {
      throw new Error(t('itemStyle必须为object、function或string!'));
    }
    data.forEach((item, i) => { item.index = i; });
    return (
      <div style={({ ...defaultStyle, ...style })} className={className}>
        <div className={styles.listHeader}>
          {header}
        </div>
        <div className={styles.listOverflow} ref={this.srcollBoxDom}>
          {
            data.map((item, index, dataArr) => {
              const evenBg = {
                backgroundColor: '#F9F9F9',
              };

              const oddBg = {
                backgroundColor: '#FFFFFF',
              };

              let itemStyle = {};

              if (separate) {
                itemStyle = index % 2 === 1 ? oddBg : evenBg;
              }

              let itemClass = '';
              if (type && type === 'function') {
                const classOrStyle = rowStyleOrClass(item, index);
                if (typeof classOrStyle === 'string') {
                  itemClass = classOrStyle;
                } else if (typeof classOrStyle === 'object') {
                  itemStyle = classOrStyle;
                }
              } else {
                switch (type) {
                  case 'object':
                    itemStyle = rowStyleOrClass;
                    break;
                  case 'string':
                    itemClass = rowStyleOrClass;
                    break;
                  default:
                    break;
                }
              }
              // checkbox为true的情况
              if (checkbox) {
                return (
                  <div
                    key={index}
                    style={itemStyle}
                    className={`${styles.checkListItem} ${itemClass}`}
                    onClick={() => {
                      if (item.disabled) {
                        return;
                      }
                      item.checked = !item.checked;
                      onSelected(dataArr.filter((i) => i.checked));
                      // 强制更新，触发Check组件的重新渲染
                      this.forceUpdate();
                    }}
                  >
                    <span className={styles.checkListItemCheck}>
                      <Check
                        disabled={item.disabled}
                        checked={item.checked}
                      />
                    </span>
                    <div className={`${styles.checkListItemList} ${checkListItemClass}`}>
                      <ListItem
                        rows={rows}
                        rowData={item}
                        itemStyle={itemStyle}
                        itemClass={itemClass}
                        onRowClick={onRowClick}
                      />
                    </div>
                  </div>
                );
              }
              return (
                <ListItem
                  key={index}
                  rows={rows}
                  rowData={item}
                  itemStyle={itemStyle}
                  itemClass={itemClass}
                  onRowClick={onRowClick}
                  {...this.props}
                />
              );
            })
          }
        </div>
      </div>
    );
  }
}

LazyLoadList.propTypes = {
  rows: PropTypes.arrayOf(PropTypes.array),
  data: PropTypes.arrayOf(PropTypes.shape()),
  style: PropTypes.shape(),
  className: PropTypes.string,
  checkListItemClass: PropTypes.string,
  header: PropTypes.element,
  rowStyleOrClass: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.func,
    PropTypes.object,
  ]),
  separate: PropTypes.bool,
  checkbox: PropTypes.bool,
  onSelected: PropTypes.func,
  onRowClick: PropTypes.func,
  autoScroll: PropTypes.bool,
  scrollRowNums: PropTypes.number,
  isProgressShow: PropTypes.bool,
  loadMore: PropTypes.func,
};

LazyLoadList.defaultProps = {
  rows: [],
  data: [],
  style: {},
  separate: false, // 是否显示斑马线
  checkbox: false, // 是否显示checkbox
  onSelected: (arr) => {
    // 将选中[包括disabled]的数据传出去，用户从data传进来的对象数组【包括id,value等其它字段】
    console.log('LazyLoadList', arr);
  },
  onRowClick: () => { },
  autoScroll: false, // 是否自动滚动到指定行
  scrollRowNums: 0, // 滚动到第几行
  isProgressShow: false, // 显示背景色进度条
  loadMore: () => { }, // 加载更多
};

export default LazyLoadList;
