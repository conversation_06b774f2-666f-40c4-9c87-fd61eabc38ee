import React from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Cells, Cell, CellBody, CellFooter } from 'react-weui/build/packages/components/cell';
import style from '../style.css';


const CellsMenu = (props) => {
  const {
    cells,
    access,
    drawingTaskNum,
  } = props;
  return (
    <Cells>
      {
        cells.map(v => (
          <Cell href={v.href} access={access} key={v.href}>
            <CellBody>
              {v.title}
            </CellBody>
            <CellFooter>
              {v.footer}
              {v.showRedDotted ? <span className={style.redDottedSpan}>{null}</span> : null}
              {v.href === '#/refund-scan/receive-drawing-task' && drawingTaskNum && (
                <span style={{ color: '#FF8C00', fontWeight: 'bold' }}>{drawingTaskNum['1']}</span>
              )}
              {v.href === '#/refund-scan/receive-drawing-task/2' && drawingTaskNum && (
                <span style={{ color: '#FF8C00', fontWeight: 'bold' }}>{drawingTaskNum['2']}</span>
              )}
              {v.href === '#/refund-scan/receive-drawing-task/3' && drawingTaskNum && (
                <span style={{ color: '#FF8C00', fontWeight: 'bold' }}>{drawingTaskNum['3']}</span>
              )}
            </CellFooter>
          </Cell>
        ))
      }
    </Cells>
  );
};

CellsMenu.defaultProps = {
  cells: [
    {
      title: 'test1',
      href: '#/example/test1',
      footer: '123',
    },
    {
      title: 'test2',
      href: '#/example/test2',
    },
    {
      title: 'test3',
      href: '#/example/test3',
    },
    {
      title: 'test4',
      href: '#/example/test4',
    },
    {
      title: 'test5',
      href: '#/example/test5',
    },
  ],
  access: true,
};

CellsMenu.propTypes = {
  cells: PropTypes.arrayOf(PropTypes.shape),
  access: PropTypes.bool,
  drawingTaskNum: PropTypes.shape(),
};

export default i18n(CellsMenu);
