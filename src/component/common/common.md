# common组件

## header.jsx
> 组件的头部包涵标题，基础的操作按钮

基础使用

````jsx
    import Header from '../common/header.jsx';

    const Container = (props) => {
    	return (
    	<div>
    		<Header title="测试标题" />
    	</div>
    	)
    }
````

基本的操作按钮使用

````jsx
	 import Header from '../common/header.jsx';

    const Container = (props) => {
    	return (
    	<div>
    		<Header title="测试标题">
    			<div onClick={() => console.log('测试')}>
    				测试按钮
    			</div>
    		</Header>
    	</div>
    	);
    }
````

by: <EMAIL>


## footer.jsx
> 组件的底部，包涵返回按钮，以及其他按钮

基础使用

````jsx
    import Footer from '../../common/footer';

    const Container = (props) => {
        return (
            <Footer />
        );
    }
````

支持传入按钮（暂只优化一个，需要使用footer-btn.jsx）

````jsx
    import Footer from '../../common/footer';
    import FooterBtn from '../../common/footer-btn';

    const Container = (props) => {
    	return (
        <Footer>
          <FooterBtn
            onClick={() => {
            console.log('点击了查看')
            }}
           >
            查看
          </FooterBtn>
        </Footer>)
    }
````

可以通过入参`beforeBack`，来控制返回前的操作，并且将返回的函数入参传给`beforeBack`。

````jsx
    import Footer from '../.../common/footer';

    return (
    <Footer
        dispatch={dispatch}
        beforeBack={(back) => {
            console.log('点击了返回');
            back();
        }}
    />
    )
````

by: <EMAIL>

## footer-btn.jsx
> 用来传入footer.jsx的按钮组件

基础使用
````jsx
    import Footer from '../../common/footer';
    import FooterBtn from '../../common/footer-btn';

    const Container = (props) => {
    	return (
        <Footer dispatch={dispatch}>
            <FooterBtn
                onClick={() => {
                    console.log('点击了查看')
                }}
            >
                查看
            </FooterBtn>
        </Footer>
      )
    }
````

by: <EMAIL>

## focus-input.jsx
> 封装了原来的input和label等组件，提供了`onPressEnter`方法，并且进行了保持聚焦操作

基础使用(需要使用react-weui的Form)

````jsx
	import { Form } from 'react-weui/build/packages/components/form';
	import { FocusInput } from '../../common/focus-input';

	const Container = () => {
    return (
      <Form>
        <FocusInput
          placeholder="请扫描"
          autoFocus
          value={goodsSn}
          keepFocus={false}
          onChange={() => {
            console.log('change');
          }}
          onPressEnter={() => {
            console.log('onPressEnter');
          }}
        >
          // 可以使用label标签，也可以写label属性
          <label>
            商品条码
          </label>
        </FocusInput>
      </Form>
    )
	}

````

属性 | 默认值 | 描述
 --- | --- | ---
 autoFocus | false | 自动聚焦
 keepFocus | true | 保持聚焦
 label | 空 |

### 聚焦border-bottom还未实现

by: <EMAIL>

## list.jsx
> 封装list信息展示组件

````jsx
import List from '../../common/list';

const rows = [
  [
    {
      title: '库位',
      render: 'location',
      width: 70,
    },
    {
      title: '短拣数',
      render: 'num'
      width: 30,
    }
  ],
  [
    {
      title: 'SKC',
      render: 'skc'
    }
  ],
  [
    {
      title: '尺码',
      render: 'size',
      width: 25,
    },
    {
      title: '姓名',
      render: 'name',
      width: 25,
    },
    {
      title: '年龄',
      render: 'age'
      width: 25,
    },
    {
      title: '处理',
      width: 25,
      render: (v, i) => {
          return (
            <span onClick={() => { console.log('处理')}}>处理</span>
          )
      }
    }
  ]
]

const data = [
  {
    id: 1,
    skc: '123',
    location: 'kuwei1',
    size: 'XL',
    num: 12,
    name: 'wp',
    age: 28
  },
  {
    id: 2,
    skc: '456',
    location: 'kuwei2',
    size: 'M',
    num: 13,
    name: 'wp',
    age: 28,
  },
  {
    id: 3,
    skc: '789',
    location: 'kuwei3',
    size: 'XXL',
    num: 14,
    name: 'wp',
    age: 28
  },
]

<List
  rows={rows}
  data={data}
  style={{ height: 300, overflowY: 'auto' }}
  className={'className'}
  rowStyleOrClass={(rowData, index) => {
    console.log(rowData, index);
    return 'className'
  }}
/>

````

属性 | 默认值 | 描述
 --- | --- | ---
 data | [] | 列表数据源
 rows | [] | 定义每行字段名，类似于table组件的columns
 style | {} | 外层容器style
 className | null | 外层容器class
 header | null | 一个自定义的react元素
 rowStyleOrClass | null | 给每一行加样式。传string，表示className;传function,必须返回一个string,表示className;还可以传object,表示style
 checkbox | false | 是否出现复选框
 onSelected | 无 | 配合checkbox，将当前勾选数据传出去--对象数组

 rows中width: 可以传number,如30，表示：30%。还可以传string, 如'30px'(px必须有)，表占30px

by: <EMAIL>

## menus.jsx
> 首页的菜单，在`root.jsx`中使用

by: <EMAIL>


## pickers.jsx
> picker的封装，将FocusInput 和 Picker封装在内部，减少引入代码

基础使用

````jsx
	import Pickers from '../../common/pickers';
	import { Form } from 'react-weui/build/packages/components/form'

	const pickerData = [ /* 示例数据查看 example/test1 */ ]

	const Container = (props) => {
		const { person, show } = props;
		return (
			<Form>
				<Pickers
					value={person}
                    label="测试"
                    placeholder="请选择内容"
                    onClick={() => store.changeData({ data: { show: true } })}
                    onChange={(select) => {
                    store.changeData({ data: { show: false, person: select.label } });
                    }}
                    show={show}
                    pickerData={pickerData}
                    onCancel={() => store.changeData({ data: { show: false } })}
				/>
			</Form>
		)
	}

````

属性 | 默认值 | 描述
 --- | --- | ---
 value |空 | 显示的内容
 label | 空 | 显示内容的标签
 placeholder | 空 | 未选择的显示
 onClick | 空 | 点击事件(需要将show变为true)
 onChange | 空 | 弹出选择点击确定的事件
 show | false | 弹出层的显示
 pickerData | [] | 弹出的选项内容
 onCancel | [] | 点击取消的事件
 leftBtn | 取消 | 左边按钮的文本
 rightBtn | 确定 | 右边按钮的文本

 by: <EMAIL>

## pop-sheet.jsx
> 弹出的菜单，对ActionSheet封装

基础使用

````jsx
	import { Button } from 'react-weui/build/packages/components/button';
	import PopSheet from '../../common/pop-sheet';


	const Container = (props) => {
		const items = [/* 示例数据查看 example/test1 */]
		 return (
		 	<div>
		 		<Button
		 			onClick={() => store.changeData({ data: { show: true } })}
		 		>
		 			测试
		 		</Button>
		 		<PopSheet
		 			onClick={(v) => {
            console.log(v);
            store.changeData({ data: { person: v.label, show: false } });
          }}
          onClose={() => {
            store.changeData({ data: { show: false } });
          }}
          cancelBtn
          menus={items}
          show={show}
		 		/>
		 	</div>
		 )
	}

````

by: <EMAIL>

## row-info.jsx
> 信息展示页面

基础使用

````jsx
    import RowInfo from '../../common/row-info';

    const Container = () => {
        return (
            <Form>
                <RowInfo
                    label="测试"
                    value="222"
                    type="info"
                />

                <RowInfo
                    data={[
                        {
                            label: '测试1',
                            value: '222'，
                        }，
                        {
                            label: '测试2',
                            value: '333',
                            type: 'warn'
                        }
                    ]}
                />
            </Form>
        )
    }
````
## modal.jsx
> 错误提示modal 和 classFocus,是对react-weui中dialog组件的封装，有modal.error,success,confirm法法

基础使用

````jsx
import modal from '../../common/modal';

<Button
  onClick={() => {
    modal.error({
      title: 'title',
      content: 'content',
      className: 'classFocus',
      type: 'ios',
      onOk: () => {console.log(11)}
    })
}}
>
  dialog
</Button>
````
属性 | 默认值 | 描述
 --- | --- | ---
 title |null | (和content一样)dialog中显示的内容
 content | null | (和title一样)dialog中显示的内容
 className | null | 关闭dialog后焦点集中，
 onOk | 空 | 点击确认后回调函数, 当传入buttons时，onOk无效
 onCancel | 空 | 点击取消后回调函数, 当传入buttons时，onCancel无效,且只有在confirm时有效
 cancelText | 空 | modal.confirm时，定义取消按钮的文字
 okText | 空 | modal.confirm时，定义确认按钮的文字
 buttons | [] | (不建议使用)自定义dialog的‘footer’, 与react-weui中dialog的buttons用法一致

Modal有confirm、success、error三种方法

 by: 王培@shein.com

## tag.jsx
> 展示tag或tags的组件

基础使用

````jsx
    import Tag from '../../common/tag';

    const Container = (props) => {
        retun (
            <div>
                <Tag type="error" content="紧急">
                <Tag type="error">紧急</Tag>
                <Tag dataSource={['张姗姗', '亚历山大', '跳跳糖', '冲鸭']} />
            </div>
        )
    }

````
属性 | 默认值 | 描述
 --- | --- | --- 
 type |无 | 
 content | 无 | 
 dataSource | [] | 不传值则默认是单个；传数组值则不需传type和content 

## pages.jsx
> 滚动

基础使用
````jsx
import { pages } from '../../common/pages';
const { Page } = pages;

// list滚动
return (
  <Page>
    <Header title="库位库存查询" />

    <Form>
      <FocusInput>
        <label>库位</label>
      </FocusInput>
    </Form>
    <Form>
      <FocusInput>
        <label>条码</label>
      </FocusInput>
    </Form>

    <CellsTitle>查询结果</CellsTitle>

    <List
      rows={rows}
      data={list}
    />

    <Footer
      dispatch={dispatch}
    />
  </Page>
);

// header以下的内容滚动
return (
  <div>
    <Header title="库位库存查询" />
    <Page flex={false} diff={100}>
      <Form>
        <FocusInput>
          <label>库位</label>
        </FocusInput>
      </Form>
      <Form>
        <FocusInput>
          <label>条码</label>
        </FocusInput>
      </Form>

      <CellsTitle>查询结果</CellsTitle>

      <div style={{ height: 400 }}>xx</div>
    </Page>
    <Footer
      dispatch={dispatch}
    />
  </div>

);
````
属性 | 默认值 | 描述
 --- | --- | ---
 false |true | 默认为true，为flex布局，在list滚动情况下不需要设置。如果footer一下内容滚动，必须设为false。
 diff | 56(number) | 56为footer高度，在list滚动的情况下不需要设置该值。如果footer一下内容滚动，需要设置为100（根据具体需求），100 = footer高度+header高度。window.innerHeight - diff = 滚动区域的高度

 by: 王培@shein.com


## table.jsx
> 表格，类似antd

````jsx
const Container = (props) => {
	return (
    <Table
      columns={[
      {
      title: '序号',
      dataIndex: 'test1',
      width: 10,
      },
      {
      title: '大箱号',
      dataIndex: 'test2',
      width: 20,
      },
      {
      title: '运单号',
      dataIndex: 'test3',
      width: 20,
      },
      ]}
      dataSource={[
      {
      test1: '1',
      test2: '**********',
      test3: '**********',
      },
      {
      test1: '2',
      test2: '**********',
      test3: '**********',
      },
      {
      test1: '3',
      test2: '**********',
      test3: '**********',
      },
      ]}
    />
	)
}
````
 by: <EMAIL>

## drag-circle.jsx
> 可拖拽的悬浮图标，可用于上传报错等

基础使用

````jsx
	import { DragCircle } from '../../common';

	/* 示例查看 example/test1, example/test4 */

	const Container = (props) => {
		const handleClick = () => {
          console.log('上传报错');
        };
		return (
			<div>
				<DragCircle
                    onClick={() => handleClick()}
                    icon="announcement"
                    size={64}
                    backgroundColor="#FF9636"
                 />
			</div>
		)
	}

````
属性 | 默认值 | 描述
 --- | --- | ---
 onClick |空 | 点击事件
 icon | send | 悬浮图标内icon的名称，必须是@shein-components/Icon下有的图标
 size | 32 | 悬浮图标的大小
 backgroundColor | #0059ce | 悬浮圆形的背景色，默认为主题色
 color | #ffffff | shein icon的颜色，默认白色

 by: <EMAIL>

## switch-icon.jsx
> 开关组件

基础使用

````jsx
	import { SwitchIcon } from '../common';
	
	<SwitchIcon value={isShowQuickEntry} size="small" />
	
````

属性 | 默认值 | 描述
 --- | --- | --- 
 value |false | 开关是否打开
 size  | middle | 开关整体大小; 默认值 middle，其它值：small，large
 onClick | 空 | 点击事件(将当前value传出去)

 by: <EMAIL>
 
 ## icon-title-list.jsx
 > 菜单标题列表组件
 
 基础使用
 
 ````jsx
 	import { IconTitleList } from '../common';
 	
 	<IconTitleList
        dataSource={quickEntryList}
        editable
        onClick={(obj, idx) => {
          quickEntryList.splice(idx, 1);
          this.setState({ quickEntryList: [...quickEntryList] });
        }}
      />
 	
 ````
 
 属性 | 默认值 | 描述
  --- | --- | --- 
  dataSource |[] | 列表数据对象数组，对象字段{iconName: 'icon名', title: '标题', rule: '跳转地址[可不传]/new-pda/back-warehouse'}
  editable  | false | 是否(删除)编辑状态; 编辑状态不会跳转
  onClick | 空 | 将当前点击的icon标题对应对象和数组下标传出去
 
  by: <EMAIL>
  
  ## accordion.jsx
   > 手风琴组件
   
   基础使用
   
   ````jsx
   	import { IconTitleList } from '../common';
   	
   	<Accordion
        dataSource={accordionData}
        onSelected={(arr) => {
          const quickEntryListArr = arr.map((obj) => {
            // 只存储必要的字段到云服务器
            const { rule, title, parentIconName } = obj;
            return { rule, title, iconName: parentIconName };
          });
          this.setState({ quickEntryList: quickEntryListArr });
        }}
        expendOne
        multiple
        badge
        limitNum={6}
        limitNumTip=''
      />
   ````
   
   属性 | 默认值 | 描述
    --- | --- | --- 
    dataSource |[] | 对象数组，具体字段要求看accordion.jsx文件
    expendOne  | false | 是否同一时刻，只能展开一个手风琴
    multiple  | false | 子级菜单是否单选
    badge  | false | 是否显示选中数量的徽标【multiple需为true】
    limitNum  | 0 | 限制全部子级菜单的最多选中个数，0代表不限制个数；为1时__整个手风琴只能选一个，所以multiple 则为false【只能单选】
    limitNumTip  | '' | 当limitCheckedNum达到上限时，弹窗提示话语。有设置limitNum且limitNum大于1时，需要加上对应提示话语
    onSelected | 空 | 将全部选中的子级数组传回父组件，将用户从dataSource传进来的数据传回去【包括id,value等其它字段】
   
    by: <EMAIL>

## check.jsx
> 补充checkbox或radio组件

基础使用

````jsx
	import { Check } from '../common';
	
    <Form style={{ paddingBottom: 6 }}>
      <div
        onClick={() => {
          this.setState({ isAllChecked: !isAllChecked });
          handleItemsChecked(!isAllChecked);
        }}
        style={{ marginBottom: 10 }}
      >
        <Check
          checked={isAllChecked}
        /> 全选
      </div>
      {
        checkedList.map(i => (
          <span
            key={i.text}
            style={{ marginRight: 18 }}
            onClick={() => {
              if (i.disabled) {
                return;
              }
              i.checked = !i.checked;
              handleAllItemChecked();
            }}
          >
            <Check
              checked={i.checked}
              disabled={i.disabled}
            />
            <span style={{ marginLeft: 10 }}>{i.text}</span>
          </span>
        ))
      }
    </Form>
	
````

属性 | 默认值 | 描述
 --- | --- | --- 
 checked |false | 是否选中
 disabled  | false | 是否禁用
 onChange | 空 | checked值改变回调事件(将当前选中值传出去)

 by: <EMAIL>
 
 
 ## time-picker.jsx
 > 时分组件
 
 基础使用
 
 ````jsx
 	import { TimePicker } from '../common';
 	
 	<TimePicker
      show={startShowPicker}
      selected={startTimeSelected}
      onCancel={() => this.setState({ startShowPicker: false })}
      onSelected={(timeArr => this.setState({
        startTimeSelected: timeArr,
        startShowPicker: false,
        startTimeText: getDateText(timeArr),
      }))}
    />
 	
 ````
 
 属性 | 默认值 | 描述
  --- | --- | --- 
  leftBtn | 取消 | 已国际化
  rightBtn  | 确定 | 已国际化
  show |false | 是否显示
  selected  | [] | 初始选中数值，如[6, 16]，第一个是时，第二个是分
  onCancel | 空 | 取消关闭回调
  onSelected | 空 | 点确定时，将当前选中的时分值数组传出去
  dateList: [], // 日期选择数据，不传默认只选择时分
  dateIdx: 0, // 配合dateList一起使用，选中日期值的下标；默认数组第一个日期
 
  by: <EMAIL>
  
  
## split-bar.jsx
> 灰色间隔组件：可直接使用，也可包裹文字或标签
    
   基础使用
    
    ````jsx
    import { SplitBar } from '../common';
    
    <SplitBar />
    <SplitBar>灰色间隔组件</SplitBar>
    
    ````
    
属性 | 默认值 | 描述
--- | --- | --- 
style | {} | 支持使用时修改样式
    
    by: <EMAIL>

