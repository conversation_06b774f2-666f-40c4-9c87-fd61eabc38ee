import React from 'react';
import PropTypes from 'prop-types';
import TableHeader from './table-header';
import TableRow from './table-row';
import style from '../style.css';

// 把权重改成百分比
const toPercentage = (list) => {
  let count = 0;
  const sum = list.reduce((pre, cur) => pre + (+cur || 0), 0);
  if (sum > 0) {
    return list.map((v) => {
      const num = Math.round((v / sum) * 100);
      count += num;
      return `${num}%`;
    });
  }
  const len = list.length;
  const arr = new Array(len);
  return arr.fill(`${Math.round(100 / len)}%`);
};

const Table = (props) => {
  const {
    columns,
    dataSource,
    rowClassName,
    height,
    maxHeight,
    styleObj = {},
    noBorder,
    onRowClick,
    selectedRowIndex,
    rowIndex,
  } = props;
  const weightList = columns.map(v => v.width);
  const arr = toPercentage(weightList);

  const cls = noBorder ? style.noBorderTableBox : style.tableBox;


  return (
    <table className={cls} style={styleObj}>
      {/* <colgroup>
        {
          columns.map((v, i) => (
            <col width={arr[i]} key={i} />
          ))
        }
      </colgroup> */}
      <thead style={{ display: 'block' }}>
        <TableHeader columns={columns} widthArr={arr} />
      </thead>
      <tbody
        style={{
          height, maxHeight, overflow: 'auto', display: 'block',
        }}
      >
        {
        dataSource.map((v, i) => {
          let className = '';

          if (rowClassName) {
            if (typeof rowClassName === 'string') {
              className = rowClassName;
            } else if (typeof rowClassName === 'function') {
              className = rowClassName(v);
            }
          }

          return (
            <TableRow
              className={className}
              data={v}
              columns={columns}
              key={i}
              widthArr={arr}
              rowIndex={i}
              selectedRowIndex={selectedRowIndex}
              onRowClick={onRowClick}
            />
          );
        })
      }
      </tbody>
    </table>
  );
};

Table.propTypes = {
  columns: PropTypes.arrayOf(PropTypes.shape),
  dataSource: PropTypes.arrayOf(PropTypes.shape),
  rowClassName: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),
  height: PropTypes.number,
  maxHeight: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  styleObj: PropTypes.shape(),
  noBorder: PropTypes.bool,
  onRowClick: PropTypes.func,
  selectedRowIndex: PropTypes.number,
};
Table.defaultProps = {
  onRowClick: () => {},
};

export default Table;
