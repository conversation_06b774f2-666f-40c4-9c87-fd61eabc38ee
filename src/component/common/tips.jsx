import React from 'react';
import classnames  from 'classnames';
import PropTypes from 'prop-types';
import style from '../style.css';

const typeMap = {
  normal: style.normal,
};

const Tips = (props) => {
  const {
    content,
    type,
  } = props;
  const className = typeMap[type];

  return content ? (
    <div className={classnames(style.tip, className)}>
      {content}
    </div>
  ) : null;
};

Tips.propTypes = {
  color: PropTypes.string,
  content: PropTypes.string,
};

export default Tips;
