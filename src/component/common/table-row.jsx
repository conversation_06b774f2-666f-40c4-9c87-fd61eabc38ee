import React from 'react';
import PropTypes from 'prop-types';
import style from '../style.css';

const TableRow = (props) => {
  const {
    columns,
    data,
    className,
    widthArr,
    onRowClick,
    selectedRowIndex,
    rowIndex,
  } = props;
  const tr = [];
  columns.forEach((v, index) => {
    const {
      dataIndex,
      render,
    } = v;

    let content = null;

    if (render && typeof render === 'function') {
      content = render(data);
    } else if (data[dataIndex] === 0) {
      content = 0;
    } else {
      content = data[dataIndex] || v.default;
    }

    const td = (
      <td className={style.tableItem} key={dataIndex} style={{ width: widthArr[index] }}>
        {content}
      </td>
    );
    tr.push(td);
  });

  return (
    <tr
      className={`${style.tableRow} ${className}`}
      style={{ background: selectedRowIndex === rowIndex ? 'rgb(0, 89, 206)' : '', color: selectedRowIndex === rowIndex ? '#fff' : '#141737' }}
      onClick={() => { onRowClick(data, rowIndex); }}
    >
      {tr}
    </tr>
  );
};

TableRow.propTypes = {
  columns: PropTypes.arrayOf(PropTypes.shape()),
  data: PropTypes.shape(),
  className: PropTypes.string,
  widthArr: PropTypes.arrayOf(PropTypes.string),
  rowIndex: PropTypes.number,
  onRowClick: PropTypes.func,
  selectedRowIndex: PropTypes.number,
};

export default TableRow;
