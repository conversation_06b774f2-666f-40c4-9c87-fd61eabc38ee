import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { push } from 'react-router-redux';
import assign from 'object-assign';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import styles from '../style.css';
import navStore from '../nav/reducers';
import modal from './modal';

const headerStyle = {
  height: '44px',
  color: '#FFFFFF',
  textAlign: 'center',
  lineHeight: '44px',
  fontSize: '14px',
  fontWeight: 500,
  width: '80%',
  overflow: 'hidden',
};

const slotStyle = {
  position: 'absolute',
  right: '16px',
  bottom: '4px',
  height: '36px',
  lineHeight: '36px',
  fontSize: '14px',
  color: '#FFFFFF',
  userSelect: 'none',
};

const iconStyle = {
  position: 'absolute',
  top: 0,
  height: 44,
  lineHeight: '44px',
  padding: '0 15px',
  color: '#FFFFFF',
  userSelect: 'none',
};

class Header extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showBtnList: false,
    };
    this.listenClick = () => {
      this.hideBtnListFun();
    };
  }

  showBtnListFun() {
    document.addEventListener('click', this.listenClick);
    this.setState({ showBtnList: true });
  }

  hideBtnListFun() {
    document.removeEventListener('click', this.listenClick);
    this.setState({ showBtnList: false });
  }

  render() {
    const {
      title,
      style,
      homeIcon,
      btnListStyle,
      headerBackground,
    } = this.props;

    assign(headerStyle, {
      width: this.props.children ? '80%' : '100%',
    });
    // 传入多个操作按钮，则自动变成+扩展下拉按钮操作
    let isPlusBtnList = false;
    if (this.props.children && this.props.children.length >= 2) {
      isPlusBtnList = true;
    }
    // console.log(isPlusBtnList)
    return (
      <div style={{ position: 'relative' }}>
        <div style={{ background: location.hash.includes('gtms-pda') ? '#f00' : headerBackground || '#0059ce', position: 'relative', paddingLeft: 10 }}>

          <div style={assign({}, headerStyle, style)}>
            {title}
          </div>
          <div style={slotStyle}>
            {isPlusBtnList ? (
              <Icon
                name="plus"
                className={styles.headerPlusIcon}
                onClick={() => {
                  if (!this.state.showBtnList) {
                    this.showBtnListFun();
                  } else {
                    this.hideBtnListFun();
                  }
                }}
              />
            ) : this.props.children}
          </div>
          {this.state.showBtnList ? (
            <div
              style={{ zIndex: 9, ...btnListStyle }}
              className={styles.btnListClass}
            >
              {this.props.children}
            </div>
          ) : null}
        </div>
        {
          homeIcon &&
          (
            <div
              style={iconStyle}
              onClick={() => {
                modal.confirm({
                  content: t('确认要返回首页吗?'),
                  onOk: () => {
                    window.location.hash = 'main-menu';
                    // 将库位异常上报页面隐藏
                    navStore.changeData({ data: { showUploadError: false } });
                  },
                });
              }}
            >
              <Icon
                name="home"
                style={{ fontSize: 20 }}
              />
            </div>
          )
        }
      </div>
    );
  }
}

Header.propTypes = {
  title: PropTypes.string,
  style: PropTypes.shape(),
  homeIcon: PropTypes.bool,
  btnListStyle: PropTypes.shape(),
  headerBackground: PropTypes.string,
};

Header.defaultProps = {
  homeIcon: true,
};

export default Header;
