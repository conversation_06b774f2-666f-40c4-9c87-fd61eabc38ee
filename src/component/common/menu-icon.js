import scan from '../../source/menu/scan.png';
import putShelves from '../../source/menu/put-shelves.png';
import orderPicking from '../../source/menu/order-picking.png';
import collection from '../../source/menu/collection.png';
import sowing from '../../source/menu/sowing.png';
import query from '../../source/menu/query.png';
import takeAccount from '../../source/menu/take-account.png';
import compoundPackage from '../../source/menu/compound-package.png';
import inboundManage from '../../source/menu/inbound-manage.png';
import allotOut from '../../source/menu/allot-out.png';
import backWarehouse from '../../source/menu/back-warehouse.png';
import standardReceiveGoods from '../../source/menu/standard-receive-goods.png';
import receiveGoods from '../../source/menu/receive-goods.png';
import refundScan from '../../source/menu/refund-scan.png';
import sampleManage from '../../source/menu/sample-manage.png';


export default [
  {
    // 交接扫描
    rule: 'scan',
    src: scan,
  },
  {
    // 上架
    rule: 'put-shelves',
    src: putShelves,
  },
  {
    // 捡货
    rule: 'order-picking',
    src: orderPicking,
  },
  {
    // 集货
    rule: 'collection',
    src: collection,
  },
  {
// 分播
    rule: 'sowing',
    src: sowing,
  },
  {
// 库内查询
    rule: 'query',
    src: query,
  },
  {
// 盘点
    rule: 'take-account',
    src: takeAccount,
  },
  {
// 合包业务
    rule: 'compound-package',
    src: compoundPackage,
  },
  {
// PDA调拨出库
    rule: 'allot-out',
    src: allotOut,
  },
  {
// 返仓
    rule: 'back-warehouse',
    src: backWarehouse,
  },
  {
// 库内管理
    rule: 'inbound-manage',
    src: inboundManage,
  },
  {
// 标准化入库收货
    rule: 'standard-receive-goods',
    src: standardReceiveGoods,
  },
  {
// 收货
    rule: 'receive-goods',
    src: receiveGoods,
  },
  {
// 收货入库
    rule: 'refund-scan',
    src: refundScan,
  },
  {
// 样衣管理
    rule: 'sample-manage',
    src: sampleManage,
  },
  {
// 收货入库(PWA)
    rule: 'refund-scan',
    src: refundScan,
  },
]
