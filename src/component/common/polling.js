import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';

class Polling extends React.Component {
  constructor(props) {
    super(props);
    if (!props.callback || typeof props.callback !== 'function') {
      throw new Error(t('Polling callback必须为function'));
    }
    this.state = {
      isPolling: false, // 轮询中的标志
    };
    this.timer = null;
    this.startPolling = this.startPolling.bind(this);
    this.stopPolling = this.stopPolling.bind(this);
  }

  componentDidMount() {
    this.startPolling();
  }

  shouldComponentUpdate(nextProps) {
    if (this.props.callback === nextProps.callback && this.props?.children === nextProps?.children) {
      return false;
    }
    return true;
  }

  componentWillUnmount() {
    this.stopPolling();
  }

  // 开始轮询
  startPolling() {
    const { callback, interval, leading } = this.props;
    if (leading) {
      callback();
    }
    if (this.timer) {
      clearInterval(this.timer);
    }
    this.timer = setInterval(() => {
      callback();
    }, interval);
    this.setState({ isPolling: true });
  }

  // 终止轮询
  stopPolling() {
    clearInterval(this.timer);
    this.timer = null;
    this.setState({ isPolling: false });
  }

  render() {
    const { startPolling, stopPolling } = this;
    const { isPolling } = this.state;
    const { children } = this.props;
    return children && children({ isPolling, startPolling, stopPolling });
  }
}

Polling.propTypes = {
  /**
   * 接受一个方法, 可定制渲染的元素
   * 提供参数startPolling, stopPolling, isPolling
   * 使用如下: <Polling>{({ startPolling, stopPolling, isPolling }) => (<div></div>)}</Polling>
   * 默认为空
   */
  children: PropTypes.func,
  /**
   * 轮询间隔(单位ms), 默认10s
   */
  interval: PropTypes.number,
  /**
   * 轮询回调, 必须
   */
  callback: PropTypes.func.isRequired,
  /**
   * 每次startPolling时立即执行callback
   */
  leading: PropTypes.bool,
};

Polling.defaultProps = {
  children: null,
  interval: 10000,
  leading: true,
};

export default Polling;
