import React from 'react';
import PropTypes from 'prop-types';

const WellenTips = (props) => {
  const {
    arr = [],
  } = props;

  if (!arr.length) {
    return null;
  }

  const boxStyle = {
    marginLeft: 5,
    backgroundColor: '#F59A23',
    color: 'white',
    padding: 3,
  };

  return (
    <div style={{ margin: 8 }}>
      {arr.map(i => (
        <span key={i} style={boxStyle}>
          {i}
        </span>
      ))}
    </div>
  );
};
WellenTips.propTypes = {
  arr: PropTypes.arrayOf(),
};

export default WellenTips;
