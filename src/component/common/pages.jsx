// eslint-disable-next-line max-classes-per-file
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Toast } from 'react-weui/build/packages';
import styles from '../style.css';

const PagesContext = React.createContext();
const FOOTER_HEIGHT = 56; // 底部按钮区域高度

class PageContainer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      height: '',
      diff: FOOTER_HEIGHT,
    };
    this.setHeight = this.setHeight.bind(this);
    this.setDiff = this.setDiff.bind(this);
  }

  componentDidMount() {
    this.setHeight();
    window.addEventListener('resize', this.setHeight);
  }

  componentDidUpdate() {
    this.setHeight();
  }

  setDiff(v) {
    const d = v && Number(v);
    const { diff } = this.state;
    if (d === 0) {
      this.setState({
        diff: 0,
      }, () => { this.setHeight(); });
    } else if (d && d !== diff) {
      this.setState({
        diff: d,
      }, () => { this.setHeight(); });
    }
  }

  setHeight() {
    const { height, diff } = this.state;
    const h = window.innerHeight - diff;
    if (+height !== h) {
      this.setState({
        height: h,
      });
    }
  }

  render() {
    const { height } = this.state;
    const { children } = this.props;
    return (
      // eslint-disable-next-line react/jsx-no-constructed-context-values
      <PagesContext.Provider value={{ height, setDiff: this.setDiff }}>
        {children}
      </PagesContext.Provider>
    );
  }
}

PageContainer.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  children: PropTypes.any,
};

/* eslint-disable */

class Page extends Component {

  componentDidMount() {
    const { diff } = this.props;
    const d = diff && Number(diff);
    if (d === 0) {
      this.context.setDiff(0);
    } else if (d) {
      this.context.setDiff(d)
    } else {
      // 设置默认初始值，兼容没传diff情况
      this.context.setDiff(FOOTER_HEIGHT);
    }
  }

  render() {
    const { children, flex, initLoading = false } = this.props;
    if (initLoading) {
      return (
        <Toast icon="loading" show>Loading...</Toast>
      )
    }

    let pageStyle = {};
    let pageFlexStyle = { display: 'flex', flexDirection: 'column' };
    if (flex) {
      Object.assign(pageStyle, pageFlexStyle);
    }

    return (
      <PagesContext.Consumer>
        {
          ({ height }) => {
            if ((typeof height) === 'number') {
              pageStyle.height = height;
            }
            if (React.Children.count(children) === 1) {
              return (
                <React.Fragment>
                  {
                    React.Children.map(children, (child) => (
                      React.cloneElement(child, { style: pageStyle })
                    ))
                  }
                </React.Fragment>
              )
            } else {
              pageStyle.overflowY = 'auto';
              return (
                <div style={pageStyle}>
                  {children}
                </div>
              )
            }
          }
        }
      </PagesContext.Consumer>
    );
  }
}

Page.contextType = PagesContext;

Page.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.array,
  ]),
  style: PropTypes.shape(),
  flex: PropTypes.bool,
  diff: PropTypes.number,
  initLoading: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.number,
  ]),
};

Page.defaultProps = {
  style: {},
  flex: true,
  initLoading: false
};

/* eslint-enable */
const pages = {
  View: Page,
  PageContainer,
};

export default pages;
