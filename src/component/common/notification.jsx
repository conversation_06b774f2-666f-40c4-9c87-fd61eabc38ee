import React from 'react';
import Notification from 'rc-notification';

let message = null;
const duration = 2;
Notification.newInstance({
  style: {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    textAlign: 'center',
    color: '#fff',
  }
}, (n) => message = n);

export default {
  success(content, duration = duration){
    message.notice({
      content: <div style={{ height: 50, lineHeight: '50px', background: '#27dd25'}}>{content}</div>,
      duration,
    });
  },
  error(content, duration = duration){
    message.notice({
      content: <div style={{ height: 50, lineHeight: '50px', background: '#dd3825'}}>{content}</div>,
      duration,
    });
  },
  info(content, duration = duration){
    message.notice({
      content: <div style={{ height: 50, lineHeight: '50px', background: '#dd3825'}}>{content}</div>,
      duration,
    });
  },
}