import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';

function EfficiencyDetail(props) {
  const { data } = props;
  data.map((item) => { // 兼容当用户未绑定子仓时页面展示undefined问题
    if (item.num === 'undefined%') {
      item.num = '%';
    }
    return item;
  });
  return (
    <div style={{
      display: 'flex', flexWrap: 'wrap', marginLeft: 15, marginTop: 9, marginBottom: 9,
    }}
    >
      {data.map((item) => (
        item.desc === t('当前用户排名') ? (
          <div key={item.id} style={{ flex: 1, minWidth: 'calc((100% - 10px) / 2)', marginTop: 12 }}>
            <p style={{
              display: 'inline-block',
              fontSize: 16,
              fontWeight: 'bold',
              color: item.showOrange ? '#FF8C00' : '#141737',
            }}
            >{item.num.split('/')[0] === 'undefined' || item.num.split('/')[0] === null || !item.num ? '' : item.num.split('/')[0]}
            </p>
            <p style={{
              display: 'inline-block', fontSize: 16, fontWeight: 'bold', color: '#141737',
            }}
            >{item.num.split('/')[1] !== 'undefined' ? `/${item.num.split('/')[1]}` : '/'}
            </p>
            <p style={{
              display: 'inline-block',
              fontSize: 12,
              fontWeight: 'bold',
              color: item.showOrange ? '#FF8C00' : '#141737',
            }}
            > {item.hasUnits}
            </p>
            <p style={{ color: '#616161' }}>{item.desc}</p>
          </div>
        ) : (
          <div key={item.id} style={{ flex: 1, minWidth: 'calc((100% - 10px) / 2)', marginTop: 12 }}>
            <p style={{
              display: 'inline-block',
              fontSize: 16,
              fontWeight: 'bold',
              color: item.showOrange ? '#FF8C00' : '#141737',
            }}
            >{item.num}
            </p>
            <p style={{
              display: 'inline-block',
              fontSize: 12,
              fontWeight: 'bold',
              color: item.showOrange ? '#FF8C00' : '#141737',
            }}
            > {item.hasUnits}
            </p>
            <p style={{ color: '#616161' }}>{item.desc}</p>
          </div>
        )
      ))}
    </div>
  );
}

EfficiencyDetail.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape()),

};

export default EfficiencyDetail;
