import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import {
  Popup,
  PopupHeader,
  TextArea,
  Form,
  FormCell,
  CellBody,
} from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import { FocusInput } from './index';

class PopTextArea extends Component {
  constructor(props) {
    super(props);
    this.state = {
      valueData: props.value,
    };
  }

  render() {
    const {
      leftBtn,
      rightBtn,
      value,
      onCancel,
      onOk,
      show,
      onClick,
      label,
      placeholder,
      className,
      disabled,
      textAreaPlaceholder,
    } = this.props;
    const {
      valueData,
    } = this.state;
    const selectChange = (e) => {
      this.setState({
        valueData: e.target.value,
      });
    };
    // 用户选确定，才生效
    const selectOk = () => {
      onOk(valueData);
    };
    // 点击取消，要重新设置state
    const selectCancel = () => {
      this.setState({
        valueData: value,
      });
      onCancel();
    };
    return (
      <>
        <FocusInput
          type="text"
          placeholder={placeholder}
          className={className || ''}
          readOnly
          disabled={disabled || false}
          onClick={onClick}
          value={value}
          arrow
          keepFocus={false}
        >
          <label>{label}</label>
        </FocusInput>
        {
          show ? (
            <Popup
              show={show}
            >
              <PopupHeader
                left={t('取消') || leftBtn}
                right={t('确定') || rightBtn}
                leftOnClick={selectCancel}
                rightOnClick={selectOk}
              />
              {
                <Form>
                  <FormCell>
                    <CellBody>
                      <TextArea
                        placeholder={textAreaPlaceholder}
                        rows="3"
                        maxLength={200}
                        onChange={(e) => selectChange(e)}
                        defaultValue={value}
                        // autoFocus
                        className="textAreaPop"
                      />
                    </CellBody>
                  </FormCell>
                </Form>
              }
            </Popup>
          ) : null
        }
      </>
    );
  }
}

PopTextArea.defaultProps = {
  leftBtn: t('取消'),
  rightBtn: t('确定'),
  placeholder: t('请选择'),
  show: false,
  textAreaPlaceholder: t('请输入'),
};

PopTextArea.propTypes = {
  value: PropTypes.string,
  onCancel: PropTypes.func,
  onOk: PropTypes.func,
  leftBtn: PropTypes.string,
  rightBtn: PropTypes.string,
  show: PropTypes.bool,
  onClick: PropTypes.func,
  label: PropTypes.string,
  placeholder: PropTypes.string,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  textAreaPlaceholder: PropTypes.string,
};

export default PopTextArea;
