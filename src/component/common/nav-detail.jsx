import React from 'react';
import PropTypes from 'prop-types';
import { Tab, NavBar, NavBarItem } from 'react-weui/build/packages';
import ScrollList from './scroll-list';
import modal from './modal';
// import style from '../style.css';

const defaultStyle = {
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
};

const imgShow = (data, imgUrlFieldName) => {
  const img = <img width="100%" src={data[imgUrlFieldName]} />;
  modal.img({
    content: img,
  });
};

class NavDetail extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      activeNavKey: 0,
    };
  }

  render() {
    const {
      rowsList,
      navList,
      data,
      listStyle,
      style,
      showPagination,
      pageSize,
      imgUrlFieldName,
      totalNumList,
    } = this.props;
    const { activeNavKey } = this.state;
    const list = data[activeNavKey] || [];
    // const numList = data.map(item => item.length);
    let rows = [];

    if (rowsList.length === 1) {
      ([rows] = rowsList);
    } else if (rowsList.length > 1) {
      rows = rowsList[activeNavKey];
    }

    return (
      <div style={Object.assign({}, defaultStyle, style)}>
        <div style={{ height: 34 }}>
          <Tab>
            <NavBar>
              {
                navList.map((item, index) => (
                  <NavBarItem
                    key={index}
                    onClick={() => {
                      this.setState({
                        activeNavKey: index,
                      });
                    }}
                    active={index === activeNavKey}
                  >
                    <span style={{ color: index === activeNavKey ? '#197AFA' : '' }}>
                      {item}: {totalNumList[index]}
                    </span>
                  </NavBarItem>
                ))
              }
            </NavBar>
          </Tab>
        </div>
        <ScrollList
          rows={rows}
          data={list}
          style={listStyle}
          showNum={pageSize}
          ItemOnclick={row => imgUrlFieldName ? imgShow(row, imgUrlFieldName) : null}
          showPagination={showPagination}
        />
      </div>
    );
  }
}

NavDetail.defaultProps = {
  rowsList: [],
  navList: [],
  data: [[], []],
  listStyle: {},
  style: {},
  pageSize: 50,
  imgUrlFieldName: '',
  totalNumList: [],
  showPagination: true,
};

NavDetail.propTypes = {
  rowsList: PropTypes.arrayOf(PropTypes.array),
  navList: PropTypes.arrayOf(PropTypes.string),
  data: PropTypes.arrayOf(PropTypes.array),
  listStyle: PropTypes.shape(),
  style: PropTypes.shape(),
  showPagination: PropTypes.bool,
  pageSize: PropTypes.number,
  imgUrlFieldName: PropTypes.string,
  totalNumList: PropTypes.arrayOf(PropTypes.number),
};

export default NavDetail;
