import React from 'react';
import PropTypes from 'prop-types';
import { Dialog } from 'react-weui/build/packages';
import { i18n, t } from '@shein-bbl/react';
import { isInt, classFocus, isaNumberInRange } from 'lib/util';
import Message from '../message';
import styles from './style.css';

// 目标值校验：校验必填，1到9999正整数
const getMsg = (num) => {
  if (!num) {
    return t('目标值不能为空');
  }
  if (!isInt(num)) {
    return t('目标值为正整数');
  }
  if (!isaNumberInRange(num, 0, 9999)) {
    return t('目标值范围为1-9999');
  }
  return '';
};

function TargetModal(props) {
  const {
    show,
    yesterdayProfit,
    target,
    onChange,
    onOk,
  } = props;

  return (
    <Dialog
      title={`${t('提示')}`}
      show={show}
      buttons={[{
        label: t('确定'),
        type: 'primary',
        onClick: () => {
          // 校验
          const msg = getMsg(target);
          if (msg) {
            Message.error(msg);
            onChange('');
            classFocus(styles.modalTargetInput);
            return;
          }
          // 确定提交
          onOk(target);
        },
      }]}
    >
      <div>{t('你昨天干了')}{yesterdayProfit || 0}{t('元')}</div>
      <div style={{ marginTop: 5 }}>
        <span>{t('今天想赚')}</span>
        <input
          className={styles.modalTargetInput}
          type="text"
          value={target}
          onInput={(e) => {
            const { value } = e.target;
            // 校验输入是否数值，并触发更改
            if (parseInt(value, 10)) {
              onChange(parseInt(value, 10));
            } else {
              onChange('');
            }
          }}
          // 加上onChange方法解决控制台的报错
          onChange={() => {}}
        />
        <span>{t('元')}</span>
      </div>
    </Dialog>
  );
}
TargetModal.propTypes = {
  show: PropTypes.bool,
  yesterdayProfit: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  target: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  onChange: PropTypes.func,
  onOk: PropTypes.func,
};

TargetModal.defaultProps = {
  show: false, // 弹窗显示隐藏
  yesterdayProfit: 0, // 昨天完成值
  target: 0, // 当前目标值
  onChange: () => {}, // 当前目标值修改回调
  onOk: () => {}, // 弹窗点确定回调
};

export default i18n(TargetModal);
