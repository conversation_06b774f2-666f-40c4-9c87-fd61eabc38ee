import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Dialog, Input } from 'react-weui/build/packages';
import { i18n, t } from '@shein-bbl/react';
import { classFocus } from 'lib/util';
import Message from '../message';

function PasswordModal(props) {
  const {
    show,
    onOk,
    onCancel,
    title,
    msg,
  } = props;
  const [password, setPassword] = useState('');
  useEffect(
    () => {
      setPassword('');
      if (show) {
        setTimeout(() => {
          classFocus('modalPasswordInput');
        }, 100);
      }
    },
    [show],
  );
  return (
    <Dialog
      title={title || `${t('输入确认码')}`}
      show={show}
      buttons={[{
        label: t('取消'),
        type: 'default',
        onClick: () => { onCancel(); },
      },
      {
        label: t('确定'),
        type: 'primary',
        onClick: () => {
          if (!password) {
            Message.error(t('请输入确认码'));
            classFocus('modalPasswordInput');
            return;
          }
          onOk({ password });
        },
      }]}
    >
      <div>{msg}</div>
      <div style={{ marginTop: 5 }}>
        <Input
          className="modalPasswordInput"
          value={password}
          placeholder={t('请输入')}
          onChange={(e) => {
            const val = e.target.value;
            setPassword(val);
          }}
        />
      </div>
    </Dialog>
  );
}
PasswordModal.propTypes = {
  show: PropTypes.bool,
  onOk: PropTypes.func,
  onCancel: PropTypes.func,
  title: PropTypes.string,
  msg: PropTypes.string,
};

export default i18n(PasswordModal);
