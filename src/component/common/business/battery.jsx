import React from 'react';
import PropTypes from 'prop-types';
import { typeFn, round } from 'lib/util';
import styles from './style.css';

// 主颜色默认黄金色
const DefaultColorObj = {
  border: '#FFD39A', // 边框色
  bg: '#FFEDD5', // 背景色
  rightBulge: '#FFBA60', // 右边凸出色
  leftFont: '#FB8E00', // 左字体色
  rightFont: '#333E59', // 右字体色
};

const Battery = (props) => {
  const {
    haveProfit,
    targetProcess,
    colorObj,
    style,
  } = props;

  const colorObjVal = typeFn.isObject(colorObj)
    ? { ...DefaultColorObj, ...colorObj } : DefaultColorObj;

  return (
    <section
      className={styles.battery}
      style={{
        background: `linear-gradient(to right, ${colorObjVal.bg} ${targetProcess}%, #FFFFFF ${targetProcess}%)`,
        borderColor: colorObjVal.border,
        ...style,
      }}
    >
      <span
        style={{ color: colorObjVal.leftFont }}
      >
        &nbsp;&yen;{round(haveProfit, 2)}
      </span>
      <span style={{ color: colorObjVal.rightFont }}>{round(targetProcess, 2)}%</span>
      <span className={styles.rightBulge} style={{ backgroundColor: colorObjVal.rightBulge }} />
    </section>
  );
};
Battery.propTypes = {
  haveProfit: PropTypes.number,
  targetProcess: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  colorObj: PropTypes.shape(),
  style: PropTypes.shape(),
};

Battery.defaultProps = {
  haveProfit: 0, // 左边数值
  targetProcess: 0, // 右边百分比
  style: {}, // 最外层容器样式
  colorObj: {}, // 主颜色对象，支持仅修改其中部分属性值
};

export default Battery;
