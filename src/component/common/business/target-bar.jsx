import React from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import Battery from './battery';
import styles from './style.css';

const TargetBar = (props) => {
  const {
    haveProfit,
    targetProcess,
    userRank,
    totalRank,
    targetProfit,
    style,
  } = props;


  return (
    <section
      className={styles.targetBar}
      style={style}
    >
      <div className={styles.targetBarLeft}>
        {t('目标')}：
        <span style={{ color: '#0059CE', marginRight: 8, fontWeight: 'bold' }}>
          {targetProfit || '-'}{!!targetProfit && t('元')}
        </span>
        {
          !!targetProfit && (
            <Battery
              haveProfit={haveProfit}
              targetProcess={targetProcess}
            />
          )
        }
      </div>
      <div className={styles.targetBarRight}>
        <span style={{ color: '#0059CE', fontSize: 14, fontWeight: 'bold' }}>{userRank}</span>
        /{totalRank}
      </div>
    </section>
  );
};
TargetBar.propTypes = {
  haveProfit: PropTypes.number,
  userRank: PropTypes.number,
  totalRank: PropTypes.number,
  targetProfit: PropTypes.number,
  targetProcess: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  style: PropTypes.shape(),
};

TargetBar.defaultProps = {
  targetProfit: 0, // 总目标值
  haveProfit: 0, // 完成目标值
  targetProcess: 0, // 右边百分比
  totalRank: 0, // 总排名
  userRank: 0, // 用户排名
  style: {}, // 最外层容器样式
};

export default i18n(TargetBar);
