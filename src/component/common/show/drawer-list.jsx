import React from 'react';
import PropTypes from 'prop-types';
import { typeFn } from 'lib/util';
import DrawerContainer from './drawer-container';
import styles from './style.css';

const DrawerList = (props) => {
  const {
    list,
    itemStyle,
    ...otherPops
  } = props;
  // 处理list传参
  let listArr = list;
  if (!typeFn.isArray(list)) {
    listArr = [];
  }
  return (
    <DrawerContainer {...otherPops}>
      <section className={styles.drawerListCont}>
        {
          listArr.map(t => (
            <div className={styles.drawerListItem} key={t} style={itemStyle}>{t}</div>
          ))
        }
      </section>
    </DrawerContainer>
  );
};

// 也兼容DrawerContainer组件属性值；
DrawerList.propTypes = {
  list: PropTypes.arrayOf(PropTypes.string),
  itemStyle: PropTypes.shape(),
};

DrawerList.defaultProps = {
  list: [], // 字符串数组，timeline为false时渲染
  itemStyle: {}, // 列表item样式
};

export default DrawerList;
