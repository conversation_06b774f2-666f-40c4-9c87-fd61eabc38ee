import React from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import styles from './style.css';

const DrawerContainer = (props) => {
  const {
    title,
    children,
    visible,
    drawerWidth,
    onClose,
    maskCloseAble,
    closeable,
    containerStyle,
    drawerStyle,
    iconStyle,
    titleStyle,
  } = props;
  return (
    <section
      style={{
        zIndex: 9,
        display: visible ? 'block' : 'none',
        ...containerStyle,
      }}
      className={styles.drawerContainer}
      onClick={() => {
        if (maskCloseAble) {
          onClose();
        }
      }}
    >
      <div
        style={{ width: drawerWidth, ...drawerStyle }}
        className={styles.drawerDiv}
        onClick={(e) => {
          // 阻止事件冒泡防止触发遮罩关闭
          e.stopPropagation();
        }}
      >
        {
          title && <div className={styles.drawerTitle} style={titleStyle}>{title}</div>
        }
        { closeable && (
          <span style={iconStyle} className={styles.closeIcon} onClick={onClose}>
            <Icon name="close" />
          </span>
        )}
        {children}
      </div>
    </section>
  );
};

// 后续继扩展功能：position、showAnimation、zIndex、containerClassName、drawerStyleClassName
DrawerContainer.propTypes = {
  title: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.element,
  ]),
  children: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.element,
    PropTypes.array,
  ]),
  visible: PropTypes.bool,
  drawerWidth: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  onClose: PropTypes.func,
  maskCloseAble: PropTypes.bool,
  closeable: PropTypes.bool,
  containerStyle: PropTypes.shape(),
  drawerStyle: PropTypes.shape(),
  iconStyle: PropTypes.shape(),
  titleStyle: PropTypes.shape(),
};

DrawerContainer.defaultProps = {
  title: '', // 弹出层顶部弹窗标题，也支持传入元素
  children: '', // 弹出层里边内容
  visible: false, // 是否展示弹出层
  drawerWidth: '80%', // 弹出层内容模块宽度
  onClose: () => {}, // 点遮罩或关闭icon事件
  maskCloseAble: true, // 点遮罩是否触发onClose事件
  closeable: true, // 是否渲染关闭icon
  containerStyle: {}, // 最外层容器行内样式
  drawerStyle: {}, // 弹出层内容模块行内样式
  iconStyle: {}, // 弹出层关闭icon行内样式
  titleStyle: {}, // 弹出层顶部弹窗标题行内样式
};

export default DrawerContainer;
