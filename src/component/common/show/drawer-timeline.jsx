import React from 'react';
import PropTypes from 'prop-types';
import { typeFn } from 'lib/util';
import DrawerContainer from './drawer-container';
import styles from './style.css';

// 渲染时间线icon
const renderTimelineIcon = () => (
  <div className={styles.timelineIcon}>
    <span className={styles.timelineTop} />
    <span className={styles.timelineBottom} />
  </div>
);
const DrawerTimeline = (props) => {
  const {
    config,
    list,
    timelineIcon,
    ...otherPops
  } = props;
  return (
    <DrawerContainer {...otherPops}>
      <section className={styles.drawerTimelineWrap}>
        <section className={styles.drawerTimelineTitle}>
          {timelineIcon && renderTimelineIcon()}
          <div className={styles.textWrap}>
            <div className={styles.mainTitle}>{config.mainTitle}</div>
            <div className={styles.subTitle}>{config.subTitle}</div>
          </div>
        </section>
        <section className={styles.drawerTimelineCont}>
          {
            list.map(i => (
              <div key={i[config.mainKey] + i[config.subKey]} className={styles.timelineItem}>
                {timelineIcon && renderTimelineIcon()}
                <div className={styles.textWrap}>
                  <div className={styles.mainText}>{i[config.mainKey]}</div>
                  <div className={styles.subText}>{i[config.subKey]}</div>
                </div>
              </div>
            ))
          }
        </section>
      </section>
    </DrawerContainer>
  );
};

// 也兼容DrawerContainer组件属性值
DrawerTimeline.propTypes = {
  config: PropTypes.shape(),
  list: PropTypes.arrayOf(PropTypes.shape()),
  timelineIcon: PropTypes.bool,
};

DrawerTimeline.defaultProps = {
  config: {
    mainTitle: '', // 文案标题
    subTitle: '', // 时间标题
    mainKey: 'text', // 文案属性名，即timelineList里边对象的属性名
    subKey: 'time', // 时间属性名，即timelineList里边对象的属性名
  }, // 时间线配置对象，timeline为true时起作用
  list: [], // 对象数组，timeline为true时渲染
  timelineIcon: true, // 展示时间线icon
};

export default DrawerTimeline;
