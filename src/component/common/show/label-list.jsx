import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { typeFn } from 'lib/util';
import styles from './style.css';

function LabelList(props) {
  const {
    labelList,
    valueList,
    className,
    itemSpaceBetween,
    labelMinWidth,
    lessLabelItem,
  } = props;
  // 若labelList或valueList不是数组，则代表传参类型错误，直接返回null
  if (!typeFn.isArray(labelList) || !typeFn.isArray(valueList)) {
    return null;
  }
  return (
    <section
      className={classnames(styles.labelList, className)}
    >
      {
        labelList.map((label, idx) => (
          <div key={label} className={classnames(lessLabelItem ? styles.lessLabelItem : styles.labelItem, itemSpaceBetween ? styles.itemSpaceBetween : '')}>
            <span className={styles.labelSpan} style={{ minWidth: labelMinWidth }}>{label}：</span>
            <span className={styles.valueSpan}>{valueList[idx]}</span>
          </div>
        ))
      }
    </section>
  );
}

LabelList.propTypes = {
  labelList: PropTypes.arrayOf(PropTypes.string),
  valueList: PropTypes.arrayOf(PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.element,
  ])),
  labelMinWidth: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  className: PropTypes.string,
  itemSpaceBetween: PropTypes.bool,
  lessLabelItem: PropTypes.bool,
};

LabelList.defaultProps = {
  labelList: [], // 文案数组，字符串数组
  labelMinWidth: '70px', // 文案统一最小宽度; 若想由内容自适应就传unset
  valueList: [], // 值数组；字符串或数值数组
  className: '', // 最外层类名
  itemSpaceBetween: false, // 两端对齐
  lessLabelItem: false, // 使用小一点marginBottom
};

export default LabelList;
