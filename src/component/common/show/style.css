/* TextBar
---------------------------------------------------------------- */
.textBar {
    display: flex;
    align-items: center;
    min-height: 44px;
    font-size: 22px;
    margin: 0 15px;
    color: #197afa;
    background: linear-gradient(270deg, rgba(186, 215, 253, 0.1) 0%, rgba(213, 231, 255, 0.6) 51%, rgba(186, 215, 253, 0.1) 100%);
}
.textBarEllipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.red {
    color: #ff4d50;
    background: linear-gradient(270deg, rgba(255, 237, 237, 0.3) 0%, #FFEDED 51%, rgba(255, 237, 237, 0.3) 100%);
}
.yellow {
    color: #ff8c00;
    background: linear-gradient(270deg, rgba(255, 237, 237, 0.3) 0%, #FFEDED 51%, rgba(255, 237, 237, 0.3) 100%);
}


/* ProgressBar
---------------------------------------------------------------- */
.progressBar {
    position: relative;
}
.progress {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
}

/* DrawerContainer
---------------------------------------------------------------- */
.drawerContainer {
    width: 100vw;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    background: rgba(20, 23, 55, 0.3);
}
.drawerTitle {
    font-size: 14px;
    font-weight: bold;
    color: #333e59;
    height: 40px;
    line-height: 40px;
    padding-left: 8px;
}
.drawerDiv {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    background-color: #ffffff;
}
.closeIcon {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 24px;
    color: #999da8;
}

/* DrawerList
---------------------------------------------------------------- */
.drawerListCont {
    height: calc(100vh - 40px);
    overflow-y: auto;
    color: #141737;
}
.drawerListItem {
    height: 40px;
    line-height: 40px;
    padding-left: 8px;
    font-size: 14px;
    border-bottom: 1px solid #E8EBF0;
}


/* DrawerTimeline
---------------------------------------------------------------- */
.drawerTimelineWrap {
    height: calc(100vh - 40px);
    overflow-y: auto;
}
.timelineIcon {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    left: -12px;
    top: 8px;
}
.timelineTop {
    width: 7px;
    height: 7px;
    background: #CCCFD7;
    border-radius: 50%;
}
.timelineBottom {
    width: 1px;
    height: calc(100% - 9px);
    position: relative;
    top: 3px;
    background: #E6EAF0;
}
.textWrap {
    width: calc(100% - 12px);
}
.drawerTimelineTitle {
    display: flex;
    flex-wrap: wrap;
    background: #F2F4F7;
    border-radius: 4px;
    padding-left: 12px;
    margin: 0 8px;
    padding-bottom: 12px;
}
.drawerTimelineCont {
    margin-left: 12px;
}
.timelineItem {
    display: flex;
    flex-wrap: wrap;
    padding: 6px 0 8px 8px;
}
.mainTitle, .mainText {
    width: 100%;
    font-size: 14px;
    color: #333e59;
    line-height: 32px;
    height: 32px;
}
.subTitle, .subText {
    width: 100%;
    font-size: 12px;
    color: #999da8;
    line-height: 20px;
    height: 20px;
}

/* LabelList
---------------------------------------------------------------- */
.labelList {
    background-color: #fff;
    padding: 6px 0 0 14px;
    font-size: 14px;
}
.labelItem {
    margin-bottom: 6px;
}
.lessLabelItem{
    margin-bottom: 0px;
    padding-top: 0px;
}
.itemSpaceBetween {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 12px;
}
.labelSpan {
    color: #666c7c;
    display: inline-block;
    text-align: justify;
    text-align-last: justify;
    text-justify: inter-ideograph;
}
.valueSpan {
    color: #141737;
}
