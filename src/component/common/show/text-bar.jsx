import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { typeFn } from 'lib/util';
import styles from './style.css';

const TextBar = (props) => {
  const {
    type,
    style,
    ellipsis,
  } = props;
  let { text } = props;
  // 若文案传参是字符串或数值，则改成数组；方便后边统一判断渲染
  if (typeFn.isString(text) || typeFn.isBoolean(text)) {
    text = [text];
  }
  // 若最终不是数组，则代表传参类型错误，直接返回null
  if (!typeFn.isArray(text)) {
    return null;
  }
  // 数组文案数为1则居中，否则两端对齐
  const justifyContentVal = text.length === 1 ? 'center' : 'space-between';
  return (
    <section
      style={{ justifyContent: justifyContentVal, ...style }}
      className={classnames(styles.textBar, styles[type])}
    >
      <span className={classnames(ellipsis && text.length === 1 ? styles.textBarEllipsis : '')}>{text[0]}</span>
      {
        // 暂时仅支持显示2个文案，若有显示3个及以上的等UI设计出来再实现
        text.length >= 2 && (<span>{text[1]}</span>)
      }
    </section>
  );
};

TextBar.propTypes = {
  text: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.arrayOf(PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
    ])),
  ]),
  type: PropTypes.string,
  ellipsis: PropTypes.bool,
  style: PropTypes.shape(),
};

TextBar.defaultProps = {
  text: '', // 默认空，支持数值或字符串或数组
  type: '', // 默认是蓝色；支持传red设置红色
  ellipsis: false, // 一行超出省略，默认否; 仅一个text文案才起作用
  style: {}, // 最外层div行内样式
};

export default TextBar;
