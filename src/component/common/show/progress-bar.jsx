import React from 'react';
import PropTypes from 'prop-types';
import { typeFn } from 'lib/util';
import styles from './style.css';

const ProgressBar = (props) => {
  const {
    percentage,
    color,
    trackColor,
    strokeWidth,
    style,
  } = props;
  // 确保用户传入的是数值
  let percentageVal = '0%';
  if (typeFn.isNumber(percentage)) {
    percentageVal = `${percentage * 100}%`;
  }
  let strokeWidthVal = '10px';
  let borderRadius = 5;
  if (typeFn.isNumber(strokeWidth)) {
    strokeWidthVal = `${strokeWidth}px`;
    borderRadius = strokeWidth / 2;
  }
  return (
    <section
      style={{
        height: strokeWidthVal,
        backgroundColor: trackColor,
        borderRadius,
        ...style,
      }}
      className={styles.progressBar}
    >
      <span
        style={{ width: percentageVal, backgroundColor: color, borderRadius }}
        className={styles.progress}
      />
    </section>
  );
};

ProgressBar.propTypes = {
  percentage: PropTypes.number,
  color: PropTypes.string,
  trackColor: PropTypes.string,
  strokeWidth: PropTypes.number,
  style: PropTypes.shape(),
};

ProgressBar.defaultProps = {
  percentage: 0, // 进度条比例百分比，数值；%不用写
  color: '#04C75F', // 默认是绿色
  trackColor: '#E6EAF0', // 默认是灰色
  strokeWidth: 10, // 默认10，单位px
  style: {}, // 最外层div行内样式
};

export default ProgressBar;
