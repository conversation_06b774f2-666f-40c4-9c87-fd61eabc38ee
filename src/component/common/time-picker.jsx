import React from 'react';
import PropTypes from 'prop-types';
import { Picker } from 'react-weui/build/packages/components/picker';
import { t } from '@shein-bbl/react';

// 获取时分数组
const getTimeArr = num => new Array(num).fill(1).map((v, i) => {
  const res = i < 10 ? `0${i}` : String(i);
  return res;
});
// 处理groups数据
const handleGroupData = arr => ({
  items: arr.map(v => ({ code: v, name: v })),
  mapKeys: {
    label: 'name',
  },
});

class TimePicker extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hours: getTimeArr(24),
      minutes: getTimeArr(60),
    };
  }

  get computed() {
    let arr = [];
    const { dateList } = this.props;
    const { hours, minutes } = this.state;
    arr = [handleGroupData(hours), handleGroupData(minutes)];
    if (dateList && dateList.length) {
      // 若有传日期数组，则需在左边显示日期数组
      arr.unshift(handleGroupData(dateList));
    }
    return arr;
  }

  render() {
    const {
      leftBtn,
      rightBtn,
      show,
      selected,
      onCancel,
      onSelected,
      dateList,
      dateIdx,
      infinite,
      onGroupChange,
    } = this.props;
    // 获取最终传picker组件的数据
    const getGroupsData = () => {
      const arr = [];
      arr[0] = handleGroupData(getTimeArr(24));
      arr[1] = handleGroupData(getTimeArr(60));
      if (dateList && dateList.length) {
        // 若有传日期数组，则需在左边显示日期数组
        arr.unshift(handleGroupData(dateList));
      }
      return arr;
    };
    // 若有日期，则给日期也加上默认选中值
    if (dateList && dateList.length) {
      if (selected.length === 2) {
        selected.unshift(dateIdx);
      } else {
        selected[0] = dateIdx;
      }
    }

    const groupsDate = this.computed;
    const groupsLength = groupsDate.length;

    return (
      <div>
        {
          show ? (
            <Picker
              show={show}
              onChange={(selected) => {
                if (!infinite) {
                  onSelected(selected);
                } else {
                  selected[(groupsLength - 2)] = selected[(groupsLength - 2)] % 24;
                  selected[(groupsLength - 1)] = selected[(groupsLength - 1)] % 60;
                  onSelected(selected);
                }
              }}
              defaultSelect={selected}
              onGroupChange={(a, b, c, d, e) => {
                console.info('a, b, c, d, e--->', a, b, c, d, e);
                if (!infinite) {
                  onGroupChange && onGroupChange(a, b, c, d, e);
                } else {
                  const { hours, minutes } = this.state;
                  if (c === (groupsLength - 2) && (b + 1) > (hours.length / 3 * 2)) {
                    this.setState({
                      hours: hours.concat(getTimeArr(24)),
                    });
                  }
                  if (c === (groupsLength - 1) && (b + 1) > (minutes.length / 3 * 2)) {
                    this.setState({
                      minutes: minutes.concat(getTimeArr(60)),
                    });
                  }
                  onGroupChange && onGroupChange(a, b, c, d, e);
                }
              }}
              groups={groupsDate}
              // groups={getGroupsData()}
              onCancel={onCancel}
              lang={{ leftBtn, rightBtn }}
            />
          ) : null
        }
      </div>
    );
  }
}

TimePicker.defaultProps = {
  leftBtn: t('取消'),
  rightBtn: t('确定'),
  show: false,
  // selected：具体时间数值，如[6, 16]，第一个是时，第二个是分； 【默认当前时分】
  selected: [new Date().getHours(), new Date().getMinutes()],
  dateList: [], // 日期选择数据，不传默认只选择时分
  dateIdx: 0, // 配合dateList一起使用，选中日期值的下标；默认数组第一个日期
};

TimePicker.propTypes = {
  leftBtn: PropTypes.string,
  rightBtn: PropTypes.string,
  show: PropTypes.bool,
  selected: PropTypes.arrayOf(Number),
  onCancel: PropTypes.func,
  onSelected: PropTypes.func,
  dateList: PropTypes.arrayOf(String),
  dateIdx: PropTypes.number,
};

export default TimePicker;
