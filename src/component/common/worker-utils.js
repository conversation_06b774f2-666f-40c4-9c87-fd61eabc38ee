import WebWorker from '../../lib/worker';

const work = function () {
  let timer = null;
  this.onmessage = (e) => {
    let { timeoutSeconds, state } = e.data;
    if (state === 'stop') {
      if (timer) {
        clearInterval(timer);
        timer = null;
      }
    } else if (state === 'start') {
      const interval = 1000;
      if (!timer) {
        timer = setInterval(() => {
          timeoutSeconds--;
          this.postMessage(timeoutSeconds);
        }, interval);
      }
    }
  };
};

const worker = new WebWorker(work);

export default worker;
