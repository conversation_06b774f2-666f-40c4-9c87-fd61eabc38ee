import React, { useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { getLang } from '../js';

function Captcha(props) {
  const {
    config, onSuccess, onError, onReady,
  } = props;

  const challengeRef = useRef('');
  const captchaRef = useRef(null);
  // const idRef = useRef(`captcha-box-${Date.now()}`);

  useEffect(() => {
    // challenge变化时，才重新初始化
    if (challengeRef.current === config?.challenge) return;
    challengeRef.current = config.challenge;
    // 验证码初始化
    window.initGeetest(
      {
        ...config,
        product: 'bind',
        width: '100%',
        lang: getLang(),
        onError: (e) => {
          // 初始化失败
          onError(e);
        },
      },
      (captchaObj) => {
        // 把验证按钮放到插槽里面
        // captchaObj.appendTo(`#${idRef.current}`);
        // 初始化完成
        captchaObj.onReady(() => {
          onReady(captchaObj);
          captchaRef.current?.destroy();
          captchaRef.current = captchaObj;
        });
        // 校验成功
        captchaObj.onSuccess(() => {
          // 取验证码返回的challenge，因为用户可能会刷新验证码，直接向服务商请求新的challenge
          onSuccess(captchaObj.getValidate()?.geetest_challenge);
        });
        // 期间ajax请求失败
        captchaObj.onError((e) => {
          onError(e);
        });
      },
    );
  }, [config]);
  // 组件销毁时销毁极验实例
  useEffect(
    () => () => {
      captchaRef.current?.destroy();
    },
    [],
  );

  return <div />;
}

Captcha.propTypes = {
  config: PropTypes.shape(),
  onSuccess: PropTypes.func,
  onError: PropTypes.func,
  onReady: PropTypes.func,
};
Captcha.defaultProps = {
  config: {},
  onSuccess: () => {},
  onError: () => {},
  onReady: () => {},
};
export default Captcha;
