import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import style from '../style.css';


const SwitchIcon = (props) => {
  let { value } = props;
  const { size, content, onClick } = props;
  return (
    <span
      className={classnames(style.switchWrap, style[`${size}Switch`], value ? '' : style.switchWrapFalse)}
      onClick={() => {
        value = !value;
        onClick(value);
      }}
    >
      {/* // 原本不支持content,暂时做个middle的兼容 */}
      {size === 'middle' && (
        <>
          <span className={classnames(style.switchWrapContentLeft)}>{content[0]}</span>
          <span className={classnames(style.switchWrapContentRight)}>{content[1]}</span>
        </>
      )}
    </span>
  );
};

SwitchIcon.defaultProps = {
  value: false, // 开关是否打开
  content: ['', ''], // 开关字样
  size: 'middle', // 开关整体大小; 默认值 middle，其它值：small，large
  onClick: (value) => {
    // 父级回调, 将当前value传出去
    console.log('SwitchIcon', value);
  },
};

SwitchIcon.propTypes = {
  value: PropTypes.bool,
  content: PropTypes.arrayOf(PropTypes.string),
  size: PropTypes.string,
  onClick: PropTypes.func,
};

export default SwitchIcon;
