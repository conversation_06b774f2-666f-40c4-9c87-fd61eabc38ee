import React from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import FocusInput from './focus-input';
import styles from '../style.css';
import Select from './select';
import { isObject, filterObject } from '../../lib/util';

const useLess = [
  'selectList', 'onSelect', 'selectValue', 'onCancel', 'enterShow',
];

class SelectInput extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      show: false,
    };
  }

  render() {
    const {
      // eslint-disable-next-line react/prop-types
      selectList, onSelect, selectValue, onCancel, headerTitle,
      footerContent,
    } = this.props;
    const props = filterObject(this.props, useLess);
    const footerIcon = (
      <Icon
        name="arr-right"
        className={styles.showArrow}
        style={{ color: '#b3b7c1', fontSize: 16 }}
        onClick={() => {
          // eslint-disable-next-line react/destructuring-assignment
          if (this.props?.disabled) return;
          this.setState({ show: true });
        }}
      />
    );
    const { show } = this.state;
    const cancelClick = () => {
      this.setState({
        show: false,
      });
      onCancel();
    };
    const handleEnter = () => {
      // eslint-disable-next-line react/destructuring-assignment, react/prop-types
      if (this.props.enterShow) {
        this.setState({
          show: true,
        });
      }
    };
    return (
      <>
        <FocusInput
          {...props}
          footerIcon={footerIcon}
          onPressEnter={handleEnter}
        />
        <Select
          onMaskClick={cancelClick}
          show={show}
          headerTitle={headerTitle}
          selectValue={selectValue}
          onSelect={(...args) => {
            this.setState({ show: false });
            onSelect(...args);
          }}
          selectList={selectList}
          footerContent={footerContent}
        />
      </>
    );
  }
}

SelectInput.propTypes = {
  selectList: PropTypes.arrayOf(PropTypes.shape()),
  onSelect: PropTypes.func,
  onCancel: PropTypes.func,
  selectValue: PropTypes.string,
  disabled: PropTypes.bool,
  footerContent: PropTypes.string,
};

SelectInput.defaultProps = {
  selectList: [],
  onSelect: () => {},
  onCancel: () => {},
  selectValue: '',
};

export default SelectInput;
