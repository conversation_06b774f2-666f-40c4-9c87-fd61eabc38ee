/* eslint-disable react/prop-types */
import { t } from '@shein-bbl/react';
import React from 'react';
import { Button } from 'react-weui/build/packages';

export default function (props) {
  return (
    <div style={{ textAlign: 'center', padding: 15 }}>
      <h3>{t('抱歉，页面出错了，请 拍照保存 并联系 技术支持 处理：')}</h3>
      {/* eslint-disable-next-line react/destructuring-assignment */}
      <span>{props.errorStack?.message}</span>
      <div style={{ margin: '20px 0' }}>{t('保存照片后，等待处理过程中，可以先 刷新页面 进行其他操作 或 切换到其他页面操作~')}</div>
      <Button type="primary" onClick={() => { window.location.reload(); }}>{t('刷新页面')}</Button>
      <Button type="primary" onClick={() => { window.location.hash = '/main-menu'; }}>{t('返回首页')}</Button>
    </div>
  );
}
