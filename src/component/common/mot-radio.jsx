import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import style from './common.css';

const MotRadio = (props) => {
  const {
    name,
    checked,
    onChange,
  } = props;

  const moreCls = checked ? style.iconCheck : null;

  return (
    <div
      className={style.radionBox}
      onClick={() => {
        onChange(!checked);
      }}
    >
      <div className={classNames(style.icon, moreCls)}/>
      &nbsp;&nbsp;
      <div>{name}</div>
    </div>
  );
};
MotRadio.propTypes = {
  name: PropTypes.string.isRequired,
  checked: PropTypes.bool,
  onChange: PropTypes.func.isRequired,
};

export default MotRadio;
