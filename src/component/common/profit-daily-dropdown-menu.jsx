import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import { getUserSubWarehouseAPI, getAbcCalculatePriceAPI } from '../../server/basic/common';
import { selectDict } from '../../server/basic/data-dictionary';
import {
  Pickers,
} from './index';
import Modal from './modal';
import style from './profit-daily-dropdown-menu.less';

// 业务公共组件: abc计薪 信息展示模块
function ProfitDailyDropdownMenu(props) {
  const {
    calculateLinkType,
    expandInfoRender,
    hideCalculateType,
    isNormalPicking = false, // 是否是正常拣货
  } = props;
  const [loading, setLoading] = useState(false);
  const [subWarehouseShow, setSubWarehouseShow] = useState(false); // 作业子仓 picker 独立
  const [calculateTypeShow, setCalculateTypeShow] = useState(false); // 业务类型 picker 独立
  const [waveTypeShow, setWaveTypeShow] = useState(false); // 业务子类 0:无 1:FP波次；2:普通波次 picker 独立
  const [selectedSubWarehouse, setSelectedSubWarehouse] = useState({
    label: '',
    value: '',
  }); // 主仓数据
  const [calculateType, setCalculateType] = useState({
    label: t('非整箱'),
    value: hideCalculateType ? '' : 2,
  }); // 计价类型
  const [selectedWaveType, setSelectedWaveType] = useState({
    label: t('全部'),
    value: null,
  }); // 业务子类 只有正常拣货才需要传选择的业务子类型
  const [abcCalculatePriceObj, setAbcCalculatePriceObj] = useState({}); //  abc计薪数据
  const [userSubWarehouseList, setUserSubWarehouseList] = useState([]); // 作业子仓列表
  const [waveTypeOptionList, setWaveTypeOptionList] = useState([]); // 业务子类 0:无 1:FP波次；2:普通波次

  // 计价类型 1:整箱；2:非整箱
  const calculateTypeList = [{
    label: t('整箱'),
    value: 1,
  }, {
    label: t('非整箱'),
    value: 2,
  }];

  // 获取详细信息
  const getAbcCalculatePriceData = async (action) => {
    const params = {
      ...{
        calculateLinkType,
        calculateType: calculateType.value,
      },
      ...action,
    };

    setLoading(true);
    const {
      code,
      info,
      msg,
    } = await getAbcCalculatePriceAPI(params);
    setLoading(false);

    if (code === '0') {
      setAbcCalculatePriceObj(info[0] || {});
    } else {
      Modal.error({ content: msg });
    }
  };

  // 获取仓库信息
  const getUserSubWarehouseData = async (params = {}) => {
    setLoading(true);
    const {
      code,
      info,
      msg,
    } = await getUserSubWarehouseAPI({ calculateLinkType });
    setLoading(false);

    if (code === '0') {
      // eslint-disable-next-line max-len
      if (info && info.length > 0) {
        const list = info.map((item) => ({
          ...item,
          label: item.subWarehouseName,
          value: item.subWarehouseId,
        }));
        // 没有主仓的时候，默认第一个
        const subWarehouseItem = info.find((item) => item.isMasterWarehouse) || info[0] || {};
        setSelectedSubWarehouse(subWarehouseItem);
        setUserSubWarehouseList(list);
        getAbcCalculatePriceData({
          subWarehouseId: subWarehouseItem.subWarehouseId,
          ...params,
        });
      }
    } else {
      Modal.error({ content: msg });
    }
  };

  // 获取业务子类下拉
  const getWaveTypeOptionListData = async () => {
    setLoading(true);
    const selectData = await selectDict({ catCode: ['CALCULATE_PRICE_ABC_OPRT_TP'] });
    setLoading(false);
    if (selectData.code === '0') {
      const waveTypeListFormatData = (selectData.info.data.find((x) => x.catCode === 'CALCULATE_PRICE_ABC_OPRT_TP').dictListRsps || []).map((wi) => ({
        label: wi.dictNameZh,
        value: wi.dictCode,
      }));
      setWaveTypeOptionList(waveTypeListFormatData);
    } else {
      Modal.error({ content: selectData.msg });
    }
  };

  const warehouse = JSON.parse(localStorage.getItem('warehouse') || '{}');

  useEffect(() => {
    // 非佛山仓，不请求仓库数据
    if (warehouse.warehouseId === '1') {
      // 如果是正常拣货
      if (isNormalPicking) {
        getWaveTypeOptionListData(); // 获取业务子类下拉
        getUserSubWarehouseData({
          oprtTp: selectedWaveType.value, // 默认全部 正常拣货需要默认传oprtTp业务子类字段进行数据查询
        });
      } else {
        getUserSubWarehouseData();
      }
    }
  }, []);

  useEffect(() => {
    // 根据返回的数据第一条设置相应的波次类型下拉展示
    if (isNormalPicking && Object.keys((abcCalculatePriceObj || {})).length) {
      // eslint-disable-next-line max-len
      setSelectedWaveType((waveTypeOptionList || []).find((wi) => (wi.value === (abcCalculatePriceObj || {}).oprtTp)) || {});
    }
  }, [waveTypeOptionList, abcCalculatePriceObj]);

  // 非佛山仓，不展示组件
  if (warehouse.warehouseId !== '1') {
    return null;
  }

  return (
    <>
      <div style={{
        backgroundColor: '#FFF3E5',
        color: '#FF8C00',
        fontSize: 12,
        textAlign: 'center',
        paddingRight: 15,
        paddingBottom: 4,
        paddingLeft: 15,
      }}
      >{t('当前页面数据仅根据系统实时数据展示,具体算薪以月底最终数据为准')}
      </div>
      <div style={{
        fontSize: 12, color: '#141737', marginTop: 6,
      }}
      >
        <span
          style={{
            marginLeft: 15, marginBottom: 10, display: 'inline-block', marginTop: 10,
          }}
          // className={style.mr_10}
          onClick={() => {
            if (userSubWarehouseList.length === 0) return;
            setSubWarehouseShow(true);
          }}
        >
          {selectedSubWarehouse?.subWarehouseName}
          <Icon name="pc-arrow-fill-down" />
        </span>
        {!hideCalculateType && (
          <p
            style={{ display: 'inline-block', marginLeft: 49 }}
            // className={style.mr_10}
          >
            {selectedSubWarehouse.isMasterWarehouse ? t('主仓') : t('非主仓')}
          </p>
        )}
        {!hideCalculateType && (
          <span
            style={{ marginLeft: 90 }}
            // className={style.mr_10}
            onClick={() => {
              setCalculateTypeShow(true);
            }}
          >
            {calculateType.label}
            <Icon name="pc-arrow-fill-down" />
          </span>
        )}
        {hideCalculateType && (
          <div style={{ display: 'inline-block', marginLeft: 49 }}>
            {selectedSubWarehouse.isMasterWarehouse ? t('主仓') : t('非主仓')}
          </div>
        )}
        {isNormalPicking && Object.keys(selectedWaveType || {}).length ? (
          <span
            style={{ marginLeft: 90 }} // 正常拣货没有calculateType选择
            onClick={() => {
              setWaveTypeShow(true);
            }}
          >
            {selectedWaveType.label}
            <Icon name="pc-arrow-fill-down" />
          </span>
        ) : null}

        <div style={{ height: 3, backgroundColor: '#F4F5F8' }} />

        <div className={style.ft_12}>
          {
              expandInfoRender({
                info: abcCalculatePriceObj, // abc计薪数据
                calculateType: Number(calculateType.value), // 业务类型 1:整箱；2:非整箱
                isMasterWarehouse: Boolean(selectedSubWarehouse.isMasterWarehouse), // 是否主仓
              })
            }
        </div>
      </div>
      <Pickers
        value={selectedSubWarehouse.value}
        show={subWarehouseShow}
        pickerData={[{ items: userSubWarehouseList }]}
        disabled={loading}
        placeholder={t('请选择')}
        only
        onClick={() => setSubWarehouseShow(true)}
        onCancel={() => setSubWarehouseShow(false)}
        onChange={(select) => {
          setSubWarehouseShow(false);

          if (select !== undefined) {
            setSelectedSubWarehouse(select);
            // 如果是正常拣货
            // 正常拣货getAbcCalculatePriceData需要默认传oprtTp业务子类字段进行数据查询
            if (isNormalPicking) {
              getAbcCalculatePriceData({
                subWarehouseId: select.subWarehouseId,
                oprtTp: selectedWaveType.value,
              });
            } else {
              getAbcCalculatePriceData({
                subWarehouseId: select.subWarehouseId,
              });
            }
          }
        }}
      />
      {
        !hideCalculateType && (
          <Pickers
            value={calculateType.label}
            show={calculateTypeShow}
            pickerData={[{ items: calculateTypeList }]}
            disabled={loading}
            placeholder={t('请选择')}
            only
            onClick={() => setCalculateTypeShow(true)}
            onCancel={() => setCalculateTypeShow(false)}
            onChange={(select) => {
              setCalculateTypeShow(false);
              setCalculateType(select);
              getAbcCalculatePriceData({
                calculateType: select.value,
                subWarehouseId: selectedSubWarehouse.subWarehouseId,
              });
            }}
          />
        )
      }
      <Pickers
        value={selectedWaveType.value}
        show={waveTypeShow}
        pickerData={[{ items: waveTypeOptionList }]}
        disabled={loading}
        placeholder={t('请选择')}
        only
        onClick={() => setWaveTypeShow(true)}
        onCancel={() => setWaveTypeShow(false)}
        onChange={(select) => {
          setWaveTypeShow(false);
          if (select !== undefined) {
            setSelectedWaveType(select);
            getAbcCalculatePriceData({
              oprtTp: select.value,
              subWarehouseId: selectedSubWarehouse.subWarehouseId,
            });
          }
        }}
      />
      <div style={{ backgroundColor: '#F4F5F8', height: 5 }} />
    </>
  );
}

ProfitDailyDropdownMenu.defaultProps = {
  expandInfoRender: () => {
  },
  hideCalculateType: false, // 是否隐藏整箱，默认不隐藏
};

/**
 * calculateLinkType: 业务环节,1:入库/库内,2:出库
 * expandInfoRender(info, calculateType, isMasterWarehouse): 展开信息
 * @param {Object} info abc计薪数据
 * @param {Number} calculateType 业务类型 1:整箱；2:非整箱
 * @param {Boolean} isMasterWarehouse 是否主仓
 */
ProfitDailyDropdownMenu.propTypes = {
  calculateLinkType: PropTypes.number, // 业务环节,1:入库/库内,2:出库
  expandInfoRender: PropTypes.func, // 展开信息
  hideCalculateType: PropTypes.bool, // 是否隐藏整箱，默认不隐藏
  isNormalPicking: PropTypes.bool,
};

export default ProfitDailyDropdownMenu;
