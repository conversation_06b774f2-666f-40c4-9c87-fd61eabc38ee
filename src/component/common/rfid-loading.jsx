import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import style from './rfid-loading.less';

function RfidLoading(props) {
  const {
    tipsMsg,
  } = props;
  return (
    <div className={style.outDiv}>
      <div className={style.tipsDiv}>
        <p className={style.tipsP}>{tipsMsg || t('写入中，请对准RFID标签，请勿移动')}</p>
        <div className={style.loadingDiv}>
          <div className={style.rfidLoading}>
            <div />
            <div />
            <div />
            <div />
            <div />
            <div />
            <div />
            <div />
          </div>
        </div>
      </div>
    </div>
  );
}

RfidLoading.defaultProps = {
  tipsMsg: t('写入中，请对准RFID标签，请勿移动'),
};

RfidLoading.propTypes = {
  tipsMsg: PropTypes.string,
};

export default RfidLoading;
