import React from 'react';
import PropTypes from 'prop-types';
import styles from './style.css';

const SwitchBar = (props) => {
  const {
    title,
    list,
    listKeys,
    value,
    onChange,
    color,
    style,
  } = props;
  const [val, text] = listKeys;

  return (
    <section style={style} className={styles.switchBar}>
      {title && <div className={styles.leftTitle}>{title}</div>}
      <div className={styles.rightSwitch}>
        {
          list.map(item => (
            <div
              onClick={() => {
                // 减少触发无效change
                if (value === item[val]) {
                  return;
                }
                onChange(item[val], item);
              }}
              className={styles.switchItem}
              key={item[val]}
            >
              {
                value === item[val]
                  ? <span style={{ borderColor: color }} className={styles.switchChecked} />
                  : <span className={styles.switchDefault} />
              }
              <span className={styles.switchText}>{item[text]}</span>
            </div>
          ))
        }
      </div>
    </section>
  );
};

SwitchBar.propTypes = {
  title: PropTypes.string,
  list: PropTypes.arrayOf(PropTypes.shape()),
  listKeys: PropTypes.arrayOf(PropTypes.string),
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.bool,
  ]),
  onChange: PropTypes.func,
  color: PropTypes.string,
  style: PropTypes.shape(),
};

SwitchBar.defaultProps = {
  title: '',
  list: [],
  listKeys: ['val', 'text'],
  value: '',
  onChange: () => {},
  color: '#197AFA',
  style: {},
};

export default SwitchBar;
