

/* SwitchBar
---------------------------------------------------------------- */
.switchBar {
    display: flex;
    height: 40px;
    align-items: center;
    justify-content: space-around;
    background-color: #f4f5f8;
}
.leftTitle {
    flex: 2;
    color: #141737;
    font-size: 14px;
    padding-left: 14px;
}
.rightSwitch {
    flex: 3;
    display: flex;
    justify-content: space-around;
}
.switchItem {
    flex: 1;
    display: flex;
    align-items: center;
}
.switchChecked, .switchDefault {
    box-sizing: border-box;
    width: 18px;
    height: 18px;
    border-radius: 50%;
}
.switchChecked {
    background: #fff;
    border: 7px solid #197afa;
}
.switchDefault {
    border: 1px solid #999DA8;
}
.switchText {
    margin-left: 6px;
    color: #35383d;
    font-size: 14px;
}


/* SelectList
---------------------------------------------------------------- */
.selectList {
    background-color: #ffffff;
    color: #141737;
    font-size: 14px
}
.selectItem {
    position: relative;
    height: 44px;
    line-height: 44px;
    padding-left: 14px;
    border-bottom: 1px solid #E8EBF0;
}
.selectChecked {
    color: #197afa;
}
.selectChecked2 {
    color: #52c41a;
}
.checkIcon {
    position: absolute;
    font-size: 16px;
    top: calc(50% - 8px);
    right: 14px;
    color: #197afa;
}
.emptyWrap {
    text-align: center;
    font-size: 18px;
    color: #888;
    margin-top: 8px;
}

/* SearchInput
---------------------------------------------------------------- */
.searchInputWrap {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 6px;
}
.searchIcon, .closeIcon {
    position: absolute;
    top: calc(50% - 8px);
    color: #999da8;
}
.searchIcon {
    left: 6px;
}
.closeIcon {
    right: 8px;
    top: calc(50% - 12px);
    padding: 4px;
}
.inputWrap {
    position: relative;
    flex: 1;
    height: 36px;
    background: #F4F5F8;
    border-radius: 18px;
    padding: 0 30px;
}
.searchInput {
    height: 36px;
    font-size: 14px;
}
.searchBtn {
    padding: 8px 6px 8px 10px;
    font-size: 14px;
    color: #666c7c;
}

/* Demo
---------------------------------------------------------------- */


