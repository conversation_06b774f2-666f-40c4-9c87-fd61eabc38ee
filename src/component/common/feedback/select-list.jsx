import React from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import classnames from 'classnames';
import styles from './style.css';

const SelectList = (props) => {
  const {
    list = [],
    listKeys,
    onChange,
    style,
    itemStyle,
    empty,
    selectMore,
    type,
    color,
  } = props;
  let {
    value,
  } = props;
  const [val, text] = listKeys;
  if (!selectMore && !Array.isArray(value)) {
    value = [value];
  }
  return (
    <section style={style} className={styles.selectList}>
      {
        !list.length && (
          <div className={styles.emptyWrap}>{empty}</div>
        )
      }
      {list.map(item => (
        <div
          onClick={() => {
            if (selectMore) {
              // 点中选中的则变成取消选中
              let newValue = [...value];
              if (newValue.indexOf(item[val]) !== -1) {
                newValue = newValue.filter(v => v !== item[val]);
                onChange(newValue);
                return;
              }
              newValue.push(item[val]);
              onChange([...newValue]);
            } else {
              // 点中选中的则变成取消选中
              if (value.indexOf(item[val]) !== -1) {
                onChange('', {});
                return;
              }
              onChange(item[val], item);
            }
          }}
          className={classnames(styles.selectItem, value.indexOf(item[val]) !== -1 ? (color ? styles.selectChecked2 : styles.selectChecked) : '')}
          style={itemStyle}
          key={item[val]}
        >
          {item[text]}
          {value.indexOf(item[val]) !== -1 && <Icon className={styles.checkIcon} name="check" style={{ color }} />}
        </div>
      ))}
    </section>
  );
};

SelectList.propTypes = {
  list: PropTypes.arrayOf(PropTypes.shape()),
  listKeys: PropTypes.arrayOf(PropTypes.string),
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.bool,
    PropTypes.arrayOf(PropTypes.number),
    PropTypes.arrayOf(PropTypes.string),
  ]),
  empty: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.element,
  ]),
  onChange: PropTypes.func,
  style: PropTypes.shape(),
  itemStyle: PropTypes.shape(),
  selectMore: PropTypes.bool,
  type: PropTypes.string,
  color: PropTypes.string,
};

SelectList.defaultProps = {
  list: [],
  listKeys: ['val', 'text'],
  value: '',
  empty: '', // list为空时显示文案或组件
  onChange: () => {},
  style: {},
  itemStyle: {},
  selectMore: false,
  type: 'val', // 返回的数据，val: 返回传进来的val集合; text: 返回传进来的text集合; object: 返回传进来的对象集合
};

export default SelectList;
