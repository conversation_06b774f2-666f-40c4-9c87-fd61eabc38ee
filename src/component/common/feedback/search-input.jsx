import React from 'react';
import PropTypes from 'prop-types';
import { Input } from 'react-weui/build/packages';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import { debounceFn } from 'lib/util';
import styles from './style.css';

class SearchInput extends React.Component {
  constructor(props) {
    super(props);
    // 给输入触发搜索回调函数加上防抖
    this.debounceInputChange = debounceFn(props.onSearch, props.delay);
  }

  render() {
    const {
      value,
      onClear,
      wrapStyle,
      searchText,
      autoSearch,
      onSearch,
      ...otherProps
    } = this.props;
    return (
      <section style={wrapStyle} className={styles.searchInputWrap}>
        <div className={styles.inputWrap}>
          <Icon name="search" className={styles.searchIcon} />
          <Input
            className={styles.searchInput}
            value={value}
            onKeyUp={(e) => {
              // 回车直接触发搜索
              if (e.key === 'Enter') {
                onSearch(e.target.value);
              } else if (autoSearch) {
                // 若autoSearch为true，则输入即时触发搜索---并加上防抖
                this.debounceInputChange(e.target.value);
              }
            }}
            {...otherProps}
          />
          {value && (
            <Icon
              name="close-rev"
              className={styles.closeIcon}
              onClick={onClear}
            />
          )}
        </div>
        {searchText && (
          <span
            onClick={() => onSearch(value)}
            className={styles.searchBtn}
          >
            {searchText}
          </span>
        )}
      </section>
    );
  }
}

// 后续待优化：autoSearch兼容其它两种搜索，去掉上一个延时搜索
SearchInput.propTypes = {
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  autoSearch: PropTypes.bool,
  delay: PropTypes.number,
  onClear: PropTypes.func,
  onSearch: PropTypes.func,
  searchText: PropTypes.string,
  placeholder: PropTypes.string,
  wrapStyle: PropTypes.shape(),
};

SearchInput.defaultProps = {
  value: '',
  placeholder: t('请输入搜索关键词'),
  searchText: t('搜索'), // 传假值能隐藏搜索按钮
  autoSearch: false, // 是否输入就触发搜索
  delay: 1000, // 配合autoSearch使用，触发搜索防抖的时间间隔
  onClear: () => {}, // 输入框清除回调
  onSearch: () => {}, // 输入框搜索回调，有三个地方会触发
  wrapStyle: {}, // 最外层包裹样式
};

export default SearchInput;
