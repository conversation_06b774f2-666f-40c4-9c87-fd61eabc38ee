import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import assign from 'object-assign';
import { Input, FormCell } from 'react-weui/build/packages/components/form';
import { CellHeader, CellBody, CellFooter } from 'react-weui/build/packages/components/cell';
import Label from 'react-weui/build/packages/components/label';
import { isObject, filterObject } from '../../lib/util';
import styles from '../style.css';
import { inputFocus } from './common';

const useLess = [
  'onKeyUp', 'onPressEnter', 'label', 'children', 'onBlur', 'keepFocus', 'lineBreak',
  'arrow', 'allowClear', 'onClear', 'importance', 'inputStyle', 'allowClearBig', 'footerIcon',
  'labelShowStyleObj', 'toUpperCase', 'ignoreDisabled',
];

class FocusInput extends Component {
  constructor(props) {
    super(props);
    this.state = {
      active: false,
    };
  }

  componentWillUnmount() {
    this.setState = () => false;
  }

  active() {
    this.setState({
      active: true,
    });
  }

  inactive() {
    this.setState({
      active: false,
    });
  }

  render() {
    const {
      onPressEnter,
      disabled,
      keepFocus,
      lineBreak = true,
      importance,
      arrow,
      allowClear,
      onClear,
      onChange,
      onFocus,
      ftstyle,
      inputStyle,
      cellstyle,
      allowClearBig,
      footerIcon: ftIcon,
      labelShowStyleObj,
      toUpperCase,
      ignoreDisabled,
      isRequired,
    } = this.props;
    const { value } = this.props;
    const props = filterObject(this.props, useLess);
    let formShowStyle = {};
    let labelShowStyle = {};
    let inputShowStyle = assign({ paddingRight: '20px', boxSizing: 'border-box' }, inputStyle);
    const defaultFooterStyle = {
      position: 'absolute', bottom: 0, right: 0, backgroundColor: '#fff', zIndex: 9,
    };
    const { children } = this.props;
    let { label, footer } = this.props;
    if (Array.isArray(children)) {
      label = children.filter((v) => v.type === 'label');
      footer = children.filter((v) => v.type === 'footer');
    }
    if (isObject(children) && children.type === 'label') {
      label = children.props.children;
    }
    if (isObject(children) && children.type === 'footer') {
      footer = children.props.children;
    }
    if (importance) {
      inputShowStyle = { ...inputShowStyle, fontWeight: 700, color: 'black' };
    }
    if (lineBreak) {
      formShowStyle = {
        display: 'block',
        height: 'auto',
        paddingBottom: '5px',
        paddingTop: '5px',
      };
      labelShowStyle = {
        width: '100%',
        color: '#616161',
        // marginBottom: '5px',
      };
    } else {
      labelShowStyle = labelShowStyleObj;
    }
    const onBlur = (e) => {
      if (keepFocus && !disabled) {
        inputFocus(e).then((d) => {
          // 当前聚焦的是输入框的时候清除active状态
          if (!d) {
            this.inactive();
          }
        });
      } else {
        this.inactive();
        // eslint-disable-next-line react/destructuring-assignment
        this.props.onBlur(e);
      }
    };
    let footerIcon;
    if (arrow) {
      footerIcon = (
        <Icon name="arr-right" className={styles.showArrow} style={{ color: '#b3b7c1', fontSize: 16 }} />
      );
    }
    if (allowClear && value && (!disabled || ignoreDisabled)) {
      footerIcon = (
        allowClearBig ? (
          <span
            className={styles.showClearBig}
            onClick={() => {
              if (onChange) {
                onChange({ target: { value: '' } });
              }
            }}
          >
            <Icon
              name="close-rev"
              className={styles.showArrow}
              style={{ display: value && (!disabled || ignoreDisabled) ? '' : 'none' }}
            />
          </span>
        ) : (
          <Icon
            name="close-rev"
            className={styles.showArrow}
            style={{ display: value && (!disabled || ignoreDisabled) ? '' : 'none' }}
            onClick={() => {
              if (onChange) {
                onChange({ target: { value: '' } });
              }
              if (onClear) {
                onClear();
              }
            }}
          />
        )
      );
    }

    footerIcon = ftIcon || footerIcon;
    const { active } = this.state;
    // 处理disabled 之后恢复disabled，但是没有聚焦依然为蓝色的问题
    if (disabled && active) {
      setTimeout(() => {
        this.inactive();
      }, 0);
    }
    return (
      <FormCell
        style={assign({
          borderBottom: active && !disabled ? '2px solid #0059CE' : '2px solid #FFFFFF',
          boxSizing: 'border-box',
          height: '40px',
          marginBottom: 1,
          position: 'relative',
          fontSize: 14,
        }, formShowStyle, cellstyle)}
        ref={(node) => {
          // eslint-disable-next-line react/no-unused-class-component-methods
          this.node = node;
        }}
      >
        {
          label ? (
            <CellHeader>
              <Label
                style={labelShowStyle}
                disabled={disabled}
              >
                {isRequired && <span className={styles.required} />}
                {label}
              </Label>
            </CellHeader>
          ) : null
        }

        <CellBody>
          <Input
            style={inputShowStyle}
            {...props}
            onBlur={onBlur}
            onKeyUp={(e) => {
              if (e.key === 'Enter') {
                if (toUpperCase) {
                  onPressEnter(e.target.value.toUpperCase(), e);
                  return;
                }
                onPressEnter(e);
              }
            }}
            onChange={(e) => {
              e.persist();
              if (toUpperCase) {
                onChange(e.target.value.toUpperCase(), e);
                return;
              }
              onChange(e);
            }}
            onFocus={(e) => {
              this.active();
              onFocus(e);
            }}
          />
          {
            footerIcon
          }
        </CellBody>

        {
          footer ? (
            <CellFooter style={
              ftstyle ? assign({}, defaultFooterStyle, ftstyle) : defaultFooterStyle
            }
            >
              {footer}
            </CellFooter>
          ) : null
        }
      </FormCell>
    );
  }
}

const defaultFun = () => { };

FocusInput.defaultProps = {
  keepFocus: true,
  onBlur: defaultFun,
  arrow: false,
  importance: false,
  onClear: defaultFun,
  onPressEnter: defaultFun,
  onChange: defaultFun,
  onFocus: defaultFun,
  inputStyle: {},
  footerIcon: null,
  labelShowStyleObj: {}, // 用于设置不换行时，label样式
  toUpperCase: false,
  ignoreDisabled: false,
};

FocusInput.propTypes = {
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  onChange: PropTypes.func,
  onPressEnter: PropTypes.func,
  onClear: PropTypes.func,
  keepFocus: PropTypes.bool,
  lineBreak: PropTypes.bool,
  children: PropTypes.shape(),
  arrow: PropTypes.bool,
  importance: PropTypes.bool,
  ftstyle: PropTypes.shape(),
  cellstyle: PropTypes.shape(),
  inputStyle: PropTypes.shape(),
  allowClearBig: PropTypes.bool,
  footerIcon: PropTypes.shape(),
  labelShowStyleObj: PropTypes.shape(),
  toUpperCase: PropTypes.bool,
  ignoreDisabled: PropTypes.bool,
  isRequired: PropTypes.bool,
  disabled: PropTypes.bool,
  allowClear: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  footer: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
};

export default FocusInput;
