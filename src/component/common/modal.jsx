import React, { Component } from 'react';
import ReactDOM from 'react-dom';
import PropTypes from 'prop-types';
import { Dialog } from 'react-weui/build/packages';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
// eslint-disable-next-line import/no-cycle
import { classFocus } from 'lib/util';
import { inputBlur, preFocus } from './common';
import errorAudio from '../../source/audio/delete.mp3';
import successAudio from '../../source/audio/ok.mp3';

const audioError = new Audio(errorAudio);
audioError.load();
const audioSuccess = new Audio(successAudio);
audioSuccess.load();

class DialogContainer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      show: true,
    };
    this.onConfirm = this.onConfirm.bind(this);
  }

  componentDidMount() {
    inputBlur();
    const {
      autoFocusButton, onCancel, onOk, getModalNode,
    } = this.props;
    // 获取弹框实例
    if (getModalNode) {
      getModalNode(this);
    }
    if (autoFocusButton) {
      if (autoFocusButton === 'ok' && onOk) {
        document.querySelector('.dialog-container .weui-dialog__btn_primary').focus();
      } else if (autoFocusButton === 'cancel' && onCancel) {
        document.querySelector('.dialog-container .weui-dialog__btn_default').focus();
      }
    }
  }

  onConfirm(type) {
    const {
      className,
      onCancel,
      onOk,
      buttons = [],
      onClose,
      domContainer,
    } = this.props;
    this.setState({ show: false });
    preFocus();
    if (type === 'cancel') {
      // TODO
    }
    if (type === 'ok' && onOk && buttons.length === 0) {
      onOk();
    }
    if (type === 'cancel' && onCancel && buttons.length === 0) {
      onCancel();
    }

    if (type === 'close' && onClose) {
      onClose();
    }
    if (domContainer) {
      ReactDOM.unmountComponentAtNode(domContainer);
    } else {
      // eslint-disable-next-line react/no-find-dom-node
      const node = ReactDOM.findDOMNode(this);
      const dialogContainerNode = node.parentNode;
      document.body.removeChild(dialogContainerNode);
      ReactDOM.unmountComponentAtNode(dialogContainerNode);
    }
    if (className) {
      classFocus(className);
    }
  }

  render() {
    const {
      title,
      content,
      buttons = [],
      type,
      cancelText,
      okText,
      className,
      onOk,
      autoFocusButton,
      audioSuc,
      audioErr,
      onClose,
      styleType,
      innerTitle,
      // needIcon = true,
    } = this.props;
    const { show } = this.state;
    let btns;
    // let icon;
    let titleText;
    switch (type) {
      case 'error':
        // icon = 'cancel';
        titleText = (
          <>
            <Icon style={{ color: '#ff4d50', paddingRight: '5px' }} name="error" />
            {innerTitle || t('错误')}
          </>
        );
        break;
      case 'success':
        // icon = 'success';
        titleText = (
          <>
            <Icon style={{ color: '#52c41a', paddingRight: '5px' }} name="pc-check_broken-fill" />
            {innerTitle || t('成功')}
          </>
        );
        break;
      case 'confirm':
        // icon = 'warn';
        titleText = (
          <>
            <Icon style={{ color: '#ff8c00', paddingRight: '5px' }} name="pc-attention-circle-shineout-fill" />
            {innerTitle || t('提示')}
          </>
        );
        break;
      case 'info':
        titleText = (
          <>
            <Icon style={{ color: '#197afa', paddingRight: '5px' }} name="info-rev" />
            {innerTitle || t('提示')}
          </>
        );
        break;
      case 'img':
        titleText = '';
        break;
      case 'confirm2':
        titleText = innerTitle || title;
        break;
      default:
        break;
    }
    if (buttons.length > 0) {
      btns = buttons.map((item) => ({
        label: item.label,
        type: item.type,
        onClick: () => {
          item.onClick();
          if (!item.noClose) {
            this.onConfirm();
          }
        },
      }));
    } else {
      if (type === 'error' || type === 'success' || type === 'info') {
        btns = [{
          label: okText || t('确定'),
          type: 'primary',
          onClick: () => {
            this.onConfirm('ok');
          },
        }];
      } else if (type === 'img') {
        btns = [{
          label: cancelText || t('取消'),
          type: 'default',
          onClick: () => {
            this.onConfirm('close');
          },
        }];
      } else {
        btns = [{
          label: cancelText || t('取消'),
          type: 'default',
          onClick: () => {
            this.onConfirm('cancel');
          },
        }, {
          label: okText || t('确定'),
          type: 'primary',
          onClick: () => {
            this.onConfirm('ok');
          },
        }];
      }
    }
    if (audioSuc) {
      audioSuccess.play();
    }
    if (audioErr) {
      audioError.play();
    }

    const closeStyle = {
      position: 'absolute',
      top: 9,
      right: 9,
      fontSize: 16,
    };
    return (
      <Dialog
        title={titleText}
        show={show}
        buttons={btns}
        type={styleType}
        className={className}
      >
        {!!onClose && (
          <span onClick={() => this.onConfirm('close')} style={closeStyle}>
            <Icon name="close" />
          </span>
        )}
        {type === 'confirm2' ? (content) : (title || content)}
      </Dialog>
    );
  }
}

DialogContainer.propTypes = {
  title: PropTypes.string,
  content: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.element,
  ]),
  buttons: PropTypes.arrayOf(PropTypes.arrayOf(PropTypes.shape())),
  className: PropTypes.string,
  type: PropTypes.string,
  cancelText: PropTypes.string,
  okText: PropTypes.string,
  onOk: PropTypes.func,
  onCancel: PropTypes.func,
  getModalNode: PropTypes.func,
  autoFocusButton: PropTypes.string,
  audioSuc: PropTypes.bool,
  audioErr: PropTypes.bool,
  onClose: PropTypes.func,
  domContainer: PropTypes.shape(),
  styleType: PropTypes.number,
  innerTitle: PropTypes.string,
};

const createDialog = (options, type) => {
  let container;
  if (options.domContainer) {
    container = options.domContainer;
  } else {
    container = document.createElement('div');
    document.body.appendChild(container);
    // modalBlurInput为true：弹窗时输入框失去焦点，防止重复回车重复弹窗
    if (options.modalBlurInput) {
      container.className = 'dialog-container modalBlurInput';
    } else {
      container.className = 'dialog-container';
    }
  }
  options.type = type;
  ReactDOM.render(<DialogContainer {...options} />, container);
};

const showDialog = (type) => (options) => {
  createDialog(options, type);
  return (opts) => {
    createDialog(opts, type);
  };
};

export default {
  error: showDialog('error'),
  success: showDialog('success'),
  confirm: showDialog('confirm'),
  info: showDialog('info'),
  img: showDialog('img'),
  confirm2: showDialog('confirm2'),
};
