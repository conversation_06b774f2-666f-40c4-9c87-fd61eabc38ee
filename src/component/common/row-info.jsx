import React from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import assign from 'object-assign';
import style from '../style.css';

const RowInfo = (props) => {
  const {
    label,
    content,
    type,
    data,
    extraStyle,
    textExtraStyle,
  } = props;
  if (data && Array.isArray(data)) {
    // 兼容三个的显示: 正常PDA一行最多显示3个
    if (data.length == 3) {
      return (
        <div style={{ display: 'flex', flexDirection: 'row' }}>
          {
            data.map(item => (
              <div
                key={item.label + item.content}
                className={style.infoBox}
                style={{ ...extraStyle, flex: 1 }}
              >
                <div
                  className={classnames(style.infoLabel, style[item.type] || style.info)}
                  style={{ marginLeft: 16 }}
                >
                  {item.label}
                </div>
                <div className={style.infoContent} style={textExtraStyle}>
                  {item.content}
                </div>
              </div>
            ))
          }
        </div>
      );
    }
    // 下边是兼容以前的两个
    const [fD, sD] = data;
    return (
      <div style={{ display: 'flex', flexDirection: 'row' }}>
        <div className={style.infoBox} style={{ ...extraStyle, width: '50%', marginRight: '7px' }}>
          <div
            className={classnames(style.infoLabel, style[fD.type] || style.info)}
          >
            {fD.label}
          </div>
          <div className={style.infoContent} style={textExtraStyle}>
            {fD.content}
          </div>
        </div>
        <div className={style.infoBox} style={{ ...extraStyle, width: '50%' }}>
          <div
            className={classnames(style.infoLabel, style[sD.type] || style.info)}
            style={{ marginLeft: '19px' }}
          >
            {sD.label}
          </div>
          <div className={style.infoContent} style={textExtraStyle}>
            {sD.content}
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className={style.infoBox} style={extraStyle}>
      <div className={classnames(style.infoLabel, style[type])}>
        {label}
      </div>
      <div className={style.infoContent} style={textExtraStyle}>
        {content}
      </div>
    </div>
  );
};

RowInfo.defaultProps = {
  type: 'info',
};

RowInfo.propTypes = {
  label: PropTypes.string,
  content: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.node,
  ]),
  type: PropTypes.string,
  extraStyle: PropTypes.shape(),
  data: PropTypes.arrayOf(PropTypes.shape),
};

export default RowInfo;
