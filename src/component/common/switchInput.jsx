import React from 'react';
import { Switch } from 'react-weui/build/packages';
import PropTypes from 'prop-types';
import FocusInput from './focus-input';

class SwitchInput extends React.PureComponent {
  render() {
    const {
      title,
      inputOnChange,
      inputPressEnter,
      switchClick,
      switchClassName,
      inputClassName,
      value,
      disabled,
      autoFocus,
    } = this.props;
    return (
      <div
        className="weui-cell"
        style={{
          borderBottom: '2px solid rgb(255, 255, 255)',
          boxSizing: 'border-box',
          flexDirection: 'column',
        }}
      >
        <div style={{ width: '100%' }}>
          <label>{title}</label>
        </div>
        <div style={{ width: '100%', display: 'flex' }}>
          <FocusInput
            cellstyle={{ marginLeft: 0, flex: 1 }}
            autoFocus={autoFocus || false}
            disabled={disabled}
            className={inputClassName}
            value={value}
            onChange={inputOnChange}
            onPressEnter={inputPressEnter}
          />
          <Switch
            className={switchClassName}
            onClick={switchClick}
          />
        </div>
      </div>
    );
  }
}

SwitchInput.propTypes = {
  title: PropTypes.string,
  inputOnChange: PropTypes.func,
  inputPressEnter: PropTypes.func,
  switchClick: PropTypes.func,
  switchClassName: PropTypes.string,
  inputClassName: PropTypes.string,
  value: PropTypes.string,
  disabled: PropTypes.bool,
  autoFocus: PropTypes.bool,
};

export default SwitchInput;
