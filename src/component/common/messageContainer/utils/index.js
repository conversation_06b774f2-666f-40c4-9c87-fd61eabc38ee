// 心跳检查
const heartCheck = {
  // 心跳检查时间间隔为20s
  timeout: 20000,
  // 5s未收到pong回复认为服务断开
  responseTimeout: 5000,
  timeoutObj: null,
  serverTimeoutObj: null,
  reset() {
    clearTimeout(this.timeoutObj);
    clearTimeout(this.serverTimeoutObj);
    return this;
  },
  start(ws) {
    const self = this;
    this.timeoutObj = setTimeout(() => {
      ws.send('ping');
      self.serverTimeoutObj = setTimeout(() => {
        ws.close(4000, 'heartbeat check failed');
      }, self.responseTimeout);
    }, this.timeout);
  },
};

// 客户端重连
const reconnect = {
  tryTime: 0,
  tryTimeout: 2000,
  reset() {
    this.tryTime = 0;
  },
  start(reconnectAction) {
    this.tryTime += 1;
    setTimeout(() => {
      // eslint-disable-next-line no-console
      console.log(`try reconnect time: ${this.tryTime}`);
      reconnectAction();
    }, this.tryTimeout);
  },
};

const postData = async (token, baseUrl, url, data) => {
  if (!fetch) {
    console.error('your browser does not support fetch, please update');
    return;
  }
  // eslint-disable-next-line consistent-return
  return fetch(`${baseUrl}${url}`, {
    body: JSON.stringify(data),
    headers: {
      'content-type': 'application/json',
      Authorization: token,
    },
    method: 'POST',
  })
    .then((res) => res.json())
    .catch((e) => e);
};

const getData = async (token, baseUrl, url, pageSize, pageNo, searchValue) => {
  console.log('getData');
  if (!fetch) {
    console.error('your browser does not support fetch, please update');
    return;
  }
  let wholeUrl = `${baseUrl}${url}/${pageSize}/${pageNo}`;
  if (url === '/getHistoryRecord') {
    wholeUrl = `${baseUrl}${url}/${pageSize}/${pageNo}/${searchValue}`;
  }
  // eslint-disable-next-line consistent-return
  return fetch(wholeUrl, {
    // credentials: 'include',
    headers: {
      Authorization: token,
    },
  })
    .then((res) => res.json())
    .catch((e) => e);
};

const createIdbMsg = (msg, tasks, type, pageId) => ({
  pageId,
  timestamp: new Date().getTime(),
  type,
  messages: msg,
  tasks,
});

const supportWarn = (prop) => {
  // eslint-disable-next-line no-console
  console.warn(
    `your browser does not support ${prop}, messages in different tab pages can not auto-sync`,
  );
};

export {
  heartCheck, postData, getData, createIdbMsg, supportWarn, reconnect,
};
