.arrow {
  content: '';
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  border-width: 10px;
  border-top-width: 5px;
  border-style: solid;
  left: 50%;
  transform: translateX(-50%);
}

.bell {
  font-size: 24px;
  cursor: pointer;
  -moz-user-select: none;
  -webkit-user-select: none;
  display: block;
}


.progress {
  position: absolute;
  width: 100%;
}

.messageCount {
  min-width: 24px;
  height: 24px;
  display: inline-flex;
  background-color: red;
  border-radius: 12px;
  color: white;
  font-size: 14px;
  transform: scale(0.6);
  padding: 0 3px;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: -12px;
  right: -15px;
}

.redDot {
  width: 6px;
  height: 6px;
  border-radius: 3px;
  background-color: red;
  position: absolute;
  top: -3px;
  right: -3px;
}

.panel {
  width: 300px;
  height: 400px;
  opacity: 0;
  position: absolute;
  top: 34px;
  margin-top: 2px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  color: #333e59;
  z-index: 10000;
  background-color: white;
  left: 50%;
  transform: translateX(-50%) scale(0);
  transform-origin: 50% 0;
  .header {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    padding: 10px 0 20px 0;
    // margin-bottom: 12px;
    div {
      width: 50%;
      height: 30px;
      line-height: 30px;
      text-align: center;
      &:nth-child(1) {
        padding-left: 20px;
      }
      &:nth-child(2) {
        padding-right: 20px;
      }
    }
  }
}

.messageTip {
  padding: 2px 4px;
}

.panel::after {
  .arrow();
  border-color: transparent transparent rgba(0, 0, 0, 0.03) transparent;
  top: -15px;
}

.panel::before {
  .arrow();
  border-color: transparent transparent #fff transparent;
  z-index: 1;
  top: -13px;
}

.item {
  display: inline-block;
  cursor: pointer;
  height: 100%;
  position: relative;
}

.activeItem {
  color: #429aff;
  border-bottom: 2px solid #197afa;
}

.showPanel {
  animation: showPanel 0.2s forwards;
  // opacity: 1;
  // transform: translateX(-50%) scale(1);
  // transform-origin: 50% 0;
  // transition: opacity .2s, transform .2s;
}

.hidePanel {
  animation: hidePanel 0.2s forwards;
}

.content {
  width: 100%;
  // padding-bottom: 8px;
  height: calc(100% - 60px);
  overflow-y: scroll;
  -ms-overflow-style: none;
  overflow: -moz-scrollbars-none;
  &::-webkit-scrollbar { width: 0 !important }
}

.emptyContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 80px;
}

.searchArea {
  padding: 0 16px;
  margin-bottom: 16px;
  height: 30px;
  display: inline-flex;
  justify-content: space-between;
  .search {
    width: 50%;
  }
}

.messageList {
  margin: 0;
  // overflow-y: scroll;
  // height: 100%;
  padding: 0 16px;
  .panel_icon {
    font-size: 14px;
    color: #197afa;
    margin-right: 8px;
    font-weight: bold;
    line-height: 24px !important;
  }
  li {
    width: 100%;
    list-style: none;
    justify-content: space-between;
    margin-bottom: 8px;
    cursor: pointer;

    > div:nth-child(1) {
      font-size: 14px;
      line-height: 24px;
      height: 24px;
      margin-bottom: 2px;
      display: flex;
      position: relative;
      justify-content: space-between;
      > span:nth-child(1) {
        position: relative;
        padding-right: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      > span:nth-child(2) {
        font-size: 12px;
        color: #b3b7c1;
        white-space: nowrap;
      }
    }
    > div:nth-child(2) {
      font-size: 12px;
      color: #333e59;
      display: inline-flex;
      width: 100%;
      justify-content: space-between;
      align-items: flex-end;
      div:nth-child(1) {
        font-size: 12px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        padding-left: 22px;
      }
    }
  }

  .progressContainer {
    padding-left: 6px;
    margin-bottom: 12px;
    .progressTitle {
      margin-bottom: 2px;
      display: inline-flex;
      align-items: center;
      width: 100%;
      > span:nth-child(1) {
        width: 65%;
        margin-right: 5%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      > span:nth-child(2) {
        width: 30%;
        text-align: right;
        font-size: 12px;
        > span:nth-child(2) {
          margin-left: 6px;
        }
        > span:nth-child(1) {
          color: #197afa;
          margin-right: 6px;
        }
      }
    }
    .progressContent {
      font-size: 12px;
      padding-left: 22px;
    }
  }
}

.hide {
  display: none;
}

@keyframes showPanel {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
  30% {
    opacity: 0;
    transform: translateX(-50%) scale(0.6);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

@keyframes hidePanel {
  0% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
  60% {
    opacity: 0.15;
    transform: translateX(-50%) scale(0.6);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
}

.loadEnd {
  text-align: center;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  font-weight: 400 !important;
  font-size: 12px;
  color: #666;
  padding: 4px 0 8px 0;
}

// 样式覆盖
.cardStyle {
  border: none;
  :global {
    .so-card-header {
      padding: 0;
      &::after {
        height: 0;
      }
    }
    .so-card-indicator {
      display: none;
    }
    .so-card-body {
      padding: 10px;
      background-color: #f4f5f8;
      border-radius: 4px;
      margin-bottom: 12px;
      font-size: 12px;
    }
  }
}

.searchArea {
  :global {
    .so-pagination-item {
      min-width: 0;
    }
    .so-pagination-simple-span {
      margin-left: 8px;
    }
    .so-pagination-item,
    .so-pagination-section {
      margin-right: 4px;
    }
    .so-pagination-simple-input {
      width: 40px !important;
    }
    .so-pagination-links {
      margin-right: 0;
      position: relative;
      top: -2px;
    }
  }
}
