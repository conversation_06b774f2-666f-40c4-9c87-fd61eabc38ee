import { decode } from 'js-base64';

const keyPrefix = 'message_info_';

class MessageStorage {
  constructor(token = '') {
    const decoded = decode(token.split('.')[1] || '') || {};
    this.appKey = decoded.appKey;
    this.uid = decoded.uid;
    this.extended = decoded.extended;
  }

  // get listName() {
  //   return `${listPrefix}${this.appKey}_${this.session}`
  // }

  // get list() {
  //   return localStorage.getItem(this.listName)
  // }

  get keyName() {
    return `${keyPrefix}${this.appKey}_${this.uid}${this.extended}`;
  }

  get item() {
    return localStorage.getItem(this.keyName);
  }

  /**
   * @param {string} val
   */
  set item(val) {
    if (typeof val === 'object' || Array.isArray(val)) {
      localStorage[this.keyName] = JSON.stringify(val);
    } else {
      localStorage[this.keyName] = String(val);
    }
  }

  remove() {
    localStorage.removeItem(this.keyName);
  }

  send(message) {
    this.item = message;
    this.remove();
  }

  join() {
    this.item = 'i am joined';
    this.remove();
  }

  // set list(val) {
  //   localStorage[this.listName] = JSON.stringify(val)
  // }

  // remove() {
  //   const arr = JSON.parse(localStorage.getItem(this.listName)) || []
  //   if (arr.lenght > 1) {
  //     localStorage.removeItem(this.keyName)
  //   }
  //   this.list = arr.filter(v => v !== this.clientId)
  // }
}

export default MessageStorage;
