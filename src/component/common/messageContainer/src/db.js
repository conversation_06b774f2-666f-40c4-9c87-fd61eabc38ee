/* eslint-disable func-names */
const promisify = (func) => (...arg) => new Promise((resolve, reject) => {
  const p = func.apply(this, arg);
  p.onerror = (evt) => reject(evt);
  p.onsuccess = (evt) => resolve(evt);
  p.onupgradeneeded = (evt) => {
    const db = evt.target.result;
    db.createObjectStore('messages', { autoIncrement: true });
    db.createObjectStore('tabIndexes');
  };
});

export default class {
  constructor(db) {
    this.dbName = db;
    this.ctx = window.indexedDB
      || window.mozIndexedDB
      || window.webkitIndexedDB
      || window.msIndexedDB;
    this.evt = null;
  }

  async init() {
    const openAsync = promisify(this.ctx.open.bind(this.ctx));
    this.evt = await openAsync(this.dbName);
  }

  createMessageTranscation() {
    const transcation = this.evt.target.result.transaction(
      ['messages'],
      'readwrite',
    );
    return transcation.objectStore('messages');
  }

  createTabIndexesTranscation() {
    const transcation = this.evt.target.result.transaction(
      ['tabIndexes'],
      'readwrite',
    );
    return transcation.objectStore('tabIndexes');
  }

  async addMessage(msg) {
    const ctx = this.createMessageTranscation();
    const addAsync = promisify(ctx.add.bind(ctx));
    await addAsync(msg);
    return true;
  }

  async putTabIndex(key, msg) {
    if (!this.evt) return;
    const ctx = this.createTabIndexesTranscation();
    const putAsync = promisify(ctx.put.bind(ctx));
    await putAsync(msg, key);
  }

  async deleteTabIndex(key) {
    const ctx = this.createTabIndexesTranscation();
    const deleteAsync = promisify(ctx.delete.bind(ctx));
    await deleteAsync(key);
    return true;
  }

  async getTabIndexes() {
    return new Promise((resolve, reject) => {
      const ctx = this.createTabIndexesTranscation();
      const res = [];
      ctx.openKeyCursor().onsuccess = function (event) {
        const cursor = event.target.result;
        if (cursor) {
          res.push(cursor.key);
          cursor.continue();
        } else {
          resolve(res);
        }
      };
      ctx.openKeyCursor().onerror = function (err) {
        reject(err);
      };
    });
  }

  async getMessages() {
    const ctx = this.createMessageTranscation();
    const openCursorAsync = promisify(ctx.openCursor.bind(ctx));
    const cursor = (await openCursorAsync(undefined, 'prev')).target.result;
    if (cursor) {
      return cursor.value.messages;
    }
    return [];
  }

  async getTasks() {
    const ctx = this.createMessageTranscation();
    const openCursorAsync = promisify(ctx.openCursor.bind(ctx));
    const cursor = (await openCursorAsync(undefined, 'prev')).target.result;
    if (cursor) {
      return cursor.value.tasks;
    }
    return [];
  }

  async clearMessages() {
    const ctx = this.createMessageTranscation();
    const clearAsync = promisify(ctx.clear.bind(ctx));
    await clearAsync();
    return true;
  }

  async clearTabIndexes() {
    const ctx = this.createTabIndexesTranscation();
    const clearAsync = promisify(ctx.clear.bind(ctx));
    await clearAsync();
    return true;
  }
}
