// 这个组件是从https://ue.dev.sheincorp.cn/component/MessageContainer/2.2.2-rc.1#MessageContainer拷贝下拉的
// 因为业务组件需要安装shineout，此需求不需要用到，所以拷贝业务组件进行修改，去掉shineout代码
import { t } from '@shein-bbl/react';
/* eslint-disable max-len */
import React from 'react';
import PropTypes from 'prop-types';
// eslint-disable-next-line import/no-unresolved
import immer from 'immer';
import { v4 as uuidv4 } from 'uuid';
import es6Promise from 'es6-promise';
import axios from 'axios';
import decode from 'jwt-decode';
import sha1 from 'sha1';
import {
  heartCheck, createIdbMsg, supportWarn, reconnect,
} from '../utils';
// import Panel from './Panel';
import MessageStorage from './messageStorage';
import IndexedDB from './db';

es6Promise.polyfill();

const idbName = 'cloud-message';
// const Fonticon = Icon('//at.alicdn.com/t/font_1712965_if6lyxept6.css')
let idb;
if (window.indexedDB) idb = new IndexedDB(idbName);
const wsConnectionUrl = process.env.CLOUD_MESSAGE_ADDR || 'cloud-message-dev01.sheincorp.cn';
// const wsConnectionUrl = 'localhost:8088'

/**
 * @cn 信息容器
 * @desc 作为前端接收推送消息容器
 * <AUTHOR>
 * @date 2020-04-20
 */
class MessageContainer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      messages: [],
      tasks: [],
      panelIndex: 0,
      // historyRecords: [],
      // animationOk: false,
      // showPanel: false,
      // loading: 0, // 0 未加载， 1 加载中， 2 加载完
      // pageSize: 5,
      // pageNo: 1,
      // count: 0,
      // searchValue: '',
    };
    // 默认以浏览器版本为扩展字段
    // const defaultExtended = sha1(JSON.stringify(navigator.appVersion))
    // this.appKey = props.appKey
    // this.uid = props.uid
    // this.token = props.token
    this.delay = props.delay;

    this.baseUrl = `https://${wsConnectionUrl}`;
    this.changeActiveItem = this.changeActiveItem.bind(this);
    this.handlePanelShow = this.handlePanelShow.bind(this);
    this.clickAway = this.clickAway.bind(this);
    // this.handlePageNoChange = this.handlePageNoChange.bind(this);
    this.handleStorage = this.handleStorage.bind(this);
    // this.notCheckedMessages = [];
    // this.notReadMessages = [];
    // this.postData = curry(postData)(this.token, this.baseUrl);
    // this.getData = curry(getData)(this.token, this.baseUrl);
    this.getConnectionState = this.getConnectionState.bind(this);
    // if (this.token && typeof this.token === 'string') {
    //   this.appKey = decode(this.token).appKey
    //   this.uid = decode(this.token).uid
    //   this.extended = decode(this.token).extended || defaultExtended
    // }
    // this.key = `tabid-${this.appKey}-${this.uid}`
    this.tabid = uuidv4();
    // this.messageStorage = new MessageStorage(this.token)
    // if (crossTab) {
    //   // 是否为主tab页
    //   this.isMaster = false
    //   this.masterid = undefined

    //   if (!('onbeforeunload' in window)) {
    //     supportWarn('onbeforeunload')
    //   } else {
    //     window.addEventListener('beforeunload', () => {
    //       if (this.isMaster) {
    //         this.messageStorage.send(`leave-${this.tabid}`)
    //       }
    //     })
    //     // 防止快速刷新时beforeunload不触发
    //     window.addEventListener('unload', () => {
    //       // 防止业务系统误删除了localStorage
    //       this.messageStorage.send(`tabClose-${this.tabid}`)
    //       this.messageStorage.send('tabChange')
    //     })
    //   }

    //   if (!('onstorage' in window)) {
    //     supportWarn('onstorage')
    //   } else {
    //     window.addEventListener('storage', this.handleStorage)
    //   }
    // } else {
    //   window.addEventListener('storage', this.handleStorageNoTab)
    // }
  }

  async componentDidMount() {
    const { getToken, token, crossTab } = this.props;
    this.token = token;
    if (getToken) {
      this.token = await getToken();
    }
    if (this.token && typeof this.token === 'string') {
      const { appKey, uid, extended } = decode(this.token);
      this.appKey = appKey;
      this.uid = uid;
      this.extended = extended || sha1(JSON.stringify(navigator.appVersion)); // 默认以浏览器版本为扩展字段
      this.key = `tabid-${this.appKey}-${this.uid}`;
      this.messageStorage = new MessageStorage(this.token);
    }
    if (crossTab) {
      // 是否为主tab页
      this.isMaster = false;
      this.masterid = undefined;

      if (!('onbeforeunload' in window)) {
        supportWarn('onbeforeunload');
      } else {
        window.addEventListener('beforeunload', () => {
          if (this.isMaster) {
            this.messageStorage.send(`leave-${this.tabid}`);
          }
        });
        // 防止快速刷新时beforeunload不触发
        window.addEventListener('unload', () => {
          // 防止业务系统误删除了localStorage
          this.messageStorage.send(`tabClose-${this.tabid}`);
          this.messageStorage.send('tabChange');
        });
      }

      if (!('onstorage' in window)) {
        supportWarn('onstorage');
      } else {
        window.addEventListener('storage', this.handleStorage);
      }
    } else {
      window.addEventListener('storage', this.handleStorageNoTab);
    }

    this.changeConnectionState(true);

    if (crossTab) {
      // 总是更新所有tab页的token
      // 当建连tab页token过期时，任何一个页面的刷新或者重新登录（只要更新了this.token）都可以触发一次重连
      this.messageStorage.send(`update-token-${this.token}`);

      if (!idb) {
        supportWarn('indexedDB');
      } else {
        await idb.init();
      }
      await idb.clearTabIndexes();
      // 将tabid写入indexdb
      await idb.putTabIndex(this.tabid, this.key);
      this.messageStorage.send('tabChange');

      // 询问是否已经有master
      this.messageStorage.send('hasMaster?');

      // 从indexeddb中同步消息
      const newMessage = await idb.getMessages();
      const newTasks = await idb.getTasks();
      this.setState(immer((state) => {
        // eslint-disable-next-line no-param-reassign
        state.messages = newMessage;
        // eslint-disable-next-line no-param-reassign
        state.tasks = newTasks;
      }));

      setTimeout(async () => {
        // 补偿，防止indexDB事务阻塞导致自身还未被写入
        const indexedTabs = await idb.getTabIndexes();
        // eslint-disable-next-line max-len
        const combimedTabs = indexedTabs.includes(this.tabid) ? indexedTabs : [...indexedTabs, this.tabid];

        const tabs = combimedTabs.sort((a, b) => {
          if (a > b) return -1;
          return 1;
        });

        if (this.tabid === tabs[0] && !this.masterid) {
          this.changeConnectionState(false);
          this.isMaster = true;
          await idb.clearMessages();
          this.connect();
        }
      }, this.delay);
    } else {
      setTimeout(() => {
        this.changeConnectionState(false);
        this.messageStorage.send(`newTab-${this.tabid}`);
        this.connect();
      }, this.delay);
    }
  }

  componentWillUnmount() {
    this.disconnect();
  }

  setMessageState = (data) => {
    const { onUnreadMessagesReceive, onNewMessageReceive, crossTab } = this.props;
    return immer((state) => {
      if (Array.isArray(data)) {
        // eslint-disable-next-line no-param-reassign
        state.messages = data;
        if (onUnreadMessagesReceive) onUnreadMessagesReceive(data);
      } else {
        if (onNewMessageReceive) onNewMessageReceive(data.message);
        if (crossTab) this.messageStorage.send(`newMessage-${JSON.stringify(data)}`);
        if (data.messageType === 'message') {
          state.messages.unshift(data);
        }
        if (data.messageType === 'task') {
          const idx = state.tasks.findIndex((v) => v.message.taskId === data.message.taskId);
          if (idx === -1) {
            state.tasks.unshift(data);
          } else if (data.message.taskNum !== data.message.completedNum) {
            state.tasks.splice(idx, 1, data);
          } else {
            state.messages.unshift(data);
            state.tasks.splice(idx, 1);
          }
        }
      }
    });
  };

  // 获取连接状态
  getConnectionState() {
    if (!window.WebSocket) return undefined;
    return this.connectionState;
  }

  // 修改连接状态
  // eslint-disable-next-line react/sort-comp
  changeConnectionState(state) {
    this.connectionState = state;
    window.cloudMessageConnectionState = state;
    localStorage.setItem('cloudMessageConnectionState', state);
  }

  // eslint-disable-next-line react/sort-comp
  async handleStorage(ev) {
    if (ev.key === this.messageStorage.keyName) {
      if (ev.newValue === 'get' || ev.newValue === 'update') {
        if (idb) {
          const newMessage = await idb.getMessages();
          const newTasks = await idb.getTasks();
          this.setState(immer((state) => {
            // eslint-disable-next-line no-param-reassign
            state.messages = newMessage;
            // eslint-disable-next-line no-param-reassign
            state.tasks = newTasks;
          }));
        }
      }
      if (/^newMessage-/.test(ev.newValue)) {
        const { onNewMessageReceive } = this.props;
        const newMessage = JSON.parse(ev.newValue.substr(11) || '{}');
        if (onNewMessageReceive) onNewMessageReceive(newMessage.message);
      }
      if (/^leave-/.test(ev.newValue)) {
        this.masterid = undefined;
        const toBeDelete = ev.newValue.split('-').slice(1).join('-');
        const indexedTabs = await idb.getTabIndexes();
        const combimedTabs = indexedTabs.includes(this.tabid) ? indexedTabs : [...indexedTabs, this.tabid];
        const toBeDeleteIndex = combimedTabs.findIndex((v) => v === toBeDelete);
        const finalTabs = toBeDeleteIndex > -1 ? [...combimedTabs.slice(0, toBeDeleteIndex), ...combimedTabs.slice(toBeDeleteIndex + 1)] : combimedTabs;

        const tabs = finalTabs.sort((a, b) => {
          if (a > b) return -1;
          return 1;
        });
        if (this.tabid === tabs[0]) {
          this.isMaster = true;
          await idb.clearMessages();
          this.connect('reconnect');
        }
      }
      if (ev.newValue === 'tabChange') {
        await idb.putTabIndex(this.tabid, this.key);
      }
      if (/^tabClose-/.test(ev.newValue)) {
        const toBeDelete = ev.newValue.split('-').slice(1).join('-');
        await idb.deleteTabIndex(toBeDelete);
      }
      if (ev.newValue === 'hasMaster?') {
        if (this.isMaster) this.messageStorage.send(`iammaster-${this.tabid}`);
      }
      if (/^iammaster-/.test(ev.newValue)) {
        const masterid = ev.newValue.split('-').slice(1).join('-');
        this.masterid = masterid;
      }
      if (ev.newValue === 'readState-update') {
        const { messages } = this.state;
        this.setState({ messages: messages.map((v) => ({ ...v, hasRead: true })) });
      }
      if (/^changeConnectionState-/.test(ev.newValue)) {
        const state = ev.newValue.split('-')[1];
        let transformed;
        if (state === 'true') transformed = true;
        else transformed = false;
        this.changeConnectionState(transformed);
      }
      if (/^update-token-/.test(ev.newValue)) {
        const tk = ev.newValue.split('-').slice(2).join('-');
        this.token = tk;
        if (this.isMaster && !this.connectionState) {
          this.connect('reconnect');
        }
      }
    }
  }

  handleStorageNoTab(ev) {
    if (ev.key === this.messageStorage.keyName) {
      if (/^newTab-/.test(ev.newValue)) {
        const id = ev.newValue.split('-')[1];
        if (id !== this.tabid) {
          this.disconnect();
        }
      }
    }
  }

  disconnect() {
    const { crossTab } = this.props;
    if (this.ws) this.ws.close();
    if (this.cancelLongPoll) this.cancelLongPoll();
    if (crossTab && this.isMaster) {
      this.messageStorage.send(`leave-${this.tabid}`);
    }
    this.changeConnectionState(false);
    if (crossTab) {
      this.messageStorage.send('changeConnectionState-false');
      this.messageStorage.send(`tabClose-${this.tabid}`);
      this.messageStorage.send('tabChange');
      window.removeEventListener('storage', this.handleStorage);
      this.isMaster = false;
    } else {
      window.removeEventListener('storage', this.handleStorageNoTab);
    }
  }

  async connect(type) {
    const { crossTab, getToken } = this.props;
    if (getToken && type === 'reconnect') {
      this.token = await getToken();
    }
    // 校验token是否过期
    if (!this.token) {
      // 连接状态置为false，并通知所有tab页更新连接状态
      this.changeConnectionState(false);
      if (crossTab) this.messageStorage.send('changeConnectionState-false');

      // eslint-disable-next-line no-console
      console.log('[ CLOUD-MESSAGE ]token missing');
      return;
    }
    const decoded = decode(this.token);
    if (Date.now() > decoded.exp * 1000) {
      // 连接状态置为false，并通知所有tab页更新连接状态
      this.changeConnectionState(false);
      if (crossTab) this.messageStorage.send('changeConnectionState-false');

      this.connectionState = false;
      console.error('jsonwebtoken expired');
      return;
    }
    if (window.WebSocket) {
      this.createWsConnect();
    } else {
      this.createLongpoll(type);
    }
  }

  async createLongpoll(type, receipts) {
    const { crossTab } = this.props;
    const { token } = this;
    const { onError, getUnreadMessage, onConnectionEstablished } = this.props;
    let url = `${this.baseUrl}/getMessage?u=${this.tabid}&defaultExtended=${this.extended}`;
    if (type === 'reconnect' && receipts) {
      url = `${url}&m=${JSON.stringify(receipts)}`;
    }
    if (getUnreadMessage && type !== 'reconnect') {
      url = `${url}&getUnreadMessage=true`;
    }
    this.isMaster = true;

    const { CancelToken } = axios;
    const source = CancelToken.source();
    this.cancelLongPoll = () => source.cancel('connection canceled');
    const mixedConn = {
      close: () => {
        this.cancelLongPoll();
        if (crossTab) window.removeEventListener('storage', this.handleStorage);
      },
    };
    if (onConnectionEstablished) onConnectionEstablished(mixedConn);
    axios.get(url, { cancelToken: source.token, timeout: 30000, headers: { Authorization: token } })
      // .then(res => res.json())
      .then((r) => {
        const res = r.data;
        const { publishTime, mid } = res;
        if (res.code === '1001') {
          this.isMaster = false;
          return;
        }
        if (res.code === '1002') {
          this.createLongpoll('reconnect');
          return;
        }
        this.setState(this.setMessageState(res), () => {
          if (crossTab) this.addToIDBAndMessage('get');
        });
        let receipt;
        if (Array.isArray(res)) {
          receipt = res.map((v) => ({
            type: 'ack',
            appKey: this.appKey,
            uid: this.uid,
            publishTime: new Date(v.publishTime).getTime(),
            mid: v.mid,
          }));
        } else {
          receipt = {
            type: 'ack',
            appKey: this.appKey,
            uid: this.uid,
            publishTime: new Date(publishTime).getTime(),
            mid,
          };
        }
        this.createLongpoll('reconnect', receipt);
      })
      .catch((error) => {
        setTimeout(() => {
          this.createLongpoll('reconnect');
          if (onError) return onError(error);
          // eslint-disable-next-line no-console
          return console.log(error);
        }, 5000);
      });
  }

  async createWsConnect() {
    const { getUnreadMessage, crossTab } = this.props;
    const { token } = this;
    let wsUrl = `wss://${wsConnectionUrl}/getMessage?token=${token}&defaultExtended=${this.extended}`;

    // 是否接收离线消息
    if (getUnreadMessage) wsUrl = `${wsUrl}&getUnreadMessage=true`;

    if (this.ws) this.ws.close();
    this.ws = new WebSocket(wsUrl);

    // 默认连接状态置为true，除非连接失败时修改状态
    this.changeConnectionState(true);
    if (crossTab) this.messageStorage.send('changeConnectionState-true');

    this.ws.onopen = () => {
      this.wsGetError = false;
      const { onConnectionEstablished } = this.props;
      const mixedWs = {
        close: () => {
          this.ws.close();
          if (crossTab) window.removeEventListener('storage', this.handleStorage);
        },
      };
      if (onConnectionEstablished) onConnectionEstablished(mixedWs);
      this.isMaster = true;
      reconnect.reset();
      heartCheck.reset().start(this.ws);
    };

    this.ws.onmessage = (evt) => {
      if (evt.data === 'pong') {
        heartCheck.reset().start(this.ws); // 重置心跳检查
        return;
      }
      const data = JSON.parse(evt.data);

      const { publishTime, mid } = data;

      // this.messageStorage.item = data
      this.setState(this.setMessageState(data), () => {
        if (crossTab) this.addToIDBAndMessage('get');
      });
      // 消息回执
      let receipts;
      if (Array.isArray(data)) {
        receipts = data.map((v) => ({
          type: 'ack',
          appKey: this.appKey,
          uid: this.uid,
          publishTime: new Date(v.publishTime).getTime(),
          mid: v.mid,
        }));
      } else {
        receipts = {
          type: 'ack',
          appKey: this.appKey,
          uid: this.uid,
          publishTime: new Date(publishTime).getTime(),
          mid,
        };
      }
      this.ws.send(JSON.stringify(receipts));
    };

    this.ws.onclose = async (e) => {
      // 连接状态置为false，并通知所有tab页更新连接状态
      this.changeConnectionState(false);
      if (crossTab) this.messageStorage.send('changeConnectionState-false');

      const { code } = e;
      // 1001 终端离开 | 1005 调用ws.close()断开 | 1000 ws库7.3.0版本修改了后台ws.close()返回的状态码
      // 4000 自定义连接超时 以及其他状态码 尝试重连
      // 只检查在连过程中的断线重连
      // eslint-disable-next-line no-console
      console.log('close code: ', code, 'error: ', this.wsGetError);
      if (code !== 1000 && code !== 1001 && code !== 1005 && !this.wsGetError) {
        reconnect.start(this.connect.bind(this, 'reconnect'));
      } else {
        heartCheck.reset();
        this.isMaster = false;
        // eslint-disable-next-line no-console
        console.log('connection close');
      }
    };

    this.ws.onerror = () => {
      // 连接状态置为false，并通知所有tab页更新连接状态
      this.changeConnectionState(false);
      if (crossTab) this.messageStorage.send('changeConnectionState-false');

      this.wsGetError = true;
      reconnect.start(this.connect.bind(this, 'reconnect'));
      // eslint-disable-next-line no-console
      console.error('ws connection error');
    };
  }

  addToIDBAndMessage(type) {
    return (
      async () => {
        const { messages, tasks } = this.state;
        if (idb) await idb.addMessage(createIdbMsg(messages, tasks, type, this.tabid));
        if (type) this.messageStorage.send(type);
      }
    );
  }

  changeActiveItem(index) {
    this.setState({ panelIndex: index });
  }

  handlePanelShow() {
    const { showPanel } = this.state;
    this.setState({ showPanel: !showPanel }, async () => {
      if (!showPanel) {
        document.addEventListener('click', this.clickAway);
      }
    });
  }

  // handlePageNoChange(cur) {
  //   this.setState({ pageNo: cur }, () => {
  //     this.getHistoryRecords();
  //   });
  // }

  async clickAway() {
    const { crossTab } = this.props;
    this.setState(immer((draft) => {
      // eslint-disable-next-line no-param-reassign
      // draft.showPanel = false;
      const { panelIndex } = this.state;
      // eslint-disable-next-line no-param-reassign
      if (panelIndex === 0) draft.messages = draft.messages.map((v) => ({ ...v, hasRead: true }));
    }), () => {
      if (crossTab) this.addToIDBAndMessage();
    });
    if (crossTab) this.messageStorage.send('readState-update');
    document.removeEventListener('click', this.clickAway);
  }

  /**
   * 此方法仅作为测试文档工具用, 若增加 @private 声明则不会显示在文档中
   * @param {String} user 用户姓名
   * @param {Boolean} bValid 是否有效，默认为 false
   * @return {Number} 返回值
   */
  // testMethod(user, bValid) {
  //   return 0
  // }

  /**
   * 处理自定义文本，可用于支持多语言
   * @private
   */
  // get uiText() {
  //   const { props } = this;
  //   return { ...MessageContainer.defaultProps.uiText || {}, ...props.uiText };
  // }

  /**
   * render method
   * @return {jsx} rendered nodes
   */
  render() {
    return (
      <div>
        {/* <div onClick={this.handlePanelShow}>
          <div>
            {this.renderTip()}
          </div>
        </div> */}
      </div>
    );
  }
}

MessageContainer.defaultProps = {
  // mode: 'progress',
  getUnreadMessage: false,
  // useDefaultMessageContainer: false,
  delay: 500,
  crossTab: true,
  uiText: {
    closeTip: t('cloud-message 已经在新页面建立连接，本页面自动断开，如需重连请刷新页面～'),
  },
};

MessageContainer.propTypes = {
  style: PropTypes.shape({}),
  /**
   * token和下面的getToken二选一
   */
  token: PropTypes.string,
  /**
   * 获取token的方法, 传入的话在连接ws之前会调用一次, 需要返回字符串, 支持异步
   */
  getToken: PropTypes.string,
  /**
   * 自定义组件中使用的文本
   */
  uiText: PropTypes.shape({}),
  /**
   * 获取未读消息
   * 消息分为普通消息和任务消息 历史任务消息只展示任务结果
   * @param {Array} data 未读消息数组
   */
  onUnreadMessagesReceive: PropTypes.func,
  /**
   * 新消息到达时的回调
   * 消息字段说明：
   * title: 消息标题
   * content: 消息内容
   * taskId: 任务id，当消息类型为task时，增加唯一标识区分进度所属任务
   * <self>: 任意业务自定义字段
   * @param {Object} data 新消息
   */
  onNewMessageReceive: PropTypes.func,
  /**
   * 自定义消息内容展示
   * 如 (msg) => <span>{msg.content}</span>
   * @param {Object} message
   */
  // customContentRender: PropTypes.func,
  /**
   * 组件类发生错误时的回调
   * 如 (error) => { console.log(error) }
   * @param {Error} error
   */
  onError: PropTypes.func,
  /**
   * 任务进度模式
   * 可取值'progress' | 'text'
   */
  // mode: PropTypes.string,

  /**
   * 是否获取未读消息
   */
  getUnreadMessage: PropTypes.bool,
  /**
   * websocket建立后的回调
   * (ws) => console.log(ws)
   * 可用于前端控制关闭连接
   * @param {Websocket} ws
   */
  onConnectionEstablished: PropTypes.func,

  /**
   * 是否使用默认的消息容器
   */
  // useDefaultMessageContainer: PropTypes.bool,

  /**
   * 延迟建连时间，当系统初始化主线程较忙，出现重复建连时，应调大该值，默认500ms
   */
  delay: PropTypes.number,
  /**
   * 跨tab通讯
   * 默认为 true， 设置为 false 会关闭 indexedDB 多 tab 同步功能
   * <p>ps: 该开关设置为 false 会导致多个浏览器 tab 不共用一条 ws，<b style="color:red">服务器链接数会剧增，慎用<b></p>
   * <p>请确保用户不会有多个 tab 的场景中使用</p>
   */
  crossTab: PropTypes.bool,
};

export default MessageContainer;
