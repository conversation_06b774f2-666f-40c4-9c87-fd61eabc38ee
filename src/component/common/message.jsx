import React, { Component } from 'react';
import ReactDOM from 'react-dom';
import TopTips from 'react-weui/build/packages/components/toptips';
import PropTypes from 'prop-types';

const emptyFun = (content, duration, onClose, lock) => {};
class TopTipsContainer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      show: true,
    };
    this.close = (duration) => {
      setTimeout(() => {
        if (props.onClose && typeof props.onClose === 'function') {
          props.onClose();
        }
        // eslint-disable-next-line react/no-find-dom-node
        const node = ReactDOM.findDOMNode(this);
        document.body.removeChild(node.parentNode);
        ReactDOM.unmountComponentAtNode(node.parentNode);
      }, duration);
    };
  }

  render() {
    const {
      content,
      type,
      duration,
    } = this.props;

    const { show } = this.state;
    this.close(duration);

    return (
      <TopTips
        type={type}
        show={show}
      >
        {content}
      </TopTips>
    );
  }
}

TopTipsContainer.propTypes = {
  type: PropTypes.string,
  content: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.node,
  ]),
  duration: PropTypes.number,
  onClose: PropTypes.func,
};

const api = {
  open(props) {
    let len = document.querySelectorAll('.weui-toptips').length;
    const div = document.createElement('div');
    div.style.position = 'fixed';
    if (props.lock) {
      len = 0;
    }
    div.style.top = `${len * 55}px`;
    div.style.left = '0px';
    div.style.width = '100%';
    div.style.zIndex = 2000;

    document.body.appendChild(div);
    ReactDOM.render(<TopTipsContainer {...props} />, div);
  },
  error: emptyFun,
  warn: emptyFun,
  info: emptyFun,
  warning: emptyFun,
};

const defaultFun = () => {};

[
  { key: 'success', type: 'primary' },
  { key: 'error', type: 'warn' },
  { key: 'info', type: 'info' },
  { key: 'warning', type: 'warning' },
].forEach(({ key, type }) => {
  // eslint-disable-next-line default-param-last
  api[key] = (content, duration = 3000, onClose = defaultFun, lock) => {
    if (typeof duration === 'boolean') {
      lock = duration;
      duration = 3000;
    }
    if (typeof onClose === 'boolean') {
      lock = onClose;
      onClose = defaultFun;
    }
    if (lock === undefined) {
      lock = true;
    }
    const props = {
      content, duration, type, onClose, lock,
    };
    return api.open(props);
  };
});

export default api;
