import React from 'react';
import PropTypes from 'prop-types';
import { LoadMore } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import styles from '../style.css';

const defaultStyle = {
  height: 0,
  flex: '1 1 auto',
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
};

// 滚动到最底部时, scrollHeight - scrollTop 不一定等于 clientHeight，会存在误差
const tolerance = (num1, num2) => {
  const res = Math.abs(num1 - num2);
  return res < 1;
};

function ListItem(props) {
  const {
    rows,
    rowData,
    itemClass,
    itemStyle,
  } = props;

  if (rows.some((item) => !Array.isArray(item))) {
    throw new Error(t('rows中每一项必须为数组'));
  }

  return (
    <div className={`${styles.listItem} ${itemClass}`} style={itemStyle}>
      {
        rows.map((item, index) => (
          <div className={styles.row} key={index}>
            {
              item.map((v) => {
                let flex;
                const { width } = v;
                if (width && (typeof width === 'number')) {
                  flex = `1 1 ${width}%`;
                } else if (width && (typeof width === 'string')) {
                  flex = `1 1 ${width}`;
                }
                if (!v.render) {
                  return null;
                } else if (typeof v.render !== 'function') {
                  if (rowData[v.render] === 0) {
                    return (
                      <div style={{ flex }} key={v.render}>
                        <span className={styles.lable} style={v.titleRenderStyle}>{v.title}</span>
                        <span className={styles.value} style={v.itemRenderStyle}>0</span>
                      </div>
                    );
                  }
                  return (
                    <div style={{ flex }} key={v.render}>
                      <span className={styles.lable} style={v.titleRenderStyle}>{v.title}</span>
                      <span className={styles.value} style={v.itemRenderStyle}>{rowData[v.render] || v.default}</span>
                    </div>
                  );
                } else if (typeof v.render === 'function') {
                  return (
                    <div style={{ flex }} key={v.render}>
                      {v.render(rowData)}
                    </div>
                  );
                }
              })
            }
          </div>
        ))
      }
    </div>
  );
}

ListItem.propTypes = {
  rows: PropTypes.arrayOf(PropTypes.array),
  rowData: PropTypes.shape(),
  itemClass: PropTypes.string,
  itemStyle: PropTypes.shape(),
};

class List extends React.Component {
  constructor(props) {
    super(props);
    this.listRef = React.createRef();
    this.prevScrollTop = 0;
    this.state = {
      showData: [], // 当前展示的数据
      current: 0, // 当前展示的数据,在data中的index
      loading: false, // 是否展示loading
      fillHeight: 0, // 填充高度: 最后一组数据长度 <= showNum,这时页面是无法滚动的，所以要加个fillHeight让页面滚动
      pages: 0, // 总共多少页
      total: 0, // 数据总条数
    };
    this.setShowData = this.setShowData.bind(this);
    this.getShowData = this.getShowData.bind(this);
    this.getMaxCurrent = this.getMaxCurrent.bind(this);
    this.setFillHeight = this.setFillHeight.bind(this);
    this.getRowHeight = this.getRowHeight.bind(this);
    this.setPagination = this.setPagination.bind(this);
    this.reset = this.reset.bind(this);
  }

  componentDidMount() {
    const self = this;
    const div = this.listRef.current;

    this.setShowData();
    this.setPagination();

    window.onresize = () => {
      this.reset();
    };

    div.onscroll = () => {
      const { scrollTop, clientHeight, scrollHeight } = div;
      const { current } = self.state;
      const maxCurrent = self.getMaxCurrent();

      /*
        1: self.prevScrollTop > scrollTop: 页面向下滚动
        2: self.prevScrollTop < scrollTop: 页面向上滚动
        3: 当scrollTop === 0 && current > 0时: 页面已经滚动到最底部，获取上一组数据
        4: 当scrollHeight - scrollTop === clientHeight && current < maxCurrent时: 页面已经滚动到最顶部，获取下一组数据
      */
      if (scrollTop === 0 && current > 0 && self.prevScrollTop > scrollTop) {
        self.setState({
          current: current - 1,
          // loading: true,
        }, () => {
          self.setShowData();
          if (self.state.current === self.getMaxCurrent() - 1) {
            self.setState({
              fillHeight: 0,
            });
            div.scrollTop = self.state.scrollHeight - clientHeight - 1;
          } else {
            // -1 是为了页面可以上下两个方向滚动
            div.scrollTop = scrollHeight - clientHeight - 1;
          }
          self.prevScrollTop = scrollHeight - clientHeight;
        });
      } else if (tolerance(scrollHeight - scrollTop, clientHeight) && current < maxCurrent && self.prevScrollTop < scrollTop) {
        // 当滚动到倒数第二组数据时，存储scrollHeight，因为最后一组数据的scrollHeight与之前的不同
        if (current === self.getMaxCurrent() - 1) {
          self.setState({
            scrollHeight,
          });
        }
        self.setState({
          current: current + 1,
        }, () => {
          self.setShowData();
          // 不设为0而是1，是为了能上下两个方向滚动
          div.scrollTop = 1;
          self.prevScrollTop = 0;
        });
      }
      self.prevScrollTop = scrollTop;
    };
  }

  componentDidUpdate(prevProps) {
    const { data } = this.props;
    if (prevProps.data !== data) {
      this.reset();
    }
  }

  setShowData() {
    const showData = this.getShowData();

    this.setState({
      showData,
    }, () => {
      const { current } = this.state;
      if (current === this.getMaxCurrent()) {
        this.setFillHeight();
      }
    });
  }

  getShowData() {
    const { current } = this.state;
    const { data, showNum } = this.props;

    const start = current * showNum;
    const end = current * showNum + showNum;
    const showData = data.slice(start, end);

    return showData;
  }

  getRowHeight() {
    const { rows } = this.props;
    return rows.length * 24 + 16;
  }

  getMaxCurrent() {
    const { data: { length }, showNum } = this.props;
    const max = Math.ceil(length / showNum);
    return max - 1;
  }

  setFillHeight() {
    const div = this.listRef.current;
    const showData = this.getShowData();
    const rowHeight = this.getRowHeight();
    if (rowHeight * showData.length <= div.clientHeight) {
      const fillHeight = div.clientHeight - rowHeight * showData.length + 1;
      this.setState({
        fillHeight,
      });
    }
  }

  setPagination() {
    const { showNum, data } = this.props;
    const total = data.length;
    const rest = total % showNum;
    const pages = rest === 0 ? total / showNum : Math.floor(total / showNum) + 1;
    this.setState({
      total,
      pages,
    });
  }

  reset() {
    this.setState({
      showData: [],
      current: 0,
      loading: false,
      fillHeight: 0,
      total: 0,
      pages: 0,
    }, () => {
      this.setShowData();
      this.setPagination();
      this.listRef.current.scrollTop = 0;
    });
  }

  render() {
    const {
      rows,
      data,
      style,
      className,
      header,
      rowStyleOrClass,
      separate,
      showPagination,
      ItemOnclick,
    } = this.props;

    const {
      showData, loading, current, fillHeight, total, pages,
    } = this.state;
    if (!Array.isArray(data)) {
      throw new Error(t('data和rows必须为数组!'));
    }

    if (header && !React.isValidElement(header)) {
      throw new Error(t('header必须是react元素!'));
    }

    const type = rowStyleOrClass && (typeof rowStyleOrClass);
    if (type && type !== 'object' && type !== 'function' && type !== 'string') {
      throw new Error(t('itemStyle必须为object、function或string!'));
    }

    return (
      <div style={({ ...defaultStyle, ...style })} className={className}>
        <div className={styles.listHeader}>
          {header}
        </div>
        {
          loading &&
          (
            <div className={styles.listLoading}>
              <LoadMore loading />
            </div>
          )
        }
        <div className={styles.listOverflow} ref={this.listRef}>
          {
            showData.map((item, index) => {
              const evenBg = {
                backgroundColor: '#F9F9F9',
              };

              const oddBg = {
                backgroundColor: '#FFFFFF',
              };

              let itemStyle = {};

              if (separate) {
                itemStyle = index % 2 === 1 ? oddBg : evenBg;
              }

              let itemClass = '';
              if (type && type === 'function') {
                itemClass = rowStyleOrClass(item, index);
              } else {
                switch (type) {
                  case 'object':
                    itemStyle = rowStyleOrClass;
                    break;
                  case 'string':
                    itemClass = rowStyleOrClass;
                    break;
                  default:
                    break;
                }
              }
              return (
                <div
                  onClick={() => {
                    if (ItemOnclick && typeof ItemOnclick === 'function') {
                      ItemOnclick(item);
                    }
                  }}
                  key={index}
                >
                  <ListItem
                    key={index}
                    rows={rows}
                    rowData={item}
                    itemStyle={itemStyle}
                    itemClass={itemClass}
                  />
                </div>
              );
            })
          }
          {
            (current > 0 && current === this.getMaxCurrent()) &&
            (
              <div style={{ height: fillHeight }} />
            )
          }
        </div>
        {
          showPagination &&
          (
            <div className={styles.listPagination}>
              <div>{t('第')} {current + 1} / {pages}{t('页')}</div>
              <div>{t('共')} {total} {t('条')}</div>
            </div>
          )
        }
      </div>
    );
  }
}

List.propTypes = {
  rows: PropTypes.arrayOf(PropTypes.array),
  data: PropTypes.arrayOf(PropTypes.object),
  style: PropTypes.shape(),
  className: PropTypes.string,
  header: PropTypes.element,
  rowStyleOrClass: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.func,
    PropTypes.object,
  ]),
  separate: PropTypes.bool,
  showPagination: PropTypes.bool,
  showNum: PropTypes.number,
  ItemOnclick: PropTypes.func,
};

List.defaultProps = {
  rows: [],
  data: [],
  style: {},
  separate: false,
  showPagination: false,
  showNum: 50,
};

export default List;
