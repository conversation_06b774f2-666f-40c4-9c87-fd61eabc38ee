.radionBox {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    color: #666666;
}

.icon {
    box-sizing: border-box;
    width: 15px;
    height: 15px;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 50%;
}

.iconCheck {
    border: 4px solid #0359CE;
}

.box{
    display: flex;
    justify-content: space-around;
}

.pageStikyChild {
    position: sticky;
    top: 0;
    z-index: 1;
}
