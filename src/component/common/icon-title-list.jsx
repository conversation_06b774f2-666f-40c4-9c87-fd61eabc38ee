import React from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import style from '../style.css';


const IconTitleList = (props) => {
  const { dataSource, editable, onClick } = props;
  return (
    <div className={style.iconTitleBox}>
      {
        dataSource.map((i, idx) => (
          <div
            className={style.iconTitleItem}
            key={i.iconName + i.title}
            onClick={() => {
              // 编辑状态才执行对应的点击事件逻辑
              if (!editable) {
                return;
              }
              onClick(i, idx);
            }}
          >
            <Icon className={style.mainIcon} name={i.iconName} style={i.iconStyle}>
              {
                editable && <Icon name="close-rev" className={style.rightIcon} />
              }
            </Icon>
            <div className={style.mainTitle}>
              {i.title}
            </div>
            {
              i.rule && !editable ? <a href={`#${i.rule.replace('/new-pda', '')}`}>{null}</a> : null
            }
          </div>
        ))
      }
    </div>
  );
};

IconTitleList.defaultProps = {
  // 列表数据对象数组，对象字段{
  // iconName: 'icon名', title: '标题',
  // rule: '跳转地址[可不传]/new-pda/back-warehouse', iconStyle: '样式对象'}
  dataSource: [],
  editable: false, // 是否(删除)编辑状态; 编辑状态不会跳转
  onClick: (obj, idx) => {
    // 父级回调, 将当前点击的icon对应对象和数组下标传出去
    console.log('IconTitleList', obj, idx);
  },
};

IconTitleList.propTypes = {
  dataSource: PropTypes.arrayOf(PropTypes.shape),
  editable: PropTypes.bool,
  onClick: PropTypes.func,
};

export default IconTitleList;
