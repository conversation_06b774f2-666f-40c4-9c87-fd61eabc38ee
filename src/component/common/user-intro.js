import React from 'react';
import introJs from 'intro.js';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import navStore from '../nav/reducers';


class UserIntro extends React.Component {
  constructor(props) {
    super(props);
    this.init();
  }

  componentDidMount() {
    const { steps, showIntro } = this.props;
    if (showIntro) {
      this.createIntro(steps);
    }
  }

  componentDidUpdate(prevProps) {
    const { steps, showIntro } = this.props;
    if (prevProps.showIntro !== showIntro) {
      if (showIntro) {
        this.createIntro(steps);
      }
    }
  }

  componentWillUnmount() {
    this.dispose();
  }

  /**
   * 参数
   * @returns {*}
   */
  get options() {
    const { options } = this.props;
    return Object.assign({}, UserIntro.defaultProps.options || {}, options || {});
  }

  /**
   * 组件初始化
   */
  init() {
    this.intro = introJs();
  }

  /**
   * 创建Intro
   */
  createIntro(data) {
    const { options } = this;
    this.intro.setOptions({
      // steps: data.filter(({ element }) => document.querySelector(element)),
      ...options,
    });
    // 判断是否有下一页操作
    if (this.props.endHandle) {
      this.intro.onafterchange((v) => {
        if (this.props.afterHandle) {
          this.props.afterHandle(v);
        }
      }).onexit(() => {
        this.props.endHandle();
      }).setOption('doneLabel', t('下一页')).start();
    } else {
      this.intro.onafterchange((ele) => {
        if (this.props.afterHandle) {
          this.props.afterHandle(ele);
        }
      }).onexit(() => {
        if (this.props.finishHandle) {
          navStore.changeData({ data: { showIntro: false } });
          this.props.finishHandle();
        }
      }).start();
    }
    // this.intro.addHints();
  }

  /**
   * 销毁Intro
   */
  dispose() {
    if (this.intro) {
      this.intro.exit();
      this.intro = null;
    }
    navStore.changeData({ data: { showIntro: false } });
  }

  render() {
    return null;
  }
}

UserIntro.defaultProps = {
  options: {
    nextLabel: t('下一步'),
    prevLabel: t('上一步'),
    skipLabel: t('跳过'),
    doneLabel: t('完成'),
    hidePrev: true,
    hideNext: true,
    disableInteraction: true,
    exitOnOverlayClick: false,
  },
  showIntro: false, // 进入页面是否开启 用户指引
};

UserIntro.propTypes = {
  steps: PropTypes.arrayOf(PropTypes.shape()),
  options: PropTypes.shape(),
  showIntro: PropTypes.bool,
  endHandle: PropTypes.func, // 分页操作；若没有就不传
  finishHandle: PropTypes.func, // 跳过或完成回调
  afterHandle: PropTypes.func, // 上一步/下一步之后的回调，能获取当前dom元素
};


export default i18n(UserIntro);
