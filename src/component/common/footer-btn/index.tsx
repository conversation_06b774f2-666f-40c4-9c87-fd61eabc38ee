import React from 'react';
import { Button, Toast } from 'react-weui/build/packages';
import assign from 'object-assign';
import { filterObject } from 'lib/util';
import { btnStyle, useLess } from './constants';
import { FooterBtnProps } from './types';

const FooterBtn: React.FC<FooterBtnProps> = (props) => {
  const {
    style = {},
    children,
    showLoading = false,
    visible = false,
  } = props;
  return (
    <div>
      <Button
        {...filterObject(props, useLess)}
        style={assign({}, btnStyle, style)}
      >
        {children}
      </Button>
      {
      showLoading
        ? <Toast icon="loading" show={visible}>Loading...</Toast>
        : null
    }
    </div>
  );
};

export default FooterBtn;
