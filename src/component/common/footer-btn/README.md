# FooterBtn
FooterBtn组件为按钮组件。

## Props
下表列出了 FooterBtn 的所有属性及其类型和默认值。其他属性、事件参考 [react-weui](https://open.welink.huaweicloud.com/wecode/docs/dev/reactui_cloud/reactui_cloud.html?v=1547690387) 中的[Button](https://open.welink.huaweicloud.com/wecode/docs/dev/reactui_cloud/input/button.html?v=1547690387)

| 属性 | 类型 | 默认值 | 描述 |
| ---- | ---- | ------ | ------ |
| `style` | `object` | {} | 按钮样式 |
| `children` | `ReactNode` |  | 子元素 |
| `showLoading` | `boolean` | false | 是否需要展示loading效果 |
| `visible` | `boolean` | false | 是否为loading状态  |

### Event
| 属性 | 类型 | 默认值 | 描述 |
| ---- | ---- | ------ | ------ |
| `onClick` | `event` |  | 按钮点击事件 |

### 示例
```jsx
import { FooterBtn } from 'common';

function App() {
  return (
    <FooterBtn
      type="primary"
      className="样式名"
      plain
      disabled={disabled}
      onClick={() => {
        if (beforeBack) {
          beforeBack(back);
        } else {
          back();
        }
      }}
    >
      显示内容
    </FooterBtn>
  );
}
```

## 组件更新记录

| 更改内容 | 人 | 时间 |
| ---- | ---- | ------ |
| `改造为ts组件` | `Rebekah Hu` | `1 5th, 2024` |
