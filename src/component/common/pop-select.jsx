import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import {
  <PERSON>up,
  PopupHeader,
  Checkbox,
  CellBody,
  CellHeader,
  Form,
  FormCell,
} from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import FocusInput from './focus-input';

// pop多选组件

class PopSelect extends Component {
  render() {
    const {
      labelName,
      valueName,
      onChange,
      leftBtn,
      rightBtn,
      selectList,
      selectValue,
      onCancel,
      onOk,
      show,
      onClick,
      label,
      placeholder,
      className,
      disabled,
    } = this.props;
    // 弹窗，最大高度限制，页面3/4高
    const height = window.innerHeight * 0.75 || 382;

    let selectValueData = [];
    if (show) {
      if (selectValue && selectValue.length) {
        selectValueData = selectValue.slice(0).map((i) => parseInt(i, 10));
      }
    }
    // 初始化已选中项
    let selectItem = [];
    selectList.forEach((item, index) => {
      if (selectValueData.find((i) => parseInt(i, 10) === parseInt(item[valueName], 10))) {
        selectItem.push(item);
      } else {
        selectItem.splice(index, 1);
      }
    });

    const selectChange = (e) => {
      if (selectValueData.length === 0) {
        selectValueData.push(parseInt(e.target.value, 10));
      } else {
        const index = selectValueData.indexOf(parseInt(e.target.value, 10));
        if (index !== -1) {
          selectValueData.splice(index, 1);
        } else {
          selectValueData.push(parseInt(e.target.value, 10));
        }
      }
      // 已选中项，清空重新对比（暴力方式）
      selectItem = [];
      selectList.forEach((item) => {
        if (selectValueData.find((i) => parseInt(i, 10) === parseInt(item[valueName], 10))) {
          selectItem.push(item);
        }
      });
      if (onChange) {
        onChange(selectValueData, selectItem);
      }
    };
    // 用户选确定，才生效
    const selectOk = () => {
      if (onOk) {
        onOk(selectValueData, selectItem);
      } else {
        throw new Error(t('onOk是必传属性！'));
      }
    };
    // 用户一波操作后再取消，需要重新设置
    const selectCancel = () => {
      selectValueData = [].slice(0);
      if (onCancel) {
        onCancel();
      } else {
        throw new Error(t('onCancel是必传属性,用于控制pop显示隐藏！'));
      }
    };

    const getVal = () => {
      const arr = [];
      selectList.forEach((item) => {
        if (selectValue.find((i) => parseInt(i, 10) === parseInt(item[valueName], 10))) {
          arr.push(item);
        }
      });
      const arr2 = arr.map((item) => item[labelName]);
      return arr2.join(' ');
    };
    return (
      <>
        <FocusInput
          type="text"
          placeholder={placeholder}
          className={className || ''}
          readOnly
          disabled={disabled || false}
          onClick={onClick}
          value={getVal()}
          arrow
        >
          <label>{label}</label>
        </FocusInput>
        {
          show ? (
            <Popup
              show={show}
              style={{ maxHeight: height, overflowY: 'scroll' }}
            >
              <PopupHeader
                left={t('取消') || leftBtn}
                right={t('确定') || rightBtn}
                leftOnClick={selectCancel}
                rightOnClick={selectOk}

              />
              <Form
                checkbox
              >

                {
                    selectList && selectList.length ? (
                      selectList.map((item) => (
                        <FormCell checkbox key={item[valueName]}>
                          <CellHeader>
                            <Checkbox
                              name={item[valueName]}
                              value={item[valueName]}
                              onChange={selectChange}
                              defaultChecked={
                                selectValueData
                                  .find((i) => parseInt(i, 10) === parseInt(item[valueName], 10))
                              }
                            />
                          </CellHeader>
                          <CellBody>{item[labelName]}</CellBody>
                        </FormCell>
                      ))) : <div style={{ height: 30, lineHeight: '30px', textAlign: 'center' }}>{t('暂无数据')}</div>
                  }
              </Form>
            </Popup>
          ) : null
        }
      </>
    );
  }
}

PopSelect.defaultProps = {
  leftBtn: t('取消'),
  rightBtn: t('确定'),
  placeholder: t('请选择'),
  show: false,
  valueName: 'dictCode',
  labelName: 'dictNameZh',
};

PopSelect.propTypes = {
  selectValue: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
    ]),
  ), // // 此处传值，只能传 数字/数字字符串类型的，如， [1, 2] 或者 ['1', '2']
  onChange: PropTypes.func,
  onCancel: PropTypes.func,
  onOk: PropTypes.func,
  leftBtn: PropTypes.string,
  rightBtn: PropTypes.string,
  selectList: PropTypes.arrayOf(PropTypes.shape), // [{},{}]
  labelName: PropTypes.string, // 字段，作为可配置的，不传默认dictNameZh
  valueName: PropTypes.string, // 字段，作为可配置的，不传默认dictCode
  show: PropTypes.bool,
  onClick: PropTypes.func,
  label: PropTypes.string,
  placeholder: PropTypes.string,
  className: PropTypes.string,
  disabled: PropTypes.bool,
};

export default PopSelect;
// 使用示例见 /example/test4
