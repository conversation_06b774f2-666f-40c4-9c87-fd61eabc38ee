import {
  LOCAL_LANG, LOCAL_WAREHOUSE_NAME, LOCAL_LOCATION, LOCAL_USER, LOCAL_WAREHOUSE,
} from 'lib/storage';

/* eslint-disable max-len */
/* eslint-disable import/no-cycle */
import { queryWarehouse } from '../login/server';

export const getLang = () => {
  let lang = localStorage.getItem(LOCAL_LANG);
  if (!lang || lang === 'undefined' || lang === 'null') {
    lang = 'en';
    localStorage.setItem(LOCAL_LANG, 'en');
  }
  return lang;
};

export const getUser = () => {
  try {
    const user = localStorage.getItem(LOCAL_USER);
    if (user) {
      return JSON.parse(user);
    }
    return null;
  } catch (e) {
    return null;
  }
};

/**
 *
 * @param lang 当前选中的语言
 * @param isLoginPage 当前页面是否是登录页
 * @returns {Promise<void>}
 */
export const setWarehouseName = async (lang, isLoginPage) => {
  if (lang) {
    localStorage.setItem(LOCAL_LANG, lang);
  } else {
    lang = getLang();
  }
  // 获取仓库并按语言更换仓库的名称
  const warehouse = localStorage.getItem(LOCAL_WAREHOUSE);
  if (warehouse && !isLoginPage) {
    const { warehouseId } = JSON.parse(warehouse);
    const res = await queryWarehouse();
    if (res.code === '0') {
      const item = res.info.warehouseList.find((i) => Number(i.warehouseId) === Number(warehouseId));
      if (item) {
        item.warehouseId += '';
        localStorage.setItem(LOCAL_WAREHOUSE, JSON.stringify(item || {}));
        localStorage.setItem(LOCAL_WAREHOUSE_NAME, item?.warehouseName);
      }
    }
  }

  // 获取用户角色，只是tms的用户，则不展示仓库
  const user = getUser();
  if (user) {
    if (user.system && !(user.system || []).includes('wms')) {
      localStorage.setItem(LOCAL_WAREHOUSE_NAME, '');
    }
  }
};

/**
 *
 * @param lang 当前选中的语言
 * @param isLoginPage 当前页面是否是登录页
 */
export const jumpByLang = (lang, isLoginPage) => {
  new Promise((r) => {
    r(setWarehouseName(lang, isLoginPage));
  }).then(() => {
    window.location.reload();
  });
};

export const getLocation = () => {
  try {
    const location = localStorage.getItem(LOCAL_LOCATION);
    const re = /.*[\u4e00-\u9fa5]+.*$/;
    if (location && !re.test(localStorage.getItem(LOCAL_LOCATION))) {
      return location;
    }
    return 'zh';
  } catch (e) {
    return 'zh';
  }
};
