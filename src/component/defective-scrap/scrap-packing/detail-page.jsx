import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import { Header, Footer, List } from 'common';
import { classFocus } from 'lib/util';
import store from './reducers';

const rows = [
  [
    {
      title: 'SKC',
      render: 'skc',
    },
  ],
  [
    {
      title: `${t('尺码')}:`,
      render: 'size',
    },
  ],
];

const DetailPage = (props) => {
  const { detailList, scrapHandoverBoxCode } = props;
  return (
    <div>
      <Header title={t('装箱商品明细')} />
      <List
        rows={rows}
        data={detailList}
        style={{ height: 'calc(100vh - 110px)', overflowY: 'auto' }}
        rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0', marginLeft: -7 }}
      />
      <Footer
        beforeBack={() => {
          store.changeData({ pageStatus: 'submit' });
          classFocus(scrapHandoverBoxCode ? 'barCodeInput' : 'scrapHandoverBoxCodeInput');
        }}
      />
    </div>
  );
};

DetailPage.propTypes = {
  detailList: PropTypes.arrayOf(PropTypes.object),
  scrapHandoverBoxCode: PropTypes.string,
};
export default DetailPage;
