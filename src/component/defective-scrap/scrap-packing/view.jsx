import React, { Component } from 'react';
import { i18n, t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import {
  Header, Footer, FocusInput,
  pages,
} from 'common';
import store from './reducers';
import SubmitPage from './submit-page';
import DetailPage from './detail-page';

const { View } = pages;

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      dataLoading,
      headerTitle,
      pageStatus,
      initLoading,
    } = this.props;
    // 判断是否进到提交页
    if (pageStatus === 'submit') {
      return (
        <SubmitPage {...this.props} />
      );
    } else if (pageStatus === 'detail') {
      return (
        <DetailPage {...this.props} />
      );
    }

    return (
      <View initLoading={!initLoading}>
        <Header title={headerTitle || t('报废装箱')} />
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            data-bind="bigBoxCode"
            disabled={!dataLoading}
            className="bigBoxCodeInput"
            onPressEnter={(e) => {
              if (!e.target.value.trim()) {
                return;
              }
              store.changeData({
                bigBoxCode: e.target.value.trim().toUpperCase(),
              });
              store.scanBox({ boxNo: e.target.value.trim().toUpperCase() });
            }}
          >
            <label>{t('大箱号')}</label>
          </FocusInput>
        </Form>
        <Footer />
      </View>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  pageStatus: PropTypes.string,
  dataLoading: PropTypes.number,
  initLoading: PropTypes.number,
};

export default i18n(Container);
