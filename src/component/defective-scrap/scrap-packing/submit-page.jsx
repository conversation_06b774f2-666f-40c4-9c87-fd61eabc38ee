import React, { Component } from 'react';
import { i18n, t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import { Button, Form } from 'react-weui/build/packages';
import Icon from '@shein-components/Icon';
import {
  Header, Footer, RowInfo,
  FocusInput, modal, Table,
} from 'common';
import store from './reducers';

const spanStyle = {
  backgroundColor: 'blue',
  color: '#fff',
  padding: '2px 4px',
  marginLeft: 6,
  fontSize: 12,
  borderRadius: 2,
};

class Container extends Component {
  render() {
    const {
      dataLoading,
      headerTitle,
      boxDetail,
      bigBoxCode,
      waitScrapCount,
      scanCount,
      detailNum,
      nowScanIdx,
      containerCategory,
      isScanScrapHandoverBoxCode,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle || t('报废装箱')} />
        <Form>
          <FocusInput
            disabled
            value={bigBoxCode}
            footer={
              (
                <Button
                  size="small"
                  style={{ fontSize: 12 }}
                  disabled={!dataLoading}
                  onClick={() => {
                    modal.confirm({
                      title: t('是否确认关箱?'),
                      onOk: () => store.closeBox(),
                    });
                  }}
                >
                  {t('关箱')}
                </Button>
              )
            }
          >
            <label>
              {t('大箱号')}
              {
                containerCategory && (
                  <span style={spanStyle}>{containerCategory}</span>
                )
              }
            </label>
          </FocusInput>
        </Form>
        <Form
          onClick={() => store.getDailList()}
        >
          <RowInfo
            key={t('已装箱商品数量')}
            label={t('已装箱商品数量')}
            content={(
              <span>
                <span style={{ fontSize: 16 }}>{detailNum}</span>
                <Icon style={{ color: '#197AFA', verticalAlign: 'middle' }} name="arr-right" />
              </span>
            )}
            type="info"
          />
        </Form>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            data-bind="scrapHandoverBoxCode"
            disabled={!dataLoading}
            className="scrapHandoverBoxCodeInput"
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.scanHandoverBox({ scrapHandoverBoxCode: e.target.value, bigBoxCode });
            }}
            footer={!isScanScrapHandoverBoxCode ? null :
              (
                <Button
                  style={{ fontSize: 12 }}
                  size="small"
                  disabled={!dataLoading}
                  onClick={() => {
                    modal.confirm({
                      title: t('是否确认箱空?'),
                      onOk: () => store.emptyBox(),
                    });
                  }}
                >
                  {t('箱空')}
                </Button>
              )
            }
          >
            <label>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                {t('报废交接箱号')}
                <span data-if={isScanScrapHandoverBoxCode}>
                  {t('已扫/总计')}：{scanCount}/{waitScrapCount}
                </span>
              </div>
            </label>
          </FocusInput>
        </Form>
        <React.Fragment data-if={isScanScrapHandoverBoxCode}>
          <Form>
            <FocusInput
              placeholder={t('请扫描')}
              data-bind="barCode"
              disabled={!dataLoading}
              className="barCodeInput"
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.scanBarcode({ barCode: e.target.value });
              }}
            >
              <label>
                {t('商品条码')}
              </label>
            </FocusInput>
          </Form>
          <Table
            maxHeight={150}
            styleObj={{ backgroundColor: '#fff' }}
            selectedRowIndex={nowScanIdx}
            columns={[
              {
                title: t('SKC/属性集/尺码'),
                dataIndex: 'skcText',
                width: 30,
              },
              {
                title: t('已装箱/总数'),
                dataIndex: 'numText',
                render: r => `${r.scrapCount}/${r.waitScrapCount}`,
                width: 20,
              },
            ]}
            dataSource={boxDetail}
          />
        </React.Fragment>
        <Footer
          beforeBack={() => store.init()}
        />
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  waitScrapCount: PropTypes.number,
  scanCount: PropTypes.number,
  nowScanIdx: PropTypes.number,
  bigBoxCode: PropTypes.string,
  containerCategory: PropTypes.string,
  isScanScrapHandoverBoxCode: PropTypes.bool,
  boxDetail: PropTypes.arrayOf(PropTypes.shape),
  detailNum: PropTypes.number,
};

export default i18n(Container);
