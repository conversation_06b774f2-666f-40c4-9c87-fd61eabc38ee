import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { put } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { message, modal } from 'common';
import {
  getHeaderTitle, classFocus, typeFn,
} from 'lib/util';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import {
  scanBoxAPI, closeBoxAPI,
  scanHandoverBoxAPI, scanBarcodeAPI, emptyBoxAPI,
} from './server';

/**
 * 根据下标，将数组元素移到最前边
 * @param arr 原数组
 * @param idx 需要移到元素的下标
 * @returns {*} 返回移动后的数组
 */
const moveItemToFirst = (arr, idx) => {
  // 若传参有问题，则返回原数据
  if (!typeFn.isArray(arr) || idx <= 0 || idx >= arr.length) {
    return arr;
  }
  const item = arr.splice(idx, 1)[0];
  arr.unshift(item);
  return arr;
};

const defaultState = {
  headerTitle: '',
  initLoading: 1, // 0加载中 1加载成功 2加载失败【页面初始化】
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  pageStatus: 'init', // 用于区分渲染页面：init-初始页；submit-操作提交页；detail-明细页
  bigBoxCode: '', // 大箱号
  containerCategory: '', // 箱子品类
  detailNum: 0, // 明细页数据数量
  detailList: [], // 明细页数据
  scrapHandoverBoxCode: '', // 报废交接箱号
  isScanScrapHandoverBoxCode: false, // 是否已扫报废交接箱号，用于相关展示隐藏
  waitScrapCount: 0, // 总计
  scanCount: 0, // 已扫
  barCode: '', // 商品条码
  boxDetail: [], // 报废交接箱商品明细列表
  nowScanIdx: -1, // 当前扫描条码对应下标
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(draft, data) {
    assign(draft, data);
  },
  // 初始化
  * init() {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }
    classFocus('bigBoxCodeInput');
    yield this.changeData({ headerTitle: getHeaderTitle() });
  },
  // 扫大箱号
  * scanBox(params) {
    const { subWarehouseId } = yield 'homework-subwarehouse';
    markStatus('dataLoading');
    const { code, msg, info } = yield scanBoxAPI({
      ...params,
      warehouse: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
      subWarehouseId,
    });
    if (code === '0') {
      const { containerCategory, list } = info || {};
      const detailList = list || [];
      // 更新界面
      yield this.changeData({
        pageStatus: 'submit',
        containerCategory,
        detailList,
        detailNum: detailList.length,
      });
      classFocus('scrapHandoverBoxCodeInput');
    } else {
      // 清空并聚焦扫箱号
      yield this.changeData({ bigBoxCode: '' });
      modal.error({
        title: msg,
        className: 'bigBoxCodeInput',
      });
    }
  },
  // 实时获取箱明细数据：跟产品确认过扫大箱号没并发场景
  * getDailList() {
    const { detailList, detailNum, bigBoxCode } = yield '';
    // 减少没必要请求
    if (detailNum === 0) {
      return;
    }
    if (detailNum === detailList.length) {
      yield this.changeData({ pageStatus: 'detail' });
      return;
    }
    const { subWarehouseId } = yield 'homework-subwarehouse';
    markStatus('dataLoading');
    const { code, msg, info } = yield scanBoxAPI({
      boxNo: bigBoxCode,
      warehouse: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
      subWarehouseId,
    });
    if (code === '0') {
      const { list } = info || {};
      const newDetailList = list || [];
      // 进到明细页
      yield this.changeData({
        pageStatus: 'detail',
        detailList: newDetailList,
        detailNum: newDetailList.length,
      });
      classFocus('scrapHandoverBoxCodeInput');
    } else {
      modal.error({
        title: msg,
      });
    }
  },
  // 扫描报废交接箱
  * scanHandoverBox(params) {
    markStatus('dataLoading');
    const { code, msg, info } = yield scanHandoverBoxAPI(params);
    if (code === '0') {
      const { scrapHandoverBoxDetail, scrapNoticeCode } = info || {};
      const boxDetail = [];
      let waitScrapCount = 0;
      let scanCount = 0;
      // 处理数据并进行总数汇总
      (scrapHandoverBoxDetail || []).forEach((r) => {
        boxDetail.push({
          skcText: `${r.skc || '-'}/${r.saleAttrZh || '-'}/${r.size || '-'}`, // SKC/属性集/尺码
          skuCode: r.skuCode, // 用于扫条码匹配+1
          scrapCount: r.scrapCount || 0, // 已装箱
          waitScrapCount: r.waitScrapCount || 0, // 总数
        });
        scanCount += r.scrapCount || 0;
        waitScrapCount += r.waitScrapCount || 0;
      });
      yield this.changeData({
        isScanScrapHandoverBoxCode: true,
        scrapNoticeCode,
        boxDetail,
        nowScanIdx: -1,
        waitScrapCount,
        scanCount,
      });
      classFocus('barCodeInput');
    } else {
      // 清空并聚焦扫箱号
      yield this.changeData({ scrapHandoverBoxCode: '' });
      modal.error({
        title: msg,
        className: 'scrapHandoverBoxCodeInput',
      });
    }
  },
  // 扫描商品条码
  * scanBarcode(params) {
    const { subWarehouseId } = yield 'homework-subwarehouse';
    const {
      bigBoxCode, scrapHandoverBoxCode, scrapNoticeCode,
      scanCount, boxDetail, detailNum,
    } = yield '';
    markStatus('dataLoading');
    const { code, msg, info } = yield scanBarcodeAPI({
      ...params,
      bigBoxCode,
      scrapHandoverBoxCode,
      scrapNoticeCode,
      warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
      subWarehouseId,
    });
    yield this.changeData({ barCode: '' });
    if (code === '0') {
      const { skuCode, lastGoodsBool } = info || {};
      // lastGoodsBool返回true，代表是当前箱最后一个商品；应清空聚焦报废交接箱号，让用户重新扫描箱号【但明细页数量还是要+1】
      if (lastGoodsBool) {
        message.success(t('当前报废交接箱号已全部扫描完成'));
        yield this.changeData({
          detailNum: detailNum + 1,
          isScanScrapHandoverBoxCode: false,
          boxDetail: [],
          nowScanIdx: -1,
          scrapHandoverBoxCode: '',
        });
        classFocus('scrapHandoverBoxCodeInput');
        return;
      }
      // 扫描成功，对应条码和总数+1；且置顶变背景色
      let nowIdx = -1;
      boxDetail.forEach((r, idx) => {
        if (r.skuCode === skuCode) {
          r.scrapCount += 1;
          nowIdx = idx;
        }
      });
      yield this.changeData({
        scanCount: scanCount + 1,
        detailNum: detailNum + 1,
        boxDetail: moveItemToFirst(boxDetail, nowIdx),
        nowScanIdx: nowIdx === -1 ? -1 : 0,
      });
      classFocus('barCodeInput');
    } else {
      modal.error({
        title: msg,
        className: 'barCodeInput',
      });
    }
  },
  // 关闭大箱号
  * closeBox() {
    const { subWarehouseId } = yield 'homework-subwarehouse';
    const { bigBoxCode } = yield '';
    markStatus('dataLoading');
    const { code, msg } = yield closeBoxAPI({
      boxNo: bigBoxCode,
      warehouse: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
      subWarehouseId,
    });
    if (code === '0') {
      message.success(t('大箱{}已成功关箱', bigBoxCode));
      yield this.init();
    } else {
      modal.error({
        title: msg,
      });
    }
  },
  // 报废交接箱箱空
  * emptyBox() {
    const { subWarehouseId } = yield 'homework-subwarehouse';
    const { bigBoxCode, scrapHandoverBoxCode, scrapNoticeCode } = yield '';
    markStatus('dataLoading');
    const { code, msg } = yield emptyBoxAPI({
      bigBoxCode,
      scrapHandoverBoxCode,
      scrapNoticeCode,
      warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId || '',
      subWarehouseId,
    });
    if (code === '0') {
      message.success(t('箱空成功'));
      yield this.changeData({
        isScanScrapHandoverBoxCode: false,
        boxDetail: [],
        nowScanIdx: -1,
        scrapHandoverBoxCode: '',
      });
      classFocus('scrapHandoverBoxCodeInput');
    } else {
      modal.error({
        title: msg,
      });
    }
  },
};
