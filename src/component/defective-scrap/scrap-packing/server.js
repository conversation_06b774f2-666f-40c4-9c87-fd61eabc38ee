import { sendPostRequest } from '../../../lib/public-request';

// 报废装箱扫大箱号
export const scanBoxAPI = (param) => sendPostRequest({
  url: '/defection_box/goods_scan_box',
  param,
}, process.env.WIS_FRONT);

// 报废装箱关闭大箱号
export const closeBoxAPI = (param) => sendPostRequest({
  url: '/defection_box/goods_close_box',
  param,
}, process.env.WIS_FRONT);

// 扫描报废交接箱
export const scanHandoverBoxAPI = (param) => sendPostRequest({
  url: '/scrap/scrap_packing_scan_handover_box',
  param,
}, process.env.WIS);

// 扫描商品条码
export const scanBarcodeAPI = (param) => sendPostRequest({
  url: '/scrap/scrap_packing_scan_bar_code',
  param,
}, process.env.WIS);

// 报废交接箱箱空
export const emptyBoxAPI = (param) => sendPostRequest({
  url: '/scrap/scrap_packing_empty_handover_box',
  param,
}, process.env.WIS);
