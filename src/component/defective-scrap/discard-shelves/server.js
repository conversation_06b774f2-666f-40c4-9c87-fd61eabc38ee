import { sendPostRequest } from '../../../lib/public-request';

// 报废采购报废下架任务信息查询
export const queryPurchaseReturnInfo = (param) => sendPostRequest({
  url: '/replenish_shelves/query_purchase_scrap_task_info',
  param,
}, process.env.WIS_FRONT);

// 扫描采购报废下架周转箱
export const scanBox = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_purchase_scrap_pick_container',
  param,
}, process.env.WIS_FRONT);

// 采购报废下架扫描货位号
export const scanLocation = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_purchase_scrap_location',
  param,
}, process.env.WIS_FRONT);

// 采购报废下架关箱
export const closeBox = (param) => sendPostRequest({
  url: '/replenish_shelves/close_purchase_scrap_pick_container',
  param,
}, process.env.WIS_FRONT);

// 扫描采购报废下架商品条码
export const scanSku = (param) => sendPostRequest({
  url: '/replenish_shelves/scan_purchase_scrap_barcode',
  param,
}, process.env.WIS_FRONT);

// 采购报废短拣
export const pdaShortPick = (param) => sendPostRequest({
  url: '/replenish_shelves/purchase_scrap_short_pick',
  param,
}, process.env.WIS_FRONT);

// 提交采购报废下架商品数量
export const submitGoodsCount = (param) => sendPostRequest({
  url: '/replenish_shelves/submit_purchase_scrap_goods_count',
  param,
}, process.env.WIS_FRONT);

// 采购报废下架记录明细查询
export const getDetailAPI = (param) => sendPostRequest({
  url: '/replenish_shelves/scrap_query_record_detail',
  param,
}, process.env.WIS_FRONT);
