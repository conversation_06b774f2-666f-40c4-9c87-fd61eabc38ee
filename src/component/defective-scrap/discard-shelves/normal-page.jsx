import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import { Footer, FooterBtn } from '../../common';
import FocusInput from '../../common/focus-input';
import Info from './Info';
import RowInfo from '../../common/row-info';
import store from './reducers';

function NormalPage(props) {
  const trimLeft = (str) => {
    try {
      return str.replace(/^\s+/g, '');
    } catch (e) {
      return '';
    }
  };
  const {
    containerDisabled,
    locationDisabled,
    barCodeDisabled,
    info,
    locationInput,
    num,
    numDisabled,
    goodsSnPrint,
  } = props;
  return (
    <div>
      <Form>
        <Info data={info} />
        <RowInfo
          extraStyle={{ justifyContent: 'flex-start' }}
          textExtraStyle={{
            width: '32%',
            color: '#FF0000',
            fontWeight: 'bold',
            fontSize: 20,
          }}
          label={t('待拣数量')}
          content={info.waitOffNum}
        />
      </Form>
      <Form
        style={{ marginTop: 6 }}
      >
        <FocusInput
          importance
          data-bind="containerCode"
          disabled={containerDisabled}
          className="box"
          onPressEnter={(e) => {
            if (e.target.value.trim()) {
              store.changeData({
                containerCode: e.target.value.trim().toUpperCase(),
              });
              store.scanBox(e.target.value.trim().toUpperCase());
            }
          }}
        >
          <label>{t('拣货周转箱')}</label>
        </FocusInput>
        <FocusInput
          placeholder={t('请扫描')}
          data-bind="locationInput"
          onPressEnter={(e) => {
            if (e.target.value.trim()) {
              store.changeData({
                data: {
                  locationInput: e.target.value.trim(),
                },
              });
              store.scanLocation({
                params: {
                  containerCode: info.containerCode,
                  location: e.target.value.trim(),
                  replenishmentCode: info.replenishmentCode,
                  shiftOrderCode: info.shiftOrderCode,
                  skuCode: info.skuCode,
                },
                waitOffNum: info.waitOffNum,
              });
            }
          }}
          disabled={locationDisabled === 0}
          className="location"
        >
          <label>{t('库位号')}</label>
        </FocusInput>
        <FocusInput
          placeholder={t('请扫描')}
          data-bind="goodsSnPrint"
          onPressEnter={(e) => {
            if (e.target.value) {
              store.scanGoods({
                params: {
                  containerCode: info.containerCode,
                  goodsSn: info.goodsSn,
                  goodsSnPrint: e.target.value,
                  location: locationInput,
                  replenishmentCode: info.replenishmentCode,
                  size: info.size,
                  skuCode: info.skuCode,
                  shiftOrderCode: info.shiftOrderCode,
                },
                info,
              });
            }
          }}
          disabled={barCodeDisabled === 0}
          className="barCode"
        >
          <label>{t('商品条码')}</label>
        </FocusInput>
        <FocusInput
          placeholder={t('请输入')}
          maxLength={5}
          value={num}
          onChange={(e) => {
            const { value } = e.target;
            const reg = /^([0-9]*)$/;
            if ((!Number.isNaN(value) && reg.test(value)) || value === '') {
              store.changeData({
                data: {
                  num: trimLeft(value),
                },
              });
            }
          }}
          onPressEnter={(e) => {
            if (e.target.value) {
              store.submitCount({
                params: {
                  containerCode: info.containerCode,
                  goodsSn: info.goodsSn,
                  skuCode: info.skuCode,
                  goodsSnPrint,
                  location: locationInput,
                  num,
                  replenishmentCode: info.replenishmentCode,
                  size: info.size,
                  shiftOrderCode: info.shiftOrderCode,
                  force: false,
                },
                info,
              });
            }
          }}
          disabled={numDisabled === 0}
          className="num"
        >
          <label>{t('商品数量')}</label>
        </FocusInput>
      </Form>
      <Footer
        beforeBack={() => {
          store.init();
          store.getInfo();
        }}
      >
        <FooterBtn
          onClick={() => {
            store.getDetail({
              params: {
                replenishmentCode: info.replenishmentCode,
              },
            });
          }}
        >
          {t('明细')}
        </FooterBtn>
      </Footer>
    </div>
  );
}

NormalPage.propTypes = {
  containerDisabled: PropTypes.bool,
  locationDisabled: PropTypes.number,
  barCodeDisabled: PropTypes.number,
  info: PropTypes.shape(),
  locationInput: PropTypes.string,
  numDisabled: PropTypes.number,
  goodsSnPrint: PropTypes.string,
  num: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
};

export default NormalPage;
