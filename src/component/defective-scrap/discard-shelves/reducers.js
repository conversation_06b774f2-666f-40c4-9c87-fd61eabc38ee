import assign from 'object-assign';
import { select } from 'redux-saga/effects';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { t } from '@shein-bbl/react';
import { LOCAL_WAREHOUSE } from 'lib/storage';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import Modal from '../../common/modal';
import Message from '../../common/message';
import {
  queryPurchaseReturnInfo, scanBox, scanLocation,
  closeBox, scanSku, pdaShortPick, submitGoodsCount, getDetailAPI,
} from './server';

const defaultState = {
  status: 'default',
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  baseData: [
    {
      label: t('待领取任务数'),
      num: 0,
      type: 'info',
    },
  ],
  containerCode: '', // 外层周转箱
  boxDisabled: 1,
  info: {},
  num: '',
  locationInput: '',
  goodsSnPrint: '',
  containerDisabled: false,
  locationDisabled: 1,
  barCodeDisabled: 1,
  numDisabled: 1,
  headerTitle: '',
  showDetail: false,
  detailList: [[], []],
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  // 任务信息查询
  * getInfo(action, ctx, put) {
    markStatus('dataLoading');
    const res = yield queryPurchaseReturnInfo();
    if (res.code === '0') {
      yield put((draft) => {
        draft.baseData[0].num = res.info.waitTaskNum;
        draft.boxDisabled = 1;
      });
      classFocus('box');
    } else {
      Modal.error({ content: res.msg || t('请求数据出错'), className: 'box' });
    }
  },
  // 扫描周转箱
  * scanBox(action, ctx, put) {
    markStatus('boxDisabled');
    const res = yield scanBox({
      containerCode: action,
      warehouseId: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseId,
      warehouseName: JSON.parse(localStorage.getItem(LOCAL_WAREHOUSE) || '{}').warehouseName,
    });
    if (res.code !== '0') {
      yield put((draft) => {
        draft.containerCode = '';
      });
      Modal.error({
        content: res.msg,
        onOk: () => classFocus('box'),
      });
    } else {
      yield put((draft) => {
        draft.info = res.info;
        draft.status = 'normalPage';
        draft.containerDisabled = true;
        draft.containerCode = res.info.containerCode;
      });
      if (action !== res.info.containerCode) {
        Modal.success({
          content: t('您存在未完成的任务'),
          onOk: () => classFocus('location'),
        });
      } else {
        classFocus('location');
      }
    }
  },
  // 扫货位号
  * scanLocation(action, ctx, put) {
    yield put((draft) => {
      draft.locationDisabled = 0;
    });
    const res = yield scanLocation(action.params);
    if (res.code !== '0') {
      yield put((draft) => {
        draft.locationDisabled = 1;
        draft.locationInput = '';
      });
      Modal.error({
        content: res.msg,
        onOk: () => classFocus('location'),
      });
    } else {
      if (res.info.type === 1) {
        yield put((draft) => {
          draft.barCodeDisabled = 1;
        });
        classFocus('barCode');
      } else if (res.info.type === 2) { // 关箱
        yield put((draft) => {
          draft.locationDisabled = 1;
        });
        const status = yield new Promise((r) => Modal.confirm({
          content: t('是否确认关箱'),
          onOk: () => r(1),
          onCancel: () => r(2),
        }));
        if (status === 1) {
          yield ctx.closeBox({
            params: {
              containerCode: action.params.containerCode,
              shiftOrderCode: action.params.shiftOrderCode,
            },
            names: {
              name: 'locationInput',
              disabled: 'locationDisabled',
              className: 'location',
            },
          });
        }
        if (status === 2) {
          yield ctx.changeData({
            data: {
              locationInput: '',
              locationDisabled: 1,
            },
          });
          setTimeout(() => {
            classFocus('location');
          }, 300);
        }
      }
    }
  },
  // 关箱
  * closeBox(action, ctx) {
    const res = yield closeBox(action.params);
    if (res.code !== '0') {
      Modal.error({
        content: res.msg,
        onOk: classFocus(action.names.className),
      });
    } else {
      const status = yield new Promise((r) => Modal.success({
        content: t('关箱成功'),
        onOk: () => r(1),
      }));
      if (status === 1) {
        yield ctx.init();
        yield ctx.getInfo();
      }
    }
  },
  // 扫商品条码
  * scanGoods(action, ctx, put) {
    yield put((draft) => {
      draft.barCodeDisabled = 0;
    });
    const res = yield scanSku(action.params);
    if (res.code !== '0') {
      yield put((draft) => {
        draft.barCodeDisabled = 1;
        draft.goodsSnPrint = '';
      });
      Modal.error({
        content: res.msg,
        onOk: classFocus('barCode'),
      });
    } else {
      if (res.info.type === 1) {
        yield put((draft) => {
          draft.numDisabled = 1;
        });
        classFocus('num');
      } else if (res.info.type === 2) {
        yield ctx.closeBox({
          params: {
            containerCode: action.params.containerCode,
            shiftOrderCode: action.params.shiftOrderCode,
          },
          names: {
            name: 'goodsSnPrint',
            disabled: 'barCodeDisabled',
            className: 'barCode',
          },
        });
      }
    }
  },
  // 短拣
  * shortPick(action, ctx, put) {
    const { status, info } = yield select((state) => state['defective-scrap/discard-shelves']);
    const res = yield pdaShortPick(action.params);
    if (res.code !== '0') {
      yield put((draft) => {
        draft.locationInput = '';
        draft.locationDisabled = 1;
        draft.containerCode = '';
        draft.containerDisabled = false;
        draft.barCodeDisabled = 1;
        draft.goodsSnPrint = '';
      });
      Modal.error({
        content: res.msg,
      });
    } else {
      // 根据接口返回，判断是否需要顶部提示
      const { downBoxBool, onShelfContainerCode } = res.info;
      if (downBoxBool) {
        Message.info(t('请下架空周转箱：{}!', onShelfContainerCode));
      }
      if (res.info.type === 2) {
        yield ctx.init();
        yield ctx.getInfo();
      } else {
        yield put((draft) => {
          draft.status = status;
          draft.locationInput = '';
          draft.locationDisabled = 1;
          draft.goodsSnPrint = '';
          draft.num = '';
          draft.info = assign({}, info, res.info.taskGoodsInfo);
        });
        classFocus(status !== 'normalPage' ? 'box' : 'location');
      }
    }
  },
  // 提交
  * submitCount(action, ctx, put) {
    markStatus('numDisabled');
    const res = yield submitGoodsCount(action.params);
    if (res.code !== '0') {
      yield put((draft) => {
        draft.num = '';
      });
      Modal.error({
        content: res.msg,
        onOk: () => {
          classFocus('num');
        },
      });
    } else {
      // 根据接口返回，判断是否需要顶部提示
      const { downBoxBool, onShelfContainerCode } = res.info;
      if (downBoxBool) {
        Message.info(t('请下架空周转箱：{}!', onShelfContainerCode));
      }
      switch (res.info.type) {
        case 1:
          if (res.info.taskGoodsInfo) {
            const {
              location,
            } = res.info.taskGoodsInfo;
            yield ctx.changeData({
              data: {
                info: assign({}, action.info, res.info.taskGoodsInfo),
              },
            });
            if (action.info.location !== location) {
              yield ctx.changeData({
                data: {
                  num: '',
                  goodsSnPrint: '',
                  locationInput: '',
                  locationDisabled: 1,
                  barCodeDisabled: 1,
                },
              });
              Modal.success({
                content: t('成功下架') + action.params.num + t('件'),
                onOk: () => {
                  classFocus('location');
                },
              });
            } else {
              yield ctx.changeData({
                data: {
                  num: '',
                },
              });
              Modal.success({
                content: t('成功下架') + action.params.num + t('件'),
                onOk: () => {
                  classFocus('num');
                },
              });
            }
          }
          break;
        case 2:
          // eslint-disable-next-line no-case-declarations
          const type2 = yield new Promise((r) => Modal.confirm({
            content: t('是否确认下架'),
            okText: t('确认'),
            onOk: () => r(1),
            onCancel: () => r(2),
          }));
          if (type2 === 1) {
            yield ctx.submitCount({
              params: assign({}, action.params, { force: true }),
              info: action.info,
            });
          }
          if (type2 === 2) {
            setTimeout(() => classFocus('num'), 0);
          }
          break;
        case 3:
          // eslint-disable-next-line no-case-declarations
          const status = yield new Promise((r) => Modal.success({
            content: t('该任务下架完成'),
            okText: t('确认'),
            onOk: () => r(1),
          }));
          if (status === 1) {
            yield ctx.init();
            yield ctx.getInfo();
          }
          break;
        case 4:
          // eslint-disable-next-line no-case-declarations
          const type4 = yield new Promise((r) => Modal.confirm({
            content: t('是否确认短拣'),
            okText: t('确认'),
            onOk: () => r(1),
            onCancel: () => r(2),
          }));
          if (type4 === 1) {
            yield ctx.shortPick({
              params: assign({}, action.params),
            });
          }
          if (type4 === 2) {
            setTimeout(() => classFocus('num'), 0);
          }
          break;
        default:
          break;
      }
    }
  },
  // 获取明细查询
  * getDetail(action, ctx, put) {
    markStatus('dataLoading');
    const res = yield getDetailAPI(action.params);
    if (res.code === '0') {
      yield put((draft) => {
        draft.showDetail = true;
        draft.detailList = [res.info.hasPickedRecords || [], res.info.notPickedRecords || []];
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
};
