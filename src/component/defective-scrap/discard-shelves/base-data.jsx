import { t } from '@shein-bbl/react';
import React from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import { Form } from 'react-weui/build/packages/components/form';
import store from './reducers';
import RowInfo from '../../common/row-info';

function BaseData(props) {
  const {
    data,
  } = props;
  return (
    <div>
      <Form style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        {
          data.map((item) => (
            <RowInfo
              key={item.label}
              extraStyle={{
                borderBottom: 'none',
              }}
              label={item.label}
              content={<b style={{ paddingLeft: '15px', color: 'red' }}>{item.num}{t('单')}</b>}
              type={item.type}
            />
          ))
        }
        <div
          onClick={() => store.getInfo()}
          style={{ paddingRight: '15px', color: '#0059ce' }}
        >
          <Icon name="sync" />
        </div>
      </Form>
    </div>
  );
}

BaseData.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape),
};

export default BaseData;
