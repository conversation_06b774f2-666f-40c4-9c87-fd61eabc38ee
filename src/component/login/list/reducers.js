/* eslint-disable no-underscore-dangle */
import assign from 'object-assign';
import moment from 'moment';
import { push } from 'react-router-redux';
import { delay } from 'redux-saga';
import { t } from '@shein-bbl/react';
import { sendEvent } from 'sheinq';
import { markStatus } from 'rrc-loader-helper';
import {
  LOCAL_IS_REMEMBER, LOCAL_USER, LOCAL_LOCATION,
  SESSION_ANDON_LOGIN, SESSION_PICK_SEQ_VAL, SESSION_PDA_GUIDELINE_DATA,
  LOCAL_SESSION_ID, LOCAL_GTMS_TOKEN,
  LOCAL_WMS_LOGIN_TOKEN, LOCAL_WAREHOUSE_NAME, LOCAL_WAREHOUSE,
  SESSION_ANDON_SET_OBJ,
  SESSION_ANDON_SUBMIT_OBJ, SESSION_IS_SHOW_QUICK_ENTRY, SESSION_QUICK_ENTRY_OBJ,
  // eslint-disable-next-line no-unused-vars
  SESSION_REASON_DATA_OBJ, SESSION_SCROLL_VIEW, LOCAL_NO_PWD_AUTH,
  LOCAL_LANG,
} from 'lib/storage';
// import { conf } from 'sheinq';
// eslint-disable-next-line import/no-extraneous-dependencies
import verifyManager from '@shein-risk/si_verify';
import {
  login, timeoutApi, logoutApi, gMotLogin, devLogin, getGTApi,
} from '../server';
import Modal from '../../common/modal';
import message from '../../common/message';
import { getLang } from '../../js';
import {
  clearSomeCookie, setLoginTimeoutInterval, clearLoginTimeoutInterval, camel2Under, strTrim,
} from '../../../lib/util';
import { getPageData } from '../../../lib/cloud-sdk';
import { encrypt } from './encrypt';
import { noPwdAuthLocalStorage, homeworkSubWarehouseStorage } from '../../../lib/storage-new';

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  username: '',
  password: '',
  num: 0,
  hidePass: true,
  showPicker: false,
  pickerGroup: [
    {
      items: [
        {
          id: 1,
          label: t('中国大陆'),
          value: 'zh',
        },
        {
          id: 2,
          label: t('美国'),
          value: 'us',
        },
        {
          id: 3,
          label: t('欧洲'),
          value: 'en',
        },
      ],
    },
  ],
  location: 'zh', // 默认中国大陆
  isRememberId: true, // 是否记住id，改为是否记住密码功能
  lang: getLang(),
  showLangPicker: false,
  ulpLoginFreeTip: '', // ulp免密登录提示语
  openCaptcha: true, // 极验验证码开关，默认开启
  captchaConfig: {}, // 极验验证码配置
  captchaInstance: {}, // 极验验证码实例
  captchaLoading: false, // 极验注册接口请求或极验初始化中
  retryErrorCodes: [], // 极验验证码错误码刷新数组
  riskChannel: '0', // 默认js挑战channel值 '1' pwa  '0' pc
};

export default {
  defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  // 初始化 要判断用户是否记住id
  $init: (draft) => {
    // localStorage user里的账户信息，如果有并且用户记住了密码，则需要回填到输入框
    // const isRememberId = localStorage.getItem(LOCAL_IS_REMEMBER) === '1';
    // const userStr = localStorage.getItem(LOCAL_USER) || '';
    const isRememberId = false;
    // 最开始location记录的是中文，这里为了防止报错，正则处理了一下
    const re = /.*[\u4e00-\u9fa5]+.*$/;
    if (!localStorage.getItem(LOCAL_LOCATION) || re.test(localStorage.getItem(LOCAL_LOCATION))) {
      localStorage.setItem(LOCAL_LOCATION, 'zh');
    }

    // if (isRememberId && userStr) {
    //   user = JSON.parse(userStr);
    //   username = user.username || '';
    //   password = user.password || '';
    // }
    clearSomeCookie();
    assign(draft, defaultState, {
      isRememberId,
      // username: isRememberId ? username : '',
      // password: isRememberId ? password : '',
      username: '',
      password: '',
      location: localStorage.getItem(LOCAL_LOCATION),
    });

    // 登录页面禁止回退
    window.history.pushState(null, null, document.URL);
    window.addEventListener('popstate', (e) => {
      // 微前端劫持触发的不执行，防止死循环
      if (e?.singleSpa) {
        return;
      }
      window.history.pushState(null, null, document.URL);
    });
  },
  * init(action, ctx) {
    // yield ctx.getGT();
    yield ctx.verifyInit(); // js挑战初始化
  },
  // eslint-disable-next-line require-yield
  * verifyInit() {
    // 判断是否是pwa
    if (window?.matchMedia('(display-mode: standalone)')?.matches) {
      yield this.changeData({ data: { riskChannel: '1' } });
    }
    try {
      verifyManager.init({
        baseURL: process.env.JS_CHALLENGE,
        preload: {
          validate_scene: 'wms_eu_js',
        },
        data: () => ({
          site_id: '8001', // 品牌名称，接入时找 @Manzhao Li 申请具体id
          site_from: 'eur', // 站点，siteUid
          language: localStorage.getItem(LOCAL_LANG) || 'en',
        }),
      });
    } catch (e) {
      console.log(e);
    }
  },
  // 获取极验开关和配置信息
  * getGT() {
    try {
      yield this.changeData({ data: { captchaLoading: true } });
      const { code, info } = yield getGTApi({ riskType: 'fullpage' });
      if (code === '0' && info) {
        const { data, enabled, retryErrorCodes } = info;
        yield this.changeData({
          data: {
            captchaConfig: camel2Under(data || {}), // 极验验证码配置传参使用的是下划线命名法
            openCaptcha: enabled,
            captchaLoading: enabled, // 兼容降级场景
            retryErrorCodes: retryErrorCodes || [],
          },
        });
      } else {
        // 接口异常，则关闭极验验证码，确保用户可以登录
        yield this.changeData({ data: { openCaptcha: false, captchaLoading: false } });
      }
    } catch (e) {
      // 接口异常，则关闭极验验证码，确保用户可以登录
      yield this.changeData({ data: { openCaptcha: false, captchaLoading: false } });
    }
  },
  // 极验登录
  * challengeLogin(challenge, ctx) {
    const { username, password, location } = yield '';
    const name = strTrim(username);
    yield ctx.login({
      param: {
        username: name,
        password,
        location,
        challenge,
      },
      validTimeout: this.validTimeout,
    });
  },
  // 是wms用户，则tms登录不能影响wms用户登录
  * login(action, ctx, put) {
    markStatus('dataLoading');
    const { location, challenge, ...newParam } = action.param;
    // OFC-39747 base64加密用户密码，encodeURIComponent处理中文等字符
    const encryptedPassword = btoa(encodeURIComponent(newParam.password));
    const res = yield login({ ...newParam, password: encryptedPassword, challenge });
    const user = {
      system: [],
      username: '',
      password: '',
      enName: '', // 英文名
      userNo: '', // 工号
      formatName: '', // 展示用的用户名 格式：李四(1000xxxx)
    };
    if (res.code === '0') {
      // 登录成功，则清空安灯等存储的数据，重新初始化
      sessionStorage.removeItem(SESSION_ANDON_SUBMIT_OBJ);
      sessionStorage.removeItem(SESSION_ANDON_SET_OBJ);
      sessionStorage.removeItem(SESSION_REASON_DATA_OBJ);
      sessionStorage.setItem(SESSION_ANDON_LOGIN, '1');
      sessionStorage.removeItem(SESSION_IS_SHOW_QUICK_ENTRY);
      sessionStorage.removeItem(SESSION_QUICK_ENTRY_OBJ);
      sessionStorage.removeItem(SESSION_PICK_SEQ_VAL);
      sessionStorage.removeItem(SESSION_PDA_GUIDELINE_DATA);
      sessionStorage.removeItem(SESSION_SCROLL_VIEW);
      homeworkSubWarehouseStorage.removeItem();
      // 拥有权限的系统数组
      const system = [];
      // 跳转至首页
      localStorage.setItem(LOCAL_LOCATION, location);
      // 存储跳转APP用的sessionId
      localStorage.setItem(LOCAL_SESSION_ID, res.info.sessionId);
      // 登录成功之后wms用户跳转至仓库选择, tms用户直接进入主页
      // 要等tms接口回来才能跳转，否则可能出现来回跳转的情况
      if (res.info.isWmsUser) {
        system.push('wms');
        // yield put(push('/login/select-warehouse'));
      }
      user.system = system;
      // 优先使用系统返回的userName，系统未返回时，再用用户输入的username
      user.username = res.info.userName || newParam.username;
      user.enName = res.info.enName || user.username;
      user.userNo = res.info.userNo || '';
      // eslint-disable-next-line no-nested-ternary
      user.formatName = res.info.enName ?
        (res.info.userNo ? `${res.info.enName}(${res.info.userNo})` : `${res.info.enName}`)
        : user.username;
      localStorage.setItem(LOCAL_USER, JSON.stringify(user));
      // 判断当前的页面是不是 mot-eu-hd.biz.sheinbackend.com
      const isValidDate = (expireDate) => (moment(expireDate).diff(moment(), 'seconds', true) > 0);
      if (window.location.host === 'mot-eu-hd.biz.sheinbackend.com') {
        // 灰度判断的异常捕获
        try {
          // 去前端配置中心取配置的用户信息
          const alphaData = yield getPageData('WMS_PL_ALPHA_AUTH_WMS_USER', '');
          // 如果取到数据 并且 有用户数据
          if (alphaData && alphaData.user) {
            // 如果用户 不在 配置中
            if (!alphaData.user.map((vi) => (vi.name)).some((name) => ['@all@', action.param.username, user.formatName, res.info.userNo].includes(name))) {
              throw new Error(t('没有权限'));
            } else {
              // 如果用户在配置中 判断是否在有效期内
              const existUser = alphaData.user.find((vi) => ['@all@', action.param.username, user.formatName, res.info.userNo].includes(vi.name));
              if (!isValidDate(existUser.expireDate)) {
                throw new Error(t('没有权限'));
              }
            }
            // 如果没取到配置数据
          } else {
            throw new Error(t('没有权限'));
          }
        } catch (e) {
          Modal.error({
            title: t('您没有灰度权限！请联系  权限管理员'),
          });
          yield ctx.logout();
          return;
        }
      }
      const isRememberId = localStorage.getItem(LOCAL_IS_REMEMBER) === '1';
      if (isRememberId) {
        user.password = newParam.password;
      } else {
        user.password = '';
      }
      let lamsLoginRes = null;
      // 防止tms接口无权限报错情况，如果是wms用户就跳select-warehouse
      try {
        lamsLoginRes = yield gMotLogin({
          userName: newParam.username,
          pwd: encrypt(newParam.password),
          system: 'gtms-mot',
        });
      } catch (error) {
        if (res.info.isWmsUser) {
          yield put(push('/login/select-warehouse'));
          window.__fromLogin__ = true;
        }
        throw error;
      }
      if (lamsLoginRes && lamsLoginRes?.info?.authToken) {
        system.push('tms');
        system.push('gtms');
        user.system = system;
        localStorage.setItem(LOCAL_GTMS_TOKEN, lamsLoginRes?.info?.authToken);
        // 临时策略，调拨扫描页面换取 tms 老登录接口 cookie
        localStorage.setItem(LOCAL_WMS_LOGIN_TOKEN, res.info.token);
      }
      localStorage.setItem(LOCAL_USER, JSON.stringify(user));
      if (res.info?.isWmsUser) {
        yield put(push('/login/select-warehouse'));
        window.__fromLogin__ = true;
      } else if (lamsLoginRes && lamsLoginRes?.info?.authToken) {
        yield put({ type: 'nav/init' });
        yield put(push('/main-menu'));
      } else {
        Modal.error({ content: t('该用户不存在') });
      }
      // 登录后验证是否登录超时
      // action.validTimeout();
      setLoginTimeoutInterval(action.validTimeout);
    } else if (res.code === '460283' || res.code === '460284') {
      // 极验验证码错误需要刷新
      Modal.error({
        content: res.msg,
        onOk: () => window.location.reload(),
      });
    } else {
      // 提示错误
      Modal.error({ content: res.msg });
    }
  },
  * noPwdLogin(action, ctx, put) {
    const res = yield devLogin(action.param);
    if (String(res.code) === '0') {
      if (res.info?.freeLoginEnabled) {
        // 开启免密，跳转至仓库选择
        const user = {
          username: action.param.admintoken || '',
          system: ['wms'],
        };
        localStorage.setItem(LOCAL_USER, JSON.stringify(user));
        yield put(push('/login/select-warehouse'));
        window.__fromLogin__ = true;
      } else {
        // ulp免密登录开关已关闭，前端自动刷新页面,走正常登录
        noPwdAuthLocalStorage.setItem('0');
        window.location.reload();
      }
    } else {
      yield ctx.changeData({ data: { username: '', password: '' } });// 清空用户名和密码
      message.error(res.msg || t('工号或用户名错误，请重新输入'));
    }
  },
  // 验证登录是否超时
  * validTimeout(action, ctx) {
    try {
      const res = yield timeoutApi();
      if (res.code === '0') {
        // msgCode: 1: 正常  2：即将掉线 3：已经超时，注销登录
        const { msgCode } = res.info;
        if (msgCode === 2) {
          message.warning(res.info.message, 2000);
        } else if (msgCode === 3) {
          message.error(res.info.message, 2000);
          yield ctx.logout();
        }
      }
    } catch (e) {
      console.error(e);
    }
  },
  * logout(action, ctx, put) {
    markStatus('dataLoading'); // 搜索loading状态
    yield delay(2000);
    const res = yield logoutApi();
    if (res.code === '0') {
      // 退出登录，清除轮询是否登录过期的的setInterval
      clearLoginTimeoutInterval();
      // 跳转至登录页面，清除sessionId
      // 后端会清除 无需前端清除
      // window.document.cookie = 'PHPSESSID=\'\'';
      if (!localStorage.getItem(LOCAL_IS_REMEMBER)) {
        if (localStorage.getItem(LOCAL_USER)) {
          const user = JSON.parse(localStorage.getItem(LOCAL_USER));
          user.username = '';
          user.password = '';
          user.formatName = '';
          user.enName = '';
          user.userNo = '';
          localStorage.setItem(LOCAL_USER, JSON.stringify(user));
        }
      }
      localStorage.setItem(LOCAL_WAREHOUSE_NAME, '');
      localStorage.setItem(LOCAL_WAREHOUSE, '');
      clearSomeCookie();
      yield put(push('/login'));
    } else {
      // 提示错误
      Modal.error({ content: res.msg || t('退出登录失败') });
    }
  },
  // 极验异常埋点
  * captchaError(err) {
    const { username, retryErrorCodes } = yield '';
    /* eslint-disable @shein-bbl/bbl/translate-i18n-byT */
    const logContent = `「欧洲站前端告警」\r\n异常服务：极验服务\r\n异常人：${username}\r\n异常时间：${moment().format('YYYY-MM-DD HH:mm:ss')}\r\n异常信息：${JSON.stringify(err || {})}`;
    // 调apm埋点
    sendEvent({
      eventCategory: 'log',
      eventAction: 'error',
      eventLabel: 'FetchError',
      eventValue: logContent,
      value: 0,
    });
    // 仅特殊code值才弹窗刷新
    if (retryErrorCodes.includes(err?.error_code)) {
      const status = yield new Promise((r) => Modal.error({
        content: t('停留时间过长，点确认刷新重试'),
        onOk: () => r(1),
      }));
      if (status === 1) {
        window.location.reload();
      }
    }
  },
  // 核验校验
  * doVerify() {
    const { riskChannel } = yield '';
    return yield new Promise((r) => {
      try {
        verifyManager
          .doVerify({
            validate_type: 'js_challenge',
            validate_scene: 'wms_eu_js',
            validate_channel: riskChannel || '0', // 0.pc  1.pwa
            validate_param: {},
          })
          .then((data) => {
            r(data);
          })
          .catch(({ code }) => {
            r({
              isSuccess: false,
              info: {
                validate_token: code, // 错误码给后端 如果出现问题方便排查
              },
            });
          });
      } catch (err) {
        r({
          isSuccess: false,
          info: {
            validate_token: '-2', // verifyManager.doVerify 报错了
          },
        });
        console.info(err);
      }
    });
  },
};
