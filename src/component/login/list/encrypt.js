import { JSEncrypt } from 'jsencrypt';

const publicKey = ['production', 'alpha'].includes(process.env.motenv)
  ?
  `
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwTPY9oI5/JUr+f07/rmA
sRTbLmqLVVUDJBvrM8n7YwHCVu8vKJLk2yik/TI7TxAz0Db3B2TYmFHUs90dkZwi
MZPVvs7gqNa+4cV+Z3ny0FYBQEtv/Ow96A4K70XW4WTUqxiRtNFWYPiM90h0Z+yJ
lQrBhkRIXqfX9eCGhxzGJOR9h17NgpVylxP4uQzdjBsXD4vRaOXrMiHIACBftSj6
adgu5hmipYRf/HBluTETtmmHMt9N8ItjAK72WmCHT63UMS2em1gHNxK9d98l3OTG
jt4jgVsPRIqlueuxlzBISSk/wPxZg5f0M05OEbraxlHIRCjiNX7objmL45ZcUINU
DwIDAQAB
-----END PUBLIC KEY-----
`
  :
  `
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyzwMgQdPiVBpAFatuaXy
q0l4IGrcF69a1suqLhToV34ScBGBuGcDaKmqo+pSbJXsjqwf7EsNYND38BRlm7wH
tw4CxnGUIu8LvE8c0rrdBoASDoCH95VKrErMp6kxtNZ8dGHPxcSc9X5SJ9szPht/
rzdPhJ282ooA8Edz3ZAwCOndpxEEoIiqRznPM+IOQf11Gdd0FhgtW0IYpBqwNA1k
+t7WemSAsmwIGua20XJp5EM7iJgGg39woQCC5pSX1LhERrC1i+md3FYk9RveEJqf
tgkGmNq9w2F3uyXNT2HGITYXs+y3/aQdHMutKuwx2t2BApLrgHpDehwTESFTTbN9
HQIDAQAB
-----END PUBLIC KEY-----
`;

// 新建JSEncrypt对象
const encryptor = new JSEncrypt();

// 设置公钥
encryptor.setPublicKey(publicKey);
// 对需要加密的数据进行加密
const encrypt = (data) => encryptor.encrypt(data);

// eslint-disable-next-line import/prefer-default-export
export { encrypt };
