import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { i18n, t } from '@shein-bbl/react';
import {
  Input, Form, FormCell, Agreement,
} from 'react-weui/build/packages/components/form';
import Icon from '@shein-components/Icon';
import { CellHeader, CellBody } from 'react-weui/build/packages/components/cell';
import Label from 'react-weui/build/packages/components/label';
import { Button, Toast } from 'react-weui/build/packages';
import { LOCAL_IS_REMEMBER, LOCAL_LOCATION } from 'lib/storage';
import { Captcha } from 'common';
import Pickers from '../../common/pickers';
import store from './reducers';
import styles from './style.css';
import PopSheet from '../../common/pop-sheet';
import { jumpByLang } from '../../js';
import { langPickerGroup } from '../../language-enum';
import { strTrim } from '../../../lib/util';
import { checkNoPwdAuth } from '../../../lib/login';
import { noJsChallengeLocalStorage } from '../../../lib/storage-new';

class Container extends Component {
  componentDidMount() {
    store.init();
    // 在登录页，获取判断是否开启ulp免密登录
    checkNoPwdAuth(() => {
      // 开启ulp免密登录
      store.changeData({
        data: {
          ulpLoginFreeTip: t('降级登录界面：输入工号(用户名)、密码即可登录'),
        },
      });
    }, () => {
      // 未开启ulp免密登录
      store.changeData({
        data: {
          ulpLoginFreeTip: t(''),
        },
      });
    });
  }

  render() {
    const {
      dataLoading,
      username,
      password,
      location,
      hidePass,
      isRememberId,
      pickerGroup,
      showPicker,
      lang,
      showLangPicker,
      ulpLoginFreeTip, // ulp免密登录提示语
      openCaptcha,
      captchaConfig,
      captchaLoading,
      riskChannel,
    } = this.props;
    const initLabel = pickerGroup[0].items.find((i) => i.value === location).label || '';
    const preLogin = async () => {
      const name = strTrim(username);
      // 开启免核身服务
      if (noJsChallengeLocalStorage.getItem() === '1') {
        store.login({
          param: {
            username: name,
            password,
            location,
            // eslint-disable-next-line camelcase
            riskValidateToken: '-4',
            riskChannel,
          },
          validTimeout: store.validTimeout.bind(this),
        });
      } else {
        store.changeData({ data: { dataLoading: 0 } });
        Promise.race([store.doVerify(), new Promise((r) => {
          setTimeout(() => {
            r({
              isSuccess: false,
              info: {
                validate_token: '-3', // -3 超时了
              },
            });
          }, 10000);
        })]).then(({ info }) => {
          store.changeData({ data: { dataLoading: 1 } });
          store.login({
            param: {
              username: name,
              password,
              location,
              // eslint-disable-next-line camelcase
              riskValidateToken: info.validate_token,
              riskChannel,
            },
            validTimeout: store.validTimeout.bind(this),
          });
        });
      }
    };
    return (
      <div className={styles.loginPage}>
        <div
          className={styles.selectLang}
          onClick={() => {
            store.changeData({ data: { showLangPicker: true } });
          }}
        >
          <span>{lang}</span>
          <Icon name="arr-down" style={{ position: 'absolute', top: '5px' }} />
        </div>
        <div className={styles.shein}>
          <Icon name="shein-logo" />
        </div>
        <div className={styles.hello}>{t('您好，')}</div>
        <div className={styles.welcome}>{t('欢迎来到移动作业终端系统')}(EU)</div>
        <Form style={{ marginTop: 0, boxShadow: 'none' }}>
          <FormCell className={styles.formCell}>
            <CellHeader>
              <Label style={{ width: 32 }}>
                <Icon name="user-" style={{ color: '#b3b7c1', fontSize: 16 }} />
              </Label>
            </CellHeader>
            <CellBody>
              <Input
                autoFocus
                placeholder={t('用户名')}
                data-bind="username"
              />
            </CellBody>
          </FormCell>
          <FormCell className={styles.formCell}>
            <CellHeader>
              <Label style={{ width: 32 }}>
                <Icon name="unlock" style={{ color: '#b3b7c1', fontSize: 16 }} />
              </Label>
            </CellHeader>
            <CellBody>
              <Input
                placeholder={t('密码')}
                type={hidePass ? 'password' : 'text'}
                data-bind="password"
                onKeyUp={(event) => {
                  if (event.keyCode === 13) {
                    preLogin();
                  }
                }}
              />
              <Icon
                name={hidePass ? 'display' : 'hide'}
                className={styles.showPassword}
                style={{ color: '#b3b7c1', fontSize: 16 }}
                onClick={() => {
                  store.changeData({ data: { hidePass: !hidePass } });
                }}
              />
            </CellBody>
          </FormCell>
          <FormCell className={styles.formCell}>
            <CellHeader>
              <Label style={{ width: 32 }}>
                <Icon name="setting" style={{ color: '#b3b7c1', fontSize: 16 }} />
              </Label>
            </CellHeader>
            <CellBody
              onClick={() => {
                store.changeData({ data: { showPicker: true } });
              }}
            >
              <Input
                placeholder={t('请设置访问处理中心')}
                value={initLabel}
                readOnly
              />
              <Icon name="arr-right" className={styles.showPassword} style={{ color: '#b3b7c1', fontSize: 16 }} />
            </CellBody>
          </FormCell>
        </Form>
        {/* <Agreement */}
        {/*  className={styles.rememberUser} */}
        {/*  onChange={(e) => { */}
        {/*    store.changeData({ data: { isRememberId: e.target.checked } }); */}
        {/*    localStorage.setItem(LOCAL_IS_REMEMBER, e.target.checked ? '1' : ''); */}
        {/*  }} */}
        {/*  checked={isRememberId} */}
        {/* > */}
        {/*  &nbsp;&nbsp; */}
        {/*  {t('记住密码')} */}
        {/* </Agreement> */}
        {openCaptcha && Object.keys(captchaConfig).length > 0 && (
          <Captcha
            config={captchaConfig}
            onReady={(instance) => store.changeData({
              data: {
                captchaInstance: instance, captchaLoading: false,
              },
            })}
            onSuccess={(challenge) => store.challengeLogin(challenge)}
            onError={(err) => store.captchaError(err)}
          />
        )}
        <Button
          type="primary"
          onClick={() => {
            preLogin();
          }}
          className={styles.loginBtn}
          disabled={!username || !password || dataLoading === 0 || captchaLoading}
        >
          {t('登录')}
        </Button>
        <Pickers
          only
          defaultValue={location}
          onClick={() => store.changeData({ data: { showPicker: true } })}
          onChange={(select) => {
            store.changeData({ data: { showPicker: false, location: select.value } });
            localStorage.setItem(LOCAL_LOCATION, select.value);
          }}
          show={showPicker}
          pickerData={pickerGroup}
          onCancel={() => store.changeData({ data: { showPicker: false } })}
        />
        <PopSheet
          onClick={(v) => {
            const selectLang = langPickerGroup.find((item) => item.id === v.id);
            // setTimeout 防止PopSheet还未来得及收回去
            store.changeData({ data: { showLangPicker: false } });
            setTimeout(() => {
              if (selectLang.id !== lang) {
                store.changeData({ data: { lang: selectLang.id } });
                jumpByLang(selectLang.id, true);
              }
            }, 300);
          }}
          onClose={() => {
            store.changeData({ data: { showLangPicker: false } });
          }}
          cancelBtn
          menus={langPickerGroup}
          show={showLangPicker}
        />
        {(dataLoading === 0 || captchaLoading) && <Toast icon="loading" show>loading</Toast>}
      </div>
    );
  }
}

Container.propTypes = {
  dataLoading: PropTypes.number.isRequired,
  username: PropTypes.string.isRequired,
  password: PropTypes.string.isRequired,
  location: PropTypes.string.isRequired,
  hidePass: PropTypes.bool.isRequired,
  isRememberId: PropTypes.bool.isRequired,
  pickerGroup: PropTypes.arrayOf(PropTypes.shape).isRequired,
  showPicker: PropTypes.bool.isRequired,
  showLangPicker: PropTypes.bool.isRequired,
  lang: PropTypes.string.isRequired,
  ulpLoginFreeTip: PropTypes.string,
  openCaptcha: PropTypes.bool,
  captchaConfig: PropTypes.shape(),
  captchaLoading: PropTypes.bool,
  riskChannel: PropTypes.string,
};

const mapStateToProps = (state) => state['login/list'];
export default connect(mapStateToProps)(i18n(Container));
