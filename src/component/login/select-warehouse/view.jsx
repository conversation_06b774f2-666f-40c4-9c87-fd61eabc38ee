/* eslint-disable no-underscore-dangle */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { i18n, t } from '@shein-bbl/react';
import {
  Form,
  FormCell,
  CellsTitle,
  CellBody,
  CellFooter,
  Radio,
  Toast,
} from 'react-weui/build/packages';
import store from './reducers';
import FooterBtn from '../../common/footer-btn';
import { footerStyle } from '../../common/footer';
// import navStore from '../../nav/reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
    store.queryWarehouse({ isFromLogin: window.__fromLogin__ });
    window.__fromLogin__ = false;
  }

  render() {
    const {
      dataLoading,
      warehouseId,
      warehouseList = [],
      warehouseBinding,
    } = this.props;

    const height = window.innerHeight - 56 - 34;
    // 根据系统语言显示仓库名称
    // const getWarehouseName = (i) => {
    //   const lang = localStorage.getItem('lang') || 'zh';
    //   let name = '';
    //   switch (lang) {
    //     case 'en':
    //       name = i.warehouseNameEn;
    //       break;
    //     case 'fr':
    //       name = i.warehouseNameFr;
    //       break;
    //     case 'de':
    //       name = i.warehouseNameDe;
    //       break;
    //     default:
    //       name = i.warehouseName;
    //       break;
    //   }
    //   return name;
    // };

    if (warehouseBinding && !warehouseList?.length) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          height: '100vh',
          justifyContent: 'center',
          alignItems: 'center',
          color: '#666',
        }}
        >{t('正在进入系统中，请稍等...')}
        </div>
      );
    }

    return (
      <div>
        <CellsTitle>{t('请选择仓库')}</CellsTitle>
        <div
          style={{
            height,
            overflow: 'auto',
          }}
        >
          <Form
            radio
            checked={warehouseId}
            style={{ marginTop: 0 }}
          >
            {warehouseList.map((i) => (
              <FormCell
                radio
                key={i.warehouseId}
              >
                <CellBody>
                  {
                    i.warehouseName
                  }
                </CellBody>
                <CellFooter>
                  <Radio
                    name="warehouse"
                    value={i.warehouseId}
                    checked={warehouseId === i.warehouseId}
                    onChange={(e) => {
                      store.changeData({
                        data: {
                          warehouseId: e.target.value,
                        },
                      });
                    }}
                  />
                </CellFooter>
              </FormCell>
            ))}
          </Form>
        </div>
        <div style={footerStyle}>
          <FooterBtn
            disabled={dataLoading === 0}
            onClick={() => {
              // 如果是国外用户登录了国内地址 则忽略绑定 否则走老逻辑
              const item = warehouseList.find((i) => i.warehouseId === warehouseId);
              if (item) {
                store.bindWarehouse({ data: item });
              }
              // 获取时差
              if (warehouseId) {
                store.getTimeDiffNum({ id: warehouseId });
              }
            }}
          >
            {t('确定')}
          </FooterBtn>
        </div>
        {dataLoading === 0 && <Toast icon="loading" show>loading</Toast>}
      </div>
    );
  }
}

Container.propTypes = {
  warehouseList: PropTypes.arrayOf(PropTypes.shape()),
  dataLoading: PropTypes.number,
  warehouseId: PropTypes.string,
  warehouseBinding: PropTypes.bool,
};

const mapStateToProps = (state) => state['login/select-warehouse'];
export default connect(mapStateToProps)(i18n(Container));
