/* eslint-disable no-cond-assign */
import { t } from '@shein-bbl/react';
/* eslint-disable no-shadow */
/* eslint-disable max-len */
import assign from 'object-assign';
// import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { push } from 'react-router-redux';
import { select } from 'redux-saga/effects';
import { LOCAL_WAREHOUSE, LOCAL_WAREHOUSE_NAME } from 'lib/storage';
import { timeDiffNumStorage } from 'lib/storage-new';
import {
  bindWarehouse, queryWarehouse, getWarehouseDetailById, queryUserBindGroupWarehouse,
} from '../server';
import { logout } from '../../main-menu/server';
import Modal from '../../common/modal';
import navStore from '../../nav/reducers';
import store from '../../main-menu/reducers';
import firstStore from '../../sowing/first/reducers';
import overseaFirstStore from '../../oversea/sowing-first/reducers';
import groupStore from '../../oversea/oversea-relay-group/reducers';

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  warehouseId: '',
  warehouseList: [],
  isNotDomesticUser: false, // 国外用户是否登录了国内地址
  notDomesticWarehouseList: [],
  warehouseBinding: false,
};

export default {
  defaultState,
  init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * queryWarehouse(action, ctx, put) {
    markStatus('dataLoading');
    const { isFromLogin } = action;
    const res = yield queryWarehouse();
    let userBindWarehouseRes;
    try {
      if (isFromLogin) {
        userBindWarehouseRes = yield queryUserBindGroupWarehouse();
      }
    } catch (err) {
      console.log(err);
    }
    if (res.code === '0') {
      const list = res.info.warehouseList.map((i) => ({
        ...i,
        warehouseId: i.warehouseId.toString(),
      }));
      if (list.length) {
        // 如果用在在作业分组绑定有仓库，且仓库在授权仓库中，则直接进行绑定
        let warehouseItem;
        const bindWarehouseId = userBindWarehouseRes?.info?.warehouseId;
        if (bindWarehouseId && (warehouseItem = list.find((item) => item.warehouseId === `${bindWarehouseId}`))) {
          yield ctx.bindWarehouse({ data: warehouseItem });
        } else if (list.length === 1) { // 列表只有一个仓库，则直接进行绑定
          yield ctx.bindWarehouse({ data: list[0] });
          if (list[0]?.warehouseId) {
            yield ctx.getTimeDiffNum({ id: list[0]?.warehouseId });
          }
        } else {
          yield ctx.changeData({
            data: {
              warehouseList: list,
              warehouseId: list[0].warehouseId.toString(),
            },
          });
        }
      }
    } else {
      const status = yield new Promise((r) => Modal.error({
        content: res.msg,
        onOk: () => r(1),
      }));
      if (status === 1) {
        // 查询仓库失败的，要调用一下退出登录的接口
        yield logout();
        yield put(push('/login'));
      }
    }
  },
  * bindWarehouse(action, ctx, put) {
    markStatus('dataLoading');
    // yield navStore.changeData({ data: { showNoticePage: true } });
    yield ctx.changeData({
      data: {
        warehouseBinding: true,
      },
    });
    const res = yield bindWarehouse(action.data.warehouseId);
    if (res.code === '0') {
      const navStoreObj = yield select((state) => state.nav);
      if (navStoreObj && navStoreObj.showGridWarehouse) {
        const navBool = navStoreObj.showGridWarehouse.includes(action.data.warehouseId);
        // 如果【选择】没有配置九宫格的仓库时，需要把之前关闭所有页面的展示九宫格功能
        if (!navBool) {
          store.changeData({ data: { openJiugongge: false } });
          firstStore.changeData({ data: { openJiugongge: false } });
          overseaFirstStore.changeData({ openJiugongge: false });
          groupStore.changeData({ data: { openJiugongge: false } });
          // 初始化公共数据
          yield navStore.getConfigData();
          navStore.changeData({ data: { openJiugongge: false, quickEntryObjKey: 'openJiugongge' } });
        }
      }
      localStorage.setItem(LOCAL_WAREHOUSE, JSON.stringify(action.data));
      localStorage.setItem(LOCAL_WAREHOUSE_NAME, action.data.warehouseName);
      // 仓库绑定成功之后调用nav的获取菜单的功能
      yield put({ type: 'nav/init' });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  /**
   * 获取时差
   */
  * getTimeDiffNum(params = {}) {
    try {
      //  获取仓库时差
      markStatus('dataLoading');
      const res = yield getWarehouseDetailById(params);
      if (res.code === '0') {
        timeDiffNumStorage.setItem(res?.info?.timeDiff || '0');
      } else {
        console.log(t('获取时差配置失败'));
      }
    } catch (e) {
      console.log(t('获取时差配置失败'));
    }
  },
};
