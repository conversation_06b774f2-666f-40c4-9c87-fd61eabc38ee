// eslint-disable-next-line import/no-cycle
import {
  sendPostRequest, sendGetRequest, sendGetTmsLogin,
  sendGtms,
} from '../../lib/public-request';
// eslint-disable-next-line import/no-cycle
// import noErrorFetch from '../../lib/no-error-fetch';
// import { camel2Under, under2Camel } from '../../lib/util';
// 登录接口 wms
export const login = (param) => sendPostRequest({
  url: '/pda/login',
  param,
}, process.env.WAS_FRONT);

// 登录接口 tms
export const tLogin = (param) => sendGetTmsLogin({
  url: '/Sign/pdaLogin',
  param,
  keys: ['token'],
});

// 登录接口 tms->GTMS 拿取token
export const pdaGtmsLogin = (param) => sendGetTmsLogin({
  url: '/Sign/pdaGtmsLogin',
  param,
  keys: ['token'],
});

// GMOT Login
export const gMotLogin = (param) => sendGtms({
  url: '/gtms/lams/login',
  param,
  keys: ['userName', 'pwd', 'system'],
}, '');

// 查询用户绑定的仓库
export const queryWarehouse = () => sendPostRequest({
  url: '/warehouse/query_user_bind',
  param: {},
}, process.env.WAS_FRONT);

// 用户绑定的仓库
export const bindWarehouse = (code) => sendPostRequest({
  url: '/pda/query_or_bind_warehouse',
  param: {
    warehouseCode: code,
  },
}, process.env.WAS_FRONT);

// 免密登录
export const devLogin = (param) => sendGetRequest({
  url: `${process.env.WAS_FRONT}/login/devlogin?admintoken=${param.admintoken}&terminal_type=${param.terminalType}`,
}, '');

// // 组员排班-验收登录过期
// export const timeoutApi = (argObj) => {
//   const uri = '/wms/internal/front/member_login_time_limit_config/check_login_time';
//   return noErrorFetch(uri, {
//     method: 'POST',
//     credentials: 'include',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify(camel2Under(argObj)),
//   }, '', true).then(under2Camel);
// };

// 组员排班-验收登录过期
export const timeoutApi = (param) => sendPostRequest({
  baseUrl: process.env.WMS_INTERNAL_FRONT,
  url: '/member_login_time_limit_config/check_login_time',
  param,
  noErrorStatus: true,
});

// 退出登录
export const logoutApi = () => sendGetRequest({
  url: '/pda/logout',
}, process.env.WAS_FRONT);

// 获取极验验证信息
export const getGTApi = (param) => sendPostRequest({
  url: '/geetest/register',
  param,
}, process.env.WAS_FRONT);

export const getWarehouseDetailById = (param) => sendPostRequest({
  url: '/warehouse/get_warehouse_detail',
  param,
}, process.env.BASE_URI_WMD);

// 查询用户绑定的作业人员分组仓库id
export const queryUserBindGroupWarehouse = () => sendPostRequest({
  url: '/staff/query_user_group',
  param: {},
}, process.env.WMS_INTERNAL_FRONT);
