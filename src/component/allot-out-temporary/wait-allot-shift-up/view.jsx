import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
// import moment from 'moment';
import { i18n } from '@shein-bbl/react';
import DefaultPage from './jsx/default';
import UpperHistoryPage from './jsx/upper-history';
import DownHistoryPage from './jsx/down-history';
import store from './reducers';

function ShiftUpper(props) {
  const {
    currPageType,
  } = props;

  useEffect(() => {
    store.init();
  }, []);

  return (
    <>
      {currPageType === 1 && <DefaultPage {...props} />}
      {currPageType === 2 && <UpperHistoryPage {...props} />}
      {currPageType === 3 && <DownHistoryPage {...props} />}
    </>

  );
}

ShiftUpper.propTypes = {
  currPageType: PropTypes.number,
};

export default i18n(ShiftUpper);
