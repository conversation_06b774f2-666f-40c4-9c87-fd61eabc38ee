import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { modal, message } from 'common';
import { all } from 'redux-saga/effects';
import { getHeaderTitle, classFocus } from 'lib/util';
import {
  scanContainerAPI,
  scanLocationCodeAPI,
  handingShiftAPI,
  hasShiftCountAPI,
  getUpperHistoryListAPI,
  hasShiftDownListAPI,
} from './server';

const defaultState = {
  dataLoading: 1,
  containerCode: '', // 周转箱
  location: '', // 上架库位
  disableContainerCode: false,
  disableLocationCode: true,
  currPageType: 1, // 1-初始页 2-我的上架历史 3-已下架箱数明细
  handingShiftInfo: {}, // 进行中的移位信息
  hasShiftCount: 0, // 已移位下架箱数
  showDownCount: true, // 是否显示已下架箱数
  remainCount: 0, // 暂存位剩余库容
  upperList: [], // 我的上架历史
  downList: [], // 已下架列表
  lastFocus: 'containerCode', // 光标最后一次停留位置
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(state, data) {
    Object.assign(state, data);
  },
  * init() {
    markStatus('dataLoading');
    yield this.changeData({
      headerTitle: getHeaderTitle() || t('暂存移位上架'),
    });
    // 获取正在进行中的移位和已移位下架箱数
    yield all([
      this.getHandingShift(),
      this.getHasShiftCount(),
    ]);
    classFocus('containerCode'); // 默认聚焦
  },
  // 重置数据
  * clearData() {
    const { showDownCount } = yield '';
    yield this.changeData({
      ...defaultState,
      showDownCount,
    });
    classFocus('containerCode');
  },

  // 扫周转箱
  * scanContainer() {
    const { containerCode, location } = yield '';
    const { warehouseId } = JSON.parse(localStorage.getItem('warehouse') || '{}');
    markStatus('dataLoading');
    const { code, info, msg } = yield scanContainerAPI({
      containerCode,
      location,
      warehouseId,
    });
    if (code === '0') {
      // 没有有库位号则需要扫库位
      if (!location) {
        yield this.changeData({
          disableContainerCode: true,
          disableLocationCode: false,
          lastFocus: 'location',
        });
        classFocus('location');
        return;
      }
      message.success(t('上架完成'));
      // 库位刚好满了
      if (!info.boxNum) {
        yield this.clearData();
        return;
      }
      // 有库位号直接可以重复扫箱号
      yield this.changeData({
        containerCode: '',
        remainCount: info.boxNum || 0,
        lastFocus: 'containerCode',
      });
      classFocus('containerCode');
    } else {
      // 库位状态不对,重置页面信息
      if (code === '470351') {
        yield this.clearData();
      } else {
        yield this.changeData({ containerCode: '', lastFocus: 'containerCode' });
      }
      modal.error({ content: msg, className: 'containerCode' });
    }
  },

  // 扫描上架库位
  * scanLocation() {
    const { containerCode, location } = yield '';
    const { warehouseId } = JSON.parse(localStorage.getItem('warehouse') || '{}');
    markStatus('dataLoading');
    const { code, msg, info } = yield scanLocationCodeAPI({
      containerCode,
      location,
      warehouseId,
    });
    if (code === '0') {
      message.success(t('上架完成'));
      // 扫库位后隐藏移位下架数量和正在进行中移位信息
      yield this.changeData({
        showDownCount: false,
        handingShiftInfo: {},
      });
      // 库位刚好满了
      if (!info.boxNum) {
        yield this.clearData();
        return;
      }
      yield this.changeData({
        containerCode: '',
        disableContainerCode: false,
        disableLocationCode: true,
        remainCount: info.boxNum || 0,
        lastFocus: 'containerCode',
      });
      // // 更新进行中移位和移位下架数量
      // yield all([
      //   this.getHandingShift(),
      //   this.getHasShiftCount(),
      // ]);
      classFocus('containerCode');
    } else {
      yield this.changeData({ location: '', lastFocus: 'location' });
      modal.error({ content: msg, className: 'location' });
    }
  },

  // 获取正在进行中的移位
  * getHandingShift() {
    markStatus('dataLoading');
    const { code, info, msg } = yield handingShiftAPI({});
    if (code === '0') {
      yield this.changeData({ handingShiftInfo: info || {} });
    } else {
      modal.error({ content: msg });
    }
  },

  // 已移位下架箱数
  * getHasShiftCount() {
    markStatus('dataLoading');
    const { code, info, msg } = yield hasShiftCountAPI({});
    if (code === '0') {
      yield this.changeData({ hasShiftCount: info.boxNum || 0 });
    } else {
      modal.error({ content: msg });
    }
  },

  // 我的移位历史
  * getUpperList() {
    markStatus('dataLoading');
    const { code, info, msg } = yield getUpperHistoryListAPI({});
    if (code === '0') {
      yield this.changeData({ upperList: info?.locationInfo || [] });
    } else {
      modal.error({ content: msg });
    }
  },

  // 已移位下架箱数
  * getDownList() {
    markStatus('dataLoading');
    const { code, info, msg } = yield hasShiftDownListAPI({});
    if (code === '0') {
      yield this.changeData({ downList: info?.locationInfo || [] });
    } else {
      modal.error({ content: msg });
    }
  },

};
