import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { classFocus } from 'lib/util';
import {
  Header,
  View,
  Footer,
} from 'common';
import List from 'common/list';
import store from '../reducers';

function DownHistoryPage(props) {
  const {
    dataLoading,
    downList,
    lastFocus,
  } = props;

  useEffect(() => {
    store.getDownList();
  }, []);

  const rows = [
    [
      {
        title: t('箱号:'),
        render: 'containerCode',
      },
    ],
    [
      {
        title: t('库位:'),
        render: 'downLocation',
      },
    ],
    [
      {
        title: t('移位下架时间:'),
        render: 'shiftDownTime',
      },
    ],
  ];

  return (
    <div>
      <Header title={t('我的移位历史')} />
      <View diff={100} flex>
        <List
          rows={rows}
          data={downList}
          rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0' }}
        />
      </View>
      <Footer
        disabled={dataLoading === 0}
        beforeBack={() => {
          store.changeData({ currPageType: 1, downList: [] });
          classFocus(`${lastFocus}`);
        }}
      />
    </div>
  );
}

DownHistoryPage.propTypes = {
  dataLoading: PropTypes.number,
  downList: PropTypes.arrayOf(PropTypes.shape()),
  lastFocus: PropTypes.string,
};

export default DownHistoryPage;
