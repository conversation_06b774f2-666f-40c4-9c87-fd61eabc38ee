import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { classFocus } from 'lib/util';
import Icon from '@shein-components/Icon';
import { Form, Button } from 'react-weui/build/packages';
import {
  Header,
  View,
  Footer,
  FocusInput,
  RowInfo,
  modal,
} from 'common';
import store from '../reducers';
import styles from '../style.less';

function DefaultPage(props) {
  const {
    dataLoading,
    containerCode,
    location,
    disableContainerCode,
    disableLocationCode,
    handingShiftInfo,
    hasShiftCount,
    remainCount,
    showDownCount,
  } = props;

  return (
    <div>
      <Header title={t('暂存移位上架')}>
        <div
          onClick={() => {
            store.changeData({
              currPageType: 2,
            });
          }}
        >
          <Icon name="detail" style={{ marginRight: '5px' }} />
          {t('历史')}
        </div>
      </Header>
      <View diff={100} flex>
        {
          showDownCount && (
            <Form>
              <RowInfo
                extraStyle={{ borderBottom: '0 none' }}
                label={t('已移位下架箱数')}
                type="info"
                content={(
                  <span
                    style={{ color: '#0000FF' }}
                    onClick={() => {
                      store.changeData({ currPageType: 3 });
                    }}
                  >
                    {hasShiftCount}
                  </span>
            )}
              />
            </Form>
          )
        }
        <Form>
          <FocusInput
            autoFocus
            placeholder={t('请扫描商品条码/周转箱')}
            value={containerCode}
            disabled={dataLoading === 0 || disableContainerCode}
            className="containerCode"
            onChange={(e) => {
              store.changeData({
                containerCode: e.target.value,
              });
            }}
            onPressEnter={() => {
              if (containerCode) {
                store.scanContainer();
              }
            }}
          >
            <label>{t('箱号')}</label>
          </FocusInput>
        </Form>
        <Form>
          <FocusInput
            placeholder={t('请扫描上架库位')}
            value={location}
            disabled={dataLoading === 0 || disableLocationCode}
            className="location"
            onChange={(e) => {
              store.changeData({
                location: e.target.value.trim(),
              });
            }}
            onPressEnter={() => {
              if (location) {
                store.scanLocation();
              }
            }}
            footer={(
              location ? (
                <Button
                  type="primary"
                  style={{ marginBottom: 5 }}
                  size="small"
                  disabled={dataLoading === 0}
                  onClick={() => {
                    if (disableLocationCode) {
                      store.clearData();
                    } else {
                      store.changeData({ location: '' });
                      classFocus('location');
                    }
                  }}
                >
                  {t('清空')}
                </Button>
              ) : null
            )}
          >
            <label>{t('上架库位')}</label>
          </FocusInput>
        </Form>
        {
          !!(handingShiftInfo.location && handingShiftInfo.containerCode) && (
            <div className={styles.footerInfo}>
              <div className={styles.label}>{t('进行中移位')}:</div>
              <div>
                <span>{t('下架库位')}:</span>
                <span>{handingShiftInfo.location}</span>
              </div>
              <div>
                <span>{t('箱号')}:</span>
                <span>{handingShiftInfo.containerCode}</span>
              </div>
            </div>
          )
        }
        {remainCount > 0 && (
          <div className={styles.locationInfo}>
            <span className={styles.label}>{t('暂存位剩余库容')}:</span>
            <span className={styles.num}>{t('{}箱', remainCount)}</span>
          </div>
        )}
      </View>
      <Footer
        beforeBack={(back) => {
          if (containerCode || location) {
            modal.confirm({
              modalBlurInput: true,
              content: t('正在上架,是否离开当前界面?'),
              onOk: () => {
                back();
              },
            });
          } else {
            back();
          }
        }}
      />
    </div>
  );
}

DefaultPage.propTypes = {
  dataLoading: PropTypes.number,
  containerCode: PropTypes.string,
  location: PropTypes.string,
  disableContainerCode: PropTypes.bool,
  disableLocationCode: PropTypes.bool,
  handingShiftInfo: PropTypes.shape(),
  hasShiftCount: PropTypes.number,
  remainCount: PropTypes.number,
  showDownCount: PropTypes.bool,
};

export default DefaultPage;
