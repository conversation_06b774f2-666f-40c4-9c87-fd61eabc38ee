import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { classFocus } from 'lib/util';
import {
  Header,
  View,
  Footer,
} from 'common';
import List from 'common/list';
import store from '../reducers';

function UpperHistoryPage(props) {
  const {
    dataLoading,
    upperList,
    lastFocus,
  } = props;

  useEffect(() => {
    store.getUpperList();
  }, []);

  const rows = [
    [
      {
        title: t('箱号:'),
        render: 'containerCode',
      },
    ],
    [
      {
        title: t('库位:'),
        render: 'upperLocation',
      },
    ],
    [
      {
        title: t('上架时间:'),
        render: 'shiftUpperTime',
      },
    ],
  ];

  return (
    <div>
      <Header title={t('我的移位历史')} />
      <View diff={100} flex>
        <List
          rows={rows}
          data={upperList}
          rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0' }}
        />
      </View>
      <Footer
        disabled={dataLoading === 0}
        beforeBack={() => {
          store.changeData({ currPageType: 1, upperList: [] });
          classFocus(`${lastFocus}`);
        }}
      />
    </div>
  );
}

UpperHistoryPage.propTypes = {
  dataLoading: PropTypes.number,
  upperList: PropTypes.arrayOf(PropTypes.shape()),
  lastFocus: PropTypes.string,
};

export default UpperHistoryPage;
