import { sendPostRequest } from 'lib/public-request';

// 扫描周转箱
export const scanContainerAPI = (param) => sendPostRequest({
  url: '/box_storage_shift_upper/scan_down_container_code',
  param,
}, process.env.WTS_FRONT);

// 扫描上架库位
export const scanLocationCodeAPI = (param) => sendPostRequest({
  url: '/box_storage_shift_upper/scan_down_location',
  param,
}, process.env.WTS_FRONT);

// 进行中移位
export const handingShiftAPI = (param) => sendPostRequest({
  url: '/box_storage_shift_down/handling_shift_info',
  param,
}, process.env.WTS_FRONT);

// 我的移位历史
export const getUpperHistoryListAPI = (param) => sendPostRequest({
  url: '/box_storage_shift_upper/history_shift_info',
  param,
}, process.env.WTS_FRONT);

// 已移位下架箱数
export const hasShiftCountAPI = (param) => sendPostRequest({
  url: '/box_storage_shift_down/query_has_shift_count',
  param,
}, process.env.WTS_FRONT);

// 已移位下架箱数-明细
export const hasShiftDownListAPI = (param) => sendPostRequest({
  url: '/box_storage_shift_down/history_shift_info',
  param,
}, process.env.WTS_FRONT);
