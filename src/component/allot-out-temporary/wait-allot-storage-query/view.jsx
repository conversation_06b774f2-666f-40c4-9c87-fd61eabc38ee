import React, { Component } from 'react';
import { i18n, t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import classNames from 'classnames';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, DrawerContainer, Footer,
} from 'common';
import LocationResult from './jsx/location-result';
import ContainerResult from './jsx/container-result';
import store from './reducers';
import style from './style.less';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      dataLoading,
      isScaning,
      showDrawer,
      activeTab,
      searchCode,
      locationInfo, // 库位扫描信息
      containerInfo, // 箱号信息
      boxDetailList = [], // 大箱明细
    } = this.props;
    // 根据type 修改input的label
    const inputLabel = () => {
      switch (activeTab) {
        case 1:
          return t('暂存库位');
        case 2:
          return t('箱号');
        default:
          return t('暂存库位');
      }
    };

    return (
      <div>
        <Header title={headerTitle || t('待调拨暂存查询')}>
          <Icon className={style.loader} name="loading" data-if={dataLoading === 0} />
        </Header>
        <div className={style.contentWrapper}>
          <div className={style.handleButtonWrapper}>
            {
              [t('查暂存位'), t('查箱号')].map((i, index) => (
                <span
                  key={i}
                  className={classNames([activeTab === index + 1 ? style.active : ''])}
                  onClick={() => {
                    store.changeData({
                      activeTab: index + 1,
                      searchCode: '',
                    });
                  }}
                >
                  {i}
                </span>
              ))
            }
          </div>
        </div>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            className="searchCode"
            value={searchCode}
            disabled={!isScaning}
            autoFocus
            onChange={(e) => {
              store.changeData({ searchCode: e.target.value.trim() });
            }}
            onPressEnter={() => {
              if (!searchCode) {
                return;
              }
              switch (activeTab) {
                case 1:
                  store.scanLocation();
                  break;
                case 2:
                  store.scanContainer({ containerCode: searchCode });
                  break;
                default:
                  break;
              }
            }}
          >
            <label>{inputLabel()}</label>
          </FocusInput>
        </Form>
        {
          (Object.keys(locationInfo)?.length > 0 && activeTab === 1) &&
            <LocationResult locationInfo={locationInfo} />
        }
        {
          (Object.keys(containerInfo)?.length > 0 && activeTab === 2) &&
            <ContainerResult containerInfo={containerInfo} />
        }
        <Footer />
        <DrawerContainer
          drawerStyle={{
            width: '100%',
            height: '70%',
            bottom: '0px',
            top: 'auto',
          }}
          maskCloseAble
          closeable={false}
          visible={showDrawer}
          onClose={() => store.changeData({ showDrawer: false })}
        >
          <section className={style.drawerListContent}>
            <div className={style.drawerTitle}>{t('商品明细')}</div>
            <div className={style.list}>
              <div className={style.header}>
                <div>
                  <div>SKC</div>
                  <div>{t('尺码')}</div>
                </div>
                <div>{t('数量')}</div>
              </div>
              <div className={style.listContent}>
                {
                  (boxDetailList || []).map((item) => (
                    <div className={style.contentItem} key={`${item.skc}-${item.size}-${item.num}`}>
                      <div>
                        <div>{item.skc}</div>
                        <div>{item.size}</div>
                      </div>
                      <div>{item.num}</div>
                    </div>
                  ))
                }
              </div>
            </div>

            <button
              className={style.drawerBtn}
              onClick={() => store.changeData({ showDrawer: false })}
            >
              {t('关闭')}
            </button>
          </section>
        </DrawerContainer>
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  isScaning: PropTypes.number,
  showDrawer: PropTypes.bool,
  activeTab: PropTypes.number,
  searchCode: PropTypes.string,
  locationInfo: PropTypes.shape(), // 库位扫描信息
  containerInfo: PropTypes.shape(), // 箱号信息
  boxDetailList: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Container);
