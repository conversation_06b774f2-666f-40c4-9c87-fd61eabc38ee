import { sendPostRequest } from 'lib/public-request';

/**
 *扫描库位
 * @param param
 * @returns {*}
 */
export const scanLocationAPI = (param) => sendPostRequest({
  url: '/box_storage_pda/scan_location',
  param,
}, process.env.WTS_FRONT);

/**
 * 扫描大箱号
 * @param param
 * @returns {*}
 */
export const scanContainerAPI = (param) => sendPostRequest({
  url: '/box_storage_pda/scan_container',
  param,
}, process.env.WTS_FRONT);
