import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import LoadMore from './load-more';
import DetailLine from './detail-line';
import store from '../reducers';
import style from '../style.less';
// 展示更多
function LocationResult(props) {
  const { locationInfo } = props;
  const {
    locationCode, subWarehouseName, area,
    storageContainerCodeList = [], waitDownContainerCodeList = [],
  } = locationInfo;

  return (
    <div className={style.resultContainer}>
      <div className={style.title}>{t('查询结果')}</div>
      <DetailLine
        label={t('暂存库位')}
        content={<label>{locationCode}</label>}
      />
      <DetailLine
        showBorder
        label={t('子仓/库区')}
        content={<label>{subWarehouseName}/{area}</label>}
      />
      <DetailLine
        label={t('待下架大箱')}
        content={<label>{t('共 {} 个', waitDownContainerCodeList.length || 0)}</label>}
      />
      <LoadMore
        data={waitDownContainerCodeList}
        onClick={(containerCode) => {
          store.changeData({
            activeTab: 2,
          });
          store.scanContainer({ containerCode });
        }}
      />
      <DetailLine
        label={t('已暂存大箱')}
        content={<label>{t('共 {} 个', storageContainerCodeList.length || 0)}</label>}
      />
      <LoadMore
        data={storageContainerCodeList}
        onClick={(containerCode) => {
          store.changeData({
            activeTab: 2,
          });
          store.scanContainer({ containerCode });
        }}
      />
    </div>
  );
}

LocationResult.propTypes = {
  locationInfo: PropTypes.shape(),
};

export default LocationResult;
