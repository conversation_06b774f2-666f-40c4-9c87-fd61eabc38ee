import React from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import style from '../style.less';

function DetailLine(props) {
  const {
    label, content, showIcon = false, showBorder = false,
  } = props;

  const borderStyle = {
    borderBottom: '1px solid #e5e5e5',
  };

  return (
    <div className={style.detailLine} style={showBorder ? borderStyle : null}>
      <div className={style.label}>{label}</div>
      <div className={style.content}>
        {content}
        {
          showIcon && (
            <Icon className={style.primary} fontSize={18} name="pc-arr_right" />
          )
        }
      </div>
    </div>
  );
}

DetailLine.propTypes = {
  label: PropTypes.string,
  content: PropTypes.oneOfType([PropTypes.string, PropTypes.shape()]),
  showIcon: PropTypes.bool,
  showBorder: PropTypes.bool,
};

export default DetailLine;
