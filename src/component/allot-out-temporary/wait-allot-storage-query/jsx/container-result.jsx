import React from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import DetailLine from './detail-line';
import store from '../reducers';
import style from '../style.less';

function ContainerResult(props) {
  const { containerInfo } = props;

  const {
    status, statusName, containerCode, isLegal, orderMark, orderMarkName, purchaseCode,
    locationCode, subWarehouseName, area, storageUserName, storageTime, downUserName, downTime,
  } = containerInfo;

  const locationCodeSection = locationCode.split('-');
  // 已下架、退供下架 状态不展示： 暂存库位、子仓和库位、暂存人、暂存时间
  const invalidStatus = [2, 4];
  // 首单或紧急
  const dangerMarkList = [1, 2];
  return (
    <div className={style.resultContainer}>
      <div className={style.title}>{t('查询结果')}</div>
      <DetailLine
        showIcon
        label={t('大箱号')}
        content={(
          <label className={style.primary} onClick={() => store.changeData({ showDrawer: true })}>
            <div className={style.tag}>{statusName}</div>
            {containerCode}
          </label>
      )}
      />
      <DetailLine
        label={t('发票审核状态')}
        content={(
          <label className={isLegal ? style.primary : style.danger}>
            {isLegal ? t('通过') : t('不通过')}
          </label>
      )}
      />
      <DetailLine
        showBorder
        label={t('下单编号')}
        content={(
          <label>
            <span className={dangerMarkList.includes(orderMark) ? style.danger : style.primary}>
              {orderMarkName}
            </span>
            {purchaseCode}
          </label>
        )}
      />
      {
        !invalidStatus.includes(status) && (
          <>
            {
              locationCode && (
                <DetailLine
                  label={t('暂存库位')}
                  content={(
                    <label>
                      {locationCode.split('-').slice(0, -1).join('-')}
                      {locationCodeSection?.length > 1 ? '-' : ''}
                      <strong className={style.danger}>{locationCode.split('-').slice(-1).join('-')}</strong>
                    </label>
                  )}
                />
              )
            }
            {
              (subWarehouseName && area) && (
                <DetailLine
                  label={t('子仓/库区')}
                  content={<label>{subWarehouseName}/{area}</label>}
                />
              )
            }
            <DetailLine
              label={t('暂存人')}
              content={<label>{storageUserName}</label>}
            />
            <DetailLine
              label={t('暂存时间')}
              content={<label>{storageTime}</label>}
            />
          </>
        )
      }
      {
        downUserName && (
          <DetailLine
            label={t('下架任务领取人')}
            content={<label>{downUserName}</label>}
          />
        )
      }
      {
        // 已暂存、待下架、已下架、退供下架时显示
        downTime && [0, 1, 2, 4].includes(status) && (
          <DetailLine
            // 【已下架、退供下架】显示为「下架完成时间」，其他显示为「下架任务领取时间」
            label={[2, 4].includes(status) ? t('下架完成时间') : t('下架任务领取时间')}
            content={<label>{downTime}</label>}
          />
        )
      }
    </div>
  );
}

ContainerResult.propTypes = {
  containerInfo: PropTypes.shape(),
};

export default ContainerResult;
