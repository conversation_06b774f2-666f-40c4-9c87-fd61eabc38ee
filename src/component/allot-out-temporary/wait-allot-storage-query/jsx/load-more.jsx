import { t } from '@shein-bbl/react';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import style from '../style.less';

function LoadMore(props) {
  const [showAll, setShowAll] = useState(false);
  const [pageData, sePageData] = useState([]);

  const {
    data, key, initNum = 5, onClick,
  } = props;

  useEffect(() => {
    const pageInitData = (data || []).slice(0, initNum);
    sePageData([...pageInitData]);
  }, [data, initNum]);

  if (data.length === 0) return null;
  return (
    <div className={style.loadMoreContainer}>
      {
        (pageData || []).map((item) => (
          // 点击跳转
          <div
            className={style.loadLine}
            key={key ? item[key] : item}
            onClick={() => onClick && onClick(item)}
          >
            {key ? item[key] : item}
          </div>
        ))
      }
      {
        data?.length > initNum && (
          showAll ?
            (
              <div
                className={style.showMore}
                onClick={() => {
                  setShowAll(false);
                  sePageData((data || []).slice(0, initNum));
                }}
              >
                {t('收起')}
                <Icon name="odec-up" />
              </div>
            ) :
            (
              <div
                className={style.showMore}
                onClick={() => {
                  setShowAll(true);
                  sePageData(data);
                }}

              >
                {t('查看全部')}
                <Icon name="odec-down" />
              </div>
            )
        )
      }
    </div>
  );
}

LoadMore.propTypes = {
  data: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.shape(), PropTypes.string])),
  key: PropTypes.string,
  initNum: PropTypes.number,
  onClick: PropTypes.func,
};

export default LoadMore;
