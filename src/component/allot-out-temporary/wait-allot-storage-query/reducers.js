import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { classFocus, getHeaderTitle } from 'lib/util';
import { markStatus } from 'rrc-loader-helper';
import { modal } from 'common';
import aaooAudio from '../../../source/audio/aaoo.mp3';
import { scanLocationAPI, scanContainerAPI } from './server';

const aaooEle = new Audio(aaooAudio);
aaooEle.load();

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  isScaning: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: t('调拨暂存查询'),
  activeTab: 1, // 当前高亮tab
  searchCode: '', // 箱号/暂存位
  showDrawer: false, // 是否展示详情界面
  locationInfo: {}, // 库位扫描信息
  containerInfo: {}, // 箱号信息
  boxDetailList: [], // 箱号下 大箱明细
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
  },
  /**
   * 扫描库位
   * @param action
   * @param ctx
   */
  * scanLocation() {
    const { searchCode } = yield '';
    markStatus('isScaning');
    const { code, msg, info } = yield scanLocationAPI({ locationCode: searchCode });
    if (code === '0') {
      yield this.changeData({
        locationInfo: info,
        searchCode: '',
      });
      classFocus('searchCode');
    } else {
      modal.error({
        title: msg,
        className: 'searchCode',
      });
      yield this.changeData({
        searchCode: '',
      });
    }
  },
  /**
   * 扫描大箱号
   * @param action
   * @param ctx
   */
  * scanContainer({ containerCode }) {
    markStatus('dataLoading');
    markStatus('isScaning');
    const { code, msg, info } = yield scanContainerAPI({ containerCode });
    if (code === '0') {
      const { boxDetailList, ...containerInfo } = info;
      // 切换tab不能清空详情信息，需保持两份详情
      yield this.changeData({
        containerInfo,
        boxDetailList,
        searchCode: '',
      });
      classFocus('searchCode');
    } else {
      modal.error({
        title: msg,
        className: 'searchCode',
      });
      yield this.changeData({
        searchCode: '',
      });
    }
  },
};
