.loader {
    animation: loader-effect 1s infinite linear;
}

@keyframes loader-effect {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

// tabButton部分
.handleButtonWrapper {
    margin: 6px 10px;
    font-size: 14px;
    border-radius: 5px;
    border: 1px solid #197AFA;
    overflow: hidden;

    span {
        position: relative;
        display: inline-block;
        width: 50%;
        height: 32px;
        line-height: 32px;
        text-align: center;
        background-color: #fff;
        color: #197afa;

        &.active {
            background-color: #197afa;
            color: #fff;
            box-shadow: -1px 0px #197AFA;
        }
    }

    span::after {
        position: absolute;
        top: 10px;
        right: 0px;
        content: '';
        width: 0px;
        height: 14px;
        border-right: 1px solid #197AFA;
        z-index: 1;
    }

    span:last-child::after {
        border: none;
    }
}

.content {
    margin: 16px 4px;

    .contentTitle {
        font-size: 14px;
        color: #197afa;
    }
}

.danger {
    color: #d9001b;
}

.primary {
    color: #197afa;
}

// 加载更多
.loadMoreContainer {
    padding-bottom: 5px;
    border-bottom: 1px solid #e5e5e5;
    font-size: 14px;
    .loadLine {
        margin-bottom: 2px;
        .primary
    }

    .showMore {
        padding: 4px 0px;
        font-size: 12px;
        text-align: center;
    }
}

// 弹框样式
.drawerListContent {
    display: flex;
    flex-direction: column;
    margin: 6px;
    height: 100%;
    height: -webkit-fill-available;
    .drawerTitle {
        padding-bottom: 6px;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: bold;
        border-bottom: 1px solid #eee;
        .primary;
    }

    .list {
        height: 0;
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;
        margin-bottom: 5px;
        .header, .contentItem {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 2px 6px;
            & > div:first-child {
                font-size: 13px;
                line-height: 16px;
                text-align: left;
            }
            & > div:last-child {
                font-size: 14px;
                text-align: right;
            }
        }
        .header {
            padding: 2px; 
            font-size: 13px;
            color: #333;
            background-color: #f2f2f2;
        }
        .listContent {
            flex: 1 1 auto;
            height: 0;
            overflow-y: auto;

            .contentItem {
                border-bottom: 1px solid #eee;
            }
        }
    }

    .drawerBtn {
        flex-basis: 40px;
        height: 36px;
        line-height: 1;
        padding: 0px;
        color: #0059ce;
        font-size: 14px;
        word-break: break-all;
        border: 1px solid #0059ce;
        background-color: transparent;
    }
}

// 暂存位、箱号范围
.resultContainer {
    height: calc(100vh - 213px);
    margin: 8px;
    padding: 0px 8px;
    box-shadow: 0px 0px 8px 0px #fbfbfb, 0px 0px 2px 1px rgba(25,122,250,.15);
    overflow-y: auto;
    .title {
        padding-top: 6px;
        font-size: 14px;
        font-weight: bold;
        .primary;
    }

    .tag {
        display: inline-block;
        margin: 0px 2px 0px 0px;
        padding: 2px;
        font-size: 12px;
        line-height: 12px;
        color: #197afa;
        border: 1px solid #197AFA;
        border-radius: 0px;    
    }
}

// 详情
.detailLine {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    line-height: 20px;
    height: 36px;
    .label {
        text-align: left;
        color: #7f7f7f;
    }
    .content {
        display: flex;
        flex-direction: row;
        align-items: center;
        color: #333;
        label, i {
            display: inline-block;
        }
    }

    span {
        margin-right: 4px;
    }
}