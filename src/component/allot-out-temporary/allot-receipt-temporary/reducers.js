import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { put } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import { modal, message } from 'common';
import { getHeaderTitle, classFocus } from '../../../lib/util';
import { sanStorageLocationApi, sanStorageBoxApi } from './server';

const defaultState = {
  headerTitle: '', // 页面标题：统一从后端接口获取
  scanNumber: 0, // 扫描件数 纯前端缓存
  temporaryStorage: '', // 暂存位
  bigBoxCode: '', // 大箱号
  subWarehouseId: '', // 子仓id
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }
    yield ctx.changeData({
      data: {
        headerTitle: getHeaderTitle(),
        subWarehouseId: preSubMenu.subWarehouseId,
      },
    });
    classFocus('temporaryStorage');
  },
  // 扫描暂存位
  * sanStorageLocation() {
    const { temporaryStorage, subWarehouseId } = yield '';
    const { code, msg } = yield sanStorageLocationApi({
      subWarehouseId,
      location: temporaryStorage,
    });
    if (code === '0') {
      classFocus('bigBoxCode');
    } else {
      yield this.changeData({
        data: {
          temporaryStorage: '',
        },
      });
      modal.error({ modalBlurInput: true, content: msg, className: 'temporaryStorage' });
    }
  },
  // 扫描大箱号
  * sanStorageBox() {
    const {
      temporaryStorage, bigBoxCode, subWarehouseId, scanNumber,
    } = yield '';
    const { code, msg } = yield sanStorageBoxApi({
      subWarehouseId,
      location: temporaryStorage,
      containerCode: bigBoxCode,
    });
    if (code === '0') {
      yield this.changeData({
        data: {
          temporaryStorage: '', // 暂存位
          bigBoxCode: '', // 大箱号
          scanNumber: scanNumber + 1,
        },
      });
      classFocus('temporaryStorage');
      message.success(t('暂存成功'));
    } else {
      yield this.changeData({
        data: {
          bigBoxCode: '',
        },
      });
      modal.error({ modalBlurInput: true, content: msg, className: 'bigBoxCode' });
    }
  },
};
