import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import store from './reducers';
import { Header, FocusInput, Footer } from '../../common';
import RowInfo from '../../common/row-info';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      scanNumber,
      temporaryStorage,
      bigBoxCode,
    } = this.props;
    return (
      <div>
        <Header title={headerTitle || t('调拨收货暂存')} />
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            className="temporaryStorage"
            value={temporaryStorage}
            onChange={(e) => {
              store.changeData({
                data: {
                  temporaryStorage: e.target.value.trim().toUpperCase(),
                },
              });
            }}
            onPressEnter={(e) => {
              if (e.target.value.trim().length) {
                store.sanStorageLocation();
              }
            }}
          >
            <label>{t('暂存位')}</label>
          </FocusInput>
          <FocusInput
            placeholder={t('请扫描')}
            className="bigBoxCode"
            value={bigBoxCode}
            onChange={(e) => {
              store.changeData({
                data: {
                  bigBoxCode: e.target.value.trim().toUpperCase(),
                },
              });
            }}
            onPressEnter={(e) => {
              if (e.target.value.trim().length) {
                store.sanStorageBox();
              }
            }}
          >
            <label>{t('大箱号')}</label>
          </FocusInput>
        </Form>
        <RowInfo
          label={t('已暂存托数')}
          extraStyle={{
            borderBottom: 'none',
          }}
          content={scanNumber}
        />
        <Footer />
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  scanNumber: PropTypes.number,
  temporaryStorage: PropTypes.string,
  bigBoxCode: PropTypes.string,
};

const mapStateToProps = (state) => state['allot-out-temporary/allot-receipt-temporary'];
export default connect(mapStateToProps)(i18n(Container));
