import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form, Dialog } from 'react-weui/build/packages';
import { classFocus } from 'lib/util';
import { i18n, t } from '@shein-bbl/react';
import { Button } from 'react-weui/build/packages/components/button';
import classNames from 'classnames';
import { SUB_MENU } from 'lib/jumpUrl';
import {
  pages,
  Header,
  FocusInput,
  Footer,
  FooterBtn,
  Table,
} from '../../common';
import style from './style.less';
import store from './reducers';
import modal from '../../common/modal';

const { View } = pages;

const columns = [
  {
    title: t('暂存库位号'),
    dataIndex: 'locationCode',
    default: '- -',
    width: 10,
  },
  {
    title: t('箱号'),
    dataIndex: 'containerCode',
    width: 10,
  },

];

class Container extends Component {
  constructor(props) {
    super(props);
    this.state = {
      countDownNumber: 15, // 弹窗倒计时
    };
  }

  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      initLoading,
      dataLoading,
      isLocationDisabled,
      detailObj,
      isContainerCodeDisabled,
      isDialogVisibled,
      locationCode,
      pageState,
      activeTab,
      tableList,
      taskNo,
    } = this.props;
    let {
      countDownNumber,
    } = this.state;
    // 倒计时秒数
    if (locationCode && isDialogVisibled && !window.setIntervalId) {
      window.setIntervalId = setInterval(() => {
        countDownNumber--;
        this.setState({
          countDownNumber,
        });
        if (countDownNumber === 0) {
          store.changeData({
            isDialogVisibled: false,
            isLocationDisabled: false,
            detailObj: {},
          });
          classFocus('locationCode');
          this.setState({
            countDownNumber: 15,
          });
          clearInterval(window.setIntervalId);
          window.setIntervalId = '';
        }
      }, 1000);
    }

    return (
      <View initLoading={initLoading}>
        <Header title={headerTitle || t('待调拨整箱下架')}>
          <div
            onClick={() => {
              store.changeData({
                pageState: 'details',
              });
              store.queryDetailInfo();
            }}
          >
            {t('明细')}
          </div>
        </Header>
        {
          pageState === 'base' ? (
            <>
              <Form>
                <FocusInput
                  placeholder={t('请扫描暂存库位号')}
                  autoFocus
                  disabled={isLocationDisabled || dataLoading === 0}
                  className="locationCode"
                  data-bind="locationCode"
                  onChange={() => {
                    store.changeData({
                      containerCode: '',
                      isContainerCodeDisabled: true,
                    });
                  }}
                  onPressEnter={(e) => {
                    const val = e.target.value;
                    if (!val) {
                      return;
                    }
                    if (val || val === 0) {
                      store.scanLocation({
                        locationCode: val,
                      });
                    }
                  }}
                >
                  <label>{t('暂存库位')}</label>
                </FocusInput>
                <FocusInput
                  placeholder={t('请扫描大箱号')}
                  className="containerCode"
                  data-bind="containerCode"
                  disabled={!locationCode || isContainerCodeDisabled || dataLoading === 0}
                  onPressEnter={(e) => {
                    const val = e.target.value;
                    if (!val) {
                      return;
                    }
                    if (!locationCode) {
                      modal.error({
                        content: t('请扫描暂存库位'),
                      });
                      return;
                    }
                    if (val || val === 0) {
                      store.scanContainerCode({
                        containerCode: val,
                        locationCode,
                        taskNo: detailObj.taskNo,
                      });
                    }
                  }}
                >
                  <label>{t('大箱号')}</label>
                </FocusInput>
              </Form>
              {detailObj.taskNo && (
              <div>
                <div className={style.detailDiv}>
                  <div>
                    {t('任务单号')}：
                    <span className={style.detailOrderNum}>
                      {detailObj.taskNo}
                    </span>
                  </div>
                  <div>{detailObj.takeBoxNum}/{detailObj.totalBoxNum}</div>
                </div>
                <div className={style.detailDiv}>
                  <div>
                    {t('暂存库位')}：
                    <span className={style.detailOrderNum}>{detailObj.locationCode}</span>
                  </div>
                  <div>{detailObj.locationTakeBoxNum}/{detailObj.locationTotalBoxNum}</div>
                </div>
                <div className={style.detailDiv}>
                  <div>
                    {t('下架大箱')}：
                  </div>
                  <Button
                    className={style.detailButton}
                    disabled={!locationCode || !detailObj.containerCode || isContainerCodeDisabled}
                    onClick={() => {
                      modal.confirm2({
                        title: t('确认该大箱缺箱？'),
                        content: (
                          <div style={{ fontSize: '14px' }}>
                            <span>{t('大箱号')}：</span>
                            <span
                              style={{
                                color: 'red',
                                fontSize: '14px',
                              }}
                            >
                              {detailObj.containerCode || ''}
                            </span>
                          </div>
                        ),
                        onOk: () => {
                          store.lackContainerCode({
                            containerCode: detailObj.containerCode,
                            locationCode,
                            taskNo: detailObj.taskNo,
                          });
                        },
                      });
                    }}
                  >
                    {t('缺箱')}
                  </Button>
                </div>
                <p className={style.containerCodeNumber}>{detailObj.containerCode || ''}</p>
                <div className={style.detailDiv}>
                  <div>
                    {t('任务类型')}：
                  </div>
                  <div style={{
                    display: 'inline-block',
                    color: 'white',
                    backgroundColor: 'red',
                    fontWeight: 'bold',
                  }}
                  >{detailObj.taskType ? t('增值下架') : t('待调拨下架')}
                  </div>
                </div>
                <div className={style.incrementItem}>
                  <div className={style.incrementLabel}>
                    {t('增值项')}：
                  </div>
                  <div className={style.incrementName}>
                    {detailObj.incrementItemName || ''}
                  </div>
                </div>
              </div>
              )}
              <Dialog
                title={t('是否要领取该下架任务？')}
                show={isDialogVisibled}
                buttons={[{
                  type: 'default',
                  label: t('关闭'),
                  onClick: () => {
                    store.resetPageStore().then(() => {
                      store.changeData({
                        isDialogVisibled: false,
                        isLocationDisabled: false,
                      }).then(() => {
                        classFocus('locationCode');
                        this.setState({
                          countDownNumber: 15,
                        });
                        clearInterval(window.setIntervalId);
                        window.setIntervalId = '';
                      });
                    });
                  },
                }, {
                  type: 'primary',
                  label: t('领取'),
                  onClick: () => {
                    store.changeData({
                      isDialogVisibled: false,
                      isLocationDisabled: false,
                    }).then(() => {
                      this.setState({
                        countDownNumber: 15,
                      });
                      clearInterval(window.setIntervalId);
                      window.setIntervalId = '';
                    });
                    store.takeFask({
                      taskNo: detailObj.taskNo,
                    });
                  },
                }]}
              >
                <p className={style.dialogP1}>{t('下架任务号')}：{detailObj.taskNo}</p>
                <p className={style.dialogP2}>{t('弹框{}s后自动关闭', countDownNumber)}</p>
              </Dialog>
              <Footer
                beforeBack={() => {
                  // 去异常上架
                  window.open(SUB_MENU, '_self');
                }}
              >
                <FooterBtn
                  disabled={!detailObj.taskNo}
                  onClick={() => {
                    modal.confirm2({
                      title: t('确认要结束该任务么？'),
                      content: (
                        <div style={{ fontSize: '14px' }}>
                          <span>{t('任务单号')}：</span>
                          <span
                            style={{
                              color: 'red',
                              fontSize: '14px',
                            }}
                          >
                            {detailObj.taskNo}
                          </span>
                        </div>
                      ),
                      onOk: () => {
                        store.finishFask({
                          taskNo: detailObj.taskNo,
                        });
                      },
                    });
                  }}
                >
                  {t('结束任务')}
                </FooterBtn>
              </Footer>
            </>
          ) : (
            <>
              <div className={style.contentWrapper}>
                <div className={style.handleButtonWrapper}>
                  {
                    [t('待下架'), t('已下架')].map((i, index) => (
                      <span
                        key={i}
                        className={classNames([activeTab === index + 1 ? style.active : ''])}
                        onClick={() => {
                          store.changeData({
                            activeTab: index + 1,
                          }).then(() => {
                            store.queryDetailInfo();
                          });
                        }}
                      >
                        {i}
                      </span>
                    ))
                  }
                </div>
              </div>
              <div style={{ margin: '8px 0', padding: '0px 5px' }}>
                <span>{t('下架任务号{}{}', ' : ', taskNo)}</span>
              </div>
              <Table
                columns={columns}
                dataSource={tableList}
                maxHeight={300}
              />
              <Footer
                beforeBack={() => {
                  store.changeData({
                    pageState: 'base',
                  });
                }}
              />
            </>
          )
        }
      </View>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  initLoading: PropTypes.number,
  detailObj: PropTypes.shape(),
  dataLoading: PropTypes.string,
  isLocationDisabled: PropTypes.bool,
  isContainerCodeDisabled: PropTypes.bool,
  isDialogVisibled: PropTypes.bool,
  containerCode: PropTypes.string,
  locationCode: PropTypes.string,
  taskNo: PropTypes.string,
  pageState: PropTypes.shape(),
  activeTab: PropTypes.bool,
  tableList: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Container);
