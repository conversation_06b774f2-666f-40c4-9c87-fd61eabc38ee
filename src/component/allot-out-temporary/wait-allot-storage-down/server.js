import { sendPostRequest } from '../../../lib/public-request';

/**
 * 待调拨整箱下架-初始化页面
 * @param param
 * @returns {*}
 */
export const initDataAPI = (param) => sendPostRequest({
  url: '/take_down/init_data',
  param,
}, process.env.WTS_FRONT);

/**
 * 待调拨整箱下架-扫描暂存库位
 * @param param
 * @returns {*}
 */
export const scanLocationAPI = (param) => sendPostRequest({
  url: '/take_down/scan_location',
  param,
}, process.env.WTS_FRONT);

/**
 * 待调拨整箱下架-扫描大箱号
 * @param param
 * @returns {*}
 */
export const scanContainerCodeAPI = (param) => sendPostRequest({
  url: '/take_down/scan_container_code',
  param,
}, process.env.WTS_FRONT);

/**
 * 待调拨整箱下架-缺箱
 * @param param
 * @returns {*}
 */
export const lackContainerCodeAPI = (param) => sendPostRequest({
  url: '/take_down/lack_container_code',
  param,
}, process.env.WTS_FRONT);

/**
 * 待调拨整箱下架-领取任务
 * @param param
 * @returns {*}
 */
export const takeFaskAPI = (param) => sendPostRequest({
  url: '/take_down/take_task',
  param,
}, process.env.WTS_FRONT);

/**
 * 待调拨整箱下架-结束任务
 * @param param
 * @returns {*}
 */
export const finishFaskAPI = (param) => sendPostRequest({
  url: '/take_down/finish_task',
  param,
}, process.env.WTS_FRONT);

/**
 * 获取明细
 * @param param
 * @returns {*}
 */
export const queryDetailInfoAPI = (param) => sendPostRequest({
  url: '/take_down/query_user_detail',
  param,
}, process.env.WTS_FRONT);
