/* detail
----------------------------------------------------------------*/
.detailDiv {
  width: calc(100% - 24px);
  padding: 16px 12px 0 12px;
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}


.detailButton {
  width: 70px!important;
  height: 30px;
  line-height: 30px;
  margin: 0;
  font-size: 12px;
}

/* dialog
----------------------------------------------------------------*/
.dialogP1 {
  font-size: 13.5px;
  margin-bottom: 8px;
}

.dialogP2 {
  color: #aaaaaa;
  font-size: 12px;
  margin-bottom: 8px;
}

.containerCodeNumber {
  font-size: 25px;
  color: red;
  padding-left: 16px;
}


.contentWrapper {
  padding: 6px 12px 0px 12px;
  background-color: #fff;
}
// tabButton部分
.handleButtonWrapper {
  font-size: 14px;
  border-radius: 5px;
  border: 1px solid #197AFA;
  overflow: hidden;
  width: 100%;
  span {
    position: relative;
    display: inline-block;
    // width: 98px;
    width:50%;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background-color: #fff;
    color: #197afa;

    &.active {
      background-color: #197afa;
      color: #fff;
      box-shadow: -1px 0px #197AFA;
    }
  }

  span::after {
    position: absolute;
    top: 10px;
    right: 0px;
    content: '';
    width: 0px;
    height: 14px;
    border-right: 1px solid #197AFA;
    z-index: 1;
  }

  span:nth-child(2)::after {
    border: none;
  }
}

.incrementItem {
  display: flex;
  align-items: flex-start;
  padding: 16px 12px 0 12px;
  .incrementLabel {
    white-space: nowrap;
  }
  .incrementName {
    flex: 1;
    word-break: break-all;
  }
}