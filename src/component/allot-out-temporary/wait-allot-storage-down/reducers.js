import assign from 'object-assign';
import { put } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import { markStatus } from 'rrc-loader-helper';
import { classFocus, getHeaderTitle, getWarehouseId } from 'lib/util';
import { t } from '@shein-bbl/react';
import {
  finishFaskAPI,
  initDataAPI,
  lackContainerCodeAPI,
  scanContainerCodeAPI,
  scanLocationAPI, takeFaskAPI,
  queryDetailInfoAPI,
} from './server';
import { modal } from '../../common';
import message from '../../common/message';

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  initLoading: false, // 页面初始化 loading
  isContainerCodeDisabled: true, // 大箱号是否可以输入
  closeTaskDisabled: true, // 结束任务按钮
  isLocationDisabled: false, // 暂存位是否可用
  locationCode: '', // string 暂存库位号
  containerCode: '', // string 大箱号
  detailObj: {
    containerCode: '', // string 大箱号
    isTakeNewTask: false, // boolean 是否需要领取新的下架任务
    locationTakeBoxNum: 0, // integer 暂存库位已下架箱数
    locationTotalBoxNum: 0, // integer 暂存库位总箱数
    takeBoxNum: 0, // integer 任务已下架箱数
    taskNo: '', // string 下架任务号
    totalBoxNum: 0, // integer 任务总箱数
    incrementItemName: '', // 增值项
  },
  isDialogVisibled: false, // 弹窗确定
  pageState: 'base',
  activeTab: 1,
  tableList: [],
  taskNo: [],
  subWarehouseId: null,
};

export default {
  defaultState,
  // 初始当前页面数据
  resetPageStore(draft) {
    assign(draft, defaultState, {
      headerTitle: draft.headerTitle,
      subWarehouseId: draft.subWarehouseId,
    });
  },
  changeData(state, data) {
    assign(state, data);
  },
  changeDetailObjData(state, data) {
    assign(state.detailObj, data);
  },
  * init(action, ctx) {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }
    yield ctx.changeData({
      headerTitle: getHeaderTitle(),
      subWarehouseId: preSubMenu.subWarehouseId,
      initLoading: false,
    });
    yield ctx.initData();
  },
  /**
   * 待调拨整箱下架-初始化页面
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * initData(action, ctx) {
    const { subWarehouseId } = yield '';

    markStatus('dataLoading');
    const { code, info, msg } = yield initDataAPI({ subWarehouseId });
    classFocus('locationCode');
    if (code === '0') {
      if (info && !info.isTakeNewTask) {
        yield ctx.changeData({
          isLocationDisabled: false,
          isContainerCodeDisabled: true,
          locationCode: '',
          taskNo: info.taskNo,
          detailObj: { ...info },
        });
      }
    } else {
      modal.error({
        content: msg,
        className: 'locationCode',
      });
    }
  },
  /**
   * 待调拨整箱下架-扫描暂存库位
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * scanLocation(action, ctx) {
    const { subWarehouseId } = yield '';
    markStatus('dataLoading');
    const param = {
      ...action,
      subWarehouseId,
    };
    const { code, info, msg } = yield scanLocationAPI(param);
    if (code === '0') {
      yield ctx.changeData({
        isLocationDisabled: false,
        isContainerCodeDisabled: false,
        isDialogVisibled: false,
      });
      yield ctx.changeDetailObjData({
        taskNo: info.taskNo,
      });
      if (info.isFinish) {
        yield ctx.changeData({
          isLocationDisabled: true,
          isDialogVisibled: true,
        });
      } else {
        yield ctx.changeData({
          containerCode: '',
        });
        classFocus('containerCode');
      }
    } else {
      yield ctx.changeData({
        locationCode: '',
        containerCode: '',
        isLocationDisabled: false,
        isContainerCodeDisabled: true,
      });
      modal.error({
        content: msg,
        className: 'locationCode',
      });
    }
  },
  /**
   * 待调拨整箱下架-扫描大箱号
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * scanContainerCode(action, ctx) {
    const { subWarehouseId } = yield '';
    const param = {
      ...action,
      subWarehouseId,
    };
    markStatus('dataLoading');
    const { code, info, msg } = yield scanContainerCodeAPI(param);
    if (code === '0') {
      if (info.isTakeNewTask) {
        yield this.resetPageStore();
        yield this.initData();
        message.success(t('任务完成'));
        classFocus('locationCode');
      } else if (!info.isTakeNewLocation && !info.isTakeNewTask) {
        yield this.changeData({
          containerCode: '',
          isContainerCodeDisabled: false,
          detailObj: { ...info },
        });
        classFocus('containerCode');
      } else if (info.isTakeNewLocation && !info.isTakeNewTask) {
        yield this.changeData({
          locationCode: '',
          containerCode: '',
          isLocationDisabled: false,
          isContainerCodeDisabled: false,
          detailObj: { ...info },
        });
        classFocus('locationCode');
      }
    } else {
      yield ctx.changeData({ containerCode: '', isContainerCodeDisabled: false });
      modal.error({
        content: msg,
        className: 'containerCode',
      });
    }
  },
  /**
   * 待调拨整箱下架-缺箱
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * lackContainerCode(action, ctx) {
    const { subWarehouseId } = yield '';
    const param = {
      ...action,
      subWarehouseId,
    };
    markStatus('dataLoading');
    const { code, info, msg } = yield lackContainerCodeAPI(param);
    if (code === '0') {
      if (info.isTakeNewTask) {
        yield this.resetPageStore();
        yield this.initData();
        message.success(t('任务完成'));
        classFocus('locationCode');
      } else if (!info.isTakeNewLocation && !info.isTakeNewTask) {
        yield this.changeData({
          containerCode: '',
          isContainerCodeDisabled: false,
          detailObj: { ...info },
        });
        classFocus('containerCode');
      } else if (info.isTakeNewLocation && !info.isTakeNewTask) {
        yield this.changeData({
          locationCode: '',
          containerCode: '',
          isLocationDisabled: false,
          isContainerCodeDisabled: false,
          detailObj: { ...info },
        });
        classFocus('locationCode');
      }
    } else {
      yield ctx.changeData({ containerCode: '', isContainerCodeDisabled: false });
      modal.error({
        content: msg,
        className: 'containerCode',
      });
    }
  },
  /**
   * 待调拨整箱下架-领取任务
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * takeFask(action, ctx) {
    const { subWarehouseId } = yield '';
    const param = {
      ...action,
      subWarehouseId,
    };
    markStatus('dataLoading');
    const { code, info, msg } = yield takeFaskAPI(param);
    yield ctx.changeData({
      locationCode: '',
      isLocationDisabled: false,
      isContainerCodeDisabled: false,
      detailObj: { ...info },
    });
    if (code === '0') {
      classFocus('locationCode');
    } else {
      yield ctx.changeData({
        locationCode: '',
      });
      modal.error({
        content: msg,
        className: 'locationCode',
      });
    }
  },
  /**
   * 待调拨整箱下架-结束任务
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * finishFask(action, ctx) {
    markStatus('dataLoading');
    const data = yield finishFaskAPI(action);
    if (data.code === '0') {
      yield this.resetPageStore();
      classFocus('locationCode');
    } else {
      yield ctx.changeData({ locationCode: '', isLocationDisabled: false });
      modal.error({
        content: data.msg,
        className: 'locationCode',
      });
    }
  },
  // 获取明细详情
  * queryDetailInfo() {
    const { activeTab } = yield '';
    const params = {
      detailStatus: activeTab - 1,
      warehouseId: getWarehouseId(),
    };
    markStatus('dataLoading');
    const { code, info, msg } = yield queryDetailInfoAPI(params);
    if (code === '0') {
      yield this.changeData({
        tableList: info.detailList,
        taskNo: info.taskNo,
      });
    } else {
      modal.error({
        content: msg,
      });
    }
  },
};
