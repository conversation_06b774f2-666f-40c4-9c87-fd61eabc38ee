import React, { Component } from 'react';
import PropTypes from 'prop-types';
import CellsMenu from '../../common/cells-menu';
import Header from '../../common/header';
import Footer from '../../common/footer';
import store from './reducers';

class Container extends Component {
  componentWillMount() {
    store.init();
  }

  render() {
    const {
      dispatch,
      btnList,
      headTitle,
    } = this.props;
    return (
      <div>
        <Header title={headTitle} />
        <CellsMenu
          cells={btnList}
        />
        <Footer dispatch={dispatch} />
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  btnList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  headTitle: PropTypes.string.isRequired,
};

export default Container;
