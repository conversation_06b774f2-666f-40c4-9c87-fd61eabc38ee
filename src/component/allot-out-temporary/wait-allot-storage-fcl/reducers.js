import assign from 'object-assign';
import { markStatus } from 'rrc-loader-helper';
import { t } from '@shein-bbl/react';
import { modal } from '../../common';
import {
  coscanLocationAPI,
  scanContainerAPI,
} from './server';
import aaooAudio from '../../../source/audio/aaoo.mp3';
import { getHeaderTitle, getWarehouseId, classFocus as utilClassFocus } from '../../../lib/util';

const aaooEle = new Audio(aaooAudio);
aaooEle.load();

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  containerCode: '', // 箱号
  locationCode: '', // 库位
  containersCount: 0, // 已暂存箱数
  headerTitle: '',
  isLocationDisabled: true,
  isContainerCodeDisabled: false,
  focusPosition: '',
  detailList: [], // 明细
  type: 1, // 1 扫描页面 2 明细页面
  recommendLocationCode: '', // 推荐库位
  scanContainer: false,
  area: '', // 推荐库区
  stockingFlag: false, // 是否备货款
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * classFocus(className, ctx) {
    utilClassFocus(className);
    yield ctx.changeData({
      data: {
        focusPosition: className,
      },
    });
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    yield ctx.classFocus('containerCode');
  },
  /**
   * 扫描库位
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * scanLocation(action, ctx) {
    const {
      detailList, containersCount, containerCode,
    } = yield '';
    markStatus('dataLoading');
    const data = yield coscanLocationAPI({
      ...action.params,
      warehouseId: getWarehouseId(),
      checkArea: action.params.checkArea === 0 ? 0 : 1,
    });
    if (data.code === '0') {
      yield ctx.changeData({
        data: {
          isContainerCodeDisabled: false,
          // locationCode: '',
          containerCode: '',
          isLocationDisabled: true,
          containersCount: containersCount + 1,
          detailList: [...detailList, { containerCode, locationCode: action.params.locationCode }],
        },
      });
      if (data.info?.isClearLocationCode) {
        yield ctx.changeData({
          data: {
            locationCode: '',
          },
        });
      }
      yield ctx.classFocus('containerCode');
    } else if (data.code === '470335') {
      const status = yield new Promise((r) => modal.confirm2({
        content: data.msg,
        onOk: () => r('ok'),
        onCancel: () => r('cancel'),
        cancelText: t('取消'),
        okText: t('仍要暂存'),
      }));
      if (status === 'ok') {
        yield ctx.scanLocation({
          params: {
            ...action.params,
            checkArea: 0,
          },
        });
      }
      if (status === 'cancel') {
        yield ctx.changeData({ data: { locationCode: '', isLocationDisabled: false } });
        yield ctx.classFocus('locationCode');
      }
    } else if (data.code === '470337') {
      aaooEle.play();// 报错音提示
      modal.error({
        content: data.msg,
        className: 'locationCode',
      });
    } else {
      aaooEle.play();// 报错音提示
      yield ctx.changeData({ data: { locationCode: '', isLocationDisabled: false } });
      modal.error({
        content: data.msg,
        className: 'locationCode',
      });
    }
  },
  /**
   * 扫描大箱号
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * scanContainer(action, ctx) {
    markStatus('dataLoading');
    const data = yield scanContainerAPI({ ...action.params, warehouseId: getWarehouseId() });
    const { locationCode } = yield '';
    if (data.code === '0') {
      yield this.changeData({
        data: {
          // containerCode: '',
          scanContainer: true,
          recommendLocationCode: data.info.locationCode,
          isLocationDisabled: false,
          area: data.info.area,
          stockingFlag: data.info.stockingFlag,
        },
      });
      if (locationCode) {
        yield ctx.scanLocation({
          params: {
            containerCode: action.params.containerCode,
            locationCode,
            checkArea: 0,
          },
        });
      } else {
        yield ctx.classFocus('locationCode');
      }
    } else if (data.code === '470337') {
      aaooEle.play();// 报错音提示
      modal.error({ content: data.msg, className: 'containerCode' });
    } else {
      aaooEle.play();// 报错音提示
      yield ctx.changeData({
        data: {
          containerCode: '',
        },
      });
      modal.error({ content: data.msg, className: 'containerCode' });
    }
  },
};
