import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import {
  Form,
  Button,
} from 'react-weui/build/packages';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import Footer from '../../../common/footer';
import RowInfo from '../../../common/row-info';

class ScanContainer extends Component {
  render() {
    const {
      dataLoading,
      containersCount,
      locationCode,
      containerCode,
      isLocationDisabled,
      isContainerCodeDisabled,
      recommendLocationCode,
      scanContainer,
      area,
      stockingFlag,
    } = this.props;

    return (
      <div>
        <Form>
          <FocusInput
            placeholder={t('请扫描')}
            disabled={isContainerCodeDisabled || dataLoading === 0}
            className="containerCode"
            value={containerCode}
            onChange={(e) => {
              store.changeData({
                data: {
                  containerCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.scanContainer({
                params: {
                  containerCode: e.target.value.trim(),
                  // locationCode,
                },
              });
            }}
          >
            <label>{t('大箱号')}</label>
          </FocusInput>
        </Form>
        <Form>
          <FocusInput
            autoFocus
            placeholder={t('请扫描')}
            disabled={isLocationDisabled || !containerCode || dataLoading === 0}
            className="locationCode"
            value={locationCode}
            onChange={(e) => {
              store.changeData({
                data: {
                  locationCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.changeData({
                data: {
                  isLocationDisabled: true,
                },
              });
              store.scanLocation({
                params: {
                  containerCode,
                  locationCode: e.target.value.trim(),
                  checkArea: 1,
                },
              });
            }}
            footer={
              (
                <Button
                  style={{ marginBottom: 5 }}
                  size="small"
                  onClick={() => {
                    store.changeData({
                      data: {
                        locationCode: '',
                        // containerCode: '',
                        isLocationDisabled: !containerCode,
                        isContainerCodeDisabled: false,
                      },
                    });
                    store.classFocus(containerCode ? 'locationCode' : 'containerCode');
                  }}
                >
                  {t('更换库位')}
                </Button>
              )
            }
          >
            <label>{t('暂存库位')}</label>
          </FocusInput>
        </Form>
        <Form>
          <RowInfo
            label={t('已暂存箱数')}
            content={containersCount > 0 ? (
              <span
                style={{ color: '#0059ce' }}
                onClick={() => store.changeData({ data: { type: 2 } })}
              >
                {containersCount}
                <Icon name="arr-right" />
              </span>
            ) : containersCount}
          />
        </Form>
        <div style={{ fontSize: '16px', padding: '0 10px' }}>
          <span>{t('推荐库区{}', ' : ')}</span>
          {!scanContainer || area ? (
            <span>
              {area}
            </span>
          ) :
            t('暂无推荐')}
        </div>
        <div style={{ fontSize: '16px', padding: '0 10px' }}>
          <span>{t('推荐暂存位{}', ' : ')}</span>
          {!scanContainer || recommendLocationCode ? (
            <span>
              {recommendLocationCode}
            </span>
          ) :
            t('当前仓库暂存位已满{}请调整库位后再暂存{}', '，', '！')}
        </div>
        {stockingFlag && (
          <div
            style={{
              width: '100%',
              fontSize: '20px',
              textAlign: 'center',
              color: 'red',
              position: 'absolute',
              bottom: '100px',
              left: 0,
            }}
          >
            <span>{t('备货款')}</span>
          </div>
        )}
        <Footer
          beforeBack={(back) => {
            store.init();
            back();
          }}
        />
      </div>
    );
  }
}

ScanContainer.propTypes = {
  containersCount: PropTypes.number,
  locationCode: PropTypes.string,
  containerCode: PropTypes.string,
  isLocationDisabled: PropTypes.bool,
  isContainerCodeDisabled: PropTypes.bool,
  dataLoading: PropTypes.number,
  recommendLocationCode: PropTypes.string,
  scanContainer: PropTypes.string,
  area: PropTypes.string,
  stockingFlag: PropTypes.bool,
};

export default ScanContainer;
