import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import store from '../reducers';
import Footer from '../../../common/footer';
import List from '../../../common/list';
import style from '../../../style.css';

const rows = [
  [
    {
      title: '',
      render: (record) => <span style={{ marginRight: 0 }}>{record.containerCode}</span>,
    },
    {
      title: '',
      render: 'locationCode',
    },
  ],
];

class DetailList extends Component {
  render() {
    const {
      dispatch,
      detailList,
      focusPosition,
    } = this.props;

    const height = window.innerHeight - 44 - 56;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <List
          header={(
            <div style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: '#000',
              display: 'flex',
              paddingRight: '15px',
              justifyContent: 'space-between',
            }}
            >
              <div>{t('箱号')}</div>
              <div>{t('暂存库位号')}</div>
            </div>
          )}
          rows={rows}
          data={detailList}
          rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0' }}
        />
        <Footer
          dispatch={dispatch}
          beforeBack={() => {
            store.changeData({
              data: {
                type: 1,
              },
            });
            store.classFocus(focusPosition);
          }}
        />
      </div>
    );
  }
}

DetailList.propTypes = {
  dispatch: PropTypes.func,
  detailList: PropTypes.arrayOf(PropTypes.shape()),
  focusPosition: PropTypes.string,
};

export default DetailList;
