import React, { Component } from 'react';
import { i18n, t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import ScanContainer from './jsx/scan-container';
import DetailList from './jsx/detail';
import { Header } from '../../common';
import store from './reducers';
import style from './style.css';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      type,
      dataLoading,
    } = this.props;
    return (
      <div>
        {type === 1 ? (
          <>
            <Header title={headerTitle || t('待调拨整箱暂存')}>
              <Icon className={style.loader} name="loading" data-if={dataLoading === 0} />
            </Header>
            <ScanContainer {...this.props} />
          </>
        ) : (
          <>
            <Header title={headerTitle || t('已暂存列表')} />
            <DetailList {...this.props} />
          </>
        )}
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  type: PropTypes.number,
  containersCount: PropTypes.number,
  containerCode: PropTypes.string,
  dataLoading: PropTypes.number,
};

export default i18n(Container);
