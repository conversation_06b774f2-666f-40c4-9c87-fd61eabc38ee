import assign from 'object-assign';
import { markStatus } from 'rrc-loader-helper';
import { modal } from '../../common';
import {
  coscanLocationAPI,
  scanContainerAPI,
  queryHasShiftCountAPI,
  handlingShiftInfoAPI,
  getHistoryAPI,
} from './server';
import aaooAudio from '../../../source/audio/aaoo.mp3';
import { classFocus as utilClassFocus, getHeaderTitle, getWarehouseId } from '../../../lib/util';

const aaooEle = new Audio(aaooAudio);
aaooEle.load();

const defaultState = {
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  containerCode: '', // 箱号
  location: '', // 库位
  boxNum: 0, // 已暂存箱数
  headerTitle: '',
  isLocationDisabled: false,
  isContainerCodeDisabled: true,
  focusPosition: '',
  detailList: [], // 明细
  type: 0, // 0 默认扫库位页面 1 扫箱子页面 2 明细页面
  preType: 0, // 缓存跳转前的页面标识
  handlingShiftInfo: {}, // 进行中移位信息
  containerCodeList: [], // 大箱号列表
  locationInfo: [], // 移位历史数据
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * classFocus(className, ctx) {
    utilClassFocus(className);
    yield ctx.changeData({
      data: {
        focusPosition: className,
      },
    });
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    yield this.queryHasShiftCount(); // 已移位下架箱数
    yield this.handlingShiftInfo(); // 进行中移位信息
    yield this.classFocus('location');
  },
  * queryHasShiftCount() {
    markStatus('dataLoading');
    const data = yield queryHasShiftCountAPI({});
    if (data.code === '0') {
      yield this.changeData({
        data: {
          boxNum: data.info?.boxNum,
        },
      });
    } else {
      modal.error({ content: data.msg, className: 'location' });
    }
  },
  * handlingShiftInfo() {
    markStatus('dataLoading');
    const data = yield handlingShiftInfoAPI({});
    console.info('--', data);
    if (data.code === '0') {
      yield this.changeData({
        data: {
          handlingShiftInfo: data.info || {},
        },
      });
    } else {
      modal.error({ content: data.msg, className: 'location' });
    }
  },
  /**
   * 扫描库位
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * scanLocation(action, ctx) {
    markStatus('dataLoading');
    const data = yield coscanLocationAPI({
      ...action.params,
      warehouseId: getWarehouseId(),
    });
    if (data.code === '0') {
      yield ctx.changeData({
        data: {
          isLocationDisabled: true,
          isContainerCodeDisabled: false,
          containerCode: '',
          containerCodeList: (data.info?.containerCodeList || []).map((ci) => ({
            containerCode: ci,
          })),
          type: 1, // 跳到扫箱子页面
          focusPosition: 'location',
          scrollRowNums: 0,
        },
      });
      yield ctx.classFocus('containerCode');
    } else {
      aaooEle.play();// 报错音提示
      yield ctx.changeData({ data: { location: '', isLocationDisabled: false } });
      modal.error({
        content: data.msg,
        className: 'location',
      });
    }
  },
  /**
   * 扫描大箱号
   * @param action
   * @param ctx
   * @returns {Generator<Generator<*, void, *>|Generator<*, void, *>|*, void, *>}
   */
  * scanContainer(action, ctx) {
    markStatus('dataLoading');
    const data = yield scanContainerAPI({ ...action.params, warehouseId: getWarehouseId() });
    if (data.code === '0') {
      const { containerCodeList } = yield '';
      let currentRowNum = 0;
      const formatContainerCodeList = containerCodeList.map((ci, ciIdx) => {
        const isHasScanned = String(ci.containerCode) === String(action.params?.containerCode);
        if (isHasScanned) {
          currentRowNum = ciIdx;
          return {
            ...ci,
            hasScanned: isHasScanned,
          };
        }
        return ci;
      });
      yield this.changeData({
        data: {
          containerCode: '',
          focusPosition: 'containerCode',
          containerCodeList: formatContainerCodeList,
          scrollRowNums: currentRowNum,
        },
      });
      yield ctx.changeData({
        data: {
          containerCode: '',
        },
      });
      yield ctx.classFocus('containerCode');
    } else {
      aaooEle.play();// 报错音提示
      yield ctx.changeData({
        data: {
          containerCode: '',
        },
      });
      modal.error({ content: data.msg, className: 'containerCode' });
    }
  },
  // 获取历史
  * getHistory() {
    markStatus('dataLoading');
    const data = yield getHistoryAPI({ warehouseId: getWarehouseId() });
    if (data.code === '0') {
      const { type } = yield '';
      yield this.changeData({
        data: {
          detailList: data.info?.locationInfo || [],
          type: 2, // 跳转到移位历史页面
          preType: type, // 缓存跳转前当前页面type
        },
      });
    } else {
      modal.error({ content: data.msg });
    }
  },
};
