import { sendPostRequest } from '../../../lib/public-request';

/**
 *扫描库位
 * @param param
 * @returns {*}
 */
export const coscanLocationAPI = (param) => sendPostRequest({
  url: '/box_storage_shift_down/scan_down_location',
  param,
}, process.env.WTS_FRONT);

/**
 * 扫描大箱号
 * @param param
 * @returns {*}
 */
export const scanContainerAPI = (param) => sendPostRequest({
  url: '/box_storage_shift_down/scan_down_container_code',
  param,
}, process.env.WTS_FRONT);

/**
 * 初始化获取已移位下架箱数
 * @param param
 * @returns {*}
 */
export const queryHasShiftCountAPI = (param) => sendPostRequest({
  url: '/box_storage_shift_down/query_has_shift_count',
  param,
}, process.env.WTS_FRONT);

/**
 * 初始化获取进行中移位信息
 * @param param
 * @returns {*}
 */
export const handlingShiftInfoAPI = (param) => sendPostRequest({
  url: '/box_storage_shift_down/handling_shift_info',
  param,
}, process.env.WTS_FRONT);

/**
 * 获取历史
 * @param param
 * @returns {*}
 */
export const getHistoryAPI = (param) => sendPostRequest({
  url: '/box_storage_shift_down/history_shift_info',
  param,
}, process.env.WTS_FRONT);
