import React, { Component } from 'react';
import { i18n, t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import Default from './jsx/default';
import ScanContainer from './jsx/scan-container';
import DetailList from './jsx/detail';
import { Header } from '../../common';
import store from './reducers';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      type,
    } = this.props;
    switch (type) {
      case 1:
        return (
          <div>
            <Header title={headerTitle || t('暂存移位下架')}>
              <div
                onClick={() => {
                  store.getHistory();
                }}
              >
                <Icon name="detail" style={{ marginRight: '5px' }} />
                {t('历史')}
              </div>
            </Header>
            <ScanContainer {...this.props} />
          </div>
        );
      case 2:
        return (
          <div>
            <Header title={t('我的移位历史')} />
            <DetailList {...this.props} />
          </div>
        );
      default:
        return (
          <div>
            <Header title={headerTitle || t('暂存移位下架')}>
              <div
                onClick={() => {
                  store.getHistory();
                }}
              >
                <Icon name="detail" style={{ marginRight: '5px' }} />
                {t('历史')}
              </div>
            </Header>
            <Default {...this.props} />
          </div>
        );
    }
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  type: PropTypes.number,
  boxNum: PropTypes.number,
  containerCode: PropTypes.string,
  dataLoading: PropTypes.number,
};

export default i18n(Container);
