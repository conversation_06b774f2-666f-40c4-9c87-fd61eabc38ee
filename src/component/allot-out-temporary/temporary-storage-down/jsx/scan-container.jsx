import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import {
  Form,
} from 'react-weui/build/packages';
import ScanLocation from 'component/allot-out-temporary/temporary-storage-down/jsx/scan-location';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import Footer from '../../../common/footer';
import List from '../../../common/list';
import style from '../style.css';

class ScanContainer extends Component {
  render() {
    const {
      dataLoading,
      location,
      containerCode,
      isContainerCodeDisabled,
      containerCodeList,
      scrollRowNums,
    } = this.props;

    return (
      <div>
        <ScanLocation {...this.props} />
        <Form>
          <FocusInput
            placeholder={t('请扫描周转箱')}
            disabled={isContainerCodeDisabled || !location || dataLoading === 0}
            className="containerCode"
            value={containerCode}
            onChange={(e) => {
              store.changeData({
                data: {
                  containerCode: e.target.value.trim(),
                },
              });
            }}
            onPressEnter={(e) => {
              if (!e.target.value) {
                return;
              }
              store.scanContainer({
                params: {
                  location,
                  containerCode: e.target.value.trim(),
                },
              });
            }}
          >
            <label>{t('箱号')}</label>
          </FocusInput>
        </Form>
        <List
          autoScroll
          scrollRowNums={scrollRowNums}
          rows={[
            [
              {
                title: '',
                render: (record) => {
                  if (record.hasScanned) {
                    return <b className={style.hasScanned}>{record.containerCode}</b>;
                  } else {
                    return <span>{record.containerCode}</span>;
                  }
                },
              },
            ],
          ]}
          data={containerCodeList}
          style={{ height: 'calc(100vh - 210px)', overflowY: 'auto' }}
          rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0', fontSize: 12 }}
        />
        <Footer
          beforeBack={() => {
            store.changeData({
              data: {
                type: 0, // 返回默认页面
                location: '', // 清空库位
                isLocationDisabled: false, // 取消库位禁用
              },
            });
          }}
        />
      </div>
    );
  }
}

ScanContainer.propTypes = {
  boxNum: PropTypes.number,
  location: PropTypes.string,
  containerCode: PropTypes.string,
  isLocationDisabled: PropTypes.bool,
  isContainerCodeDisabled: PropTypes.bool,
  dataLoading: PropTypes.number,
  handlingShiftInfo: PropTypes.shape(),
  containerCodeList: PropTypes.arrayOf(PropTypes.shape()),
  scrollRowNums: PropTypes.number,
};

export default ScanContainer;
