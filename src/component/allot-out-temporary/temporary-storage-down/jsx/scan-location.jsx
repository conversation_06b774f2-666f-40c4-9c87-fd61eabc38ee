import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import {
  Form,
  Button,
} from 'react-weui/build/packages';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';

class ScanLocation extends Component {
  render() {
    const {
      dataLoading,
      location,
      isLocationDisabled,
    } = this.props;

    return (
      <Form>
        <FocusInput
          autoFocus
          placeholder={t('请扫描库位')}
          disabled={isLocationDisabled || dataLoading === 0}
          className="location"
          value={location}
          onChange={(e) => {
            store.changeData({
              data: {
                location: e.target.value.trim(),
              },
            });
          }}
          onPressEnter={(e) => {
            if (!e.target.value) {
              return;
            }
            store.changeData({
              data: {
                isLocationDisabled: true,
              },
            });
            store.scanLocation({
              params: {
                location: e.target.value.trim(),
              },
            });
          }}
          footer={location ? (
            <Button
              style={{ marginBottom: 5 }}
              size="small"
              onClick={() => {
                store.changeData({
                  data: {
                    location: '',
                    containerCode: '',
                    isLocationDisabled: false,
                    isContainerCodeDisabled: true,
                    containerCodeList: [], // 清空扫库位返回的箱号列表
                  },
                });
                store.classFocus('location');
              }}
            >
              {t('清空')}
            </Button>
          ) : ''}
        >
          <label>{t('下架库位')}</label>
        </FocusInput>
      </Form>
    );
  }
}

ScanLocation.propTypes = {
  location: PropTypes.string,
  isLocationDisabled: PropTypes.bool,
  dataLoading: PropTypes.number,
};

export default ScanLocation;
