import { t } from '@shein-bbl/react';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import store from '../reducers';
import Footer from '../../../common/footer';
import List from '../../../common/list';
import style from '../../../style.css';

const rows = [
  [
    {
      title: `${t('箱号')}：`,
      render: 'containerCode',
    },
  ],
  [
    {
      title: `${t('库位')}：`,
      render: 'downLocation',
    },
  ],
  [
    {
      title: `${t('移位下架时间')}：`,
      render: 'shiftDownTime',
    },
  ],
];

class DetailList extends Component {
  render() {
    const {
      dispatch,
      detailList,
      focusPosition,
      preType,
    } = this.props;
    const height = window.innerHeight - 44 - 56;

    return (
      <div className={style.flexColContainer} style={{ height }}>
        <List
          rows={rows}
          data={detailList}
          rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0' }}
        />
        <Footer
          dispatch={dispatch}
          beforeBack={() => {
            store.changeData({
              data: {
                type: preType,
              },
            });
            store.classFocus(focusPosition);
          }}
        />
      </div>
    );
  }
}

DetailList.propTypes = {
  dispatch: PropTypes.func,
  detailList: PropTypes.arrayOf(PropTypes.shape()),
  focusPosition: PropTypes.string,
  preType: PropTypes.string,
};

export default DetailList;
