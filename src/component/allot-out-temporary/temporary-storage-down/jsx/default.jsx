import React, { Component } from 'react';
import { t } from '@shein-bbl/react';
import PropTypes from 'prop-types';
import {
  Form,
} from 'react-weui/build/packages';
import store from '../reducers';
import Footer from '../../../common/footer';
import RowInfo from '../../../common/row-info';
import ScanLocation from './scan-location';
import style from '../style.css';

class Default extends Component {
  render() {
    const {
      boxNum,
      handlingShiftInfo,
    } = this.props;

    return (
      <div>
        <Form>
          <RowInfo
            label={t('已移位下架箱数')}
            content={boxNum}
          />
        </Form>
        <ScanLocation {...this.props} />
        {(handlingShiftInfo.location && handlingShiftInfo.containerCode) && (
          <div className={style.initialInfo}>
            <div className={style.initialInfoLabel}>{t('信息')}</div>
            <div className={style.initialInfoContent}>
              <div>{t('进行中移位')}：</div>
              <div>{t('下架库位')}：{handlingShiftInfo.location}</div>
              <div>{t('箱号')}：{handlingShiftInfo.containerCode}</div>
            </div>
          </div>
        )}
        <Footer
          beforeBack={(back) => {
            store.init();
            back();
          }}
        />
      </div>
    );
  }
}

Default.propTypes = {
  boxNum: PropTypes.number,
  location: PropTypes.string,
  containerCode: PropTypes.string,
  isLocationDisabled: PropTypes.bool,
  isContainerCodeDisabled: PropTypes.bool,
  dataLoading: PropTypes.number,
  handlingShiftInfo: PropTypes.shape(),
};

export default Default;
