import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import Icon from '@shein-components/Icon';
import { handleDatesShow, showTextLimit } from '../../../../lib/util';
import {
  Header,
  Footer,
  List,
} from '../../../common';
import SplitBar from './modules/split-bar';
import store from '../reducers';
import style from '../style.css';

const rows = [
  [
    {
      title: '',
      render: 'taskSetCode',
    },
    {
      title: '',
      render: v => <span style={{ marginRight: 20 }}>{v.taskNum}-<span style={{ color: '#FF993C' }}>{v.inStatusNum}</span></span>,
    },
  ],
  [
    {
      title: '',
      render: 'jobName',
    },
    {
      title: '',
      render: v => <span onClick={() => store.toDetailPage({ data: v })} style={{ color: '#0059CE' }}>{v.statusName}<Icon name="arr-right" style={{ marginLeft: 6 }} /></span>,
    },
  ],
  [
    {
      title: '',
      render: v => <span style={{ color: v.isRed ? '#E62E03' : '#666', marginLeft: 7 }}>{handleDatesShow(v.beginTime, v.endTime)}{v.isRed ? <Icon name="hourglass" style={{ marginLeft: 6 }} /> : null}</span>,
    },
  ],
  [
    {
      title: '',
      /* eslint-disable */
      render: v => v.status === 3 || v.status === 4 ? <span style={{ marginLeft :40 }} className={style.spanBtn} onClick={() => store.toAssignmentPage({ data: v })}>{t('分配任务')}</span> : null,
      /* eslint-enable */
    },
    {
      title: '',
      /* eslint-disable */
      render: v => v.status === 3 || v.status === 4 ? <span style={{ marginRight : 30 }} className={style.spanBtn} onClick={() => store.toReturnPage({ data: v })}>{t('退回任务')}</span> : null,
      /* eslint-enable */
    },
  ],
];

class DefaultPage extends React.Component {
  render() {
    const {
      dispatch,
      defaultPageObj,
      defaultPageLoading,
      defaultPageList,
    } = this.props;
    const {
      deptName,
      groupName,
      memberNum,
      taskSetNum,
    } = defaultPageObj;
    return (
      <div>
        <Header title={t('小组任务')} />
        <List
          header={(
            <div style={{ marginLeft: -15 }}>
              <div className={style.topBar}>
                <span>{showTextLimit(deptName)}-{showTextLimit(groupName)}（{memberNum}）</span>
                <span>{t('任务数')}：<span style={{ color: '#197AFA' }}>{taskSetNum}</span></span>
              </div>
              <SplitBar />
              {
                defaultPageLoading !== 0 && defaultPageList.length === 0 ? (
                  <div style={{ textAlign: 'center', fontSize: 26, lineHeight: '50px' }}>{t('当前无小组任务')}</div>
                ) : null
              }
            </div>
          )}
          rows={rows}
          data={defaultPageList}
          style={{ height: 'calc(100vh - 110px)', overflowY: 'auto' }}
          rowStyleOrClass={{ borderBottom: '1px solid #E8EBF0', marginLeft: -7 }}
        />
        <Footer />
      </div>
    );
  }
}

DefaultPage.propTypes = {
  dispatch: PropTypes.func.isRequired,
  defaultPageLoading: PropTypes.number.isRequired,
  defaultPageObj: PropTypes.shape().isRequired,
  defaultPageList: PropTypes.arrayOf(PropTypes.shape).isRequired,
};

export default DefaultPage;
