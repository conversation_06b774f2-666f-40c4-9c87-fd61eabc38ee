import React from 'react';
import PropTypes from 'prop-types';
import Icon from '@shein-components/Icon';
import { t } from '@shein-bbl/react';
import { handleDatesShow } from '../../../../../lib/util';
import style from '../../style.css';

class TopMsg extends React.Component {
  constructor(props) {
    super(props);
    const { defaultFold } = props;
    this.state = {
      isFold: defaultFold,
    };
  }

  render() {
    const { dataSource } = this.props;
    const {
      taskSetCode,
      statusName,
      jobName,
      taskPlaceName,
      beginTime,
      endTime,
      remark,
    } = dataSource;
    const { isFold } = this.state;
    return (
      <div
        style={{ backgroundColor: '#fff' }}
        onClick={() => {
          this.setState({ isFold: !isFold });
        }}
      >
        <div style={{ display: isFold ? 'none' : 'block' }} className={style.topMsgCont}>
          <div><span>{taskSetCode}</span><span style={{ color: '#0059CE' }}>{statusName}</span></div>
          <div><span>{jobName}</span><span>{handleDatesShow(beginTime, endTime)}</span></div>
          <div style={{ color: '#666', wordBreak: 'break-all' }}>
            <span>
              <Icon name="dingwei" style={{ marginRight: 4, color: '#0059ce' }} />
              {taskPlaceName}
            </span>
          </div>
          <div style={{ color: '#666', wordBreak: 'break-all' }}>{t('备注')}: {remark}</div>
        </div>
        <div
          style={{ textAlign: 'center', color: '#0059CE' }}
        >
          <Icon style={{ transform: isFold ? 'rotate(180deg)' : 'rotate(0)' }} name="zhiding" />
        </div>
      </div>
    );
  }
}

TopMsg.defaultProps = {
  dataSource: {}, // 对象
  defaultFold: false, // 是否折叠
};

TopMsg.propTypes = {
  dataSource: PropTypes.shape(),
  defaultFold: PropTypes.bool,
};

export default TopMsg;
