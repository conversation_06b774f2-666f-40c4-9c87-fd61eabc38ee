import React from 'react';
import PropTypes from 'prop-types';
import { Picker } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import {
  Header,
  Footer,
  List,
  Check,
  FooterBtn,
} from '../../../common';
import store from '../reducers';
import TopMsg from './modules/top-msg';
import SplitBar from './modules/split-bar';
import style from '../style.css';

const rows = [
  [
    {
      title: '',
      render: 'taskCode',
    },
    {
      title: '',
      render: 'statusName',
    },
  ],
  [
    {
      title: '',
      render: (v) => v.splicedName || '- -',
    },
  ],
];

class ReturnsPage extends React.Component {
  render() {
    const {
      topMsgObj,
      returnsReasonList,
      returnsPageList,
      returnsPageListAllChecked,
      showReturnsPagePicker,
      taskSetCode,
      groupCode,
      groupName,
    } = this.props;
    // 处理list的选中状态
    const handleListAllChecked = (v) => {
      returnsPageList.forEach((i) => {
        if (!i.disabled) {
          i.checked = v;
        }
      });
      store.changeData({
        data: { returnsPageList: [...returnsPageList] },
      });
    };
    return (
      <div>
        <Header title={t('退回任务')} />
        <List
          header={(
            <div style={{ marginLeft: -15, color: '#141737' }}>
              <TopMsg dataSource={topMsgObj} />
              <SplitBar />
              <div
                className={style.listTitleLine}
                onClick={() => {
                  handleListAllChecked(!returnsPageListAllChecked);
                  store.changeData({
                    data: { returnsPageListAllChecked: !returnsPageListAllChecked },
                  });
                }}
              >
                <span>
                  <Check
                    checked={returnsPageListAllChecked}
                  /> {t('全选')}
                </span>
              </div>
              {
                returnsPageList.length === 0 ? (
                  <div style={{ textAlign: 'center', fontSize: 26, lineHeight: '50px' }}>{t('无可退回任务')}</div>
                ) : null
              }
            </div>
          )}
          rows={rows}
          data={returnsPageList}
          checkbox
          rowStyleOrClass={(item) => {
            if (item.disabled && !item.checked) {
              return style.disabledItem;
            }
            return '';
          }}
          onSelected={(selectedArr) => {
            // eslint-disable-next-line max-len
            const cheked = !!selectedArr.length && selectedArr.length === returnsPageList.filter((i) => !i.disabled || (i.disabled && i.checked)).length;
            store.changeData({
              data: { returnsPageListAllChecked: cheked, returnsPageList: [...returnsPageList] },
            });
          }}
          style={{ height: 'calc(100vh - 110px)', overflowY: 'auto' }}
        />
        <Footer
          beforeBack={() => {
            store.init();
          }}
        >
          <FooterBtn
            disabled={returnsPageList.filter((v) => v.checked).length === 0
            || returnsPageList.some((v) => v.checked && !(v.status === 3 || v.status === 4))}
            onClick={() => {
              store.changeData({ data: { showReturnsPagePicker: true } });
            }}
          >
            {t('退回')}
          </FooterBtn>
        </Footer>
        <Picker
          show={showReturnsPagePicker}
          groups={returnsReasonList}
          onChange={(select) => {
            store.returnTask({
              data: {
                backReasonId: returnsReasonList[0].items[select[0]].id,
                ids: returnsPageList.filter((v) => v.checked).map((v) => v.id),
                taskSetCode,
                groupCode,
                groupName,
                topMsgObj,
              },
            });
          }}
          onCancel={() => store.changeData({ data: { showReturnsPagePicker: false } })}
          lang={{ leftBtn: t('取消'), rightBtn: t('确定') }}
        />
      </div>
    );
  }
}

ReturnsPage.propTypes = {
  topMsgObj: PropTypes.shape().isRequired,
  returnsPageList: PropTypes.arrayOf(PropTypes.shape).isRequired,
  returnsReasonList: PropTypes.arrayOf(PropTypes.shape).isRequired,
  returnsPageListAllChecked: PropTypes.bool.isRequired,
  showReturnsPagePicker: PropTypes.bool.isRequired,
  taskSetCode: PropTypes.string.isRequired,
  groupCode: PropTypes.string.isRequired,
  groupName: PropTypes.string.isRequired,
};

export default ReturnsPage;
