import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { handleDatesShow } from '../../../../lib/util';
import {
  Header,
  Footer,
  List,
  Check,
  FooterBtn,
} from '../../../common';
import Modal from '../../../common/modal';
import TopMsg from './modules/top-msg';
import SplitBar from './modules/split-bar';
import store from '../reducers';
import style from '../style.css';

const rows = [
  [
    {
      title: '',
      render: 'splicedName',
    },
    {
      title: '',
      render: (v) => (v.isGreen ? <span className={style.greenDot}>{null}</span> : null),
    },
  ],
  [
    {
      title: '',
      render: (v) => <span>{handleDatesShow(v.assignStartTime, v.assignEndTime) || '- -'}</span>,
    },
    {
      title: '',
      render: 'jobName',
    },
  ],
];

class AssignmentsPage extends React.Component {
  constructor(props) {
    super(props);
    // eslint-disable-next-line max-len
    const assignableNumVal = props.assignableAllNum - props.assignmentsPageList.filter((r) => r.checked).length;
    this.state = {
      listAllChecked: false, // 全选中
      listData: [...props.assignmentsPageList], // 列表数据
      assignableNum: props.topMsgObj.status === 3 ? assignableNumVal : 0, // 可分配任务数
      // eslint-disable-next-line max-len
      assignableAllNum: props.topMsgObj.status === 3 ? props.assignableAllNum : props.assignmentsPageList.filter((r) => r.checked).length, // 可分配任务数
    };
  }

  render() {
    const {
      topMsgObj,
      groupCode,
      taskSetCode,
      assignmentsPageListOrigin,
      groupName,
    } = this.props;
    const {
      listAllChecked,
      listData,
      assignableNum,
      assignableAllNum,
    } = this.state;
    // 处理list的选中状态
    const handleListAllChecked = (v) => {
      listData.forEach((i) => {
        if (!i.disabled) {
          i.checked = v;
        }
      });
      const assignableNumVal = assignableAllNum - listData.filter((r) => r.checked).length;
      this.setState({ listData: [...listData], assignableNum: assignableNumVal });
    };
    return (
      <div>
        <Header title={t('分配任务')} />
        <List
          header={(
            <div style={{ marginLeft: -15, color: '#141737' }}>
              <TopMsg dataSource={topMsgObj} />
              <SplitBar />
              <div
                className={style.listTitleLine}
                onClick={() => {
                  handleListAllChecked(!listAllChecked);
                  this.setState({ listAllChecked: !listAllChecked });
                }}
              >
                <span>
                  <Check
                    checked={listAllChecked}
                  /> {t('全选')}
                </span>
                <span style={{ color: assignableNum < 0 ? 'red' : '#141638' }}>
                  {t('可分配任务数')}：<span style={{ color: assignableNum < 0 ? 'red' : '#FF993C' }}>{assignableNum}</span>
                </span>
              </div>
            </div>
          )}
          rows={rows}
          data={listData}
          checkbox
          rowStyleOrClass={(item) => {
            if (item.disabled && !item.checked) {
              return style.disabledItem;
            }
            return '';
          }}
          onSelected={(selectedArr) => {
            // eslint-disable-next-line max-len
            const cheked = !!selectedArr.length && selectedArr.length === listData.filter((i) => !i.disabled || (i.disabled && i.checked)).length;
            const assignableNumVal = assignableAllNum - listData.filter((r) => r.checked).length;
            this.setState({ listAllChecked: cheked, assignableNum: assignableNumVal });
          }}
          style={{ height: 'calc(100vh - 110px)', overflowY: 'auto' }}
        />
        <Footer
          beforeBack={() => {
            store.init();
          }}
        >
          <FooterBtn
            onClick={() => {
              if (assignableNum < 0) {
                Modal.error({
                  content: t('勾选人数大于任务数'),
                });
                return;
              }
              store.assignmentTask({
                data: {
                  assignmentsPageListOrigin,
                  listData,
                  taskSetCode,
                  beginTime: topMsgObj.beginTime,
                  endTime: topMsgObj.endTime,
                  groupCode,
                  groupName,
                },
              });
            }}
          >
            {t('确定')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

AssignmentsPage.propTypes = {
  topMsgObj: PropTypes.shape().isRequired,
  assignableAllNum: PropTypes.number.isRequired,
  taskSetCode: PropTypes.string.isRequired,
  groupCode: PropTypes.string.isRequired,
  groupName: PropTypes.string.isRequired,
  assignmentsPageList: PropTypes.arrayOf(PropTypes.shape).isRequired,
  assignmentsPageListOrigin: PropTypes.arrayOf(PropTypes.shape).isRequired,
};

export default AssignmentsPage;
