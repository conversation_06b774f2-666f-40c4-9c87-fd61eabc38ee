import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Button } from 'react-weui/build/packages';
import moment from 'moment';
import {
  Header,
  Footer,
  List,
  Check,
  TimePicker,
} from '../../../common';
import Modal from '../../../common/modal';
import store from '../reducers';
import TopMsg from './modules/top-msg';
import SplitBar from './modules/split-bar';
import style from '../style.css';
import Popo from '../../../common/popo-picker';

const rows = [
  [
    {
      title: '',
      render: 'taskCode',
    },
    {
      title: '',
      render: 'statusName',
    },
  ],
  [
    {
      title: '',
      render: (v) => v.splicedName || '- -',
    },
  ],
];

// 判断开始时间和结束时间是否同一天，并返回对应日期数组
const getDatesArr = (beginTime, endTime) => {
  // 若传入日期值无效，则返回当天日期
  if (!beginTime.split || !endTime.split) {
    return [new Date().toLocaleDateString()];
  }
  // 判断是否同一天
  const beginTimeDate = beginTime.split(' ')[0];
  const endTimeDate = endTime.split(' ')[0];
  if (beginTimeDate === endTimeDate) {
    return [beginTimeDate.replace(/-/g, '/')];
  } else {
    return [beginTimeDate.replace(/-/g, '/'), endTimeDate.replace(/-/g, '/')];
  }
};

class DetailPage extends React.Component {
  render() {
    const {
      topMsgObj,
      detailPageListAllChecked,
      detailPageList,
      startTaskShowPicker,
      endTaskShowPicker,
      taskSetCode,
      groupCode,
      groupName,
      handleTaskLoading,
      startTaskPickerDateSelected,
      endTaskPickerDateSelected,
    } = this.props;
    const {
      beginTime,
      endTime,
    } = topMsgObj;
    // 处理list的选中状态
    const handleListAllChecked = (v) => {
      detailPageList.forEach((i) => {
        if (!i.disabled) {
          i.checked = v;
        }
      });
      store.changeData({ data: { detailPageList: [...detailPageList] } });
    };
    const startTaskDatesArr = getDatesArr(beginTime, endTime);
    const endTaskDatesArr = getDatesArr(beginTime, endTime);

    const startTaskPickerDateSelectedNew = { value: moment().format('YYYY/MM/DD HH:mm').match(/\d+/g), display: moment().format('YYYY/MM/DD HH:mm') };
    const endTaskPickerDateSelectedNew = { value: moment().format('YYYY/MM/DD HH:mm').match(/\d+/g), display: moment().format('YYYY/MM/DD HH:mm') };
    return (
      <div>
        <Header title={t('任务详情')} />
        <List
          header={(
            <div style={{ marginLeft: -15, color: '#141737' }}>
              <TopMsg dataSource={topMsgObj} />
              <SplitBar style={{ padding: '8px 0' }}>
                <div className={style.spanBtnWrap}>
                  <Button
                    className="dp-start-time"
                    disabled={handleTaskLoading === 0
                    || !(detailPageList.filter((v) => v.checked).length
                    && detailPageList.filter((v) => v.checked).every((v) => v.status === 4))}
                    onClick={() => {
                      // store.changeData({
                      //   data: {
                      //     startTaskShowPicker: true,
                      //     startTaskPickerDateSelected: [
                      //       new Date().getHours(),
                      //       new Date().getMinutes(),
                      //     ],
                      //   },
                      // })
                      Popo.picker({
                        el: '.dp-start-time',
                        infinite: true,
                        wheels: [
                          {
                            infinite: false,
                            data: startTaskDatesArr.map((v) => ({
                              value: v,
                              display: v,
                            })),
                          },
                          {
                            selected: new Date().getHours(),
                            infinite: true,
                            data: new Array(24).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                          },
                          {
                            selected: new Date().getMinutes(),
                            infinite: true,
                            data: new Array(60).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                          },
                        ],
                        save: (rps, b) => {
                          // 原来的业务逻辑拷贝
                          const timeArr = rps.result.map((v) => (v.value));
                          // eslint-disable-next-line max-len
                          const time = `${timeArr[0].replace(/\//g, '-')} ${timeArr[1]}:${timeArr[2]}:00`;
                          if (new Date(time).getTime() < new Date(beginTime).getTime() || new Date(time).getTime() > new Date(endTime).getTime()) {
                            Modal.error({
                              content: t('提交时间必须在计划时间之内'),
                            });
                            return;
                          }
                          store.handleTask({
                            data: {
                              time,
                              operationType: 1,
                              ids: detailPageList.filter((v) => v.checked).map((v) => v.id),
                              taskSetCode,
                              groupCode,
                              groupName,
                              topMsgObj,
                            },
                          });
                          store.changeData({
                            data: {
                              startTaskPickerDateSelectedNew: {
                                value: '', display: '',
                              },
                            },
                          });
                        },
                      });
                    }}
                    size="small"
                  >
                    {t('开始任务')}
                  </Button>
                  <Button
                    disabled={handleTaskLoading === 0
                    || !(detailPageList.filter((v) => v.checked).length
                    && (detailPageList.filter((v) => v.checked).every((v) => v.status === 5)
                    || detailPageList.filter((v) => v.checked).every((v) => v.status === 6)))}
                    onClick={() => {
                      // eslint-disable-next-line max-len
                      const operationType = detailPageList.filter((v) => v.checked).every((v) => v.status === 5) ? 2 : 3;
                      store.handleTask({
                        data: {
                          operationType,
                          ids: detailPageList.filter((v) => v.checked).map((v) => v.id),
                          taskSetCode,
                          groupCode,
                          groupName,
                          topMsgObj,
                        },
                      });
                    }}
                    size="small"
                  >
                    {t('暂停/恢复')}
                  </Button>
                  <Button
                    className="dp-end-time"
                    disabled={handleTaskLoading === 0
                    || !(detailPageList.filter((v) => v.checked).length
                    && detailPageList.filter((v) => v.checked).every((v) => v.status === 5))}
                    onClick={() => {
                      // store.changeData({
                      //   data: {
                      //     endTaskShowPicker: true,
                      //     endTaskPickerDateSelected: [
                      //       new Date().getHours(),
                      //       new Date().getMinutes(),
                      //     ],
                      //   },
                      // })
                      Popo.picker({
                        el: '.dp-end-time',
                        wheels: [
                          {
                            infinite: false,
                            data: endTaskDatesArr.map((v) => ({
                              value: v,
                              display: v,
                            })),
                          },
                          {
                            selected: new Date().getHours(),
                            infinite: true,
                            data: new Array(24).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                          },
                          {
                            selected: new Date().getMinutes(),
                            infinite: true,
                            data: new Array(60).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                          },
                        ],
                        save: (rps, b) => {
                          // 原来的业务逻辑拷贝
                          const timeArr = rps.result.map((v) => (v.value));
                          const time = `${timeArr[0].replace(/\//g, '-')} ${timeArr[1]}:${timeArr[2]}:00`;
                          store.changeData({
                            data: {
                              endTaskPickerDateSelectedNew: {
                                value: '', display: '',
                              },
                            },
                          });
                          // eslint-disable-next-line max-len
                          if (new Date(time).getTime() < new Date(beginTime).getTime() || new Date(time).getTime() > new Date(endTime).getTime()) {
                            Modal.error({
                              content: t('提交时间必须在计划时间之内'),
                            });
                            return;
                          }
                          store.handleTask({
                            data: {
                              time,
                              operationType: 4,
                              ids: detailPageList.filter((v) => v.checked).map((v) => v.id),
                              taskSetCode,
                              groupCode,
                              groupName,
                              topMsgObj,
                            },
                          });
                        },
                      });
                    }}
                    size="small"
                  >
                    {t('结束任务')}
                  </Button>
                </div>
              </SplitBar>
              <div
                className={style.listTitle}
              >
                <span
                  className={style.listTitleAllChecked}
                  onClick={() => {
                    handleListAllChecked(!detailPageListAllChecked);
                    store.changeData({
                      data: { detailPageListAllChecked: !detailPageListAllChecked },
                    });
                  }}
                >
                  <Check
                    checked={detailPageListAllChecked}
                  /> {t('全选')}
                </span>
              </div>
            </div>
          )}
          rows={rows}
          data={detailPageList}
          checkbox
          rowStyleOrClass={(item) => {
            if (item.disabled && !item.checked) {
              return style.disabledItem;
            }
            return '';
          }}
          onSelected={(selectedArr) => {
            // eslint-disable-next-line max-len
            const cheked = !!selectedArr.length && selectedArr.length === detailPageList.filter((i) => !i.disabled || (i.disabled && i.checked)).length;
            store.changeData(
              {
                data: { detailPageListAllChecked: cheked, detailPageList: [...detailPageList] },
              },
            );
          }}
          style={{ height: 'calc(100vh - 110px)', overflowY: 'auto' }}
        />
        <Footer
          beforeBack={() => {
            store.init();
          }}
        />
        <TimePicker
          infinite
          show={startTaskShowPicker}
          dateList={startTaskDatesArr}
          selected={startTaskPickerDateSelected}
          onCancel={() => store.changeData({ data: { startTaskShowPicker: false } })}
          onSelected={(timeArr) => {
            const time = `${startTaskDatesArr[timeArr[0]].replace(/\//g, '-')} ${timeArr[1]}:${timeArr[2]}:00`;
            // eslint-disable-next-line max-len
            if (new Date(time).getTime() < new Date(beginTime).getTime() || new Date(time).getTime() > new Date(endTime).getTime()) {
              store.changeData({ data: { startTaskShowPicker: false } });
              Modal.error({
                content: t('提交时间必须在计划时间之内'),
              });
              return;
            }
            store.handleTask({
              data: {
                time,
                operationType: 1,
                ids: detailPageList.filter((v) => v.checked).map((v) => v.id),
                taskSetCode,
                groupCode,
                groupName,
                topMsgObj,
              },
            });
          }}
        />
        <TimePicker
          infinite
          show={endTaskShowPicker}
          dateList={endTaskDatesArr}
          selected={endTaskPickerDateSelected}
          onCancel={() => store.changeData({ data: { endTaskShowPicker: false } })}
          onSelected={(timeArr) => {
            const time = `${endTaskDatesArr[timeArr[0]].replace(/\//g, '-')} ${timeArr[1]}:${timeArr[2]}:00`;
            // eslint-disable-next-line max-len
            if (new Date(time).getTime() < new Date(beginTime).getTime() || new Date(time).getTime() > new Date(endTime).getTime()) {
              store.changeData({ data: { endTaskShowPicker: false } });
              Modal.error({
                content: t('提交时间必须在计划时间之内'),
              });
              return;
            }
            store.handleTask({
              data: {
                time,
                operationType: 4,
                ids: detailPageList.filter((v) => v.checked).map((v) => v.id),
                taskSetCode,
                groupCode,
                groupName,
                topMsgObj,
              },
            });
          }}
        />
      </div>
    );
  }
}

DetailPage.propTypes = {
  topMsgObj: PropTypes.shape().isRequired,
  detailPageList: PropTypes.arrayOf(PropTypes.shape).isRequired,
  detailPageListAllChecked: PropTypes.bool.isRequired,
  startTaskShowPicker: PropTypes.bool.isRequired,
  endTaskShowPicker: PropTypes.bool.isRequired,
  taskSetCode: PropTypes.string.isRequired,
  groupCode: PropTypes.string.isRequired,
  groupName: PropTypes.string.isRequired,
  handleTaskLoading: PropTypes.number.isRequired,
  startTaskPickerDateSelected: PropTypes.arrayOf(PropTypes.number).isRequired,
  endTaskPickerDateSelected: PropTypes.arrayOf(PropTypes.number).isRequired,
};

export default DetailPage;
