import { sendPostRequest } from '../../../lib/public-request';

// sendPostRequestApi: 统一修改接口url前缀
const baseUrl = `${process.env.WMS_INTERNAL}/pda/task/`;
const sendPostRequestApi = argObj => sendPostRequest(argObj, baseUrl);

// 首页小组任务列表查询
export const groupTaskQueryApi = param => sendPostRequestApi({
  url: 'group_task_query',
  param,
});

// 分配任务列表查询
export const assignTaskQueryApi = param => sendPostRequestApi({
  url: 'assign_task_query',
  param,
});

// 分配确定
export const assignSureApi = param => sendPostRequestApi({
  url: 'assign_sure',
  param,
});

// 退回任务/任务详情列表查询
export const taskDetailQueryApi = param => sendPostRequestApi({
  url: 'task_detail_query',
  param,
});

// 退回
export const taskReturnApi = param => sendPostRequestApi({
  url: 'task_return',
  param,
});

// 开始/暂停/恢复/结束
export const taskOperationApi = param => sendPostRequestApi({
  url: 'task_operation',
  param,
});

// 获取原组长管理小组
export const getAgentManagerGroupApi = param => sendPostRequest({
  url: '/pda/staff/get_agent_manager_group',
  param,
}, process.env.WMS_INTERNAL_FRONT);
