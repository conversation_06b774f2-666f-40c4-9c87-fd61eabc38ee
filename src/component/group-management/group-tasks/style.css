.topMsgCont {
  padding: 8px 12px;
}
.topMsgCont>div {
  display: flex;
  justify-content: space-between;
}
.topBar {
  display: flex;
  background: #fff;
  justify-content: space-between;
  padding: 8px;
  color: #141737;
}
.topBar >span:first-of-type {
  flex: 1;
  word-break: break-all;
}
.topBar >span:last-of-type {
  width: 80px;
  text-align: right;
}
.spanBtn {
  color: #0059ce;
  padding: 2px 4px;
  border-radius:2px;
  border:1px solid #0059CE;
}
.greenDot {
  display: inline-block;
  width: 6px;
  border-radius: 50%;
  background: #35ad69;
  height: 6px;
  margin-right: 4px;
}
.disabledItem {
  opacity: 0.9;
  background:rgba(245,245,245,1);
}
.listTitleLine {
  color: #666c7c;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  padding: 8px 10px;
}
.listTitle {
  color: #666c7c;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
}
.listTitle .listTitleAllChecked {
  width: 30%;
  padding: 8px 10px;
}
.spanBtnWrap {
  margin-top: -10px;
  padding-bottom: 6px;
}
.spanBtnWrap>button {
  margin-left: 12px;
}
