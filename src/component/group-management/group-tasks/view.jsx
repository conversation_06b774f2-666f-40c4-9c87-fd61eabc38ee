import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import DefaultPage from './jsx/default-page';
import DetailPage from './jsx/detail-page';
import AssignmentsPage from './jsx/assignments-page';
import ReturnsPage from './jsx/returns-page';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      pageType,
    } = this.props;

    // 1-小组任务首页，2-分配任务，3-退回任务，4-任务详情
    switch (pageType) {
      case 1:
        return (<DefaultPage {...this.props} />);
      case 2:
        return (<AssignmentsPage {...this.props} />);
      case 3:
        return (<ReturnsPage {...this.props} />);
      case 4:
        return (<DetailPage {...this.props} />);
      default:
        return (
          <div>{t('小组任务-loading')}</div>
        );
    }
  }
}

Container.propTypes = {
  pageType: PropTypes.number,
};

export default i18n(Container);
