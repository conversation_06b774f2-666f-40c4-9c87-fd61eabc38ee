import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { t } from '@shein-bbl/react';
import Modal from '../../common/modal';
import { message } from '../../common';
import { selectDict } from '../../../server/basic/data-dictionary';
import {
  groupTaskQueryApi,
  assignTaskQueryApi,
  assignSureApi,
  taskDetailQueryApi,
  taskReturnApi,
  taskOperationApi,
  getAgentManagerGroupApi,
} from './server';


const defaultState = {
  pageType: 1, // 1-小组任务首页，2-分配任务，3-退回任务，4-任务详情
  groupCode: '', // 小组编码
  groupName: '', // 小组名字
  defaultPageLoading: 1, // 0加载中 1加载成功 2加载失败【初始页面请求】
  handleTaskLoading: 1, // 0加载中 1加载成功 2加载失败【开始、暂停/恢复、结束任务】
  defaultPageObj: {}, // 首页基本信息
  defaultPageList: [], // 首页列表信息
  topMsgObj: {}, // 其它页面顶部信息
  assignmentsPageList: [], // 分配页列表信息
  assignmentsPageListOrigin: [], // 初始列表数据，用于确定时对比哪些数据更改过
  assignableAllNum: 0, // 可分配任务总数
  taskSetCode: '', // 任务集号
  returnsPageList: [], // 回退页列表信息
  returnsPageListAllChecked: false, // 回退页列表是否全选中
  showReturnsPagePicker: false, // 是否展示退回原因Picker
  returnsReasonList: [], // 退回原因Picker列表数据
  detailPageList: [], // 任务详情页列表信息
  detailPageListAllChecked: false, // 任务详情页列表是否全选中
  startTaskShowPicker: false, // 是否显示开始任务TimePicker
  endTaskShowPicker: false, // 是否显示结束任务TimePicker
  startTaskPickerDateSelected: [new Date().getHours(), new Date().getMinutes()], // Picker默认当前时分
  endTaskPickerDateSelected: [new Date().getHours(), new Date().getMinutes()], // Picker默认当前时分
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    // 获取原组长管理小组
    const resObj = yield getAgentManagerGroupApi();
    if (resObj.code === '0') {
      // 请求首页初始化数据
      markStatus('defaultPageLoading');
      const res = yield groupTaskQueryApi({ warehouseId: 1, groupId: resObj.info.groupId });
      if (res.code === '0') {
        const {
          taskSetInfoList,
          groupCode,
          groupId,
          ...defaultPageObj
        } = res.info;
        yield ctx.changeData({
          data: {
            defaultPageObj,
            groupCode,
            groupName: defaultPageObj.groupName,
            defaultPageList: (taskSetInfoList || []).map(v => ({
              ...v,
              isRed: v.isRed === 1,
              groupCode,
              groupId,
              groupName: defaultPageObj.groupName,
            })),
          },
        });
      } else {
        Modal.error({ content: res.msg });
      }
    } else {
      Modal.error({ title: resObj.msg });
    }
  },
  // 跳转到任务详情页
  * toDetailPage(action, ctx) {
    const { taskSetCode, groupCode } = action.data;
    // 请求任务详情页初始化数据
    const res = yield taskDetailQueryApi({ taskSetCode, groupCode, isReturnQuery: 2 });
    if (res.code === '0') {
      const detailPageList = ((res.info || {}).data || [])
        .map(v => ({ ...v, disabled: v.status === 7 }));
      yield ctx.changeData({
        data: {
          pageType: 4,
          taskSetCode,
          detailPageList,
          topMsgObj: { ...action.data },
        },
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 跳转到分配任务页
  * toAssignmentPage(action, ctx) {
    const {
      taskSetCode,
      groupCode,
      groupId,
      beginTime,
      endTime,
      inStatusNum,
    } = action.data;
    // 请求分配页初始化数据
    const res = yield assignTaskQueryApi({
      taskSetCode,
      groupCode,
      groupId,
      beginTime,
      endTime,
    });
    if (res.code === '0') {
      const listData = ((res.info || {}).data || []).map(v => ({
        ...v,
        isGreen: v.isGreen === 1,
        checked: v.assignStatus === 2 || v.assignStatus === 3,
        disabled: v.assignStatus === 3 || v.assignStatus === 4,
      }));
      const assignableAllNum = inStatusNum + listData.filter(v => v.checked).length;
      yield ctx.changeData({
        data: {
          pageType: 2,
          groupCode,
          assignableAllNum,
          taskSetCode,
          assignmentsPageList: [...listData],
          assignmentsPageListOrigin: JSON.parse(JSON.stringify(listData.filter(v => !v.disabled))),
          topMsgObj: { ...action.data },
        },
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 跳转到退回任务页
  * toReturnPage(action, ctx) {
    const { taskSetCode, groupCode } = action.data;
    // 请求退回页初始化数据和退回原因下拉数据
    const [selectData, res] = yield Promise.all([
      selectDict({ catCode: ['TASK_RETURN_REASON'] }),
      taskDetailQueryApi({ taskSetCode, groupCode, isReturnQuery: 1 }),
    ]);
    if (selectData.code === '0' && res.code === '0') {
      yield ctx.changeData({
        data: {
          taskSetCode,
          pageType: 3,
          // eslint-disable-next-line max-len
          returnsPageList: ((res.info || {}).data || []).map(v => ({ ...v, disabled: !(v.status === 3 || v.status === 4) })),
          topMsgObj: { ...action.data },
          returnsReasonList: [{
            // eslint-disable-next-line max-len
            items: (selectData.info.data[0].dictListRsps || []).map(v => ({ label: v.dictNameZh, id: v.dictCode })),
          }],
        },
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 分配任务
  * assignmentTask(action, ctx) {
    const {
      assignmentsPageListOrigin,
      listData,
      taskSetCode,
      beginTime,
      endTime,
      groupCode,
      groupName,
    } = action.data;
    const userNameList = []; // 用于获取更改过勾选状态的数组
    assignmentsPageListOrigin.forEach((obj, idx) => {
      if (obj.checked !== listData[idx].checked) {
        userNameList.push(obj.userName);
      }
    });
    // 若没更改直接返回上个页面，并提示分配成功
    if (!userNameList.length) {
      message.success(t('分配任务成功！'));
      yield ctx.init();
      return;
    }
    const res = yield assignSureApi({
      taskSetCode,
      userNameList,
      beginTime,
      endTime,
      groupCode,
      groupName,
    });
    if (res.code === '0') {
      message.success(t('分配任务成功！'));
      yield ctx.init();
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 退回任务
  * returnTask(action, ctx) {
    const {
      taskSetCode,
      groupCode,
    } = action.data;
    const { topMsgObj, ...returnTaskParam } = action.data;
    const res = yield taskReturnApi(returnTaskParam);
    if (res.code === '0') {
      yield ctx.changeData({
        data: {
          showReturnsPagePicker: false,
        },
      });
      message.success(t('退回任务成功！'));
      // 修改上边状态
      if (res.info) {
        topMsgObj.statusName = res.info;
      }
      // 刷新列表数据
      const resData = yield taskDetailQueryApi(
        {
          taskSetCode,
          groupCode,
          isReturnQuery: 1,
        },
      );
      if (resData.code === '0') {
        yield ctx.changeData({
          data: {
            topMsgObj: { ...topMsgObj },
            returnsPageListAllChecked: false,
            // eslint-disable-next-line max-len
            returnsPageList: ((resData.info || {}).data || []).map(v => ({ ...v, disabled: !(v.status === 3 || v.status === 4) })),
          },
        });
      } else {
        Modal.error({ content: resData.msg });
      }
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 开始，暂停或恢复，结束任务
  * handleTask(action, ctx) {
    markStatus('handleTaskLoading');
    if (action.data.operationType === 1) {
      yield ctx.changeData({ data: { startTaskShowPicker: false } });
    } else if (action.data.operationType === 4) {
      yield ctx.changeData({ data: { endTaskShowPicker: false } });
    }
    const {
      taskSetCode,
      groupCode,
    } = action.data;
    const { topMsgObj, ...handleTaskParam } = action.data;
    const res = yield taskOperationApi(handleTaskParam);
    if (res.code === '0') {
      message.success(t('操作成功！'), 1000);
      // 修改上边状态
      if (res.info) {
        topMsgObj.statusName = res.info;
      }
      // 刷新列表数据
      markStatus('handleTaskLoading');
      const resData = yield taskDetailQueryApi(
        {
          taskSetCode,
          groupCode,
          isReturnQuery: 2,
        },
      );
      if (resData.code === '0') {
        const detailPageList = ((resData.info || {}).data || [])
          .map(v => ({ ...v, disabled: v.status === 7 }));
        yield ctx.changeData({
          data: {
            topMsgObj: { ...topMsgObj },
            detailPageListAllChecked: false,
            detailPageList,
          },
        });
      } else {
        Modal.error({ content: resData.msg });
      }
    } else {
      Modal.error({ content: res.msg });
    }
  },
};
