import assign from 'object-assign';
import { select } from 'redux-saga/effects';
import { getBtnList } from '../../../lib/util';
import { getRedDottedListApi } from './server';

const defaultState = {
  menus: [],
  btnList: [],
  headTitle: '',
  isTeamLeader: true, // 判断是否为组长
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData: (draft, action) => {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    const nav = yield select(state => state.nav);
    if (nav) {
      let btnList = [];
      const { menus } = nav;
      try {
        const collection = menus.children
          .find(v => v.rule === '/new-pda/group-management');
        yield ctx.changeData({ data: { headTitle: collection.title } });
        btnList = getBtnList(collection);
      } catch (e) {
        btnList = [];
      }
      // 请求判断是否红点接口
      const res = yield getRedDottedListApi({ warehouseId: 1 });
      if (res.code === '0') {
        const redDottedList = res.info.data || [];
        btnList.forEach((v) => {
          v.showRedDotted = redDottedList.includes(v.href.replace('#', '/new-pda'));
        });
        yield ctx.changeData({ data: { menus, btnList, isTeamLeader: true } });
      } else {
        yield ctx.changeData({ data: { menus, btnList, isTeamLeader: false } });
      }
    }
  },
};
