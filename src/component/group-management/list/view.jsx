import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import CellsMenu from '../../common/cells-menu';
import Header from '../../common/header';
import Footer from '../../common/footer';

class Container extends Component {
  componentWillMount() {
    store.init();
  }

  render() {
    const {
      dispatch,
      btnList,
      headTitle,
      isTeamLeader,
    } = this.props;
    return (
      <div>
        <Header title={headTitle} />
        {
          isTeamLeader ? (
            <CellsMenu
              cells={btnList}
            />
          ) : <div style={{ textAlign: 'center', fontSize: 20, lineHeight: '60px' }}>{t('无管理小组')}</div>
        }
        <Footer dispatch={dispatch} />
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func,
  btnList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  headTitle: PropTypes.string.isRequired,
  isTeamLeader: PropTypes.bool.isRequired,
};

export default i18n(Container);
