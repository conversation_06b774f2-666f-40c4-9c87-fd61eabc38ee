import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { getHeaderTitle } from '../../../lib/util';
import { endingPickingApi, getGroupTaskInfoApi, getAgentManagerGroupApi } from './server';
import Modal from '../../common/modal';

const defaultState = {
  listAllChecked: false, // 是否全选
  list: [],
  deptId: '',
  groupId: '',
  deptName: '',
  groupName: '',
  groupUserNum: 0,
  checkedNum: 0, // 选中的数量
};

/**
 * 将有数据的置顶
 * @param list
 * @returns {Array}
 */
const getDataFormat = (list) => {
  if (!list || !list.length) {
    return [];
  }
  const res = [];

  for (let i = 0; i < list.length; i++) {
    const item = {
      ...list[i],
      disabled: !list[i].taskId,
    };
    if (item.taskId) {
      res.unshift(item);
    } else {
      res.push(item);
    }
  }
  return res;
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    // 获取原组长管理小组
    const res = yield getAgentManagerGroupApi();
    if (res.code === '0') {
      const {
        groupId,
      } = res.info;
      // 获取任务数据
      yield ctx.getGroupTaskInfo({ data: { groupId } });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  /**
   * 获取组任务数据
   * @param action
   * @param ctx
   * @returns {IterableIterator<*>}
   */
  * getGroupTaskInfo(action, ctx) {
    const res = yield getGroupTaskInfoApi({ groupId: action.data.groupId });
    if (res.code === '0') {
      const {
        detail,
        groupId,
        groupName,
        deptName,
        groupUserNum,
        deptId,
      } = res.info;
      yield ctx.changeData({
        data: {
          list: getDataFormat(detail),
          groupId,
          deptName,
          groupName,
          groupUserNum,
          deptId,
        },
      });
    } else {
      Modal.error({ title: res.msg });
    }
  },
  /**
   * 结束拣货
   * @param action
   * @param ctx
   * @returns {IterableIterator<Promise<any>|*|IterableIterator<*|*>|*|IterableIterator<*|*|*|*|*>>}
   */
  * endingPicking(action, ctx) {
    const { params } = action;
    const res = yield endingPickingApi(params);
    if (res.code === '0') {
      if (params.isCheck) {
        // 校验数据
        const status = yield new Promise((r) => Modal.confirm({
          content: t('是否确认结束勾选组员的拣货任务?'),
          onOk: () => r('ok'),
          onCancel: () => r('cancel'),
        }));
        if (status === 'ok') {
          yield ctx.endingPicking({
            params: {
              ...params,
              isCheck: false,
            },
          });
        }
      } else {
        const { showTip, tip } = res.info;
        if (showTip === 1) {
          const status = yield new Promise((r) => Modal.info({
            content: tip,
            onOk: () => r('ok'),
          }));
          if (status === 'ok') {
            // 刷新一下数据
            yield ctx.init();
          }
        } else {
          // 结束任务成功
          const status = yield new Promise((r) => Modal.info({
            content: t('操作成功'),
            onOk: () => r('ok'),
          }));
          if (status === 'ok') {
            yield ctx.getGroupTaskInfo({
              data: {
                groupId: '',
              },
            });
          }
        }
      }
    } else {
      Modal.error({ title: res.msg });
    }
  },
};
