import { sendPostRequest } from '../../../lib/public-request';

// 结束拣货
export const endingPickingApi = param => sendPostRequest({
  url: '/pda/ending_picking',
  param,
}, process.env.WOS_URI);

// 获取拣货组信息
export const getGroupTaskInfoApi = param => sendPostRequest({
  url: '/pda/query_group_task_info',
  param,
}, process.env.WOS_URI);

// 获取原组长管理小组
export const getAgentManagerGroupApi = param => sendPostRequest({
  url: '/pda/staff/get_agent_manager_group',
  param,
}, process.env.WMS_INTERNAL_FRONT);
