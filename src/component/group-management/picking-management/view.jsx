import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import {
  Check, Footer, FooterBtn,
  Header,
  List,
} from '../../common';
import styles from '../../example/style.css';
import SplitBar from '../group-tasks/jsx/modules/split-bar';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      listAllChecked,
      list,
      groupId,
      deptName,
      groupName,
      groupUserNum,
      deptId,
      checkedNum,
    } = this.props;

    const rows = [
      [
        {
          title: '',
          render: 'pickUserName',
        },
        {
          title: '',
          render: (r) => {
            if (r.taskId) {
              return `${r.pickedNum}/${r.totalNum}`;
            }
            return '';
          },
        },
      ],
      [
        {
          title: '',
          render: (r) => r.taskCode || '--',
        },
      ],
    ];

    const groupNameStyle = {
      fontSize: 16,
      fontWeight: 'blod',
      padding: '5px 10px',
      backgroundColor: '#fff',
    };

    const overflowStyle = {
      height: window.innerHeight - 170,
      overflow: 'auto',
    };

    const selectAllStyle = {
      padding: '6px 9px',
      fontSize: 13,
      color: '#666',
    };

    return (
      <div>
        <Header
          title={headerTitle || ''}
        />
        <div>
          {deptName && (
            <div style={groupNameStyle}>
              {deptName}-{groupName}({groupUserNum})
            </div>
          )}
          <SplitBar style={{ padding: '2px 0' }}>
            <div style={selectAllStyle}>
              <span
                onClick={() => {
                  const newList = list.map((i) => {
                    if (!i.disabled) {
                      return {
                        ...i,
                        checked: !listAllChecked,
                      };
                    }
                    return i;
                  });
                  store.changeData({
                    data: {
                      list: newList,
                      listAllChecked: !listAllChecked,
                      checkedNum: newList.filter((i) => i.checked).length,
                    },
                  });
                }}
              >
                <Check
                  checked={listAllChecked}
                  disabled={list.every((i) => i.disabled)}
                />
                <span style={{ marginLeft: 10 }}>{t('全选')}（{checkedNum}）</span>
              </span>
            </div>
          </SplitBar>
          <List
            rows={rows}
            data={list}
            checkbox
            rowStyleOrClass={(item) => {
              if (item.disabled && !item.checked) {
                return styles.disabledItem;
              }
              return '';
            }}
            onSelected={(selectedArr) => {
              store.changeData({
                data: {
                  listAllChecked: selectedArr.length === list.filter((i) => !i.disabled).length,
                  checkedNum: selectedArr.length,
                },
              });
            }}
            style={overflowStyle}
          />
        </div>
        <Footer>
          <FooterBtn
            disabled={!checkedNum}
            onClick={() => {
              const checkedList = list.filter((i) => i.checked);
              store.endingPicking({
                params: {
                  deptId,
                  groupId,
                  isCheck: true,
                  userList: checkedList.map((i) => ({
                    userId: i.pickUserId,
                    userName: i.pickUserNameCn,
                    taskCode: i.taskCode,
                  })),
                },
              });
            }}
          >
            {t('结束拣货')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  listAllChecked: PropTypes.bool,
  list: PropTypes.arrayOf(PropTypes.shape()),
  groupId: PropTypes.number,
  deptName: PropTypes.string,
  groupName: PropTypes.string,
  groupUserNum: PropTypes.number,
  deptId: PropTypes.number,
  checkedNum: PropTypes.number,
};

const mapStateToProps = (state) => state['group-management/picking-management'];
export default connect(mapStateToProps)(i18n(Container));
