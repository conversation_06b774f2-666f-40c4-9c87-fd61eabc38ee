import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import {
  <PERSON>er,
  Footer,
  Check,
  FooterBtn,
  Pickers,
  TimePicker,
  Tag,
  FocusInput,
  Popo,
} from '../../../common';
import Modal from '../../../common/modal';
import SplitBar from '../../group-tasks/jsx/modules/split-bar';
import store from '../reducers';
import style from '../style.css';

class ArrangeModifyPage extends React.Component {
  constructor(props) {
    super(props);
    const {
      assignStartTime,
      assignEndTime,
      isOnline,
      jobInitObj,
      jobTypeListStr,
    } = props;
    let startTimeSelected = [new Date().getHours(), new Date().getMinutes()];
    let endTimeSelected = [new Date().getHours(), new Date().getMinutes()];
    if (assignStartTime) {
      const arr = assignStartTime.split(' ')[1].split(':');
      startTimeSelected = [arr[0] * 1, arr[1] * 1];
    }
    if (assignEndTime) {
      const arr = assignEndTime.split(' ')[1].split(':');
      endTimeSelected = [arr[0] * 1, arr[1] * 1];
    }

    /* ---- new -----*/
    let startTimeSelectedNew = { value: moment().format('YYYY-MM-DD HH:mm').match(/\d+/g), display: moment().format('YYYY-MM-DD HH:mm') };
    let endTimeSelectedNew = { value: moment().format('YYYY-MM-DD HH:mm').match(/\d+/g), display: moment().format('YYYY-MM-DD HH:mm') };
    if (assignStartTime) {
      startTimeSelectedNew = { value: assignStartTime.match(/\d+/g), display: assignStartTime };
    } else {
      startTimeSelectedNew = { value: '', display: '' };
    }

    if (assignEndTime) {
      endTimeSelectedNew = { value: assignEndTime.match(/\d+/g), display: assignEndTime };
    } else {
      endTimeSelectedNew = { value: '', display: '' };
    }

    // 设置手风琴默认勾选数据
    const jobTypeList = JSON.parse(jobTypeListStr);
    jobTypeList.forEach((v) => {
      if (jobInitObj.jobTypeCode === v.jobTypeCode) {
        v.children.forEach((i) => {
          if (jobInitObj.jobCode === i.jobCode) {
            i.isChecked = true;
          }
        });
      }
    });

    this.state = {
      isDuty: isOnline,
      showDutyPicker: false,
      dutyVal: isOnline ? jobInitObj.jobName : '',
      showReasonPicker: false,
      reasonVal: !isOnline ? jobInitObj.jobName : '',
      startTimeText: assignStartTime,
      endTimeText: assignEndTime,
      startTimeSelected,
      endTimeSelected,
      startTimeSelectedNew,
      endTimeSelectedNew,
      startShowPicker: false,
      endShowPicker: false,
      jobTypeList, // 在岗手风琴初始数据
      accordionArr: [], // 手风琴当前选中的数组
    };
  }

  render() {
    const {
      tags,
      jobInitObj,
      noJobNameList,
      date,
      dateList2,
      dateIdx,
      arrangeModifyLoading,
      arrangeModifyList,
      noWorkJobTypeCodeObj,
    } = this.props;
    const {
      isDuty,
      showReasonPicker,
      reasonVal,
      startTimeText,
      endTimeText,
      startTimeSelected,
      endTimeSelected,
      startTimeSelectedNew,
      endTimeSelectedNew,
      startShowPicker,
      endShowPicker,
    } = this.state;

    return (
      <div>
        <Header title={t('安排修改')} />
        <div style={{ backgroundColor: '#fff' }}>
          <SplitBar style={{ paddingLeft: 15 }}>{`${date} ${moment().format('HH:mm')}`}</SplitBar>
          <div className={style.checkedWrap}>
            <span
              onClick={() => {
                this.setState({
                  startTimeSelectedNew: {
                    value: [],
                    display: '',
                  },
                  startTimeText: '',
                });
                this.setState({
                  endTimeSelectedNew: {
                    value: [],
                    display: '',
                  },
                  endTimeText: '',
                });
                this.setState({ isDuty: true });
              }}
            >
              <Check checked={isDuty} />
              {t('在岗')}
            </span>
            <span
              onClick={() => {
                const dateDisplay = moment(new Date(), 'YYYY-MM-DD').format('YYYY-MM-DD');
                this.setState({
                  startTimeSelectedNew: {
                    value: [9, '00'],
                    display: `${dateDisplay} 9:00`,
                  },
                  startTimeText: `${dateDisplay} 9:00`,
                });
                this.setState({
                  endTimeSelectedNew: {
                    value: [18, '00'],
                    display: `${dateDisplay} 18:00`,
                  },
                  endTimeText: `${dateDisplay} 18:00`,
                });
                this.setState({ isDuty: false });
              }}
            >
              <Check checked={!isDuty} />
              {t('不在岗')}
            </span>
          </div>
          <div style={{ display: !isDuty ? 'block' : 'none' }}>
            <Pickers
              defaultValue={jobInitObj.jobCode}
              value={reasonVal}
              label={t('不在岗原因')}
              onClick={() => this.setState({ showReasonPicker: true })}
              onChange={(select = {}) => {
                const jobInitObj = {
                  jobCode: select.value,
                  jobName: select.label,
                  jobTypeCode: noWorkJobTypeCodeObj.jobTypeCode,
                  jobTypeName: noWorkJobTypeCodeObj.jobTypeName,
                };
                // submitData = Object.assign({}, submitData, {
                //   jobCode: select.value,
                //   jobName: select.label,
                //   jobTypeCode: noWorkJobTypeCodeObj.jobTypeCode,
                //   jobTypeName: noWorkJobTypeCodeObj.jobTypeName,
                // });
                this.setState({
                  showReasonPicker: false,
                  reasonVal: select.label,
                });
                store.changeData({
                  data: {
                    jobInitObj,
                  },
                });
              }}
              show={showReasonPicker}
              pickerData={noJobNameList}
              onCancel={() => this.setState({ showReasonPicker: false })}
              keepFocus={false}
            />
          </div>
          <FocusInput
            arrow
            cellstyle={{ borderBottom: '1px solid #e8ebf0', margin: '0 10px' }}
            className="amp-start-time"
            value={startTimeSelectedNew.display}
            onClick={(e) => {
              Popo.picker({
                el: '.amp-start-time',
                infinite: true,
                wheels: [
                  {
                    infinite: false,
                    selected: startTimeSelectedNew.display ? startTimeSelectedNew.display.split(' ')[0] : moment().format('YYYY-MM-DD'),
                    data: dateList2.map((v) => ({
                      value: v,
                      display: v,
                    })),
                  },
                  {
                    selected: startTimeSelectedNew.display ? startTimeSelectedNew.display.split(' ')[1].split(':')[0] : moment().format('YYYY-MM-DD HH:mm').split(' ')[1].split(':')[0],
                    infinite: true,
                    data: new Array(24).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                  },
                  {
                    selected: startTimeSelectedNew.display ? startTimeSelectedNew.display.split(' ')[1].split(':')[1] : moment().format('YYYY-MM-DD HH:mm').split(' ')[1].split(':')[1],
                    infinite: true,
                    data: new Array(60).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                  },
                ],
                save: (rps, b) => {
                  const timeArr = rps.result.map((v) => (v.value));
                  // 原先的业务逻辑拷贝
                  const startTimeStr = `${timeArr[0]} ${timeArr[1]}:${timeArr[2]}`;
                  const startTimeTxt = moment(startTimeStr, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD HH:mm');
                  this.setState({
                    startTimeSelectedNew: {
                      value: timeArr,
                      display: startTimeTxt,
                    },
                    startTimeText: startTimeTxt,
                  }, () => {
                    console.info(t('选择的开始时间'), this.state.startTimeSelectedNew);
                  });
                },
              });
            }}
          >
            <label>{t('开始时间')}</label>
          </FocusInput>
          <FocusInput
            arrow
            cellstyle={{ borderBottom: '1px solid #e8ebf0', margin: '0 10px' }}
            className="amp-end-time"
            value={endTimeSelectedNew.display}
            onClick={(e) => {
              Popo.picker({
                el: '.amp-end-time',
                wheels: [
                  {
                    infinite: false,
                    selected: endTimeSelectedNew.display ? endTimeSelectedNew.display.split(' ')[0] : moment().format('YYYY-MM-DD'),
                    data: dateList2.map((v) => ({
                      value: v,
                      display: v,
                    })),
                  },
                  {
                    selected: endTimeSelectedNew.display ? endTimeSelectedNew.display.split(' ')[1].split(':')[0] : moment().format('YYYY-MM-DD HH:mm').split(' ')[1].split(':')[0],
                    infinite: true,
                    data: new Array(24).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                  },
                  {
                    selected: endTimeSelectedNew.display ? endTimeSelectedNew.display.split(' ')[1].split(':')[1] : moment().format('YYYY-MM-DD HH:mm').split(' ')[1].split(':')[1],
                    infinite: true,
                    data: new Array(60).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                  },
                ],
                save: (rps, b) => {
                  // 原来的业务逻辑拷贝
                  const timeArr = rps.result.map((v) => (v.value));
                  const endTimeStr = `${timeArr[0]} ${timeArr[1]}:${timeArr[2]}`;
                  const endTimeTxt = moment(endTimeStr, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD HH:mm');
                  if (new Date(startTimeText).getTime() >= new Date(endTimeTxt).getTime()) {
                    Modal.error({ content: t('结束时间不可早于开始时间') });
                    return;
                  }
                  if (moment(endTimeTxt).diff(moment(startTimeText), 'days', true) > 1) {
                    Modal.error({ content: t('安排时间不可超过24h') });
                    return;
                  }
                  this.setState({
                    endTimeSelectedNew: {
                      value: timeArr,
                      display: endTimeTxt,
                    },
                    endTimeText: endTimeTxt,
                  }, () => {
                    console.info(t('选择的结束时间'), this.state.endTimeSelectedNew);
                  });
                },
              });
            }}
          >
            <label>{t('结束时间')}</label>
          </FocusInput>
          <SplitBar />
          <div style={{ paddingLeft: 15 }}>
            <div style={{ padding: '6px 0' }}>{t('成员')}({tags.length})</div>
            <Tag dataSource={tags} />
          </div>
        </div>
        <Footer
          beforeBack={() => {
            store.changeData({
              data: {
                pageType: 3,
              },
            });
          }}
        >
          <FooterBtn
            disabled={!startTimeText || !endTimeText
             || (!isDuty && !reasonVal) || arrangeModifyLoading}
            onClick={() => {
              let changeModelList = [];
              const partialParams = {
                isWorking: isDuty ? 1 : 2,
                assignStartTime: moment(startTimeText).format('YYYY-MM-DD HH:mm:ss'),
                assignEndTime: moment(endTimeText).format('YYYY-MM-DD HH:mm:ss'),
              };
              if (isDuty) {
                changeModelList = arrangeModifyList.map((row) => {
                  const {
                    id,
                    userName,
                    createTime,
                    assignStartTime,
                    assignEndTime,
                    deptCode,
                    isWorking,
                    jobName,
                    // jobCode,
                    // jobTypeCode,
                    // orgJobCode,
                    // orgJobName,
                    // orgJobTypeCode,
                    // orgJobTypeName,
                  } = row;
                  // 在岗时，岗位类型相关字段传空字符串
                  return {
                    ...partialParams,
                    ...jobInitObj,
                    id,
                    userName,
                    createTime,
                    oldAssignStartTime: assignStartTime,
                    oldAssignEndTime: assignEndTime,
                    deptCode,
                    oldJobTypeName: '',
                    oldJobName: isWorking ? '' : jobName, // 上次岗位名称根据上次在岗状态传值
                    jobCode: '',
                    jobName: '',
                    jobTypeCode: '',
                    jobTypeName: '',
                  };
                });
              } else {
                changeModelList = arrangeModifyList.map((row) => {
                  const {
                    id,
                    userName,
                    createTime,
                    assignStartTime,
                    assignEndTime,
                    deptCode,
                    isWorking,
                    jobName,
                  } = row;
                  return {
                    ...partialParams,
                    ...jobInitObj,
                    id,
                    userName,
                    createTime,
                    oldAssignStartTime: assignStartTime,
                    oldAssignEndTime: assignEndTime,
                    oldJobTypeName: '',
                    oldJobName: isWorking ? '' : jobName, // 上次岗位名称根据上次在岗状态传值
                    deptCode,
                  };
                });
              }
              store.arrangeModify({
                params: {
                  changeModelList,
                },
              });
            }}
          >
            {t('确定')}
          </FooterBtn>
        </Footer>
        <TimePicker
          infinite
          dateList={dateList2}
          dateIdx={dateIdx}
          show={startShowPicker}
          selected={startTimeSelected}
          onCancel={() => this.setState({ startShowPicker: false })}
          onSelected={(timeArr) => {
            const dateIndex = timeArr.shift();
            const dateStr = dateList2[dateIndex];
            const startTimeStr = `${dateStr} ${timeArr.join(':')}`;
            const startTimeTxt = moment(startTimeStr, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD HH:mm');
            this.setState({
              startTimeSelected: [...timeArr],
              startShowPicker: false,
              startTimeText: startTimeTxt,
            });
          }}
        />
        <TimePicker
          infinite
          dateList={dateList2}
          dateIdx={dateIdx}
          show={endShowPicker}
          selected={endTimeSelected}
          onCancel={() => this.setState({ endShowPicker: false })}
          onSelected={(timeArr) => {
            const dateIndex = timeArr.shift();
            const dateStr = dateList2[dateIndex];
            const endTimeStr = `${dateStr} ${timeArr.join(':')}`;
            const endTimeTxt = moment(endTimeStr, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD HH:mm');
            if (new Date(startTimeText).getTime() >= new Date(endTimeTxt).getTime()) {
              Modal.error({ content: t('结束时间不可早于开始时间') });
              this.setState({
                endShowPicker: false,
              });
              return;
            }
            if (moment(endTimeTxt).diff(moment(startTimeText), 'days', true) > 1) {
              Modal.error({ content: t('安排时间不可超过24h') });
              this.setState({
                endShowPicker: false,
              });
              return;
            }
            this.setState({
              endTimeSelected: [...timeArr],
              endShowPicker: false,
              endTimeText: endTimeTxt,
            });
          }}
        />
      </div>
    );
  }
}

ArrangeModifyPage.propTypes = {
  tags: PropTypes.arrayOf(PropTypes.string),
  assignStartTime: PropTypes.string,
  assignEndTime: PropTypes.string,
  isOnline: PropTypes.bool,
  jobInitObj: PropTypes.shape(),
  noJobNameList: PropTypes.arrayOf(PropTypes.shape),
  date: PropTypes.string,
  dateIdx: PropTypes.number,
  dateList2: PropTypes.arrayOf(PropTypes.string),
  arrangeModifyList: PropTypes.arrayOf(PropTypes.shape()),
  arrangeModifyLoading: PropTypes.bool,
  noWorkJobTypeCodeObj: PropTypes.shape(),
  jobTypeListStr: PropTypes.string,
};

export default ArrangeModifyPage;
