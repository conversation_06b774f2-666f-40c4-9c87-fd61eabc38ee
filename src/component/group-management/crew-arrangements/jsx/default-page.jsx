import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import { Button } from 'react-weui/build/packages';
import { handleDatesShow, showTextLimit, classFocus } from '../../../../lib/util';
import {
  Header,
  Footer,
  List,
  Check,
  FooterBtn,
  message,
  FocusInput,
  modal,
} from '../../../common';
import Modal from '../../../common/modal';
import SplitBar from '../../group-tasks/jsx/modules/split-bar';
import store from '../reducers';
import style from '../style.css';

const modalRef = React.createRef();

let updateModal;

const reg = /^[1-9][0-9]*$/;

class DefaultPage extends React.Component {
  changeNum(e) {
    const value = e.target.value.trim();
    if (!value || reg.test(value)) {
      store.changeData({
        data: {
          challengeGoal: !value ? '' : Number(value),
        },
      });
      setTimeout(() => {
        updateModal(this.createModalContent());
      }, 0);
    }
  }

  createModalContent(v, name) {
    const {
      challengeGoal,
      splicedName,
      userName,
    } = this.props;

    return {
      content: (
        <div>
          <div style={{ display: 'flex', marginBottom: 10 }}>
            <div style={{ textAlign: 'right', width: 70 }}>{t('姓名')}:</div>
            <div style={{ marginLeft: 15 }}>{name || splicedName || userName}</div>
          </div>
          <div style={{ display: 'flex' }}>
            <div style={{ paddingTop: 7, flex: '1 0 auto', width: 70 }}>{t('挑战目标')}:</div>
            <div>
              <FocusInput
                value={v || challengeGoal}
                onChange={(e) => {
                  this.changeNum(e);
                }}
                className="challengeGoal"
              />
            </div>
          </div>
        </div>
      ),
      onOk: () => {
        const {
          rowId, challengeGoal,
        } = this.props;
        if (!reg.test(challengeGoal)) {
          modal.error({
            content: t('请输入正整数'),
            onOk: () => {
              store.changeData({
                data: {
                  challengeGoal: '',
                },
              });
              setTimeout(() => {
                updateModal(this.createModalContent());
              }, 0);
            },
            className: 'challengeGoal',
          });
          return;
        }
        store.changeChallengeGoal({
          params: {
            challengeGoal,
            id: rowId,
          },
        });
      },
      onCancel: () => {
        store.changeData({
          data: {
            challengeGoal: '',
          },
        });
      },
      domContainer: modalRef.current,
    };
  }

  render() {
    const {
      defaultPageObj,
      defaultPageList,
      listAllChecked,
      groupId,
      postDataLoading,
      date,
      // endEarlyList,
    } = this.props;
    // 日期格式小于10则在前面填充0，且统一变成字符串
    // /* eslint-disable */
    // const fillZero = num => num < 10 ? `0${num}` : String(num);
    // /* eslint-enable */
    // // 获取日期字符串
    // const getDateText = () => {
    //   const nD = new Date();
    //   const monthNum = nD.getMonth() + 1;
    //   return `${nD.getFullYear()}-${fillZero(monthNum)}-${fillZero(nD.getDate())}`;
    // };
    const rows = [
      [
        {
          title: '',
          render: (v) => (
            <span>
              <span style={{ color: v.isRed ? 'red' : 'unset' }}>{v.splicedName}</span>
              <span style={{ marginLeft: 8 }}>
                <Icon style={{ fontSize: 16, color: '#f18a1e', marginRight: 4 }} name="calendar" />
                <span style={{ color: v.actualWorkingDays > v.shouldWorkingDays ? '#f1771e' : '#C1C4CB' }}>{v.actualWorkingDays}{t('天')}</span>
              </span>
            </span>
          ),
        },
        {
          title: '',
          render: (v) => (
            !v.isSchedule ? <span>{t('尚未排班')}</span> :
              (
                <div style={{ position: 'relative', top: 8 }}>
                  {
                    !v.isWorking ?
                      <span style={{ color: '#EF0606' }}>{v.jobName}</span> :
                      (
                        <div>
                          {
                            v.id === null ? t('在岗') :
                              (
                                <div>
                                  {
                                    v.challengeGoal === 0 ?
                                      (
                                        <span
                                          style={{ color: '#aaa' }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            store.changeData({
                                              data: {
                                                rowId: v.id,
                                                userName: v.userName,
                                                splicedName: v.splicedName,
                                              },
                                            });
                                            updateModal = modal.confirm(this.createModalContent('', v.splicedName));
                                            classFocus('challengeGoal');
                                          }}
                                        >
                                          {t('请填目标')}
                                        </span>
                                      ) :
                                      (
                                        <div
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            store.changeData({
                                              data: {
                                                rowId: v.id,
                                                userName: v.userName,
                                                splicedName: v.splicedName,
                                              },
                                            });
                                            updateModal = modal.confirm(this.createModalContent(v.challengeGoal, v.splicedName));
                                            classFocus('challengeGoal');
                                          }}
                                        >
                                          <span>{v.challengeGoal}</span>
                                          <span
                                            style={{ color: 'rgb(0, 89, 206)', padding: 4 }}
                                          >
                                            <Icon name="edit" />
                                          </span>
                                        </div>
                                      )
                                  }
                                </div>
                              )
                          }
                        </div>
                      )
                  }
                </div>
              )
          ),
        },
      ],
      [
        {
          title: '',
          render: (v) => (
            <span style={{ color: !v.isSchedule ? '#C1C4CB' : '#141737' }}>{handleDatesShow(v.isSchedule ? v.assignStartTime : v.lastAssignStartTime, v.isSchedule ? v.assignEndTime : v.lastAssignEndTime) || '- -'}</span>
          ),
        },
        {
          title: '',
          render: (v) => (!v.isSchedule ? <span style={{ color: '#C1C4CB' }}>{v.lastJobName || '- -'}</span> : null),
        },
      ],
    ];
    // 处理list的选中状态
    const handleListAllChecked = (v) => {
      defaultPageList.forEach((i) => {
        if (!i.disabled) {
          i.checked = v;
        }
      });
      store.changeData({ data: { defaultPageList: [...defaultPageList] } });
    };
    return (
      <div>
        <Header title={t('组员安排')} />
        <List
          header={(
            <div style={{ marginLeft: -15 }}>
              <div className={style.topBar}>
                <span style={{ padding: '8px 0' }}>
                  {showTextLimit(defaultPageObj.deptName)}
                  -{showTextLimit(defaultPageObj.groupName)}（{defaultPageObj.userCount}）
                </span>
                <span
                  style={{ fontSize: 14, padding: '8px 15px 8px 0', position: 'relative' }}
                  onClick={() => {
                    store.changeData({
                      data: {
                        showPopSheet: true,
                      },
                    });
                  }}
                >
                  {date}
                  <Icon
                    name="triangle-up"
                    style={{
                      position: 'absolute', top: 9, right: 0, fontSize: 12,
                    }}
                  />
                  <Icon
                    name="triangle-down"
                    style={{
                      position: 'absolute', top: 17, right: 0, fontSize: 12,
                    }}
                  />
                </span>
              </div>
              <SplitBar style={{ padding: '8px 0' }}>
                <div className={style.listHeadWrap} style={{ justifyContent: 'space-between' }}>
                  <div
                    style={{ color: '#141737', flex: 'none' }}
                    onClick={() => {
                      handleListAllChecked(!listAllChecked);
                      store.changeData({ data: { listAllChecked: !listAllChecked } });
                    }}
                  >
                    <Check
                      checked={listAllChecked}
                    /> {t('全选')}
                  </div>
                  <div className={style.spanBtnWrap} style={{ flex: 'none' }}>
                    <Button
                      style={{ margin: '0px 14px 3px 0' }}
                      // 开始时间为当天则禁用 复用上次
                      disabled={defaultPageList.filter((v) => v.checked).length === 0
                      || defaultPageList.filter((v) => v.checked && v.lastAssignStartTime)
                        .some((v) => v.lastAssignStartTime && moment(v.lastAssignStartTime).format('YYYY-MM-DD') === date)}
                      onClick={() => {
                        // 复用上次：用当天或明天[跨天]操作日期和上次时间结合
                        defaultPageList.forEach((v) => {
                          // 选中且有上次安排的才修改数据，且开始时间不为当天
                          if (v.checked && v.hasLastAssign
                            && v.lastAssignStartTime && moment(v.lastAssignStartTime).format('YYYY-MM-DD') !== date) {
                            const isNextDay = v.lastAssignStartTime.split(' ')[0] !== v.lastAssignEndTime.split(' ')[0];
                            v.isSchedule = true;
                            v.assignStartTime = `${date} ${v.lastAssignStartTime.split(' ')[1]}`;
                            v.assignEndTime = `${isNextDay ? moment(date).add(1, 'days').format('YYYY-MM-DD') : date} ${v.lastAssignEndTime.split(' ')[1]}`;
                            v.isWorking = v.lastIsWorking;
                            v.jobCode = v.lastJobCode;
                            v.jobName = v.lastJobName;
                            v.jobTypeCode = v.lastJobTypeCode;
                            v.jobTypeName = v.lastJobTypeName;
                          }
                        });
                        store.changeData({ data: { defaultPageList: [...defaultPageList] } });
                        message.success(t('加载数据成功！'));
                      }}
                      size="small"
                    >
                      {t('复用')}
                    </Button>
                    <Button
                      disabled={defaultPageList.filter((v) => v.checked).length === 0}
                      onClick={() => {
                        const defaultPageCheckedList = defaultPageList.filter((v) => v.checked);
                        let assignStartTimeVal = '';
                        let assignEndTimeVal = '';
                        let isOnlineVal = true;
                        let jobInitObj = { jobTypeCode: '', jobCode: '', jobName: '' }; // 初始值
                        // let jobIdVal = 1;
                        // 勾选数据有安排了未提交的，且这些数据一样，则进入时间安排页面有初始值
                        // eslint-disable-next-line max-len
                        const defaultPageScheduleList = defaultPageCheckedList.filter((v) => v.isSchedule);
                        const isAllSame = !!defaultPageScheduleList.length
                          && defaultPageScheduleList.every((v) => {
                            const isSame = defaultPageScheduleList[0].job === v.job
                              && defaultPageScheduleList[0].isWorking === v.isWorking
                              && defaultPageScheduleList[0].assignEndTime === v.assignEndTime
                              && defaultPageScheduleList[0].assignStartTime === v.assignStartTime;
                            return isSame;
                          });
                        if (isAllSame) {
                          assignStartTimeVal = defaultPageScheduleList[0].assignStartTime || '';
                          assignEndTimeVal = defaultPageScheduleList[0].assignEndTime || '';
                          isOnlineVal = defaultPageScheduleList[0].isWorking;
                          jobInitObj = {
                            jobTypeCode: defaultPageScheduleList[0].jobTypeCode || '',
                            jobCode: defaultPageScheduleList[0].jobCode || '',
                            jobName: defaultPageScheduleList[0].jobName || '',
                          };
                        }
                        // 进入到安排时间详情页
                        store.changeData({
                          data: {
                            pageType: 2,
                            tags: defaultPageCheckedList.map((v) => v.splicedName),
                            // eslint-disable-next-line max-len
                            assignStartTime: assignStartTimeVal.substring(0, assignStartTimeVal.length - 3),
                            // eslint-disable-next-line max-len
                            assignEndTime: assignEndTimeVal.substring(0, assignEndTimeVal.length - 3),
                            isOnline: isOnlineVal, // 是否在岗
                            // jobId: jobIdVal,
                            jobInitObj, // 岗位初始值
                          },
                        });
                      }}
                      size="small"
                    >
                      {t('排班排产')}
                    </Button>
                    <Button
                      style={{ margin: '0 10px' }}
                      onClick={() => {
                        store.changeData({
                          data: {
                            pageType: 3,
                          },
                        });
                      }}
                      size="small"
                    >
                      {t('点名')}
                    </Button>
                  </div>
                </div>
              </SplitBar>
              <div className={style.bar}>
                <div>{t('姓名')}</div>
                <div>{t('挑战目标/状态')}</div>
              </div>
            </div>
          )}
          data={defaultPageList}
          rows={rows}
          checkbox
          rowStyleOrClass={(item) => {
            if (item.disabled && !item.checked) {
              return style.disabledItem;
            }
            return '';
          }}
          onSelected={(selectedArr) => {
            // eslint-disable-next-line max-len
            const cheked = !!selectedArr.length && selectedArr.length === defaultPageList.filter((i) => !i.disabled || (i.disabled && i.checked)).length;
            store.changeData({
              data: { listAllChecked: cheked, defaultPageList: [...defaultPageList] },
            });
          }}
          style={{ height: 'calc(100vh - 110px)', overflowY: 'auto' }}
        />
        <Footer
          beforeBack={(back) => {
            if (defaultPageList.some((v) => !v.disabled && v.isSchedule)) {
              Modal.confirm({
                content: t('当前有安排数据未上传,返回会丢失，是否返回?'),
                onOk: () => {
                  store.init({
                    date: `${date} ${moment().format('HH:mm:ss')}`,
                  });
                  store.changeData({
                    data: {
                      date,
                    },
                  });
                  back();
                },
              });
            } else {
              back();
            }
          }}
        >
          <FooterBtn
            disabled={defaultPageList.filter((v) => v.checked).length === 0
            || defaultPageList.filter((v) => v.checked).some((v) => !v.isSchedule) || postDataLoading}
            onClick={() => {
              // 判断是否有不在岗且空岗位
              const errData = defaultPageList
              // eslint-disable-next-line max-len
                .filter((v) => v.checked && v.isSchedule && !v.isWorking && (!v.jobCode || !v.jobTypeCode));
              if (errData.length) {
                modal.error({
                  content: `${t('以下用户未选择不在岗原因：{}', errData.map((v) => v.userName).join('、'))}`,
                });
                return;
              }
              const checkedList = defaultPageList.filter((v) => v.checked && v.isSchedule);
              let tip = t('提交数据后不可修改,确定提交吗?');
              // 判断勾选数据是否存在"实际出勤天数"大于"应出勤天数"
              const overList = checkedList.filter((v) => v.actualWorkingDays > v.shouldWorkingDays);
              if (overList.length) {
                tip = (
                  <div style={{ fontSize: 12, margin: '0 -10px' }}>
                    <div>{t('请确认以下员工是否还要继续排班')}：</div>
                    <div style={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto' }}>
                      {
                      overList.map((v) => <div style={{ marginTop: 8 }}>{v.userName}{t('本月应出勤')}{v.shouldWorkingDays}{t('天')}，{t('实际已出勤')}<span style={{ color: '#f11e1e' }}>{v.actualWorkingDays}{t('天')}</span></div>)
                    }
                    </div>
                  </div>
                );
              }
              Modal.confirm({
                content: tip,
                onOk: () => {
                  // eslint-disable-next-line max-len
                  const postList = checkedList.map((v) => {
                    const obj = {
                      assignStartTime: v.assignStartTime,
                      assignEndTime: v.assignEndTime,
                      isWorking: v.isWorking ? 1 : 2,
                      // job: v.isWorking ? v.job : 0,
                      // status: !v.isWorking ? v.job : 0,
                      jobCode: v.jobCode,
                      jobTypeCode: v.jobTypeCode,
                      userNameList: [v.userName],
                    };
                    // 在岗 岗位类型+岗位名称传空字符串
                    if (v.isWorking) {
                      obj.jobCode = '';
                      obj.jobTypeCode = '';
                    }
                    // 有值才传这两个字段
                    if (v.lastAssignStartTime) {
                      obj.lastAssignStartTime = v.lastAssignStartTime;
                    }
                    if (v.lastAssignEndTime) {
                      obj.lastAssignEndTime = v.lastAssignEndTime;
                    }
                    return obj;
                  });
                  store.postData({
                    data: {
                      list: postList,
                      groupId,
                      warehouseId: 1,
                      // date: moment(date).format('YYYY-MM-DD HH:mm:ss'),
                    },
                  });
                },
              });
            }}
          >
            {t('提交')}
          </FooterBtn>
        </Footer>
        <div ref={modalRef} />
      </div>
    );
  }
}

DefaultPage.propTypes = {
  defaultPageObj: PropTypes.shape(),
  defaultPageList: PropTypes.arrayOf(PropTypes.shape()),
  groupId: PropTypes.number,
  listAllChecked: PropTypes.bool,
  postDataLoading: PropTypes.bool,
  date: PropTypes.string,
  challengeGoal: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  rowId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  userName: PropTypes.string,
  splicedName: PropTypes.string,
};

export default DefaultPage;
