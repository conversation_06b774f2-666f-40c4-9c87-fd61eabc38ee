import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import { Button } from 'react-weui/build/packages';
import { handleDatesShow, showTextLimit, classFocus } from '../../../../lib/util';
import {
  Header,
  Footer,
  List,
  Check,
  FooterBtn,
  TimePicker,
  FocusInput,
  modal,
} from '../../../common';
import Modal from '../../../common/modal';
import SplitBar from '../../group-tasks/jsx/modules/split-bar';
import store from '../reducers';
import style from '../style.css';

const modalRef = React.createRef();

let updateModal;

const reg = /^[1-9][0-9]*$/;

// 日期格式小于10则在前面填充0，且统一变成字符串
/* eslint-disable */
const fillZero = num => num < 10 ? `0${num}` : String(num);
/* eslint-enable */
// 获取日期字符串
const getDateText = () => {
  const nD = new Date();
  const monthNum = nD.getMonth() + 1;
  return `${nD.getFullYear()}-${fillZero(monthNum)}-${fillZero(nD.getDate())}`;
};
// 获取数组中对应的去重日期数组，以及当天的数组下标
const getDatesData = (arr) => {
  let datesArr = [];
  let todayIdx = 0;
  // 去重并获取所有日期
  arr.forEach((r) => {
    ['assignStartTime', 'assignEndTime'].forEach((key) => {
      const str = (r[key].split(' ')[0] || '').replace(/-/g, '/');
      if (str && !datesArr.includes(str)) {
        datesArr.push(str);
        if (todayIdx === 0
          && new Date(str).toLocaleDateString() === new Date().toLocaleDateString()) {
          todayIdx = datesArr.length - 1;
        }
      }
    });
  });
  // 若数组为空，则返回当天日期
  if (!datesArr.length) {
    datesArr = [new Date().toLocaleDateString()];
  }
  return [datesArr, todayIdx];
};

class EndEaelyPage extends React.Component {
  changeNum(e) {
    const value = e.target.value.trim();
    if (!value || reg.test(value)) {
      store.changeData({
        data: {
          challengeGoal: !value ? '' : Number(value),
        },
      });
      setTimeout(() => {
        updateModal(this.createModalContent());
      }, 0);
    }
  }

  createModalContent(v, name) {
    const {
      challengeGoal,
      splicedName,
    } = this.props;

    return {
      content: (
        <div>
          <div style={{ display: 'flex', marginBottom: 10 }}>
            <div style={{ textAlign: 'right', width: 70 }}>{t('姓名')}:</div>
            <div style={{ marginLeft: 15 }}>{name || splicedName}</div>
          </div>
          <div style={{ display: 'flex' }}>
            <div style={{ paddingTop: 7, flex: '1 0 auto', width: 70 }}>{t('挑战目标')}:</div>
            <div>
              <FocusInput
                value={v || challengeGoal}
                onChange={(e) => {
                  this.changeNum(e);
                }}
                className="challengeGoal"
              />
            </div>
          </div>
        </div>
      ),
      onOk: () => {
        const {
          rowId, challengeGoal,
        } = this.props;
        if (!reg.test(challengeGoal)) {
          modal.error({
            content: t('请输入正整数'),
            onOk: () => {
              store.changeData({
                data: {
                  challengeGoal: '',
                },
              });
              setTimeout(() => {
                updateModal(this.createModalContent());
              }, 0);
            },
            className: 'challengeGoal',
          });
          return;
        }
        store.changeChallengeGoal({
          params: {
            challengeGoal,
            id: rowId,
          },
        });
      },
      onCancel: () => {
        store.changeData({
          data: {
            challengeGoal: '',
          },
        });
      },
      domContainer: modalRef.current,
    };
  }

  render() {
    const {
      defaultPageObj,
      endEarlyList,
      endEarlyAllChecked,
      groupId,
      endEarlyLoading,
      endTimeShowPicker,
      endTimeDatesArr,
      endTimeDateIdx,
      endTimePickerDateSelected,
      date,
      rollCallLoading,
      minRollCallNum,
      scrollRowNums,
    } = this.props;
    // 处理list的选中状态
    const handleListAllChecked = (v) => {
      endEarlyList.forEach((i) => {
        if (!i.disabled) i.checked = v;
      });
      store.changeData({ data: { endEarlyList: [...endEarlyList] } });
    };
    const rows = [
      [
        {
          title: '',
          render: 'splicedName',
        },
        {
          title: '',
          render: (v) => (
            <div style={{ position: 'relative', top: 15 }}>
              {
                !v.isWorking ?
                  <span style={{ color: '#EF0606' }}>{v.jobName}</span> :
                  (
                    <div>
                      {
                        v.id === null ? t('在库') :
                          (
                            <div>
                              {
                                v.challengeGoal === 0 ?
                                  (
                                    <span
                                      style={{ color: '#aaa' }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        store.changeData({
                                          data: {
                                            rowId: v.id,
                                            userName: v.userName,
                                            splicedName: v.splicedName,
                                          },
                                        });
                                        updateModal = modal.confirm(this.createModalContent('', v.splicedName));
                                        classFocus('challengeGoal');
                                      }}
                                    >
                                      {t('请填目标')}
                                    </span>
                                  ) :
                                  (
                                    <div
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        store.changeData({
                                          data: {
                                            rowId: v.id,
                                            userName: v.userName,
                                            splicedName: v.splicedName,
                                          },
                                        });
                                        updateModal = modal.confirm(this.createModalContent(v.challengeGoal, v.splicedName));
                                        classFocus('challengeGoal');
                                      }}
                                    >
                                      <span>{v.challengeGoal}</span>
                                      <span
                                        style={{ color: 'rgb(0, 89, 206)', padding: 4 }}
                                      >
                                        <Icon name="edit" />
                                      </span>
                                    </div>
                                  )
                              }
                            </div>
                          )
                      }
                    </div>
                  )
              }
            </div>
          ),
        },
      ],
      [
        {
          title: '',
          render: (v) => <span style={{ color: '#141737' }}>{handleDatesShow(v.assignStartTime, v.assignEndTime) || '- -'}</span>,
        },
      ],
    ];
    const btnDisabled = (endEarlyList) => {
      const selectedRows = endEarlyList.filter((v) => v.checked);
      if (selectedRows.length !== 1) return true;
      return !selectedRows[0].isWorking;
    };
    return (
      <div>
        <Header title={t('组员点名')} />
        <List
          header={(
            <div style={{ marginLeft: -15 }}>
              <div className={style.topBar} style={{ padding: 8 }}>
                <span>
                  {showTextLimit(defaultPageObj.deptName)}
                  -{showTextLimit(defaultPageObj.groupName)}（{defaultPageObj.userCount}）
                </span>
                <span style={{ fontSize: 14 }}>{date}</span>
              </div>
              <SplitBar style={{ padding: '8px 0' }}>
                <div className={style.listHeadWrap} style={{ justifyContent: 'space-between' }}>
                  <div
                    style={{ color: '#141737' }}
                    onClick={() => {
                      handleListAllChecked(!endEarlyAllChecked);
                      store.changeData({ data: { endEarlyAllChecked: !endEarlyAllChecked } });
                    }}
                  >
                    <Check
                      checked={endEarlyAllChecked}
                    /> {t('全选')}
                  </div>
                  <div style={{ marginRight: 10, flex: 'none' }}>
                    {
                      (date === moment().format('YYYY-MM-DD') || date === moment().subtract(1, 'days').format('YYYY-MM-DD')) && (
                      <Button
                        size="small"
                        type="primary"
                        disabled={btnDisabled(endEarlyList)}
                        loading={rollCallLoading}
                        onClick={() => {
                          const { id, userName, splicedName } = endEarlyList.find((v) => v.checked);
                          store.userRollCall({
                            params: {
                              tsMemberAssignedId: id,
                            },
                            userName,
                            splicedName,
                          });
                        }}
                      >
                        {t('点名')} {minRollCallNum}
                      </Button>
                      )
                    }
                  </div>
                </div>
              </SplitBar>
              <div className={style.bar}>
                <div>{t('姓名')}</div>
                <div>{t('挑战目标/状态')}</div>
              </div>
            </div>
          )}
          data={endEarlyList}
          rows={rows}
          checkbox
          autoScroll
          scrollRowNums={scrollRowNums}
          onSelected={(selectedArr) => {
            const cheked = !!selectedArr.length && (selectedArr.length === endEarlyList.length);
            store.changeData({
              data: { endEarlyAllChecked: cheked, endEarlyList: [...endEarlyList] },
            });
          }}
          style={{ height: 'calc(100vh - 110px)', overflowY: 'auto' }}
        />
        <Footer
          beforeBack={() => {
            store.init({
              date: `${date} ${moment().format('HH:mm:ss')}`,
            });
            store.changeData({
              data: {
                date,
              },
            });
          }}
        >
          <FooterBtn
            disabled={endEarlyList.filter((v) => v.checked).length === 0 || endEarlyLoading}
            onClick={() => {
              const checkedList = endEarlyList.filter((v) => v.checked);
              let assignStartTimeVal = '';
              let assignEndTimeVal = '';
              let isOnlineVal = true;
              let jobInitObj = {
                jobTypeCode: '', jobCode: '', jobName: '', jobTypeName: '',
              }; // 初始值
              // const scheduleList = checkedList.filter(v => v.isSchedule);
              const isAllSame = !!checkedList.length
                && checkedList.every((v) => {
                  const isSame = checkedList[0].job === v.job
                    && checkedList[0].isWorking === v.isWorking
                    && checkedList[0].assignEndTime === v.assignEndTime
                    && checkedList[0].assignStartTime === v.assignStartTime;
                  return isSame;
                });
              if (isAllSame) {
                assignStartTimeVal = checkedList[0].assignStartTime || '';
                assignEndTimeVal = checkedList[0].assignEndTime || '';
                isOnlineVal = checkedList[0].isWorking;
                jobInitObj = {
                  jobTypeCode: checkedList[0].jobTypeCode || '',
                  jobCode: checkedList[0].jobCode || '',
                  jobName: checkedList[0].jobName || '',
                  jobTypeName: checkedList[0].jobTypeName || '',
                };
              }
              store.changeData({
                data: {
                  pageType: 4,
                  tags: checkedList.map((v) => v.splicedName),
                  isOnline: isOnlineVal,
                  assignStartTime: assignStartTimeVal.substring(0, assignStartTimeVal.length - 3),
                  assignEndTime: assignEndTimeVal.substring(0, assignEndTimeVal.length - 3),
                  jobInitObj,
                  arrangeModifyList: checkedList,
                },
              });
            }}
          >
            {t('修改')}
          </FooterBtn>
        </Footer>
        <TimePicker
          infinite
          show={endTimeShowPicker}
          dateList={endTimeDatesArr}
          dateIdx={endTimeDateIdx}
          selected={endTimePickerDateSelected}
          onCancel={() => store.changeData({ data: { endTimeShowPicker: false } })}
          onSelected={(timeArr) => {
            store.changeData({ data: { endTimeShowPicker: false } });
            Modal.confirm({
              content: t('是否提前结束?'),
              onOk: () => {
                const endTime = `${endTimeDatesArr[timeArr[0]].replace(/\//g, '-')} ${fillZero(timeArr[1])}:${fillZero(timeArr[2])}:00`;
                store.endEarly({
                  data: {
                    userNameList: endEarlyList.filter((v) => v.checked).map((v) => v.userName),
                    groupId,
                    warehouseId: 1,
                    endTime,
                  },
                });
              },
            });
          }}
        />
        <div ref={modalRef} />
      </div>
    );
  }
}

EndEaelyPage.propTypes = {
  defaultPageObj: PropTypes.shape(),
  endEarlyList: PropTypes.arrayOf(PropTypes.shape()),
  groupId: PropTypes.number,
  endEarlyAllChecked: PropTypes.bool,
  endEarlyLoading: PropTypes.bool,
  endTimeShowPicker: PropTypes.bool,
  endTimeDateIdx: PropTypes.number,
  endTimePickerDateSelected: PropTypes.arrayOf(PropTypes.number),
  endTimeDatesArr: PropTypes.arrayOf(PropTypes.string),
  date: PropTypes.string,
  rollCallLoading: PropTypes.bool,
  challengeGoal: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  rowId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  userName: PropTypes.string,
  splicedName: PropTypes.string,
  minRollCallNum: PropTypes.number,
  scrollRowNums: PropTypes.number,
};

export default EndEaelyPage;
