import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import { Popup, PopupHeader } from 'react-weui/build/packages';
import {
  <PERSON><PERSON>,
  <PERSON>er,
  Check,
  FooterBtn,
  Pickers,
  TimePicker,
  Tag,
  Accordion,
  FocusInput,
  Popo,
} from '../../../common';
import Modal from '../../../common/modal';
import SplitBar from '../../group-tasks/jsx/modules/split-bar';
import store from '../reducers';
import style from '../style.css';

class SchedulePage extends React.Component {
  constructor(props) {
    super(props);
    const {
      assignStartTime,
      assignEndTime,
      isOnline,
      jobInitObj,
      jobTypeListStr,
    } = props;
    let startTimeSelected = [new Date().getHours(), new Date().getMinutes()];
    let endTimeSelected = [new Date().getHours(), new Date().getMinutes()];
    if (assignStartTime) {
      const arr = assignStartTime.split(' ')[1].split(':');
      startTimeSelected = [arr[0] * 1, arr[1] * 1];
    }
    if (assignEndTime) {
      const arr = assignEndTime.split(' ')[1].split(':');
      endTimeSelected = [arr[0] * 1, arr[1] * 1];
    }

    /* ---- new -----*/
    let startTimeSelectedNew = { value: [], display: '' };
    let endTimeSelectedNew = { value: [], display: '' };
    if (assignStartTime) {
      startTimeSelectedNew = { value: assignStartTime.match(/\d+/g), display: assignStartTime };
    }
    if (assignEndTime) {
      endTimeSelectedNew = { value: assignEndTime.match(/\d+/g), display: assignEndTime };
    }

    // 设置手风琴默认勾选数据
    const jobTypeList = JSON.parse(jobTypeListStr);
    jobTypeList.forEach((v) => {
      if (jobInitObj.jobTypeCode === v.jobTypeCode) {
        v.children.forEach((i) => {
          if (jobInitObj.jobCode === i.jobCode) {
            i.isChecked = true;
          }
        });
      }
    });
    this.state = {
      isDuty: isOnline,
      showDutyPicker: false,
      dutyVal: isOnline ? jobInitObj.jobName : '',
      showReasonPicker: false,
      reasonVal: !isOnline ? jobInitObj.jobName : '',
      startTimeText: assignStartTime,
      endTimeText: assignEndTime,
      startTimeSelected,
      endTimeSelected,
      startTimeSelectedNew,
      endTimeSelectedNew,
      startShowPicker: false,
      endShowPicker: false,
      nowJobObj: {}, //  当前安排时间的岗位信息对象，用于'确定'按钮
      workTempJobObj: {}, // 用于切换在岗时重新设置nowJobObj
      noWorkTempJobObj: {}, // 用于切换不在岗时重新设置nowJobObj
      jobTypeList, // 在岗手风琴初始数据
      accordionArr: [], // 手风琴当前选中的数组
    };
  }

  render() {
    const {
      tags,
      defaultPageList,
      jobInitObj,
      noJobNameList,
      noWorkJobTypeCodeObj,
      date,
      mostJobObj,
    } = this.props;
    const {
      isDuty,
      showDutyPicker,
      dutyVal,
      showReasonPicker,
      reasonVal,
      startTimeText,
      endTimeText,
      startTimeSelected,
      endTimeSelected,
      startTimeSelectedNew,
      endTimeSelectedNew,
      startShowPicker,
      endShowPicker,
      nowJobObj,
      workTempJobObj,
      noWorkTempJobObj,
      jobTypeList,
      accordionArr,
    } = this.state;
    const nowDate = new Date();
    // 日期格式小于10则在前面填充0，且统一变成字符串
    /* eslint-disable */
    const fillZero = num => num < 10 ? `0${num}` : String(num);
    /* eslint-enable */
    // 获取日期字符串
    const getDateText = (timeArr, isNextDay = false) => {
      let nD = nowDate;
      if (!timeArr) {
        timeArr = [nD.getHours(), nD.getMinutes()];
      }
      if (isNextDay) {
        nD = new Date(nowDate.getTime() + 86400000);
      }
      const monthNum = nD.getMonth() + 1;
      return `${nD.getFullYear()}-${fillZero(monthNum)}-${fillZero(nD.getDate())} ${fillZero(timeArr[0])}:${fillZero(timeArr[1])}`;
    };
    return (
      <div>
        <Header title={t('安排时间')} />
        <div style={{ backgroundColor: '#fff' }}>
          <SplitBar style={{ paddingLeft: 15 }}>{`${date} ${moment().format('HH:mm')}`}</SplitBar>
          <div className={style.checkedWrap}>
            <span
              onClick={() => {
                this.setState({
                  startTimeSelectedNew: {
                    value: [],
                    display: '',
                  },
                  startTimeText: '',
                });
                this.setState({
                  endTimeSelectedNew: {
                    value: [],
                    display: '',
                  },
                  endTimeText: '',
                });
                this.setState({ isDuty: true, nowJobObj: { ...workTempJobObj } });
              }}
            >
              <Check checked={isDuty} />  {t('在岗')}
            </span>
            <span
              onClick={() => {
                const dateDisplay = moment(new Date(), 'YYYY-MM-DD').format('YYYY-MM-DD');
                this.setState({
                  startTimeSelectedNew: {
                    value: [9, '00'],
                    display: `${dateDisplay} 9:00`,
                  },
                  startTimeText: `${dateDisplay} 9:00`,
                });
                this.setState({
                  endTimeSelectedNew: {
                    value: [18, '00'],
                    display: `${dateDisplay} 18:00`,
                  },
                  endTimeText: `${dateDisplay} 18:00`,
                });
                this.setState({ isDuty: false, nowJobObj: { ...noWorkTempJobObj } });
              }}
            >
              <Check checked={!isDuty} />  {t('不在岗')}
            </span>
          </div>
          {/* OFC-9375 岗位无需选择 */}
          <div style={{ display: 'none' }}>
            <FocusInput
              value={dutyVal}
              arrow
              readOnly
              onClick={() => {
                this.setState({ showDutyPicker: true });
              }}
            >
              <label>{t('岗位')}</label>
            </FocusInput>
            <Popup
              show={showDutyPicker}
            >
              <PopupHeader
                left={t('取消')}
                right={t('确定')}
                leftOnClick={() => {
                  this.setState({ showDutyPicker: false });
                }}
                rightOnClick={() => {
                  if (!accordionArr[0]) {
                    return;
                  }
                  const tempNowJobObj = {
                    jobCode: accordionArr[0].jobCode,
                    jobName: accordionArr[0].jobName,
                    jobTypeCode: accordionArr[0].jobTypeCode,
                    jobTypeName: accordionArr[0].jobTypeName,
                  };
                  this.setState({
                    showDutyPicker: false,
                    dutyVal: accordionArr[0].jobName,
                    nowJobObj: tempNowJobObj,
                    workTempJobObj: { ...tempNowJobObj },
                  });
                }}
              />
              {
                showDutyPicker ? (
                  <div style={{ maxHeight: 'calc(100vh - 45px)', overflowY: 'auto' }}>
                    <Accordion
                      dataSource={jobTypeList}
                      limitNum={1}
                      onSelected={(arr) => {
                        this.setState({ accordionArr: arr });
                      }}
                    />
                  </div>
                ) : null
              }
            </Popup>
          </div>
          <div style={{ display: !isDuty ? 'block' : 'none' }}>
            <Pickers
              defaultValue={jobInitObj.jobCode}
              value={reasonVal}
              label={t('不在岗原因')}
              onClick={() => this.setState({ showReasonPicker: true })}
              onChange={(select = {}) => {
                const tempNowJobObj = {
                  jobCode: select.value,
                  jobName: select.label,
                  jobTypeCode: noWorkJobTypeCodeObj.jobTypeCode,
                  jobTypeName: noWorkJobTypeCodeObj.jobTypeName,
                };
                this.setState({
                  showReasonPicker: false,
                  reasonVal: select.label,
                  nowJobObj: tempNowJobObj,
                  noWorkTempJobObj: { ...tempNowJobObj },
                });
              }}
              show={showReasonPicker}
              pickerData={noJobNameList}
              onCancel={() => this.setState({ showReasonPicker: false })}
              keepFocus={false}
            />
          </div>
          <FocusInput
            arrow
            cellstyle={{ borderBottom: '1px solid #e8ebf0', margin: '0 10px' }}
            className="sp-start-time"
            value={startTimeSelectedNew.display}
            onClick={(e) => {
              Popo.picker({
                el: '.sp-start-time',
                infinite: true,
                wheels: [
                  {
                    selected: startTimeSelectedNew.display ? startTimeSelectedNew.display.split(' ')[1].split(':')[0] : new Date().getHours(),
                    infinite: true,
                    data: new Array(24).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                  },
                  {
                    selected: startTimeSelectedNew.display ? startTimeSelectedNew.display.split(' ')[1].split(':')[1] : new Date().getMinutes(),
                    infinite: true,
                    data: new Array(60).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                  },
                ],
                save: (rps, b) => {
                  // 原来的业务逻辑拷贝
                  const timeArr = rps.result.map((v) => (v.value));
                  // 判断开始时间是否早于上次结束时间
                  const isErrTime = defaultPageList.filter((v) => v.checked)
                    .some((v) => v.lastAssignEndTime && new Date(v.lastAssignEndTime).getTime() > new Date(`${date} ${timeArr.join(':')}:00`).getTime());
                  if (isErrTime) {
                    Modal.error({ content: t('时间安排存在冲突') });
                    return;
                  }
                  const startTimeStr = `${date} ${timeArr.join(':')}`;
                  const startTimeTxt = moment(startTimeStr, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD HH:mm');
                  this.setState({
                    startTimeSelectedNew: {
                      value: timeArr,
                      display: startTimeTxt,
                    },
                    startTimeText: startTimeTxt,
                  }, () => {
                    console.info(t('选择的开始时间'), this.state.startTimeSelectedNew);
                  });
                },
              });
            }}
          >
            <label>{t('开始时间')}</label>
          </FocusInput>
          <FocusInput
            arrow
            cellstyle={{ borderBottom: '1px solid #e8ebf0', margin: '0 10px' }}
            className="sp-end-time"
            value={endTimeSelectedNew.display}
            onClick={(e) => {
              // 原来的业务逻辑拷贝
              if (!startTimeText) {
                Modal.error({ content: t('请先选择开始时间') });
                return;
              }
              Popo.picker({
                el: '.sp-end-time',
                infinite: true,
                wheels: [
                  {
                    selected: endTimeSelectedNew.display ? endTimeSelectedNew.display.split(' ')[1].split(':')[0] : new Date().getHours(),
                    infinite: true,
                    data: new Array(24).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                  },
                  {
                    selected: endTimeSelectedNew.display ? endTimeSelectedNew.display.split(' ')[1].split(':')[1] : new Date().getMinutes(),
                    infinite: true,
                    data: new Array(60).fill('').map((v, i) => ({ value: i < 10 ? `0${i}` : i, display: i < 10 ? `0${i}` : i })),
                  },
                ],
                save: (rps, b) => {
                  const timeArr = rps.result.map((v) => (v.value));
                  let isNextDay = false;
                  const endTimeStr = `${date} ${timeArr.join(':')}`;
                  if (moment(endTimeStr).diff(moment(startTimeText), 'minutes', true) <= 0) {
                    isNextDay = true;
                  }
                  const endTimeTxt = !isNextDay
                    ? moment(endTimeStr, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD HH:mm')
                    : moment(endTimeStr, 'YYYY-MM-DD HH:mm').add(1, 'days').format('YYYY-MM-DD HH:mm');
                  this.setState({
                    endTimeSelectedNew: {
                      value: timeArr,
                      display: endTimeTxt,
                    },
                    endTimeText: endTimeTxt,
                  }, () => {
                    console.info(t('选择的结束时间'), this.state.endTimeSelectedNew);
                  });
                },
              });
            }}
          >
            <label>{t('结束时间')}</label>
          </FocusInput>
          <SplitBar />
          <div style={{ paddingLeft: 15 }}>
            <div style={{ padding: '6px 0' }}>{t('成员')}({tags.length})</div>
            <Tag dataSource={tags} />
          </div>
        </div>
        <Footer
          beforeBack={() => {
            store.changeData({
              data: {
                pageType: 1,
              },
            });
          }}
        >
          <FooterBtn
            disabled={!startTimeText || !endTimeText || (!isDuty && !reasonVal)}
            onClick={() => {
              // 回到首页并修改相关数据
              defaultPageList.forEach((v) => {
                if (v.checked) {
                  v.isSchedule = true;
                  v.assignStartTime = `${startTimeText}:00`;
                  v.assignEndTime = `${endTimeText}:00`;
                  v.isWorking = isDuty;
                  // 有修改过nowJobObj才进行赋值，不在岗设置
                  if (Object.keys(nowJobObj).length && !isDuty) {
                    v.jobCode = nowJobObj.jobCode;
                    v.jobName = nowJobObj.jobName;
                    v.jobTypeCode = nowJobObj.jobTypeCode;
                    v.jobTypeName = nowJobObj.jobTypeName;
                  }
                  // 岗位设置
                  // 当用户岗位不存在时，拿当前组人数最多的岗位进行赋值
                  if (isDuty) {
                    v.jobCode = v.orgJobCode || mostJobObj.orgJobCode;
                    v.jobName = v.orgJobName || mostJobObj.orgJobName;
                    v.jobTypeCode = v.orgJobTypeCode || mostJobObj.orgJobTypeCode;
                    v.jobTypeName = v.orgJobTypeName || mostJobObj.orgJobTypeName;
                  }
                }
              });
              store.changeData({
                data: {
                  pageType: 1,
                },
              });
            }}
          >
            {t('确定')}
          </FooterBtn>
        </Footer>
        <TimePicker
          infinite
          show={startShowPicker}
          selected={startTimeSelected}
          onCancel={() => this.setState({ startShowPicker: false })}
          onSelected={(timeArr) => {
            // 判断开始时间是否早于上次结束时间
            const isErrTime = defaultPageList.filter((v) => v.checked)
              // .some(v => v.lastAssignEndTime && new Date(v.lastAssignEndTime).getTime() > new Date(`${getDateText(timeArr)}:00`).getTime());
              .some((v) => v.lastAssignEndTime && new Date(v.lastAssignEndTime).getTime() > new Date(`${date} ${timeArr.join(':')}:00`).getTime());
            if (isErrTime) {
              Modal.error({ content: t('时间安排存在冲突') });
              this.setState({
                startShowPicker: false,
              });
              return;
            }
            const startTimeStr = `${date} ${timeArr.join(':')}`;
            const startTimeTxt = moment(startTimeStr, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD HH:mm');
            this.setState({
              startTimeSelected: timeArr,
              startShowPicker: false,
              startTimeText: startTimeTxt,
            });
          }}
        />
        <TimePicker
          infinite
          show={endShowPicker}
          selected={endTimeSelected}
          onCancel={() => this.setState({ endShowPicker: false })}
          onSelected={(timeArr) => {
            console.info('timeArr', timeArr);
            let isNextDay = false;
            // eslint-disable-next-line max-len
            if (timeArr[0] < startTimeSelected[0] || (timeArr[0] === startTimeSelected[0] && timeArr[1] === startTimeSelected[1])) {
              isNextDay = true;
            }
            const endTimeStr = `${date} ${timeArr.join(':')}`;
            const endTimeTxt = !isNextDay
              ? moment(endTimeStr, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD HH:mm')
              : moment(endTimeStr, 'YYYY-MM-DD HH:mm').add(1, 'days').format('YYYY-MM-DD HH:mm');
            this.setState({
              endTimeSelected: [...timeArr],
              endShowPicker: false,
              endTimeText: endTimeTxt,
            });
          }}
        />
      </div>
    );
  }
}

SchedulePage.propTypes = {
  tags: PropTypes.arrayOf(PropTypes.string),
  assignStartTime: PropTypes.string,
  assignEndTime: PropTypes.string,
  isOnline: PropTypes.bool,
  noWorkJobTypeCodeObj: PropTypes.shape(),
  jobInitObj: PropTypes.shape(),
  noJobNameList: PropTypes.arrayOf(PropTypes.shape),
  jobTypeListStr: PropTypes.string,
  defaultPageList: PropTypes.arrayOf(PropTypes.shape()),
  date: PropTypes.string,
  mostJobObj: PropTypes.shape(),
};

export default SchedulePage;
