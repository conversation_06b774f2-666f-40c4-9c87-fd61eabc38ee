.topBar {
  display: flex;
  background: #fff;
  justify-content: space-between;
  padding: 0 8px;
  color: #141737;
}
.topBar >span:first-of-type {
  flex: 1;
  word-break: break-all;
}
.topBar >span:last-of-type {
  width: 85px;
  text-align: right;
}
.listHeadWrap {
  display: flex;
  align-items: center;
  padding: 0 4px 0 10px;
}
.listHeadWrap>div {
  flex: 2;
}
div.spanBtnWrap {
  flex: 4;
  margin-top: -12px;
}
.pickItem {
  position: relative;
  padding: 6px 0;
  margin: 0 15px;
  border-top: 1px solid #E8EBF0;
  font-size: 16px;
}
.pickItem>span {
  color:#333e59;
}
.pickItem>div {
  color: #616161;
  min-height: 22px;
}
.pickItem>i {
  position: absolute;
  right: 0;
  top: 24px;
  font-size: 16px;
  color: rgb(179, 183, 193);
}
.checkedWrap {
  display: flex;
  height: 40px;
  align-items: center;
}
.checkedWrap>span {
  flex: 1;
  padding-left: 15px;
}
.pickersDiv {
  border-bottom: 1px solid #e8ebf0 !important;
}
.bar {
  display: flex;
  justify-content: space-between;
  padding: 0 10px 0 33px;
  background-color: rgb(230,232,241);
  color: black;
}
