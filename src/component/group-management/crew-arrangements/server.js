import { sendPostRequest } from '../../../lib/public-request';

// sendPostRequestApi: 统一修改接口url前缀
const baseUrl = `${process.env.WMS_INTERNAL_FRONT}/pda/staff/`;
const sendPostRequestApi = (argObj) => sendPostRequest(argObj, baseUrl);

// 查询组员安排
export const assignApi = (param) => sendPostRequestApi({
  url: 'assign',
  param,
});

// 提交组员安排数据
export const commitApi = (param) => sendPostRequestApi({
  url: 'commit',
  param,
});

// 根据部门获取岗位类型和岗位名称
export const queryJobDataApi = (param) => sendPostRequest({
  url: '/job/query_by_dept',
  param,
}, process.env.WMS_INTERNAL_FRONT);

// 提前结束
export const endEarlyApi = (param) => sendPostRequestApi({
  url: 'early_finish',
  param,
});

// 修改挑战目标
export const changeChallengeGoalApi = (param) => sendPostRequestApi({
  url: 'change_challenge_goal',
  param,
});

// 获取原组长管理小组
export const getAgentManagerGroupApi = (param) => sendPostRequestApi({
  url: 'get_agent_manager_group',
  param,
});

// 安排修改
export const arrangeModifyApi = (param) => sendPostRequest({
  url: '/member_time_management/modify_check',
  param,
}, process.env.WMS_INTERNAL_FRONT);

// 确认安排修改
export const arrangeModifySureApi = (param) => sendPostRequest({
  url: '/member_time_management/modify_sure',
  param,
}, process.env.WMS_INTERNAL_FRONT);

// 点名完成
export const rollCallFinishApi = (param) => sendPostRequestApi({
  url: 'roll_call_finish',
  param,
});

// 用户点名
export const userRollCallApi = (param) => sendPostRequestApi({
  url: 'user_roll_call',
  param,
});

// 获取当前系统时间
export const getSystemTimeApi = (param) => sendPostRequest({
  url: '/member_time_management/get_system_time',
  param,
}, process.env.WMS_INTERNAL_FRONT);
