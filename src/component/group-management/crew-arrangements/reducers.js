import assign from 'object-assign';
import moment from 'moment';
import { t } from '@shein-bbl/react';
import { select } from 'redux-saga/effects';
import Modal from '../../common/modal';
import { message } from '../../common';
import {
  assignApi,
  commitApi,
  queryJobDataApi,
  endEarlyApi,
  arrangeModifyApi,
  arrangeModifySureApi,
  rollCallFinishApi,
  changeChallengeGoalApi,
  userRollCallApi,
  getSystemTimeApi,
  getAgentManagerGroupApi,
} from './server';
import { syncModal } from '../../../lib/util';

const defaultState = {
  pageType: 1, // 1-组员安排首页，2-安排时间, 3-组员安排_提前结束页, 4-安排修改页面
  defaultPageObj: {}, // 首页基本信息
  defaultPageList: [], // 首页列表信息
  listAllChecked: false, // 首页列表是否全选中
  groupId: 0, // 小组ID
  tags: [], // 详情页tags数据
  assignStartTime: '', // 详情页安排开始时间
  assignEndTime: '', // 详情页安排结束时间
  isOnline: true, // 详情页是否在岗
  jobInitObj: {
    jobTypeCode: '', jobCode: '', jobName: '', jobTypeName: '',
  }, // 岗位初始值[时间安排页面]
  dutyPickerData: [], // 在岗下拉数据
  dutyPickerDataObj: {}, // 在岗value、label转换对象
  reasonPickerData: [], // 不在岗原因下拉数据
  reasonPickerDataObj: {}, // 不在岗原因value、label转换对象
  jobTypeList: [], // 岗位类型
  jobTypeListStr: '', // JSON化jobTypeList，用于后期设置默认值等
  noJobNameList: [], // 不在岗岗位名称下拉数据
  noWorkJobTypeCodeObj: {}, // 不在岗岗位类型
  postDataLoading: false, // 是否提交中
  endEarlyList: [], // 提前结束数组
  endEarlyAllChecked: false, // 提前结束页列表是否全选中
  endEarlyLoading: false, // 提前结束操作Loading
  endTimeShowPicker: false, // 是否显示结束时间TimePicker
  endTimeDatesArr: [], // 结束时间可选日期
  endTimeDateIdx: 0, // 默认当天
  endTimePickerDateSelected: [new Date().getHours(), new Date().getMinutes()], // Picker默认当前时分
  date: moment().format('YYYY-MM-DD'),
  dateList: [
    {
      value: moment().add(-1, 'days').format('YYYY-MM-DD'),
      label: moment().add(-1, 'days').format('YYYY-MM-DD'),
      key: 0,
    },
    {
      value: moment().format('YYYY-MM-DD'),
      label: moment().format('YYYY-MM-DD'),
      key: 1,
    },
    {
      value: moment().add(1, 'days').format('YYYY-MM-DD'),
      label: moment().add(1, 'days').format('YYYY-MM-DD'),
      key: 2,
    },
  ],
  showPopSheet: false,
  mostJobObj: null,
  dateList2: [
    moment().subtract(1, 'days').format('YYYY-MM-DD'),
    moment().format('YYYY-MM-DD'),
    moment().add(1, 'days').format('YYYY-MM-DD'),
  ],
  dateIdx: 1,
  arrangeModifyLoading: false,
  arrangeModifyList: [],
  rollCallDisabled: false,
  rollCallName: t('点名完成'),
  rollCallLoading: false,
  challengeGoal: '',
  rowId: '',
  userName: '',
  splicedName: '',
  minRollCallNum: 0, // 最小点名次数
  scrollRowNums: 0,
};

/**
 * 获取当前组人数最多的那个岗位对象
 * @param list
 * @returns {*}
 */
const getMostJobObj = (list) => {
  const defaultRes = {
    orgJobCode: '',
    orgJobName: '',
    orgJobTypeCode: '',
    orgJobTypeName: '',
  };
  const temp = {};
  for (let i = 0; i < list.length; i++) {
    const { orgJobCode, orgJobTypeCode } = list[i];
    if (orgJobCode && orgJobTypeCode) {
      const key = [orgJobCode, orgJobTypeCode].join(',');
      temp[key] = (temp[key] || 0) + 1;
    }
  }
  const keys = Object.keys(temp);
  if (!keys.length) {
    return defaultRes;
  }
  const mostKey = keys
    .reduce((prev, cur) => {
      if (temp[cur] > temp[prev]) {
        return cur;
      }
      return prev;
    });
  const tar = list.find((i) => [i.orgJobCode, i.orgJobTypeCode].join(',') === mostKey);
  return tar || defaultRes;
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  // 获取系统时间
  * getSystemTime(action, ctx) {
    // 获取原组长管理小组
    const resObj = yield getAgentManagerGroupApi();
    if (resObj.code === '0') {
      const [timeRes] = yield Promise.all([
        getSystemTimeApi({ groupId: resObj.info.groupId }),
      ]);
      if (timeRes.code === '0') {
        // 若有系统时间，则使用系统时间 date dateList dateList2
        if (timeRes.info && timeRes.info.systemTime) {
          // 按服务器时间请求列表数据
          yield ctx.init({ date: timeRes.info.systemTime });
          const nowDay = moment(timeRes.info.systemTime).format('YYYY-MM-DD');
          const preDay = moment(timeRes.info.systemTime).subtract(1, 'days').format('YYYY-MM-DD');
          const nextDay = moment(timeRes.info.systemTime).add(1, 'days').format('YYYY-MM-DD');
          yield ctx.changeData({
            data: {
              date: nowDay,
              dateList: [
                {
                  value: preDay,
                  label: preDay,
                  key: 0,
                },
                {
                  value: nowDay,
                  label: nowDay,
                  key: 1,
                },
                {
                  value: nextDay,
                  label: nextDay,
                  key: 2,
                },
              ],
              dateList2: [
                preDay,
                nowDay,
                nextDay,
              ],
            },
          });
        } else {
          // 按设备时间请求列表数据
          yield ctx.init({ date: moment().format('YYYY-MM-DD HH:mm:ss') });
        }
      } else {
        Modal.error({ content: timeRes.msg || t('获取系统时间报错') });
        // 按设备时间请求列表数据
        yield ctx.init({ date: moment().format('YYYY-MM-DD HH:mm:ss'), groupIdRps: resObj });
      }
    } else {
      Modal.error({ title: resObj.msg });
    }
  },
  * init(action, ctx) {
    // 获取原组长管理小组
    const resObj = yield (action.groupIdRps || getAgentManagerGroupApi());
    if (resObj.code === '0') {
      // 请求首页初始化数据和下拉数据
      const [res] = yield Promise.all([
        assignApi({ warehouseId: 1, date: action.date, groupId: resObj.info.groupId }),
      ]);
      if (res.code === '0') {
        // 页面数据
        const {
          assignStaffList,
          groupId,
          deptCode,
          allowCall,
          rollCallStatus,
          minRollCallNum,
          ...defaultPageObj
        } = res.info;
        // 最后一条未安排数据下标
        let lastNotScheduleIdx = -1;
        // 提前结束数组
        let endEarlyList = [];
        // 首页列表数组
        const defaultPageListArr = assignStaffList.map((v, idx) => {
          const isSchedule = !!(v.assignStartTime && v.assignEndTime);
          if (!isSchedule) {
            lastNotScheduleIdx = idx;
          } else {
            endEarlyList.push({ ...v, isWorking: v.isWorking === 1 });
          }
          return {
            ...v,
            isWorking: v.isWorking === 1,
            lastIsWorking: v.lastIsWorking === 1,
            disabled: isSchedule, // 初始安排了时间则代表禁用【界面显示不更改】
            isSchedule, // 是否已安排了时间【根据操作会发生改变】
            isRed: !v.orgJobCode, // 没有岗位安排则置为红色
          };
        });
        // isWorking：1 在岗，2：不在岗，将在岗的排在不在岗的前面
        const isWorkingList = endEarlyList.filter((item) => item.isWorking);
        const notWorkingList = endEarlyList.filter((item) => !item.isWorking);
        endEarlyList = [...isWorkingList, ...notWorkingList];

        yield ctx.changeData({ data: { mostJobObj: getMostJobObj(assignStaffList) } });

        // 如果存在已安排的数据，则给第一条安排的数据isFirstSchedule赋值true：用于渲染分隔操作栏
        if (defaultPageListArr[lastNotScheduleIdx + 1]) {
          defaultPageListArr[lastNotScheduleIdx + 1].isFirstSchedule = true;
        }
        yield ctx.changeData({
          data: {
            defaultPageObj,
            groupId,
            defaultPageList: defaultPageListArr,
            endEarlyList,
            rollCallDisabled: !allowCall,
            rollCallName: rollCallStatus === 1 ? t('已点名') : t('点名完成'),
            minRollCallNum,
          },
        });
        // 根据部门获取岗位类型和岗位名称
        yield ctx.queryJobDataByDept({ params: { deptCode } });
      } else {
        Modal.error({ content: res.msg });
      }
    } else {
      Modal.error({ title: resObj.msg });
    }
  },
  // 根据部门获取岗位类型和岗位名称
  * queryJobDataByDept(action, ctx) {
    const queryJobDataRes = yield queryJobDataApi(action.params);
    if (queryJobDataRes.code === '0') {
      // 处理岗位相关数据
      const jobTypeList = [];
      (queryJobDataRes.info.jobTypeJobRelRspList || []).forEach((v) => {
        const obj = {
          ...v,
          title: v.jobTypeName,
          children: (v.jobInfoList || []).map((i) => ({
            ...i,
            title: i.jobName,
            jobTypeCode: v.jobTypeCode,
            jobTypeName: v.jobTypeName,
          })),
        };
        jobTypeList.push(obj);
      });
      // 不在岗相关数据
      const noJobList = queryJobDataRes.info.noWorkJobTypeJobRelRspList;
      const noJobNameListArr = noJobList && noJobList[0] ? noJobList[0].jobInfoList || [] : [];
      const noJobNameList = [
        {
          items: noJobNameListArr.map((v) => ({ label: v.jobName, value: v.jobCode })),
        },
      ];
      const noWorkJobTypeCodeObj = noJobList && noJobList[0] ? noJobList[0] || {} : {};
      yield ctx.changeData({
        data: {
          jobTypeList,
          jobTypeListStr: JSON.stringify(jobTypeList),
          noJobNameList,
          noWorkJobTypeCodeObj,
        },
      });
    } else {
      yield ctx.changeData({
        data: {
          jobTypeList: [],
          jobTypeListStr: JSON.stringify([]),
          noJobNameList: [],
          noWorkJobTypeCodeObj: {},
        },
      });
    }
  },
  // 提交数据
  * postData(action, ctx) {
    yield ctx.changeData({
      data: {
        postDataLoading: true,
      },
    });
    const res = yield commitApi(action.data);
    yield ctx.changeData({
      data: {
        postDataLoading: false,
      },
    });
    if (res.code === '0') {
      message.success(t('提交成功'));
      const { date } = yield select((state) => state['group-management/crew-arrangements']);
      yield ctx.init({
        date: moment(`${date} ${moment().format('HH:mm:ss')}`).format('YYYY-MM-DD HH:mm:ss'),
      });
      yield ctx.changeData({
        data: {
          date,
        },
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 提前结束
  * endEarly(action, ctx) {
    yield ctx.changeData({
      data: {
        endEarlyLoading: true,
      },
    });
    const res = yield endEarlyApi(action.data);
    yield ctx.changeData({
      data: {
        endEarlyLoading: false,
      },
    });
    if (res.code === '0') {
      message.success(t('操作成功'));
      const { date } = yield select((state) => state['group-management/crew-arrangements']);
      yield ctx.init({
        date: moment(`${date} ${moment().format('HH:mm:ss')}`).format('YYYY-MM-DD HH:mm:ss'),
      });
      yield ctx.changeData({
        data: {
          date,
        },
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 安排修改
  * arrangeModify(action, ctx) {
    yield ctx.changeData({
      data: {
        arrangeModifyLoading: true,
      },
    });
    let res = yield arrangeModifyApi(action.params);
    yield ctx.changeData({
      data: {
        arrangeModifyLoading: false,
      },
    });
    if (res.code === '0') {
      // 表示需要二次确定修改
      if (res.info) {
        const re = yield new Promise((r) => (
          Modal.confirm({
            content: res.info,
            onOk: () => r('ok'),
            onCancel: () => r('cancel'),
          })
        ));
        if (re === 'cancel') {
          return;
        }
        // 调用确认修改
        res = yield arrangeModifySureApi(action.params);
      }
      if (res.code === '0') {
        message.success(t('安排修改成功'));
        const { date } = yield select((state) => state['group-management/crew-arrangements']);
        yield ctx.init({
          date: moment(`${date} ${moment().format('HH:mm:ss')}`).format('YYYY-MM-DD HH:mm:ss'),
        });
        yield ctx.changeData({
          data: {
            date,
            pageType: 3,
          },
        });
      } else {
        Modal.error({ content: res.msg });
      }
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 点名完成
  * rollCallFinish(action, ctx) {
    yield ctx.changeData({
      data: {
        rollCallLoading: true,
      },
    });
    const res = yield rollCallFinishApi(action.params);
    yield ctx.changeData({
      data: {
        rollCallLoading: false,
      },
    });
    if (res.code === '0') {
      message.success(t('点名完成成功'));
      const { date } = yield select((state) => state['group-management/crew-arrangements']);
      yield ctx.init({
        date: moment(`${date} ${moment().format('HH:mm:ss')}`).format('YYYY-MM-DD HH:mm:ss'),
      });
      yield ctx.changeData({
        data: {
          date,
          pageType: 3,
          rollCallDisabled: true,
          rollCallName: t('已点名'),
        },
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
  // 用户点名
  * userRollCall(action, ctx) {
    yield ctx.changeData({
      data: {
        rollCallLoading: true,
      },
    });
    const res = yield userRollCallApi(action.params);
    yield ctx.changeData({
      data: {
        rollCallLoading: false,
      },
    });
    if (res.code === '0') {
      message.success(`${t('{}点名成功', action.splicedName)}`);
      const { minRollCallNum } = res.info; // 最小点名次数
      const { endEarlyList } = yield select((state) => state['group-management/crew-arrangements']);
      // 取消当前勾选，勾选下一个
      const selectRowIndex = endEarlyList.findIndex((row) => row.checked);
      endEarlyList[selectRowIndex].checked = false;
      if (selectRowIndex < endEarlyList.length - 1 && endEarlyList[selectRowIndex + 1].isWorking) {
        endEarlyList[selectRowIndex + 1].checked = true;
      }
      yield ctx.changeData({
        data: {
          minRollCallNum,
          endEarlyList: [...endEarlyList],
          scrollRowNums: selectRowIndex + 1,
        },
      });
    } else {
      const syncStatus = yield syncModal({
        content: res.msg,
      }, 'error');
      if (syncStatus && res.code === '424322') {
        yield this.init();
        yield this.getSystemTime();
      }
    }
  },
  * changeChallengeGoal(action, ctx) {
    const res = yield changeChallengeGoalApi(action.params);
    if (res.code === '0') {
      message.success(t('修改挑战目标成功'));
      const { date, pageType } = yield select((state) => state['group-management/crew-arrangements']);
      yield ctx.init({
        date: moment(`${date} ${moment().format('HH:mm:ss')}`).format('YYYY-MM-DD HH:mm:ss'),
      });
      yield ctx.changeData({
        data: {
          date,
          pageType,
        },
      });
    } else {
      Modal.error({ content: res.msg });
    }
  },
};
