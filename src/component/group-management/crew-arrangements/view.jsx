import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { t, i18n } from '@shein-bbl/react';
import store from './reducers';
import DefaultPage from './jsx/default-page';
import SchedulePage from './jsx/schedule-page';
import EndEaelyPage from './jsx/end-early-page';
import ArrangeModifyPage from './jsx/arrange-modify-page';
import PopSheet from '../../common/pop-sheet';

class Container extends Component {
  componentDidMount() {
    store.getSystemTime();
  }

  render() {
    const {
      pageType,
      dateList,
      showPopSheet,
      date,
    } = this.props;
    let content;

    // 1-组员安排首页，2-安排时间, 3-组员安排_提前结束页, 4-安排修改页面
    switch (pageType) {
      case 1:
        content = <DefaultPage {...this.props} />;
        break;
      case 2:
        content = <SchedulePage {...this.props} />;
        break;
      case 3:
        content = <EndEaelyPage {...this.props} />;
        break;
      case 4:
        content = <ArrangeModifyPage {...this.props} />;
        break;
      default:
        content = <div>{t('组员安排')}-loading</div>;
        break;
    }
    return (
      <>
        {content}
        <PopSheet
          onClick={(v) => {
            if (v.value !== date) {
              store.init({
                date: moment(`${v.value} ${moment().format('HH:mm:ss')}`).format('YYYY-MM-DD HH:mm:ss'),
              });
            }
            store.changeData({ data: { date: v.value, showPopSheet: false } });
          }}
          onClose={() => {
            store.changeData({ data: { showPopSheet: false } });
          }}
          cancelBtn
          menus={dateList}
          show={showPopSheet}
        />
      </>
    );
  }
}

Container.propTypes = {
  pageType: PropTypes.number,
  date: PropTypes.string,
  dateList: PropTypes.arrayOf(PropTypes.shape()),
  showPopSheet: PropTypes.bool,
};

export default i18n(Container);
