import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import { LoadMore } from 'react-weui';
import {
  Header,
  Footer,
  FooterBtn,
  SearchInput, Gapbar, SelectList,
} from 'common';
import store from './reducers';

class Container extends Component {
  componentDidMount() {
    const { match: { params } } = this.props;
    store.init(params);
  }

  render() {
    const {
      dispatch,
      headerTitle,
      subWarehouseId,
      list,
      showSearch,
      searchText,
      initReady,
      redirectTo,
      status,
    } = this.props;
    // 初始加载数据
    if (!initReady) {
      return <LoadMore loading>{t('加载中...')}</LoadMore>;
    }
    return (
      <div>
        <Header
          title={headerTitle}
        />
        {
          showSearch && (
            <>
              <SearchInput
                value={searchText}
                onClear={() => store.changeData({ searchText: '' })}
                onChange={(e) => store.changeData({ searchText: e.target.value })}
                onSearch={() => store.handleSearch()}
              />
              <Gapbar />
            </>
          )
        }
        <SelectList
          empty={t('无子仓数据')}
          style={{ overflowY: 'auto', height: `calc(100vh - ${showSearch ? 170 : 120}px)` }}
          value={subWarehouseId}
          listKeys={['subWarehouseId', 'subWarehouseName']}
          list={list}
          onChange={(val) => {
            store.changeData({ subWarehouseId: val });
          }}
        />
        <Footer
          beforeBack={() => {
            dispatch(push('/main-menu'));
          }}
        >
          <FooterBtn
            disabled={!subWarehouseId}
            type="primary"
            onClick={() => {
              if (redirectTo) {
                if (status) {
                  dispatch(push(`${redirectTo}/${status}`));
                } else {
                  dispatch(push(`${redirectTo}`));
                }
              } else {
                dispatch(push('/sub-menu'));
              }
            }}
          >
            {t('确认')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  match: PropTypes.shape(),
  dispatch: PropTypes.func,
  headerTitle: PropTypes.string,
  subWarehouseId: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]),
  showSearch: PropTypes.bool,
  initReady: PropTypes.bool,
  searchText: PropTypes.string,
  list: PropTypes.arrayOf(PropTypes.shape()),
  redirectTo: PropTypes.string,
  status: PropTypes.string,
};

export default i18n(Container);
