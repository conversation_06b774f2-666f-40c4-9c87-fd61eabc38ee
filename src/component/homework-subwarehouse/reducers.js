import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { put } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import { getUsername, getWarehouseId } from 'lib/util';
import { homeworkSubWarehouseStorage } from 'lib/storage-new/index';
import { modal } from 'common';
import { getBindSubWarehouseApi } from './server';

const defaultState = {
  initReady: false, // 初始数据是否加载完成
  headerTitle: t('作业子仓'),
  allList: [], // 全部子仓下拉数据
  list: [], // 当前页面子仓展示数据，可能是过滤后的
  showSearch: false,
  subWarehouseId: '', // 作业子仓
  searchText: '', // 搜索值
  redirectTo: '', // 跳转页面
  status: '', // 跳转页面携带的状态值
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData: (draft, data) => {
    // 将当前页面state存储到sessionStorage中，用于子应用使用
    homeworkSubWarehouseStorage.setItem({ ...draft, ...data });
    assign(draft, data);
  },
  * init(params) {
    if (params?.redirectTo) {
      yield this.changeData({
        redirectTo: decodeURIComponent(params.redirectTo),
        status: params?.status,
      });
    }

    const { code, info, msg } = yield getBindSubWarehouseApi({
      id: getWarehouseId(),
      userName: getUsername(),
    });
    if (code === '0') {
      const list = info.data || [];
      // 若只有一个子仓，则直接帮用户选择并跳转子目录页
      if (list.length === 1) {
        yield this.changeData({
          subWarehouseId: list[0].subWarehouseId,
          allList: [...list],
          initReady: true,
        });
        if (params?.redirectTo) {
          if (params?.status) {
            yield put(push(`${decodeURIComponent(params?.redirectTo)}/${params?.status}`));
          } else {
            yield put(push(`${decodeURIComponent(params?.redirectTo)}`));
          }
        } else {
          yield put(push('/sub-menu'));
        }
        return;
      }
      yield this.changeData({
        list,
        initReady: true,
        allList: [...list],
        showSearch: list.length > 8,
      });
    } else {
      modal.error({ content: msg });
    }
  },
  * handleSearch() {
    const { allList, subWarehouseId, searchText } = yield this.state;
    // searchText为空则不过滤，否则模糊过滤；并将选中元素放到最前边
    let first = null;
    const searchList = allList.filter((obj) => {
      if (subWarehouseId === obj.subWarehouseId) {
        first = obj;
        return false;
      }
      return String(obj.subWarehouseName).includes(searchText);
    });
    if (first) {
      searchList.unshift(first);
    }
    // 将选中的放到最前面
    yield this.changeData({
      list: searchList,
    });
  },
};
