import assign from 'object-assign';
import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { select } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import {
  SESSION_MOT_IS_SHOW_RECORD, LOCAL_USER, LOCAL_WAREHOUSE_NAME, LOCAL_WAREHOUSE,
  LOCAL_WHETHER_SHOW_PROFIT, LOCAL_IS_REMEMBER, LOCAL_USER_MENU,
} from 'lib/storage';
import { logout, getProfitInfoApi } from './server';
import { queryWarehouse } from '../login/server';
import Modal from '../common/modal';
import { getLang } from '../js';
import { langPickerGroup } from '../language-enum';
import {
  clearSomeCookie, getMenuByLevel, getUsername, getWarehouseId, clearLoginTimeoutInterval,
} from '../../lib/util';
import { message, modal } from '../common';
import { dbHelper } from '../../lib/indexed-db-helper';

const defaultState = {
  username: '',
  formatName: '',
  enName: '',
  dataLoading: 1,
  showLangPicker: false,
  langPickerGroup,
  lang: getLang(),
  showDrawer: false, // 是否显示左侧抽屉，默认不展开
  warehouseName: '', // 用户绑定的仓库的名称
  showLoading: false, // 退出登录时的loading
  isShowQuickEntryPage: false, // 是否显示设置快捷入口界面
  isShowQuickEntry: false, // 快捷入口开关
  isShowRecord: sessionStorage.getItem(SESSION_MOT_IS_SHOW_RECORD) === 'true', // 调试日志开关
  quickEntryList: [], // 快捷菜单数组
  isOnline: true,
  redDottedList: [], // Grids菜单小红点url数组
  menuList: [],
  showJiugongge: true, // 左侧栏显示一分九宫格
  openJiugongge: true, // 左侧栏开启一分九宫格
  showSplitABFrame: false, // 左侧栏显示二分AB架模式
  openSplitABFrame: false, // 左侧栏开启二分AB架模式
  whetherShowProfit: '', // 是否展示收益信息
  todayProfit: '', // 今日收益
  targetProcess: '', // 目标进度
};

export default {
  defaultState,
  $init: (draft) => {
    let username = '';
    let formatName = '';
    let enName = '';
    if (localStorage.getItem(LOCAL_USER)) {
      const user = JSON.parse(localStorage.getItem(LOCAL_USER) || '{}');
      username = user.username || '';
      formatName = user.formatName || '';
      enName = user.enName || '';
    }
    assign(draft, defaultState, {
      username: username || '',
      formatName: formatName || '',
      enName: enName || '',
      warehouseName: localStorage.getItem(LOCAL_WAREHOUSE_NAME) || '',
      menuList: getMenuByLevel(1),
    });
  },
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * getProfitInfo(action, ctx) {
    try {
      const res = yield getProfitInfoApi({
        userName: getUsername(),
        warehouseId: getWarehouseId(),
      });
      if (res.code === '0') {
        yield ctx.changeData({
          data: {
            whetherShowProfit: res.info.whetherShowProfit.toString(),
            todayProfit: res.info.todayProfit,
            targetProcess: res.info.targetProcess,
          },
        });
        localStorage.setItem(LOCAL_WHETHER_SHOW_PROFIT, res.info.whetherShowProfit.toString());
      } else {
        console.log(res.msg);
      }
    } catch (e) {
      console.error(e);
    }
  },
  // 快捷菜单初始化数据
  * initQuickData(action, ctx) {
    const {
      quickEntryLang,
      isShowQuickEntry,
      quickEntryList,
      childMenusObj,
    } = yield select((state) => state.nav);
    // 若语言环境，quickEntryList与当前menus不一致，则将quickEntryList对应标题改成和menus一致
    if (quickEntryLang !== getLang()) {
      quickEntryList.forEach((quickEntry) => {
        if (childMenusObj[quickEntry.rule]) {
          quickEntry.title = childMenusObj[quickEntry.rule].title;
        }
      });
    }
    // 获取所有二级目录
    const menuLevel2 = getMenuByLevel(2);
    // 获取所有三级目录
    const menuLevel3 = getMenuByLevel(3);
    // 过滤出有权限的菜单节点
    // eslint-disable-next-line max-len
    const filters = quickEntryList.filter((q) => [...menuLevel2, ...menuLevel3].find((m) => m.rule === q.rule));

    yield ctx.changeData({
      data: {
        isShowQuickEntry,
        quickEntryList: filters || [],
      },
    });
  },
  * init(action, ctx) {
    // 执行快捷菜单初始化数据
    yield ctx.initQuickData();
    // 获取今日收益和目标
    yield ctx.getProfitInfo();
  },
  * logout(action, ctx, put) {
    markStatus('dataLoading'); // 搜索loading状态
    const res = yield logout();
    if (res.code === '0') {
      // 跳转至登录页面，清除sessionId
      // 后端会清除 无需前端清除
      // window.document.cookie = 'PHPSESSID=\'\'';
      // if (!localStorage.getItem(LOCAL_IS_REMEMBER)) {
      //   if (localStorage.getItem(LOCAL_USER)) {
      //     const user = JSON.parse(localStorage.getItem(LOCAL_USER) || '{}');
      //     user.username = '';
      //     user.password = '';
      //     user.formatName = '';
      //     user.enName = '';
      //     user.userNo = '';
      //     localStorage.setItem(LOCAL_USER, JSON.stringify(user));
      //   }
      // }
      if (localStorage.getItem(LOCAL_USER)) {
        const user = JSON.parse(localStorage.getItem(LOCAL_USER) || '{}');
        user.username = '';
        user.password = '';
        user.formatName = '';
        user.enName = '';
        user.userNo = '';
        localStorage.setItem(LOCAL_USER, JSON.stringify(user));
      }
      // 清空存储userMenu
      dbHelper?.closeAndClear();
      localStorage.removeItem(LOCAL_USER_MENU);
      localStorage.setItem(LOCAL_WAREHOUSE_NAME, '');
      localStorage.setItem(LOCAL_WAREHOUSE, '');
      action.hideDrawer();
      clearSomeCookie();
      yield put(push('/login'));
      window.location.reload();
      // 退出登录，清除轮询是否登录过期的的setInterval
      clearLoginTimeoutInterval();
    } else {
      // 提示错误
      Modal.error({ content: res.msg || t('退出登录失败') });
    }
  },
  * goToSelectWarehouse(action, ctx, put) {
    // 当前用户绑定2个以上仓库时才跳转到切换仓库页面
    const res = yield queryWarehouse();
    if (res.code === '0') {
      if (res.info?.warehouseList?.length >= 2) {
        yield put(push('/login/select-warehouse'));
      }
    } else {
      Modal.error({
        content: res.msg,
      });
    }
  },
  // 清除localStorage、cacheStorage、indexedDB、Cookies、Service workers的方法
  * clearCache(action) {
    const status = yield new Promise((r) => {
      modal.confirm({
        content: t('清除缓存后需要重新登录'),
        onOk: () => r('ok'),
        onCancel: () => r('cancel'),
      });
    });
    if (status === 'ok') {
      markStatus('dataLoading'); // 搜索loading状态
      // 清除localStorage
      // 参考文档：https://developer.mozilla.org/en-US/docs/Web/API/Window/localStorage
      localStorage.clear();
      // 清除sessionStorage
      // 参考文档：https://developer.mozilla.org/en-US/docs/Web/API/Window/sessionStorage
      // 在关闭窗口或标签页之后就会删除这些数据,所以可以不用手动清除
      sessionStorage.clear();
      // 清除cache storage
      // 参考文档：https://developer.mozilla.org/en-US/docs/Web/API/CacheStorage
      // CacheStorage 是 Cache 对象存储的接口，可以通过window.caches获取 (https状态下有效)
      if (window.caches) {
        const keys = yield caches.keys();
        keys.forEach((key) => (caches.delete(key)));
      }
      // 清除indexedDB
      // 参考文档：https://stackoverflow.com/questions/9384128/how-to-delete-indexeddb
      if (window.indexedDB) {
        if (typeof indexedDB.databases === 'function') {
          const dbs = yield indexedDB.databases();
          dbs.forEach((db) => { indexedDB.deleteDatabase(db.name); });
        }
      }
      // 清除serviceWorker
      // 参考文档：https://stackoverflow.com/questions/33704791/how-do-i-uninstall-a-service-worker
      if (window.navigator && navigator.serviceWorker) {
        const registrations = yield navigator.serviceWorker.getRegistrations();
        registrations.forEach((registration) => { registration.unregister(); });
      }
      // 清除cookie
      // 参考文档：https://stackoverflow.com/questions/179355/clearing-all-cookies-with-javascript
      // HttpOnly的无法删除，因为HttpOnly会禁止js对cookie的访问
      const cookies = document.cookie.split('; ');
      for (let c = 0; c < cookies.length; c++) {
        const d = window.location.hostname.split('.');
        while (d.length > 0) {
          const cookieBase = `${encodeURIComponent(cookies[c].split(';')[0].split('=')[0])}=; expires=Thu, 01-Jan-1970 00:00:01 GMT; domain=${d.join('.')} ;path=`;
          const p = window.location.pathname.split('/');
          document.cookie = `${cookieBase}/`;
          while (p.length > 0) {
            document.cookie = cookieBase + p.join('/');
            p.pop();
          }
          d.shift();
        }
      }
      window.open('#/login', '_self');
      window.location.reload();
      action.hideDrawer();
      message.success(t('缓存清除成功，请重新登录～'));
    }
  },
};
