import React, { Component } from 'react';
import {
  Toast,
} from 'react-weui/build/packages';
import { i18n, t } from '@shein-bbl/react';
import classnames from 'classnames';
import Icon from '@shein-components/Icon';
import PropTypes from 'prop-types';
import { SESSION_SCROLL_VIEW } from 'lib/storage';
import { Menus, IconTitleList } from '../common';
import styles from './style.css';
import store from './reducers';
import DrawerPage from '../drawer-page/view';
import QuickEntryPage from './quick-entry-page';
import navStore from '../nav/reducers';
import Profit from './profit';

class Container extends Component {
  // eslint-disable-next-line react/no-deprecated
  componentWillMount() {
    store.init();
    // 回主菜单和子菜单时，清空指引展示
    navStore.changeData({ data: { showIntro: false, pageStore: null } });
    setTimeout(() => {
      document.getElementById('scrollView')?.scroll(0, sessionStorage.getItem(SESSION_SCROLL_VIEW));
    }, 0);
  }

  componentWillUnmount() {
    sessionStorage.setItem(SESSION_SCROLL_VIEW, document.getElementById('scrollView')?.scrollTop || 0);
  }

  // 此页面作为首页
  render() {
    const {
      // enName,
      enName,
      warehouseName,
      showDrawer,
      showLoading,
      isShowQuickEntryPage,
      isShowQuickEntry,
      quickEntryList,
      isOnline,
      redDottedList,
      menuList,
    } = this.props;

    // 显示设置快捷入口界面
    if (isShowQuickEntryPage) {
      return <QuickEntryPage />;
    }

    return (
      <div
        className={showDrawer ? styles.hideScroll : ''}
        style={{ marginBottom: 0, background: '#FAFAFA' }}
      >
        <div className={styles.containerTop}>
          <div className={styles.containerBg}>
            <div
              className={styles.user}
              onClick={() => {
                store.changeData({ data: { showDrawer: !showDrawer } });
              }}
            >
              <Icon className={styles.avatar} name="shein-s" />
            </div>
            <div className={styles.containerInfo}>
              <div className={styles.userName}>
                <span
                  className={classnames(
                    styles.lineIcon,
                    isOnline ? styles.onlineIcon : styles.offlineIcon,
                  )}
                >
                  {enName}
                </span>
              </div>
              <div
                className={styles.warehouseName}
                onClick={() => store.goToSelectWarehouse()}
              >
                {warehouseName}
              </div>
            </div>
          </div>
          <div
            className={styles.bannerContainer}
            style={{ width: window.innerWidth - 16 }}
          >
            banner
          </div>
        </div>
        <Profit {...this.props} />
        <div className={styles.scrollCont} id="scrollView">
          {
            isShowQuickEntry && (
              <div style={{ padding: '0 15px 10px 15px' }}>
                <div className={styles.quickEntryTitle}>
                  <span>{t('快捷入口')}</span>
                  <span
                    className={styles.quickEntryTitleRight}
                    onClick={() => {
                      store.changeData({ data: { isShowQuickEntryPage: true } });
                    }}
                  >
                    {t('设置快捷')}
                    <Icon name="arr-right" style={{ fontSize: 12, marginLeft: 4 }} />
                  </span>
                </div>
                <div className={styles.quickEntryCont}>
                  {
                    (quickEntryList || []).length ? (
                      <div className={styles.quickEntryList}>
                        <IconTitleList dataSource={quickEntryList} />
                      </div>
                    ) : (
                      <div className={styles.quickEntryEmpty}>
                        {t('暂无内容，请前去设置添加')}
                      </div>
                    )
                  }
                </div>
                <div className={styles.quickEntryTitle}>
                  <span>{t('功能列表')}</span>
                </div>
              </div>
            )
          }
          <div style={{ padding: '5px 15px 15px 15px' }}>
            <Menus menuList={menuList} redDottedList={redDottedList} />
          </div>
        </div>
        <DrawerPage
          show={showDrawer}
          {...this.props}
        />
        {showLoading && <Toast icon="loading" show>{t('正在退出')}</Toast>}
      </div>
    );
  }
}

Container.propTypes = {
  // dispatch: PropTypes.func.isRequired,
  showDrawer: PropTypes.bool.isRequired,
  enName: PropTypes.string.isRequired,
  warehouseName: PropTypes.string.isRequired,
  showLoading: PropTypes.bool.isRequired,
  isShowQuickEntry: PropTypes.bool.isRequired,
  isShowQuickEntryPage: PropTypes.bool.isRequired,
  quickEntryList: PropTypes.arrayOf(PropTypes.shape),
  isOnline: PropTypes.bool.isRequired,
  redDottedList: PropTypes.arrayOf(PropTypes.string),
  menuList: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Container);
