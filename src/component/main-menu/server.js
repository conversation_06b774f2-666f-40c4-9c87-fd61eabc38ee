import { sendGetRequest, sendPostRequest } from '../../lib/public-request';

// 退出登录接口(GET方式)
export const logout = () => sendGetRequest({
  url: '/pda/logout',
}, process.env.WAS_FRONT);

// 获取权限和红点接口
export const getRedDottedListApi = (param) => sendPostRequest({
  url: '/pda/check_red',
  param,
}, process.env.WMS_INTERNAL);

// 首页展示收益和进度
export const getProfitInfoApi = (param) => sendPostRequest({
  url: '/profit_daily/main_info',
  param,
}, process.env.WMS_INTERNAL_FRONT);
