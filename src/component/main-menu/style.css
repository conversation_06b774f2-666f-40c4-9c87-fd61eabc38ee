.containerTop {
  position: relative;
  height: 55px;
  color: #ffffff;
}
.containerBg {
  display: flex;
  /*background: #197AFA;*/
  background: #0059CE;
  /*height: 160px;*/
  height: 55px;
  /*border-bottom-left-radius: 20%;
  border-bottom-right-radius: 20%;*/
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.user {
  position: relative;
  width: 36px;
  height: 36px;
  margin: 0px 10px;
  align-self: center;
  border-radius: 50%;
  overflow: hidden;
  background-color: #ffffff;
}

.containerInfo {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.userName, .warehouseName {
  font-size: 16px;
}

.avatar {
  position: absolute;
  font-size: 36px !important;
  left: 0;
  top: 0;
  color: #bde2ff;
}

.bannerContainer {
  display: none;
  width: 304px;
  height: 127px;
  border-radius: 8px;
  background: #FFFFFF;
  position: absolute;
  top: 56px;
  left: 8px;
  box-shadow:0px 2px 4px 0px rgba(25,122,250,0.15);
}

/* .user {
  position: absolute;
  top: 16px;
  left: 16px;
  font-size: 16px;
  min-width: 80px;
  min-height: 30px;
}
.language {
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 14px;
}
.warehouse {
  position: absolute;
  right: 15px;
  top: 16px;
  font-size: 12px;
} */



/*左侧抽屉样式*/
.drawerContainer {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
}

.drawerPage{
  position: absolute;
  left: 0;
  top: 0;
  z-index: 20;
  height: 100%;
  background-color: #ffffff;
}
.drawerMask{
  position: absolute;
  left: 0;
  top: 0;
  z-index: 10;
  width: 100%;
  height: 100%;
  background:rgba(20,23,55,0.3);
}

.logoutItem {
  width:100%;
  height:44px;
  line-height: 44px;
  background:rgba(255,255,255,1);
  box-shadow:0px 1px 0px 0px rgba(232,235,240,1);
  color: #333e59;
  text-align: center;
  position: absolute;
  bottom: 0;
  border-top: 1px solid #E8EBF0;
}

/*展开左侧抽屉时，要禁掉下方主页面的滚动条*/
.hideScroll {
  height: 100%;
  overflow: hidden;
}

.chooseLang {
  margin-top: 16px;
  height: 44px;
  box-shadow:0px 1px 0px 0px rgba(232,235,240,1);
  position: relative;
}

.chooseLang span {
  margin-left: 15px;
  line-height: 44px;
}

.chooseLang img {
  position: absolute;
  right: 15px;
  top: 16px;
}

.drawerPage .username {
  position: absolute;
  width: 100%;
  height: 24px;
  bottom: 44px;
  left: 15px;
  color: #ffffff;
  font-size: 14px;
  line-height: 56px;
}

.languagePicker {
  position: absolute;
  top: 144px;
  height: 100%;
  width: 100%;
  z-index: 10;
  background-color: #ffffff;
}

.languagePicker img {
  width: 16px;
  height: 16px;
}

.avatarWrap {
  width: 24px;
  height: 24px;
  background-color: #ffffff;
  border-radius: 50%;
  float: left;
  position: relative;
  margin-right: 5px;
  overflow: hidden;
}

.avatarWrapBig {
  width: 56px;
  height: 56px;
}

.avatarBig {
  font-size: 32px !important;
  left: 12px;
  top: 12px;
}

.drawerPage .arrLeft {
  color: #ffffff;
  margin-right: 5px;
  font-size: 14px;
}

/*快捷入口显示相关样式*/
.scrollCont{
  padding-top: 5px;
  box-sizing: border-box;
  height: calc(100vh - 85px);
  overflow-y: auto;
}
.quickEntryTitle{
  height:22px;
  line-height:22px;
  position: relative;
}
.quickEntryTitle span:first-of-type {
  font-size:16px;
  font-weight:600;
  color:rgba(51,62,89,1);
}
.quickEntryTitle span.quickEntryTitleRight {
  position: absolute;
  right: 0;
  font-size:14px;
  font-weight:400;
  color:rgba(114,119,131,1);
  padding-left: 10px;
}
.quickEntryCont{
  padding: 10px 0 20px 0;
}
.quickEntryEmpty {
  padding: 10px 0;
  text-align: center;
  font-size:14px;
  font-weight:400;
  color:rgba(160,160,160,1);
}
.quickEntryList{
  background:rgba(255,255,255,1);
  border-radius:8px;
}

.quickEntryPage{
  height: calc(100vh - 104px);
  overflow-y: auto;
}
.quickEntryPageTop{
  padding: 10px 15px;
}
.quickEntryTip {
  font-size:12px;
  font-weight:400;
  color:rgba(233,118,59,1);
}
.quickEntryListWrap {
  background:rgba(255,255,255,1);
  border-radius:8px;
  margin: 10px 0;
}
.quickEntryListEmpty {
  padding: 20px 5px;
  text-align: center;
  font-size:14px;
  font-weight:400;
  color:rgba(151,151,151,1);
}
.quickEntryListEmpty:before {
  content: '';
  display: inline-block;
  vertical-align: middle;
  width:32px;
  height:32px;
  background:rgba(241,241,241,1);
  border-radius:8px;
  border:1px dashed rgba(198,198,198,1);
  margin-right: 8px;
}

.lineIcon:after {
  content: "";
  display: inline-block;
  margin-left: 4px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.onlineIcon:after {
  background-color: #58ae2f;
}
.offlineIcon:after {
  background-color: #afb2b9;
}

.profitBox {
  padding: 5px;
  font-size: 12px;
  display: flex;
  background-color: #ffffff;
}

.profitItem {
  flex: 1;
  text-align: center;
}
