import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import style from './style.css';
import { addPlus } from '../../lib/util';

const Profit = (props) => {
  const {
    dispatch,
    todayProfit,
    targetProcess,
    whetherShowProfit,
  } = props;

  // 不展示收益
  if (whetherShowProfit !== '2') {
    return null;
  }
  // 根据进度显示颜色
  const color = +targetProcess >= 100 ? '#FF9900' : '#333333';

  return (
    <div
      className={style.profitBox}
      onClick={() => dispatch(push('/profit'))}
    >
      <div className={style.profitItem}>
        {t('今日收益')}：
        <span style={{ color }}>{addPlus(todayProfit)}</span>
      </div>
      <div className={style.profitItem}>
        {targetProcess || targetProcess === 0 ? (
          <span>
            {t('目标进度')}：<span style={{ color }}>{targetProcess}%</span>
          </span>
        ) : (
          <span style={{ color: '#3399FE' }}>{t('请点击设定今日目标')}</span>
        )}
      </div>
    </div>
  );
};
Profit.propTypes = {
  dispatch: PropTypes.func.isRequired,
  todayProfit: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  targetProcess: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  whetherShowProfit: PropTypes.string.isRequired,
};

export default Profit;
