import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { connect } from 'react-redux';
import {
  <PERSON><PERSON>,
  Header,
  Accordion,
  IconTitleList,
  FooterBtn,
  modal,
} from '../common';
import { getIconName } from '../common/constant';
import store from './reducers';
import navStore from '../nav/reducers';
import styles from './style.css';
import { getMenuByLevel } from '../../lib/util';

class QuickEntryPage extends React.Component {
  constructor(props) {
    super(props);
    const { quickEntryList } = props;
    this.state = {
      quickEntryList: JSON.parse(JSON.stringify(quickEntryList)),
      quickEntryListStr: JSON.stringify(quickEntryList),
    };
  }

  render() {
    const menus = getMenuByLevel(2);
    const { quickEntryList, quickEntryListStr } = this.state;
    // 判断是否编辑过
    const isEditChanged = quickEntryListStr !== JSON.stringify(quickEntryList);
    let accordionData = menus && menus.length
      ? JSON.parse(JSON.stringify(menus)) : [];
    const subCheckedArr = []; // 用于过滤quickEntryList，防止取消该用户某菜单权限后，还能看到快捷入口
    // 给菜单数组加上赋值：iconName，是否选中
    accordionData.forEach((obj) => {
      const iconName = getIconName(obj.rule.split('/new-pda/').pop());
      obj.iconName = iconName;
      // 增加二级级菜单勾选
      obj.parentIconName = iconName;
      if (quickEntryList.some((i) => obj.rule && i.rule === obj.rule)) {
        obj.isChecked = true;
        subCheckedArr.push(obj.rule);
      } else {
        obj.isChecked = false;
      }
      // 三级菜单勾选
      obj.children.forEach((sub) => {
        sub.parentIconName = iconName;
        if (quickEntryList.some(v => sub.rule && v.rule === sub.rule)) {
          sub.isChecked = true;
          subCheckedArr.push(sub.rule);
        } else {
          sub.isChecked = false;
        }
      });
    });
    accordionData = JSON.parse(JSON.stringify(accordionData));
    // 快捷菜单入口过滤掉无权限入口
    if ((quickEntryList || []).length > (subCheckedArr || []).length) {
      const quickEntryListArr = quickEntryList.filter(v => subCheckedArr.some(r => r === v.rule));
      this.setState({ quickEntryList: [...quickEntryListArr] });
    }



    return (
      <div style={{ backgroundColor: '#fafafa' }}>
        <Header title={t('设置快捷')} homeIcon={false} />
        <div className={styles.quickEntryPage}>
          <div className={styles.quickEntryPageTop}>
            <div className={styles.quickEntryTip}>*{t('快捷入口最多设置6个。')}</div>
            <div className={styles.quickEntryListWrap}>
              {
                (quickEntryList || []).length ? (
                  <IconTitleList
                    dataSource={quickEntryList}
                    editable
                    onClick={(obj, idx) => {
                      quickEntryList.splice(idx, 1);
                      this.setState({ quickEntryList: [...quickEntryList] });
                    }}
                  />
                ) : (
                  <div className={styles.quickEntryListEmpty}>
                    {t('暂无内容，请在下面设置添加')}
                  </div>
                )
              }
            </div>
            <div className={styles.quickEntryTitle}>
              <span>{t('快捷入口设置')}</span>
            </div>
          </div>
          <Accordion
            dataSource={accordionData}
            onSelected={(arr) => {
              console.info('----', arr)
              const quickEntryListArr = (arr || []).map((obj) => {
                // 只存储必要的字段到云服务器
                const { rule, title, parentIconName } = obj;
                // 异常处理使用特殊icon和背景色
                if (['/new-pda/inbound-manage/location-abnormal-upload'].includes(rule)) {
                  return {
                    rule,
                    title,
                    iconName: 'jubaoyichang',
                    iconStyle: {
                      background: 'linear-gradient(136deg,#FF7756,#E21E54)',
                    },
                  };
                } else {
                  return { rule, title, iconName: parentIconName };
                }
              });
              this.setState({ quickEntryList: quickEntryListArr });
            }}
            expendOne
            multiple
            badge
            limitNum={6}
            limitNumTip={t('快捷入口最多设置6个。')}
          />
        </div>
        <Footer
          beforeBack={() => {
            // 若用户进出前后没改变菜单列表，则直接返回
            if (!isEditChanged) {
              store.changeData({
                data: {
                  isShowQuickEntryPage: false,
                },
              });
              return;
            }
            // 若用户进出前后改变菜单列表，则弹窗确认是否保存；再返回
            modal.confirm({
              content: t('是否保存已编辑的内容?'),
              onOk: () => {
                // 存储到云服务器
                navStore.changeData({ data: { quickEntryList: [...quickEntryList], quickEntryObjKey: 'quickEntryList' } });
                store.changeData({
                  data: {
                    isShowQuickEntryPage: false,
                    quickEntryList: [...quickEntryList],
                  },
                });
              },
              onCancel: () => {
                store.changeData({
                  data: {
                    isShowQuickEntryPage: false,
                  },
                });
              },
            });
          }}
        >
          <FooterBtn
            disabled={!isEditChanged}
            onClick={() => {
              // 存储到云服务器
              navStore.changeData({ data: { quickEntryList: [...quickEntryList], quickEntryObjKey: 'quickEntryList' } });
              store.changeData({
                data: {
                  isShowQuickEntryPage: false,
                  quickEntryList: [...quickEntryList],
                },
              });
            }}
          >{t('完成')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

QuickEntryPage.propTypes = {
  quickEntryList: PropTypes.arrayOf(PropTypes.shape),
};

const NavProps = state => state.nav;

export default connect(NavProps)(QuickEntryPage);
