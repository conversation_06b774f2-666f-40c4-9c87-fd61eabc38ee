import React from 'react';
import { t } from '@shein-bbl/react';
import { classFocus, getHeaderTitle } from 'lib/util';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import { modal, message } from 'common';
import {
  getCurrentContainerAPI,
  getCurrentHangUpAPI,
  handleMergeAPI,
  handleMergeConfirmAPI,
  emptyBoxAPI,
  closeBoxAPI,
  hangUpListAPI,
  hangUpListConfirmAPI,
  hangUpBoxAPI,
  getDetailAPI,
  getGoodsAPI,
  bindAPI,
} from './server';

const defaultState = {
  initLoading: 1, // 页面初始化 loading  0加载中 1加载成功 2加载失败
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  headerTitle: '', // 页面标题：统一从后端接口获取
  currentPage: 1, // 1 初始页面info，2 分波次页面content，3 明细页面detail

  // ===info===
  containerCode: '', // 分波中的合并周转箱
  infoMergeContainerCode: '', // 合并周转箱
  containerList: [], // 挂起的合并周转箱list

  // ===content===
  mergeContainerCode: '', // 合并周转箱
  goodsSnPrint: '', // 商品条码
  sowingContainerCode: '', // 分波周转箱号
  sowingNum: 0, // 已分
  watingSowingNum: 0, // 待分
  mergeTaskCode: '', // 合并任务号
  content: {},
  sowingDisabled: true, // 分波周转箱号 禁用
  goodsDisabled: false, // 商品条码 禁用

  // ===detail===
  detailNav: 0, // 0 未分波，1 已分波
  unsowList: [], // 未分波
  sowedList: [], // 已分波
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(state, data) {
    Object.assign(state, data);
  },
  * init() {
    yield this.changeData({ headerTitle: getHeaderTitle() || t('分波次') });
    markStatus('initLoading');
    const { code, info, msg } = yield getCurrentContainerAPI();
    if (code === '0') {
      yield this.changeData({
        containerCode: info || '',
      });
      classFocus('infoMergeContainerCode');
    } else {
      modal.error({
        content: msg,
      });
    }

    yield this.getCurrentHangUp();
  },

  /**
   * 获取挂起的合并周转箱
   */
  * getCurrentHangUp() {
    markStatus('initLoading');
    const { code, info, msg } = yield getCurrentHangUpAPI();
    if (code === '0') {
      yield this.changeData({
        containerList: info?.containerList || [],
      });
      classFocus('infoMergeContainerCode');
    } else {
      modal.error({
        content: msg,
      });
    }
  },

  /**
   * 回车-合并周转箱
   * @param containerCode 分波中的合并周转箱
   */
  * handleMerge({ containerCode }) {
    const { infoMergeContainerCode } = yield '';
    const param = {
      mergeContainerCode: containerCode || infoMergeContainerCode,
    };
    yield this.changeData({ dataLoading: 0 });
    const { code, info, msg } = yield handleMergeAPI(param);
    if (code === '0') {
      yield this.changeData({ dataLoading: 1 });
      yield this.handleResultType({ info, type: 1 });
    } else {
      yield this.changeData({ dataLoading: 2 });
      modal.error({
        content: msg,
      });
      yield this.changeData({
        infoMergeContainerCode: '',
      });
      classFocus('infoMergeContainerCode');
    }
  },

  /**
   * 处理后端返回resultType
   * @param info 后端返回的info
   * @param type, 回车-合并周转箱 type为1，点击挂起的合并周转箱 type为0
   */
  * handleResultType({ info, type }) {
    const { mergeTaskCode } = yield '';
    if (info.resultType === 1) {
    // 1 弹窗提示-存在分波中合并周转箱
      const status = yield new Promise((r) => modal.confirm({
        modalBlurInput: true,
        title: t('请先完成分波中的合并周转箱{}', info.mergeContainerCode),
        buttons: [{
          type: 'primary',
          label: t('确定'),
          onClick: () => (r(true)),
        }],
      }));
      if (status) {
        yield this.changeData({
          infoMergeContainerCode: '',
        });
        classFocus('infoMergeContainerCode');
      }
    } else if (info.resultType === 2) {
      // 2 进入分波执行页面
      yield this.changeData({
        sowingNum: info.sowingNum, // 已分
        watingSowingNum: info.watingSowingNum, // 待分
        mergeContainerCode: info.mergeContainerCode, // 合并周转箱箱号
        mergeTaskCode: info.mergeTaskCode || mergeTaskCode,
        currentPage: 2,
      });
      classFocus('goodsSnPrint');
    } else if (info.resultType === 3) {
      // 3 弹窗提示-该拣货箱含n个任务商品,确认后开始分波
      const status = yield new Promise((r) => modal.confirm({
        modalBlurInput: true,
        title: t('该拣货箱含{}个任务商品,确认后开始分波！', info.goodsNum),
        onOk: () => r(true),
        onCancel: () => r(false),
      }));
      if (status) {
        const param = {
          mergeTaskCode: info.mergeTaskCode,
          mergeContainerCode: info.mergeContainerCode,
        };
        const API = type ? handleMergeConfirmAPI : hangUpListConfirmAPI;
        const res = yield API(param);
        if (res.code === '0') {
          yield this.handleResultType({ info: res.info, type });
        } else {
          modal.error({
            content: res.msg,
          });
        }
      } else {
        yield this.changeData({
          infoMergeContainerCode: '',
        });
        classFocus('infoMergeContainerCode');
      }
    } else if (info.resultType === 4) {
      // 回到分波次页面, 清空周转箱输入内容, 焦点置于合并周转箱
      const status = yield new Promise((r) => modal.confirm({
        modalBlurInput: true,
        title: t('当前合并周转箱分波次查询状态不为挂起，请确认！'),
        buttons: [{
          type: 'primary',
          label: t('确定'),
          onClick: () => (r(true)),
        }],
      }));
      if (status) {
        yield this.changeData({
          infoMergeContainerCode: '',
        });
        classFocus('infoMergeContainerCode');
        yield this.getCurrentHangUp();
      }
    }
  },

  /**
   * 点击挂起的合并周转箱
   */
  * handleHangUpList({ item }) {
    const param = {
      mergeContainerCode: item.mergeContainerCode,
      mergeTaskCode: item.mergeTaskCode,
    };
    yield this.changeData({ dataLoading: 0 });
    const { code, info, msg } = yield hangUpListAPI(param);
    if (code === '0') {
      yield this.changeData({ dataLoading: 1 });
      yield this.handleResultType({ info, type: 0 });
    } else {
      yield this.changeData({ dataLoading: 2 });
      modal.error({
        content: msg,
      });
    }
  },

  /**
   * 回到分波次初始页面，并聚焦在周转箱
   */
  * returnInfoPage() {
    yield this.changeData({
      currentPage: 1,
    });
    classFocus('infoMergeContainerCode');
  },

  // ============content page START =============

  /**
   * 扫描商品条码
   */
  * scanGoods() {
    const {
      goodsSnPrint, mergeContainerCode, mergeTaskCode, sowingNum, watingSowingNum,
    } = yield '';
    const param = {
      goodsSnPrint,
      mergeContainerCode,
      mergeTaskCode,
    };
    markStatus('dataLoading');
    const { code, info, msg } = yield getGoodsAPI(param);
    if (code === '0') {
      // 1需要绑定分波周转箱 2继续扫描商品条码 3合并周转箱拣货完成 7 多货登记
      if (info.resultType === 1) {
        // 1、清空商品条码输入框内容
        // 2、显示波次号、SKC、尺码、任务序列号
        // 3、焦点置于分波周转箱
        yield this.changeData({
          goodsSnPrint: '',
          content: info,
          sowingContainerCode: '',
          sowingDisabled: false,
          goodsDisabled: true,
        });
        classFocus('sowingContainerCode');
      } else if (info.resultType === 2) {
        // 1、清空商品条码输入框内容
        // 2、显示波次号、SKC、尺码、任务序列号
        // 3、分波周转箱输入框展示对应分波中的分波周转箱，不可编辑
        // 4、焦点置于商品条码
        // 5、页面中已分数+1、待分数-1
        yield this.changeData({
          goodsSnPrint: '',
          sowingDisabled: true,
          goodsDisabled: false,
          sowingContainerCode: info.sowingContainerCode,
          content: info,
          sowingNum: sowingNum + 1,
          watingSowingNum: watingSowingNum - 1,
        });
        classFocus('goodsSnPrint');
      } else if (info.resultType === 3) {
        const status = yield new Promise((r) => modal.confirm({
          modalBlurInput: true,
          content: (
          // eslint-disable-next-line react/jsx-filename-extension
            <div>
              <div>{t('商品请置于任务')}:<span style={{ color: 'red', fontSize: 24 }}>{info.taskSeq}</span></div>
              <div>{t('当前分波周转箱分波完成')}</div>
            </div>
          ),
          buttons: [{
            type: 'primary',
            label: t('确定'),
            onClick: () => (r(true)),
          }],
        }));
        if (status) {
          yield this.returnInfoPage();
        }
      } else if (info.resultType === 7) {
        const status = yield new Promise((r) => modal.confirm({
          modalBlurInput: true,
          title: t('该拣货周转箱不存在当前扫描的商品，请去多件登记'),
          buttons: [{
            type: 'primary',
            label: t('确定'),
            onClick: () => (r(true)),
          }],
        }));
        if (status) {
          yield this.changeData({
            goodsSnPrint: '',
          });
          classFocus('goodsSnPrint');
        }
      }
    } else {
      modal.error({
        content: msg,
      });
      yield this.changeData({
        goodsSnPrint: '',
      });
      classFocus('goodsSnPrint');
    }
  },

  /**
   * 扫描分波周转箱
   */
  * scanBox() {
    const {
      sowingContainerCode, sowingNum, watingSowingNum, content, mergeTaskCode, mergeContainerCode,
    } = yield '';
    const param = {
      sowingContainerCode,
      mergeTaskCode,
      mergeContainerCode,
      taskCode: content.taskCode,
      taskSeq: content.taskSeq,
      skuCode: content.skuCode,
    };
    markStatus('dataLoading');
    const { code, info, msg } = yield bindAPI(param);
    if (code === '0') {
      // 2 继续扫描商品条码，3 合并周转箱分波完成
      if (info.resultType === 2) {
        // 1、清空分波周转箱输入框内容
        // 2、清空波次号、SKC、尺码、任务序列号
        // 3、焦点置于商品条码
        // 4、页面中已分数+1、待分数-1
        yield this.changeData({
          sowingContainerCode: '',
          content: {},
          goodsDisabled: false,
          sowingDisabled: true,
          sowingNum: sowingNum + 1,
          watingSowingNum: watingSowingNum - 1,
        });
        classFocus('goodsSnPrint');
      } else if (info.resultType === 3) {
        message.success(t('当前拣货箱分波完成,请扫描新的合并周转箱'));
        yield this.returnInfoPage();
      }
    } else {
      modal.error({
        content: msg,
      });
      yield this.changeData({
        sowingContainerCode: '',
      });
      classFocus('sowingContainerCode');
    }
  },

  /**
   * 按钮-箱空
   */
  * emptyBox() {
    const status = yield new Promise((r) => modal.confirm({
      modalBlurInput: true,
      title: t('是否确认操作箱空'),
      onOk: () => r(true),
    }));
    if (status) {
      const { mergeTaskCode, mergeContainerCode } = yield '';
      const param = {
        mergeTaskCode,
        mergeContainerCode,
      };
      markStatus('dataLoading');
      const { code, msg } = yield emptyBoxAPI(param);
      if (code === '0') {
        message.success(t('周转箱分波完成'));
        yield this.returnInfoPage();
      } else {
        modal.error({
          content: msg,
        });
      }
    }
  },

  /**
   * 按钮-关箱
   */
  * closeBox() {
    const status = yield new Promise((r) => modal.confirm({
      modalBlurInput: true,
      title: t('是否确认操作关箱'),
      onOk: () => r(true),
    }));
    if (status) {
      const { sowingContainerCode, content } = yield '';
      const param = {
        taskCode: content.taskCode,
        sowingContainerCode,
      };
      markStatus('dataLoading');
      const { code, msg } = yield closeBoxAPI(param);
      if (code === '0') {
        // 1、清空周转箱输入框内容
        // 2、清空波次号、SKC、尺码、任务序列号
        // 3、焦点置于商品条码
        yield this.changeData({
          sowingContainerCode: '',
          content: {},
        });
        classFocus('goodsSnPrint');
      } else {
        modal.error({
          content: msg,
        });
      }
    }
  },

  /**
   * 按钮-挂起
   */
  * hangUpBox() {
    const status = yield new Promise((r) => modal.confirm({
      modalBlurInput: true,
      title: t('是否确认操作挂起'),
      onOk: () => r(true),
    }));
    if (status) {
      const { mergeTaskCode, mergeContainerCode } = yield '';
      const param = {
        mergeTaskCode,
        mergeContainerCode,
      };
      markStatus('dataLoading');
      const { code, msg } = yield hangUpBoxAPI(param);
      if (code === '0') {
        message.success(t('周转箱已挂起'));
        yield this.returnInfoPage();
      } else {
        modal.error({
          content: msg,
        });
      }
    }
  },

  /**
   * 回到分波次执行页面，并聚焦在商品条码或分波周转箱上
   */
  * returnContentPage() {
    yield this.changeData({
      currentPage: 2,
    });
    const { sowingDisabled } = yield '';
    if (sowingDisabled) {
      classFocus('goodsSnPrint');
    } else {
      classFocus('sowingContainerCode');
    }
  },
  // ============content page END =============

  /**
   * 获取明细页面数据
   */
  * getDetail() {
    const { mergeTaskCode, mergeContainerCode } = yield '';
    const param1 = {
      mergeTaskCode,
      mergeContainerCode,
      sowingStatus: 0, // 分波状态：0 未分波，1 已分波
    };
    const param2 = {
      mergeTaskCode,
      mergeContainerCode,
      sowingStatus: 1, // 分波状态：0 未分波，1 已分波
    };
    markStatus('dataLoading');
    const [res1, res2] = yield Promise.all([
      getDetailAPI(param1),
      getDetailAPI(param2),
    ]);
    if (res1.code === '0' && res2.code === '0') {
      yield this.changeData({
        currentPage: 3,
        unsowList: res1.info?.data || [],
        sowedList: res2.info?.data || [],
      });
    } else {
      modal.error({
        content: res1.msg || res2.msg,
      });
    }
  },

};
