import { sendPostRequest } from 'lib/public-request';

// 当前用户分波中的合并周转箱
export const getCurrentContainerAPI = (param) => sendPostRequest({
  url: '/merge_sowing/get_current_user_merge_container',
  param,
}, process.env.WOS_URI);

// 当前用户挂起的合并周转箱
export const getCurrentHangUpAPI = (param) => sendPostRequest({
  url: '/merge_sowing/query_hand_up_container',
  param,
}, process.env.WOS_URI);

// 箱空合并周转箱确认
export const emptyBoxAPI = (param) => sendPostRequest({
  url: '/merge_sowing/release_merge_container_confirm',
  param,
}, process.env.WOS_URI);

// 扫描合并周转箱-弹窗
export const handleMergeAPI = (param) => sendPostRequest({
  url: '/merge_sowing/scan_merge_container',
  param,
}, process.env.WOS_URI);

// 扫描合并周转箱-确认
export const handleMergeConfirmAPI = (param) => sendPostRequest({
  url: '/merge_sowing/scan_merge_container_confirm',
  param,
}, process.env.WOS_URI);

// 分波周转箱关箱
export const closeBoxAPI = (param) => sendPostRequest({
  url: '/merge_sowing/close_sowing_container',
  param,
}, process.env.WOS_URI);

// 点击挂起的合并周转箱-弹窗
export const hangUpListAPI = (param) => sendPostRequest({
  url: '/merge_sowing/click_pick_container',
  param,
}, process.env.WOS_URI);

// 扫描合并周转箱-确认
export const hangUpListConfirmAPI = (param) => sendPostRequest({
  url: '/merge_sowing/click_pick_container_confirm',
  param,
}, process.env.WOS_URI);

// 分波明细查看
export const getDetailAPI = (param) => sendPostRequest({
  url: '/merge_sowing/merge_sowing_detail_view',
  param,
}, process.env.WOS_URI);

// 分波挂起
export const hangUpBoxAPI = (param) => sendPostRequest({
  url: '/merge_sowing/hang_up',
  param,
}, process.env.WOS_URI);

// 扫描 商品条码
export const getGoodsAPI = (param) => sendPostRequest({
  url: '/merge_sowing/scan_goods',
  param,
}, process.env.WOS_URI);

// 扫描分波周转箱进行绑定扫描
export const bindAPI = (param) => sendPostRequest({
  url: '/merge_sowing/bind_sowing_container',
  param,
}, process.env.WOS_URI);
