import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import classnames from 'classnames';
import { Form } from 'react-weui';
import { Button } from 'react-weui/build/packages/components/button';
import {
  Header, Footer, FooterBtn, View,
  FocusInput, RowInfo,
} from 'common';
import store from '../reducers';
import styles from '../style.less';

class Content extends React.Component {
  render() {
    const {
      headerTitle,
      dataLoading,
      sowingNum,
      watingSowingNum,
      mergeContainerCode,
      goodsSnPrint,
      sowingContainerCode,
      sowingDisabled,
      goodsDisabled,
      content,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle}>
          <div
            onClick={() => {
              store.emptyBox();
            }}
          >
            {t('箱空')}
          </div>
        </Header>

        <View
          diff={100} // 默认56; 页面内容高度：window.innerHeight - diff 中的 diff 值【必填: 100是头部标题44+底部按钮56之和】
          flex={false}
          loading={dataLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面【选填: 根据实际情况决定】
        >
          <Form>
            <RowInfo
              extraStyle={{
                borderBottom: 'none',
              }}
              label={t('已分/待分')}
              type="warn"
              content={(<span>{sowingNum}/{watingSowingNum}</span>)}
            />
          </Form>

          <div className={classnames(styles.sowingBox, styles[sowingNum % 2 === 0 ? 'evenBox' : 'oddBox'])}>
            <div>
              <div><span className={styles.sowingText}>{t('任务')}：</span>{content.taskCode || ''}</div>
              <div><span className={styles.sowingText}>SKC/{t('尺码')}：</span>{content.goodsSn && content.size ? `${content.goodsSn}/${content.size}` : ''}</div>
            </div>
            <div className={styles[sowingNum % 2 === 0 ? 'evenBoxColor' : 'oddBoxColor']}>{content.taskSeq || ''}</div>
          </div>

          <Form>
            <FocusInput
              value={mergeContainerCode}
              disabled
              footer={
              (
                <Button
                  size="small"
                  style={{ fontSize: 12 }}
                  disabled={!dataLoading}
                  onClick={() => {
                    store.hangUpBox();
                  }}
                >
                  {t('挂起')}
                </Button>
              )
            }
            >
              <label>{t('合并周转箱')}</label>
            </FocusInput>

            <FocusInput
              value={goodsSnPrint}
              className="goodsSnPrint"
              placeholder={t('请扫描')}
              disabled={goodsDisabled || !dataLoading}
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  goodsSnPrint: value,
                });
              }}
              onPressEnter={(e) => {
                if (e.target.value) {
                  store.changeData({
                    goodsSnPrint: e.target.value,
                  });
                  store.scanGoods();
                }
              }}
            >
              <label>{t('商品条码')}</label>
            </FocusInput>

            <FocusInput
              value={sowingContainerCode}
              className="sowingContainerCode"
              placeholder={t('请扫描')}
              disabled={sowingDisabled}
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  sowingContainerCode: value,
                });
              }}
              onPressEnter={(e) => {
                if (e.target.value) {
                  store.changeData({
                    sowingContainerCode: e.target.value,
                  });
                  store.scanBox();
                }
              }}
            //   footer={
            //   (
            //     <Button
            //       size="small"
            //       style={{ fontSize: 12 }}
            //       disabled={!sowingContainerCode || !dataLoading}
            //       onClick={() => {
            //         store.closeBox();
            //       }}
            //     >
            //       {t('关箱')}
            //     </Button>
            //   )
            // }
            >
              <label>{t('分波周转箱')}</label>
            </FocusInput>
          </Form>
        </View>

        <Footer
          beforeBack={() => {
            store.changeData({
              currentPage: 1,
            });
          }}
        >
          <FooterBtn
            onClick={() => {
              store.getDetail();
            }}
          >{t('明细')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Content.propTypes = {
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  sowingNum: PropTypes.number,
  watingSowingNum: PropTypes.number,
  mergeContainerCode: PropTypes.string,
  goodsSnPrint: PropTypes.string,
  sowingContainerCode: PropTypes.string,
  goodsDisabled: PropTypes.bool,
  sowingDisabled: PropTypes.bool,
  content: PropTypes.shape(),
};

export default Content;
