/* eslint-disable react/no-array-index-key */
import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Tab, NavBar, NavBarItem } from 'react-weui/build/packages';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, Footer, View, RowInfo, Table, modal,
} from 'common';
import store from '../reducers';
import styles from '../style.less';

class Detail extends React.Component {
  render() {
    const {
      dataLoading,
      detailNav,
      unsowList,
      sowedList,
    } = this.props;

    const handleNum = (arr) => {
      const numArr = (arr.map((item) => item.detailList.map((v) => v.num).join(',')).join(',')).split(',');
      let num = 0;
      numArr.forEach((item) => { num += Number(item); });
      return num;
    };

    const sowedNum = handleNum(sowedList);
    const unsowNum = handleNum(unsowList);
    return (
      <div>
        <Header title={t('分波明细')} />

        <View
          diff={100} // 默认56; 页面内容高度：window.innerHeight - diff 中的 diff 值【必填: 100是头部标题44+底部按钮56之和】
          flex
          loading={dataLoading}
          style={{ flexDirection: 'column' }}
        >
          <div style={{ height: 34 }}>
            <Tab>
              <NavBar>
                <NavBarItem
                  active={detailNav === 1}
                  className={styles.navBarItem}
                  onClick={() => {
                    store.changeData({ detailNav: 1 });
                  }}
                >
                  <span className={detailNav === 1 ? styles.active : ''}>
                    {t('已分波')}（{sowedNum}{t('件')}）
                  </span>
                </NavBarItem>
                <NavBarItem
                  active={detailNav === 0}
                  className={styles.navBarItem}
                  onClick={() => {
                    store.changeData({ detailNav: 0 });
                  }}
                >
                  <span className={detailNav === 0 ? styles.active : ''}>
                    {t('未分波')}（{unsowNum}{t('件')}）
                  </span>
                </NavBarItem>
              </NavBar>
            </Tab>
          </div>
          <div style={{ flex: 1, overflowY: 'auto' }}>
            {/* 已分波 */}
            {detailNav === 1 &&
              sowedList.map((item, index) => (
                <div key={index}>
                  <RowInfo
                    label={(
                      <div>
                        <div><span className={styles.sowingText}>{t('任务号')}</span>：{item.taskCode}</div>
                        <div><span className={styles.sowingText}>{t('分波周转箱')}</span>：{item.sowingContainerCode}</div>
                      </div>
                    )}
                    type="info"
                    extraStyle={{
                      borderBottom: 'none',
                      fontSize: 14,
                      lineHeight: '26px',
                      marginTop: 4,
                    }}
                  />
                  <Form>
                    <Table
                      dataSource={item.detailList || []}
                      columns={[
                        {
                          title: <div className={styles.leftAlign}>SKC/{t('尺码')}</div>,
                          dataIndex: 'goodsSn',
                          width: 15,
                          render: (row) => (
                            <div className={styles.leftAlign}>
                              <span>{row.goodsSn}</span>
                              /
                              <span>{row.size}</span>
                            </div>
                          ),
                        },
                        {
                          title: t('数量'),
                          dataIndex: 'num',
                          width: 5,
                          render: (row) => (
                            <span
                              className={styles.sowingBtnText}
                              onClick={() => {
                                modal.img({
                                  content: <img width="100%" src={row.imageUrl} />,
                                  cancelText: <span className={styles.sowingBtnText}>{t('确定')}</span>,
                                });
                              }}
                            >{row.num}
                            </span>
                          ),
                        },
                      ]}
                    />
                  </Form>
                </div>
              ))}

            {/* 未分波 */}
            {detailNav === 0 &&
               unsowList.map((item, index) => (
                 <div key={index}>
                   <RowInfo
                     label={(
                       <span style={{ fontSize: 14 }}>{t('任务号')}：{item.taskCode}</span>
                     )}
                     type="info"
                     extraStyle={{
                       borderBottom: 'none',
                       marginTop: 4,
                     }}
                     content={
                       item.taskSeq ? (<span style={{ color: '#7FABE6' }}>{item.taskSeq}{t('号')}</span>) : null
                    }
                   />
                   <Form>
                     <Table
                       dataSource={item.detailList || []}
                       columns={[
                         {
                           title: <div className={styles.leftAlign}>SKC/{t('尺码')}</div>,
                           dataIndex: 'goodsSn',
                           width: 15,
                           render: (row) => (
                             <div className={styles.leftAlign}>
                               <span>{row.goodsSn}</span>
                               /
                               <span>{row.size}</span>
                             </div>
                           ),
                         },
                         {
                           title: t('数量'),
                           dataIndex: 'num',
                           width: 5,
                           render: (row) => (
                             <span
                               className={styles.sowingBtnText}
                               onClick={() => {
                                 modal.img({
                                   content: <img width="100%" src={row.imageUrl} />,
                                   cancelText: <span className={styles.sowingBtnText}>{t('确定')}</span>,
                                 });
                               }}
                             >{row.num}
                             </span>
                           ),
                         },
                       ]}
                     />
                   </Form>
                 </div>
               ))}
          </div>
        </View>

        <Footer
          beforeBack={() => {
            store.returnContentPage();
          }}
        />
      </div>
    );
  }
}

Detail.propTypes = {
  dataLoading: PropTypes.number,
  detailNav: PropTypes.number,
  unsowList: PropTypes.arrayOf(PropTypes.shape()),
  sowedList: PropTypes.arrayOf(PropTypes.shape()),
};

export default Detail;
