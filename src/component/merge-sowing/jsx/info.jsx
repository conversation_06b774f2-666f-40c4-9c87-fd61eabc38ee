import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages/components/form';
import {
  Header, FocusInput, Footer, View, RowInfo,
} from 'common';
import store from '../reducers';

class Info extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      initLoading,
      dataLoading,
      containerCode,
      infoMergeContainerCode,
      containerList,
    } = this.props;

    return (
      <div>
        <Header title={headerTitle} />

        <View
          diff={100} // 默认56; 页面内容高度：window.innerHeight - diff 中的 diff 值【必填: 100是头部标题44+底部按钮56之和】
          flex={false} // flex布局，默认为true，当需要固定单个输入框时，不启用【选填: 根据实际情况决定】
          loading={initLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面【选填: 根据实际情况决定】
        >
          <Form>
            {containerCode ? (
              <RowInfo
                label={t('分波中的合并周转箱')}
                type="info"
                extraStyle={{
                  borderBottom: 'none',
                }}
                content={(
                  <span
                    style={{ color: '#0059CE' }}
                    onClick={() => {
                      store.handleMerge({ containerCode });
                    }}
                  >
                    {containerCode}
                  </span>
              )}
              />
            )
              : null}
          </Form>

          <Form>
            <FocusInput
              value={infoMergeContainerCode}
              className="infoMergeContainerCode"
              placeholder={t('请扫描')}
              disabled={!dataLoading}
              autoFocus
              onChange={(e) => {
                const { value } = e.target;
                store.changeData({
                  infoMergeContainerCode: value,
                });
              }}
              onPressEnter={(e) => {
                if (!e.target.value) {
                  return;
                }
                store.handleMerge();
              }}
            >
              <label>{t('合并周转箱')}</label>
            </FocusInput>
          </Form>

          {containerList?.length > 0 ? (
            <Form>
              <RowInfo
                label={t('挂起的合并周转箱')}
                content={`${containerList.length}${t('箱')}`}
                type="info"
              />
              {containerList.map((item) => (
                <div
                  key={item.mergeContainerCode}
                  style={{ color: '#329cff', cursor: 'pointer', margin: 10 }}
                  onClick={() => {
                    store.handleHangUpList({ item });
                  }}
                >{item.mergeContainerCode}
                </div>
              ))}
            </Form>
          )
            : null}
        </View>

        <Footer
          beforeBack={(back) => {
            back();
          }}
        />
      </div>
    );
  }
}

Info.propTypes = {
  initLoading: PropTypes.number,
  headerTitle: PropTypes.string,
  dataLoading: PropTypes.number,
  containerCode: PropTypes.string,
  infoMergeContainerCode: PropTypes.string,
  containerList: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Info);
