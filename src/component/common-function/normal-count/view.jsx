import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import Modal from '../../common/modal';
import store from './reducers';

import {
  Header,
  Footer,
  FocusInput,
  pages,
} from '../../common';
import Pickers from '../../common/pickers';
import RowInfo from '../../common/row-info';

const { View } = pages;

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      dispatch,
      dataLoading,
      headerTitle,
      pieceWorkTypeList,
      show,
      pieceWorkType,
      barcode,
      scannedBars,
      count,
    } = this.props;

    return (
      <View>
        <Header title={headerTitle || t('商品库存查询')} />
        <Form>
          <RowInfo
            label={t('已扫描')}
            content={count}
            type="info"
            textExtraStyle={{ color: 'orange', fontWeight: 'bold' }}
          />
          <Pickers
            value={pieceWorkType}
            label={t('当前工种')}
            placeholder={t('请选择')}
            onClick={() => store.changeData({ data: { show: true } })}
            onChange={(select) => {
              store.changeData({ data: { show: false, pieceWorkType: select.label } });
            }}
            show={show}
            pickerData={pieceWorkTypeList}
            onCancel={() => store.changeData({ data: { show: false } })}
          />
          <FocusInput
            placeholder={t('请扫描')}
            autoFocus
            value={barcode}
            className="barcode"
            disabled={dataLoading === 0}
            onChange={(e) => {
              store.changeData({
                data: {
                  barcode: e.target.value,
                },
              });
            }}
            onPressEnter={() => {
              if (barcode && barcode.length <= 32 && !(/[\u4e00-\u9fa5]+/.test(barcode)) && pieceWorkType !== '') {
                const typeObj = pieceWorkTypeList[0].items.find(i => (
                  i.label === pieceWorkType
                )) || {};
                store.sacnGoods({
                  param: {
                    barcode,
                    pieceWorkType: typeObj.value || '',
                  },
                  scannedBars,
                  count,
                });
              } else {
                Modal.error({
                  modalBlurInput: true,
                  content: t('当前工种为必选且条码字符串长度不能超过32位且不能包含中文字符！'),
                  className: 'barcode',
                });
                store.changeData({
                  data: {
                    barcode: '',
                  },
                });
              }
            }}
          >
            <label>{t('条码')}</label>
          </FocusInput>
        </Form>
        <Footer
          dispatch={dispatch}
        />
      </View>
    );
  }
}

Container.propTypes = {
  barCode: PropTypes.string,
  dispatch: PropTypes.func,
  dataLoading: PropTypes.number,
  headerTitle: PropTypes.string,
};

export default i18n(Container);
