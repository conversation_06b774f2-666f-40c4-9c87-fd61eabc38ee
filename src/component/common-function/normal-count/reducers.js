import assign from 'object-assign';
import markStatus from 'rrc-loader-helper/lib/mark-status';
import {  scanGoodsSnApi } from '../server';
import { classFocus, getHeaderTitle } from '../../../lib/util';
import {selectDict} from "../../../server/basic/data-dictionary";
import { modal } from '../../common';

const defaultState = {
  dataLoading: 1,
  headerTitle: '',
  pieceWorkType: '',
  barcode: '',
  pieceWorkTypeList: [
    {
      items: [

      ],
    },
],
  show: false,
  scannedBars: [],
  count: 0,
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  * init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    const selectData = yield selectDict({ catCode: ['PIECE_WORK_TYPE'] });
    if (selectData.code === '0') {
      yield ctx.changeData({
        data: {
          pieceWorkTypeList: [
            {
              items: selectData.info.data[0].dictListRsps.map(v => (
                {
                  label: v.dictNameZh,
                  value: v.dictCode,
                }
              ))
            }
          ],
        }
      });
    } else {
      modal.error({
        content: selectData.msg,
      });
    }

  },
  * sacnGoods(action, ctx) {
    markStatus('dataLoading'); // 搜索loading状态
    const res = yield scanGoodsSnApi(action.param);
    if (res.code === '0') {
      classFocus('barcode');
      yield ctx.changeData({
        data: {
          scannedBars: [...action.scannedBars, (action.param.barcode + ''+ action.param.pieceWorkType)],
          count: ++ action.count,
          barcode: '',
        },
      });
    } else {
      yield ctx.changeData({
        data: {
          barcode: '',
        },
      });
      modal.error({
        className: 'barcode',
        content: res.msg,
      });
    }
  },
};
