import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { markStatus } from 'rrc-loader-helper';
import { push } from 'react-router-redux';
import {
  classFocus, getHeaderTitle, licenseRegZhHeader,
} from 'lib/util';
import { message, modal } from 'common';
import { selectDict } from '../../../server/basic/data-dictionary';
import {
  checkPalletCodeAPI,
  getPalletAndContainerAPI,
  deliverAPI,
  loadingAPI,
  checkHandoverExistAPI,
} from './server';

export const loadTypeMap = new Map([
  [t('托盘'), 1],
  [t('入库周转箱'), 2],
  [t('拣货周转箱'), 3],
  [t('回货周转箱'), 4],
  [t('补货周转箱'), 5],
]);

const defaultState = {
  initLoading: 1, // 初始化加载 0加载中 1加载成功 2加载失败
  dataLoading: 1, // 数据加载 0加载中 1加载成功 2加载失败
  totalContainerNum: 0, // 总箱数
  totalPalletNum: 0, // 总托盘数

  licencePlate: '', // 车牌号
  licencePlateEnterFlag: false, // 扫描车牌号 回车确认

  palletCode: '', // 托盘号
  palletCodeEnterFlag: false, // 扫描托盘号 回车确认

  loadType: '', // 装载类型
  loadTypeList: [], // 装载类型列表
  loadTypeShow: false,

  loadNum: '', // 数量
};

export default {
  state: defaultState,

  $init: () => defaultState,

  * init(action, ctx, put) {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }

    markStatus('initLoading');

    yield this.changeData({
      headerTitle: getHeaderTitle(),
    });
    const [selectData] = yield Promise.all([
      selectDict({ catCode: ['PDA_EMPTY_BOX_HANDOVER_LIST_LOAD_TYPE'] }),
    ]);
    if (selectData.code === '0') {
      const list = selectData.info.data[0].dictListRsps || [];
      yield this.changeData({
        loadTypeList: list,
      });
    }
  },

  changeData(state, data) {
    assign(state, data);
  },

  /**
   * 扫描车牌号
   * @param {string} licencePlate 车牌号
   */
  * scanLicencePlate(licencePlate) {
    if (licenseRegZhHeader.test(licencePlate)) {
      yield this.getPalletAndContainer(licencePlate);
    } else {
      const status = yield new Promise((r) => {
        modal.confirm({
          modalBlurInput: true,
          title: t('请输入正确的车牌号'),
          onOk: () => r(true),
        });
      });
      if (status) {
        const { loadTypeList } = yield '';
        yield this.changeData({
          ...defaultState,
          initLoading: 1,
          loadTypeList,
        });
        classFocus('licencePlate');
      }
    }
  },

  /**
   * 根据当前车牌号获取待发货已装车托盘数和周转箱数
   * @param {string} licencePlate 车牌号
   */
  * getPalletAndContainer(licencePlate) {
    markStatus('dataLoading');
    const res = yield getPalletAndContainerAPI({ licencePlate });
    if (res.code === '0') {
      const { totalContainerNum, totalPalletNum } = res.info;
      yield this.changeData({
        totalContainerNum, // 总箱数
        totalPalletNum, // 总托盘数
        licencePlateEnterFlag: true,
      });
      classFocus('palletCode');
    } else if (res.code === '503203') {
      // 清空车牌输入内容，焦点置于车牌号
      const status = yield new Promise((r) => {
        modal.confirm({
          modalBlurInput: true,
          content: res.msg,
          buttons: [{
            type: 'primary',
            label: t('确定'),
            onClick: () => (r(true)),
          }],
        });
      });
      if (status) {
        yield this.changeData({
          licencePlate: '',
        });
        classFocus('licencePlate');
      }
    } else {
      yield this.changeData({
        licencePlate: '',
      });
      modal.error({ content: res.msg, className: 'licencePlate' });
    }
  },

  /**
   * 扫描托盘
   * @param {string} palletCode 托盘号
   */
  * scanPallet(palletCode) {
    const { subWarehouseId } = yield 'homework-subwarehouse';
    const { licencePlate } = yield '';

    markStatus('dataLoading');
    const result = yield checkPalletCodeAPI({
      licencePlate,
      palletCode,
      subWarehouseId,
    });
    const {
      msg, code,
    } = result;
    if (code === '0') {
      yield this.changeData({
        palletCodeEnterFlag: true,
      });
      classFocus('loadType');
    } else if (code === '503204') {
      const status = yield new Promise((r) => {
        modal.confirm({
          modalBlurInput: true,
          content: msg,
          onOk: () => r(true),
        });
      });
      if (status) {
        yield this.changeData({
          palletCode: '',
        });
        classFocus('palletCode');
      }
    } else {
      modal.error({ content: msg, className: 'palletCode' });
      yield this.changeData({
        palletCode: '',
      });
    }
  },
  /**
   * 左下角 返回/完成按钮 确认发货
   * @param {*} param
   */
  * deliver(param) {
    const { back: backCb } = param;
    const { licencePlate } = yield '';
    const { subWarehouseId } = yield 'homework-subwarehouse';

    const checkHandoverExistRes = yield checkHandoverExistAPI({
      licencePlate, subWarehouseId,
    });

    if (checkHandoverExistRes.code === '0') {
      if (checkHandoverExistRes.info === true) {
        // info为true, 有数据
        const status = yield new Promise((r) => {
          modal.confirm({
            content: t('该车牌号存在待发货状态的交接单，请确认'),
            okText: t('确认发货'),
            cancelText: t('直接返回'),
            onOk: () => r('ok'),
            onCancel: () => r('cancel'),
            onClose: () => {},
          });
        });
        if (status === 'ok') {
          const { msg, code } = yield deliverAPI({ licencePlate, subWarehouseId });
          if (code === '0') {
            // 返回
            message.success(t('发货成功'));
            backCb();
          } else {
            modal.error({ content: msg });
          }
        }
        if (status === 'cancel') {
          backCb();
        }
      } else {
        backCb();
      }
    } else {
      modal.error({ content: checkHandoverExistRes.msg });
    }
  },
  /**
   * 装车
   */
  * carLoading() {
    const {
      licencePlate, loadNum, loadType, palletCode,
    } = yield '';
    markStatus('dataLoading');
    const { subWarehouseId } = yield 'homework-subwarehouse';
    const param = {
      licencePlate,
      loadNum,
      loadType,
      palletCode,
      subWarehouseId,
    };
    const { msg, code } = yield loadingAPI(param);

    if (code === '0') {
      // 清空数量
      yield this.changeData({
        loadNum: '',
      });
      message.success(t('装车成功'));
      classFocus('palletCode');

      // 更新总箱数/总托盘数
      const res = yield getPalletAndContainerAPI({ licencePlate });
      if (res.code === '0') {
        const { totalContainerNum, totalPalletNum } = res.info;
        yield this.changeData({
          totalContainerNum, // 总箱数
          totalPalletNum, // 总托盘数
        });
      }
    } else if (code === '503204') {
      // 清空托盘号、装载类型及数量，光标定位托盘号
      message.error(msg);
      yield this.changeData({
        palletCode: '',
        loadType: '',
        loadNum: '',
      });
      classFocus('palletCode');
    } else if (code === '503201') {
      message.error(msg);
      classFocus('loadType');
    } else if (code === '503202') {
      // 光标定位数量输入框
      message.error(msg);
      classFocus('loadNum');
    } else {
      message.error(msg);
    }
  },
};
