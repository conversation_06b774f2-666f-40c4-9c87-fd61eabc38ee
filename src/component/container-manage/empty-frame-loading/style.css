.inLine{
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    min-height: 55px;
    font-size: 14px;
    box-shadow: 0px 2px 4px 0px rgba(25,122,250,0.15);
}
.marginTopTen{
    margin-top: 10px;
}

/* row-info的样式 */
.infoBox {
    min-height: 40px;
    /* line-height: 40px; */
    display: flex;
    flex-direction: row;
    /* border-bottom: 1px solid #e5e5e5; */
    margin-right: 15px;
    justify-content: space-between;
    align-items: center;
  }
  
  .infoLabel {
    color: #616161;
    font-weight: 400;
    position: relative;
    margin-left: 27px;
  }
  
  .infoContent {
    color: #414141;
    font-size: 18px;
    font-weight: 500;
    text-align: right;
  }
  
  .infoLabel:before {
    content: "";
    width: 4px;
    height: 14px;
    box-shadow: 0px 2px 4px 0px rgba(54,210,120,0.3);
    border-radius: 2px;
    position: absolute;
    top: 4px;
    left: -12px;
    background: #0059ce;
  }
  
  