import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import {
  Header,
  View,
  RowInfo,
  FooterBtn,
  modal,
  FocusInput,
  Footer,
  PopRadio,
} from 'common';
import { classFocus, validNum } from 'lib/util';
import store, { loadTypeMap } from './reducers';
import styles from './style.css';

const maxLoadNum = (loadType) => {
  switch (loadType) {
    case loadTypeMap.get(t('入库周转箱')):
      return 90;
    case loadTypeMap.get(t('拣货周转箱')):
      return 60;
    case loadTypeMap.get(t('回货周转箱')):
    case loadTypeMap.get(t('补货周转箱')):
      return 48;
    case loadTypeMap.get(t('托盘')):
    default:
      return 999;
  }
};

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      initLoading,
      dataLoading,
      headerTitle,
      totalContainerNum,
      totalPalletNum,
      licencePlate,
      palletCode,
      loadType,
      loadTypeShow,
      loadTypeList,
      loadNum,
      licencePlateEnterFlag,
      palletCodeEnterFlag,
    } = this.props;

    const carLoadingDisabled = loadNum === '';

    // 数量最大值，根据装载类型而变动
    const maxNum = maxLoadNum(loadType);

    // 装车
    const submit = () => {
      if (!loadType) {
        modal.info({ content: t('装载类型不可为空'), className: 'loadType' });
        return;
      }
      if (!loadNum) {
        modal.info({ content: t('数量不可为空'), className: 'loadNum' });
        return;
      }
      modal.confirm({
        content: t('是否确认装车？'),
        onOk: () => {
          store.carLoading();
        },
        onCancel: () => {
          classFocus('loadNum');
        },
      });
    };

    return (
      <div>
        <Header title={headerTitle} homeIcon={false} />
        <View
          flex={false}
          diff={110}
          initLoading={initLoading === 0}
        >
          <div className={styles.inLine}>
            <RowInfo
              extraStyle={{
                borderBottom: 'none',
              }}
              label={t('已装车托盘')}
              content={<label>:{totalPalletNum}</label>}
            />
            <RowInfo
              extraStyle={{
                borderBottom: 'none',
              }}
              label={t('已装车周转箱')}
              content={<label>:{totalContainerNum}</label>}
            />
          </div>
          <Form style={{ marginTop: 10 }}>
            <FocusInput
              label={t('车牌')}
              placeholder={t('请扫描车牌')}
              autoFocus
              value={licencePlate}
              disabled={!dataLoading || licencePlateEnterFlag}
              className="licencePlate"
              lineBreak={false}
              onChange={(e) => {
                store.changeData({
                  licencePlate: e.target.value,
                });
              }}
              onPressEnter={() => {
                if (licencePlate) {
                  store.scanLicencePlate(licencePlate);
                }
              }}
            />
            <FocusInput
              label={t('托盘')}
              placeholder={t('请扫描托盘')}
              autoFocus
              value={palletCode}
              disabled={!dataLoading || !licencePlateEnterFlag}
              className="palletCode"
              lineBreak={false}
              onChange={(e) => {
                store.changeData({
                  palletCode: e.target.value,
                  palletCodeEnterFlag: false,
                  loadType: '',
                  loadNum: '',
                });
              }}
              onPressEnter={() => {
                if (palletCode) {
                  store.scanPallet(palletCode);
                }
              }}
            />
            <PopRadio
              label={t('装载类型')}
              disabled={!palletCodeEnterFlag}
              selectValue={loadType}
              selectList={loadTypeList}
              show={loadTypeShow}
              lineBreak={false}
              className="loadType"
              valueName="dictCode"
              labelName="dictNameZh"
              placeholder={t('请选择')}
              onClick={() => {
                store.changeData({ loadTypeShow: true });
              }}
              onCancel={() => {
                store.changeData({ loadTypeShow: false });
              }}
              onOk={(val) => {
                store.changeData({
                  loadTypeShow: false,
                  loadType: val,
                  loadNum: '',
                });
                classFocus('loadNum');
              }}
            />
            <FocusInput
              label={t('数量')}
              placeholder={loadType ? t('请输入数量, 最大值{}', maxNum) : t('请输入数量')}
              autoFocus
              value={loadNum}
              disabled={!palletCodeEnterFlag || !loadType}
              className="loadNum"
              lineBreak={false}
              onChange={(e) => {
                const num = e.target.value;
                if ((num && validNum(num, maxNum)) || num === '') {
                  store.changeData({
                    loadNum: num,
                  });
                } else {
                  store.changeData({
                    loadNum,
                  });
                }
              }}
            />
          </Form>
        </View>
        <Footer
          footerText={licencePlateEnterFlag ? t('完成') : t('返回')}
          beforeBack={(back) => {
            if (licencePlateEnterFlag) {
              store.deliver({ back });
            } else {
              back();
            }
          }}
        >
          <FooterBtn
            disabled={carLoadingDisabled || !dataLoading}
            onClick={submit}
          >
            {t('装车')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  initLoading: PropTypes.number,
  dataLoading: PropTypes.number,
  headerTitle: PropTypes.string,
  totalContainerNum: PropTypes.number,
  totalPalletNum: PropTypes.number,
  licencePlate: PropTypes.string,
  palletCode: PropTypes.string,
  loadType: PropTypes.number,
  loadTypeList: PropTypes.arrayOf(PropTypes.shape()),
  loadNum: PropTypes.string,
  loadTypeShow: PropTypes.bool,
  licencePlateEnterFlag: PropTypes.bool,
  palletCodeEnterFlag: PropTypes.bool,
};

export default i18n(Container);
