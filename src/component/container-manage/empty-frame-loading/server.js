import { sendPostRequest } from 'lib/public-request';

// 根据当前车牌号获取待发货已装车托盘数和周转箱数
export const getPalletAndContainerAPI = (param) => sendPostRequest({
  url: '/pda_empty_box_handover_list/get_total_pallet_and_container_num',
  param,
}, process.env.WWS_URI);

// 校验托盘号
export const checkPalletCodeAPI = (param) => sendPostRequest({
  url: '/pda_empty_box_handover_list/check_pallet_code',
  param,
}, process.env.WWS_URI);

// 返回（确认发货）
export const deliverAPI = (param) => sendPostRequest({
  url: '/pda_empty_box_handover_list/deliver',
  param,
}, process.env.WWS_URI);

// 装车操作
export const loadingAPI = (param) => sendPostRequest({
  url: '/pda_empty_box_handover_list/loading',
  param,
}, process.env.WWS_URI);

// 完成（查询交接单是否有数据）（PDA）
export const checkHandoverExistAPI = (param) => sendPostRequest({
  url: '/pda_empty_box_handover_list/check_handover_exist',
  param,
}, process.env.WWS_URI);
