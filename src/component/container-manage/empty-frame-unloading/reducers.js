import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { push } from 'react-router-redux';
import { modal, message } from 'common';
import {
  classFocus,
  getHeaderTitle,
} from 'lib/util';
import {
  getHandoverDetailsAPI,
  receivingAPI,
  forceCompleteAPI,
} from './server';

const defaultState = {
  initLoading: 1, // 0加载中 1加载成功 2加载失败
  dataLoading: 1, // 0加载中 1加载成功 2加载失败

  palletCode: '', // 托盘号
  receiveNum: '', // 收货数量

  receivingDisabled: 0, // 确认收货Disabled

  scanedInfo: {
    licencePlate: '', // 车牌号
    containerNum: 0, // 周转箱总数
    containerReceiveNum: 0, // 周转箱收货数
    palletNum: 0, // 托盘总数
    palletReceiveNum: 0, // 托盘收货数
    detailList: [], // 交接单明细列表
  },
  showDetail: false,
  enterFlag: false,
};

export default {
  state: defaultState,
  $init: () => defaultState,
  * init(action, ctx, put) {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }

    markStatus('initLoading');

    yield this.changeData({ headerTitle: getHeaderTitle() });
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  /**
   * 扫描托盘
   * @param {string} palletCode 托盘号
   */
  * scanPallet(palletCode) {
    markStatus('dataLoading');
    const { msg, code, info } = yield getHandoverDetailsAPI({ palletCode });
    if (code === '0') {
      yield this.changeData({
        showDetail: true,
        scanedInfo: info,
        enterFlag: true,
      });
      classFocus('receiveNum');
    } else {
      modal.error({ content: msg, className: 'palletCode' });
      yield this.changeData({
        ...defaultState,
        initLoading: false,
      });
    }
  },
  /**
   * 确认收货
   */
  * confirmReceiving(action) {
    markStatus('dataLoading');
    const { subWarehouseId } = yield 'homework-subwarehouse';
    const { palletCode, receiveNum } = yield '';
    const { type = undefined } = action;

    const params = {
      palletCode,
      subWarehouseId,
      receiveNum,
      type: type || undefined,
    };

    const { msg, code, info } = yield receivingAPI(params);
    if (code === '0') {
      let status;
      const { type: infoType, msg: infoMsg } = info;

      switch (infoType) {
        case 2: // 弹框是否继续收货
          status = yield new Promise((r) => modal.confirm({
            content: infoMsg,
            onOk: () => r('ok'),
            onCancel: () => r('cancel'),
          }));
          if (status === 'ok') {
            yield this.confirmReceiving({ type: 2 });
          }
          if (status === 'cancel') {
            classFocus('receiveNum');
          }
          break;
        case 3: // 弹框是否需要强制完结交接明细
          status = yield new Promise((r) => modal.confirm({
            content: infoMsg,
            onOk: () => r(1),
            onCancel: () => r(2),
            okText: t('部分收货'),
            cancelText: t('强制完结'),
          }));
          yield this.confirmReceiving({ type: status });
          break;
        case 4: // 收货完成
          // 初始化页面
          yield this.changeData({
            ...defaultState,
            initLoading: false,
          });
          classFocus('palletCode');
          message.success(t('收货完成'));
          break;
        default:
          break;
      }
    } else {
      modal.error({ content: msg });
    }
  },
  /**
   * 强制完结
   */
  * forceComplete(param) {
    const { palletCode, back: backCb } = param;
    // 获取子仓
    const { subWarehouseId } = yield 'homework-subwarehouse';
    const { msg, code } = yield forceCompleteAPI({
      palletCode,
      subWarehouseId,
    });
    if (code === '0') {
      backCb(); // 返回
    } else {
      modal.error({ content: msg });
    }
  },
};
