import { sendPostRequest } from 'lib/public-request';

// 根据托盘号获取待收货交接单明细
export const getHandoverDetailsAPI = (param) => sendPostRequest({
  url: '/pda_empty_box_handover_list/get_handover_details',
  param,
}, process.env.WWS_URI);

// 确认收货
export const receivingAPI = (param) => sendPostRequest({
  url: '/pda_empty_box_handover_list/receiving',
  param,
}, process.env.WWS_URI);

// 返回（强制完结）
export const forceCompleteAPI = (param) => sendPostRequest({
  url: '/pda_empty_box_handover_list/force_complete',
  param,
}, process.env.WWS_URI);
