import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import {
  Header, View, FooterBtn, modal, FocusInput, Footer, message,
} from 'common';
import { classFocus, validNum } from 'lib/util';
import store from './reducers';
import styles from './style.css';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      initLoading,
      dataLoading,
      headerTitle,
      palletCode,
      receiveNum,
      scanedInfo,
      showDetail,
      enterFlag,
    } = this.props;

    const {
      licencePlate, // 车牌号
      containerNum, // 周转箱总数
      containerReceiveNum, // 周转箱收货数
      palletNum, // 托盘总数
      palletReceiveNum, // 托盘收货数
      detailList, // 交接单明细列表
    } = scanedInfo;

    return (
      <div>
        <Header title={headerTitle} />
        <View
          flex={false}
          diff={110}
          initLoading={initLoading === 0}
        >
          <Form>
            <FocusInput
              label={t('托盘号')}
              disabled={!dataLoading}
              value={palletCode}
              className="palletCode"
              placeholder={t('请扫描托盘号')}
              lineBreak={false}
              autoFocus
              onChange={(e) => {
                store.changeData({
                  palletCode: e.target.value,
                  receiveNum: '',
                  enterFlag: false,
                });
              }}
              onPressEnter={() => {
                if (palletCode) {
                  store.scanPallet(palletCode);
                }
              }}
            />
          </Form>
          {showDetail && (
            <>
              <div className={styles.blockStyle}>
                <div className={styles.blockTop}>
                  <div>{t('车牌号')}：{licencePlate}</div>
                  <div style={{ display: 'flex', flexWrap: 'wrap' }}>
                    <span className={styles.lineMargin}>
                      {t('周转箱总数')}：{containerNum}/{containerReceiveNum}
                    </span>
                    <span>
                      {t('托盘总数')}：{palletNum}/{palletReceiveNum}
                    </span>
                  </div>
                </div>
                <div className={styles.detailList}>
                  {
                    detailList.length ?
                      (
                        <div className={styles.itemStyle}>
                          <div className={styles.itemPallet}>{t('托盘号')}：{detailList[0].palletCode}</div>
                          <div className={styles.itemHandover}>{t('交接单号')}：{detailList[0].handoverCode}</div>
                          <div>
                            <span className={styles.lineMargin}>{t('装载类型')}：{detailList[0].loadTypeName} </span>
                            <span className={styles.lineMargin}>{t('发货数量')}：{detailList[0].loadNum}</span>
                            <span>{t('已收数量')}：{detailList[0].receiveNum}</span>
                          </div>
                        </div>
                      )
                      :
                      (<div style={{ textAlign: 'center', fontSize: 12 }}>{t('暂无数据')}</div>)
                  }
                </div>
              </div>
              <Form>
                <FocusInput
                  label={t('收货数量')}
                  value={receiveNum}
                  className="receiveNum"
                  placeholder={t('请输入收货数量')}
                  lineBreak={false}
                  autoFocus
                  onChange={(e) => {
                    const num = e.target.value;

                    if (validNum(num, 1650) || num === '') {
                      store.changeData({
                        receiveNum: num,
                      });
                    } else {
                      store.changeData({
                        receiveNum,
                      });
                    }
                  }}
                />
              </Form>
            </>
          )}
        </View>
        <Footer
          beforeBack={(back) => {
            if (palletCode) {
              modal.confirm({
                content: t('托盘号未收货完成，是否需要强制完结交接明细？'),
                onOk: () => {
                  store.forceComplete({ palletCode, back });
                },
                onCancel: () => {
                  back();
                },
                okText: t('强制完结'),
                cancelText: t('直接返回'),
              });
            } else {
              back();
            }
          }}
        >
          <FooterBtn
            disabled={!dataLoading || !enterFlag}
            onClick={() => {
              if (receiveNum === '') {
                message.error(t('收货数量不可为空'));
                classFocus('receiveNum');
                return;
              }
              modal.confirm({
                content: t('是否确认收货?'),
                onOk: () => {
                  store.confirmReceiving({});
                },
                onCancel: () => {
                  classFocus('receiveNum');
                  setTimeout(() => {
                    document.querySelectorAll('.receiveNum')?.forEach((element) => {
                      element?.blur();
                    });
                  }, 100);
                },
              });
            }}
          >
            {t('确认收货')}
          </FooterBtn>
        </Footer>
      </div>

    );
  }
}

Container.propTypes = {
  initLoading: PropTypes.number,
  dataLoading: PropTypes.number,
  headerTitle: PropTypes.string,
  palletCode: PropTypes.string,
  receiveNum: PropTypes.string,
  enterFlag: PropTypes.bool,
  scanedInfo: PropTypes.shape(),
  showDetail: PropTypes.bool,
};

export default i18n(Container);
