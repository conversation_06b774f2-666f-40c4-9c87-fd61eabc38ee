import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form } from 'react-weui/build/packages';
import Icon from '@shein-components/Icon';
import {
  Header,
  View,
  FooterBtn,
  modal,
  message,
  FocusInput,
  Footer,
  PopRadio,
} from 'common';
import { validNum } from 'lib/util';
import ModalInput from './jsx/modalInput';
import Detail from './jsx/detail';
import store, { changeTypeMap, getToolFullPalletNum } from './reducers';
import styles from './style.less';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      dataLoading,
      headerTitle,
      subWarehouseName,
      toolFullPalletNumMap,
      containerPhysicalPropertieList,
      licencePlate,
      licencePlateEnterFlag,
      licencePlateInfo,
      licensePlateType,
      licensePlateTypeInfo,
      toolType,
      toolTypeInfo,
      modalInputShow,
      modalInputInfo,
      detailInfoList,
    } = this.props;

    const {
      receivableSubWarehouseList, // 应收子仓
      schedulingTaskCode, // 调度任务号
    } = licencePlateInfo;

    const {
      licensePlateTypePopUpShow,
      licensePlateTypeList,
    } = licensePlateTypeInfo;

    const {
      toolTypeListPopUpShow,
    } = toolTypeInfo;

    const {
      modalInputIndex,
      modalInputParam,
      modalInputParamName,
      modalInputValue,
    } = modalInputInfo;

    // modalInput 标题
    const modalInputHeaderTitle = modalInputIndex !== null ?
      `${t('修改')}${containerPhysicalPropertieList.find((x) => x.dictCode === toolType[modalInputIndex].toolType)?.dictNameZh || ''} ${modalInputParamName}` :
      '';
    // modalInput 输入框占位符
    const modalInputPlaceholder = modalInputIndex !== null ? `${t('原数值')}: ${modalInputValue}` : t('请输入');

    // 装车
    const submit = () => {
      if (!licencePlateEnterFlag) {
        modal.info({ content: t('请输入车牌号进行装车发货') });
        return;
      }
      if (toolType.length === 0) {
        modal.info({ content: t('工具类型不可为空') });
        return;
      }
      if (toolType.find((x) => (x.palletNum !== 0 && !validNum(x.palletNum)) || x.palletNum < 0)) {
        modal.info({ content: t('托数需要输入大于等于{}的整数', 0) });
        return;
      }
      // 当工具类型不为蓝托盘及红托盘时
      if (toolType.find((x) => ![12, 13].includes(x.toolType) &&
        (!validNum(x.boxShipNum) || x.boxShipNum <= 0))) {
        modal.info({ content: t('周转箱数量需要大于{}的整数', 0) });
        return;
      }
      // 当工具类型为蓝托盘及红托盘时
      if (toolType.find((x) => [12, 13].includes(x.toolType) &&
        (!validNum(x.palletShipNum) || x.palletShipNum <= 0))) {
        modal.info({ content: t('当工具类型为蓝托盘及红托盘时，托盘数量需填入大于{}的整数', 0) });
        return;
      }
      modal.confirm({
        content: t('请确认该车牌下的装车数据是否有误？确认发货后将无法继续装车'),
        onOk: () => {
          store.loadShip();
        },
      });
    };

    if (detailInfoList.length > 0) {
      return (<Detail info={detailInfoList} />);
    }

    return (
      <div>
        <Header
          title={headerTitle}
          homeIcon={false}
        >
          <span onClick={() => store.viewDetail()}>{t('查看')}</span>
        </Header>
        <View
          flex={false}
          diff={100}
          initLoading={dataLoading === 0}
        >
          <Form>
            <FocusInput
              label={t('装货子仓')}
              value={subWarehouseName}
              disabled
              lineBreak={false}
            />
            <FocusInput
              label={t('车牌')}
              value={licencePlate}
              disabled={!dataLoading || licencePlateEnterFlag}
              className="licencePlate"
              placeholder={t('请扫描车牌')}
              lineBreak={false}
              autoFocus
              onChange={(e) => {
                store.changeData({
                  licencePlate: e.target.value,
                });
              }}
              onPressEnter={() => {
                if (licencePlate) {
                  store.scanLicencePlate({ licencePlate });
                }
              }}
            />
            <PopRadio
              label={t('车牌类型')}
              disabled={!licencePlateEnterFlag}
              selectValue={licensePlateType}
              selectList={licensePlateTypeList}
              show={licensePlateTypePopUpShow}
              lineBreak={false}
              className="licensePlateType"
              valueName="dictCode"
              labelName="dictNameZh"
              placeholder={t('请选择')}
              onClick={() => {
                store.changeData({
                  licensePlateTypeInfo: {
                    ...licensePlateTypeInfo,
                    licensePlateTypePopUpShow: true,
                  },
                });
              }}
              onCancel={() => {
                store.changeData({
                  licensePlateTypeInfo: {
                    ...licensePlateTypeInfo,
                    licensePlateTypePopUpShow: false,
                  },
                });
              }}
              onOk={(value) => {
                if (value) {
                  store.changeData({
                    licensePlateType: value,
                    licensePlateTypeInfo: {
                      ...licensePlateTypeInfo,
                      licensePlateTypePopUpShow: false,
                    },
                  });
                }
              }}
            />
            {
              licencePlateEnterFlag && (
                <>
                  {
                    schedulingTaskCode && (
                      <FocusInput
                        label={t('调度任务')}
                        value={schedulingTaskCode}
                        disabled
                        lineBreak={false}
                      />
                    )
                  }
                  {
                    receivableSubWarehouseList.length > 0 && (
                      <FocusInput
                        label={t('应收子仓')}
                        value={receivableSubWarehouseList.map((x) => x.value).join(',')}
                        disabled
                        lineBreak={false}
                      />
                    )
                  }
                </>
              )
            }
          </Form>
          {
            toolType.map((item, index) => (
              <div className={styles.toolTypeItem} key={item.toolType}>
                <div className={[styles.toolTypeItemLine, styles.fontSize16].join(' ')}>
                  <span className={styles.colorBlue}>{containerPhysicalPropertieList.find((x) => x.dictCode === item.toolType)?.dictNameZh || ''}</span>
                  <div
                    className={styles.toolTypeItemIcon}
                    onClick={() => {
                      store.handleToolTypeChange({
                        type: 'delete',
                        index,
                      });
                    }}
                  >
                    <Icon name="m-delete2" />
                  </div>
                </div>
                <div className={[styles.toolTypeItemLine, styles.marginTop5].join(' ')}>
                  <div
                    className={styles.disFlex}
                  >
                    <span className={styles.marginRight8}>{t('托数')}:</span>
                    <div
                      className={styles.toolTypeItemIcon}
                      onClick={() => {
                        if (item.palletNum > 0) {
                          store.handleToolTypeChange({
                            type: 'edit',
                            index,
                            value: item.palletNum - 1,
                            param: changeTypeMap.get(t('托数')),
                          });
                        }
                      }}
                    >
                      <Icon name="m-reduce" />
                    </div>
                    <div
                      className={styles.toolTypeItemInput}
                      onClick={() => {
                        store.changeData({
                          modalInputShow: true,
                          modalInputInfo: {
                            modalInputIndex: index,
                            modalInputParam: changeTypeMap.get(t('托数')),
                            modalInputParamName: t('托数'),
                            modalInputValue: item.palletNum,
                          },
                        });
                      }}
                    >{item.palletNum}
                    </div>
                    <div
                      className={styles.toolTypeItemIcon}
                      onClick={() => {
                        store.handleToolTypeChange({
                          type: 'edit',
                          index,
                          value: item.palletNum + 1,
                          param: changeTypeMap.get(t('托数')),
                        });
                      }}
                    >
                      <Icon name="m-add" />
                    </div>
                  </div>
                  <div className={styles.disFlex}>{t('满托')}{getToolFullPalletNum(item.toolType, toolFullPalletNumMap)}</div>
                </div>

                <div className={[styles.toolTypeItemLine, styles.marginTop5].join(' ')}>
                  {
                    ![12, 13].includes(item.toolType) && ( // 当工具类型不为蓝托盘及红托盘时才显示
                      <div
                        className={styles.disFlex}
                        onClick={() => {
                          store.changeData({
                            modalInputShow: true,
                            modalInputInfo: {
                              modalInputIndex: index,
                              modalInputParam: changeTypeMap.get(t('周转箱数量')),
                              modalInputParamName: t('周转箱数量'),
                              modalInputValue: item.boxShipNum,
                            },
                          });
                        }}
                      >
                        <span>{t('周转箱数量')}: </span>
                        <div className={styles.toolTypeItemInput}>{item.boxShipNum}</div>
                      </div>
                    )
                  }
                  <div
                    className={styles.disFlex}
                    onClick={() => {
                      store.changeData({
                        modalInputShow: true,
                        modalInputInfo: {
                          modalInputIndex: index,
                          modalInputParam: changeTypeMap.get(t('托盘数量')),
                          modalInputParamName: t('托盘数量'),
                          modalInputValue: item.palletShipNum,
                        },
                      });
                    }}
                  >
                    <span className={styles.width73}>{t('托盘数量')}: </span>
                    <div className={styles.toolTypeItemInput}>{item.palletShipNum}</div>
                  </div>
                </div>
              </div>
            ))
          }
          {
            licencePlateEnterFlag && (
              <>
                <div
                  className={styles.addIcon}
                  onClick={() => {
                    store.changeData({
                      toolTypeInfo: {
                        ...toolTypeInfo,
                        toolTypeListPopUpShow: true,
                      },
                    });
                  }}
                >
                  <span>{t('新增工具类型')}+</span>
                </div>
                <PopRadio
                  hideInput
                  selectList={
                    containerPhysicalPropertieList
                      .filter((x) => !toolType.map((y) => y.toolType).includes(x.dictCode))
                  }
                  show={toolTypeListPopUpShow}
                  className="licensePlateType"
                  valueName="dictCode"
                  labelName="dictNameZh"
                  onCancel={() => {
                    store.changeData({
                      toolTypeInfo: {
                        ...toolTypeInfo,
                        toolTypeListPopUpShow: false,
                      },
                    });
                  }}
                  onOk={(value) => {
                    if (value) {
                      store.changeData({
                        toolTypeInfo: {
                          ...toolTypeInfo,
                          toolTypeListPopUpShow: false,
                        },
                      });
                      store.handleToolTypeChange({
                        type: 'add',
                        value,
                      });
                    }
                  }}
                />
              </>
            )
          }
          {
            modalInputShow && (
              <ModalInput
                visible={modalInputShow}
                headerTitle={modalInputHeaderTitle}
                placeholder={modalInputPlaceholder}
                onClose={() => {
                  store.changeData({
                    modalInputShow: false,
                    modalInputInfo: {
                      modalInputIndex: null, // index
                      modalInputParam: '', // 类型：palletNum boxShipNum palletShipNum
                      modalInputParamName: '', // 类型名称：托数 周转箱数量 托盘数量
                      modalInputValue: '', // 值
                    },
                  });
                }}
                onOk={(value) => {
                  if (Number(value) !== 0 && !validNum(value)) {
                    message.error(t('请输入正确的数字'));
                    return;
                  }
                  store.handleToolTypeChange({
                    type: 'edit',
                    index: modalInputIndex,
                    value: Number(value),
                    param: modalInputParam,
                  });
                  store.changeData({
                    modalInputShow: false,
                    modalInputInfo: {
                      modalInputIndex: null, // index
                      modalInputParam: '', // 类型：palletNum boxShipNum palletShipNum
                      modalInputParamName: '', // 类型名称：托数 周转箱数量 托盘数量
                      modalInputValue: '', // 值
                    },
                  });
                }}
              />
            )
          }
        </View>
        <Footer
          footerText={t('返回')}
          beforeBack={(back) => {
            if (licencePlateEnterFlag && toolType.length > 0) {
              modal.confirm({
                title: t('该车牌号已填写装车数据，请确认'),
                onOk: () => {
                  back();
                },
                okText: t('返回'),
              });
            } else {
              back();
            }
          }}
        >
          <FooterBtn
            disabled={!dataLoading}
            onClick={submit}
          >
            {t('发货')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  dataLoading: PropTypes.number,
  headerTitle: PropTypes.string,
  subWarehouseName: PropTypes.string,
  toolFullPalletNumMap: PropTypes.arrayOf(PropTypes.shape()),
  containerPhysicalPropertieList: PropTypes.arrayOf(PropTypes.shape()),
  licencePlate: PropTypes.string,
  licencePlateEnterFlag: PropTypes.bool,
  licencePlateInfo: PropTypes.shape(),
  licensePlateType: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  licensePlateTypeInfo: PropTypes.shape(),
  toolType: PropTypes.arrayOf(PropTypes.shape()),
  toolTypeInfo: PropTypes.shape(),
  modalInputShow: PropTypes.bool,
  modalInputInfo: PropTypes.shape(),
  detailInfoList: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Container);
