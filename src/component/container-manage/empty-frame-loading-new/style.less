.disFlex {
  display: flex;
  align-items: center;
}

.toolTypeItem {
  border: 1px solid #d9d9d9;
  margin: 10px 10px 0 10px;
  padding: 5px 10px;
  border-radius: 10px;
  font-size: 14px;
}

.toolTypeItemLine {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
}

.toolTypeItemInput {
  width: 50px;
  margin-left: 5px;
  border: 1px solid #ccc;
  border-radius: 5px;
  display: flex;
  justify-content: center;
}

.toolTypeItemIcon {
  padding: 0px 10px;
  color: #141737;
}

.detailInfoItem {
  margin: 10px 10px 0 10px;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 10px;
  font-size: 14px;
}

.detailInfoItemLine {
  display: flex;
  justify-content: space-between;
}

.addIcon {
  padding: 10px 14px;
  font-size: 14px;
  color: red;
}

.marginTop5 {
  margin-top: 5px;
}

.marginRight8 {
  margin-right: 8px;
}

.width73 {
  width: 73px;
}

.fontSize16 {
  font-size: 16px;
}

.colorBlue {
  color: #197afa;
}
