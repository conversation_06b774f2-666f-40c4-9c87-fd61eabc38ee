import { sendPostRequest } from 'lib/public-request';

// 装车-扫描车牌号
export const loadScanLicensePlateAPI = (param) => sendPostRequest({
  url: '/pda/supplies_handover/load/scan_license_plate',
  param,
}, process.env.WWS_URI);

// 装车-发货
export const loadShipAPI = (param) => sendPostRequest({
  url: '/pda/supplies_handover/load/ship',
  param,
}, process.env.WWS_URI);

// 装车-查看交接明细(普通车牌)
export const loadQueryHandoverDetailAPI = (param) => sendPostRequest({
  url: '/pda/supplies_handover/load/query_handover_detail',
  param,
}, process.env.WWS_URI);
