import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import {
  Header,
  View,
  Footer,
} from 'common';
import store from '../reducers';
import styles from '../style.less';

class Detail extends Component {
  render() {
    const {
      info,
    } = this.props;

    return (
      <div>
        <Header
          title={t('查看')}
          homeIcon={false}
        />
        <View
          flex={false}
          diff={110}
        >
          <div>
            {
              info.map((item) => (
                <div key={item.id} className={styles.detailInfoItem}>
                  <div className={styles.detailInfoItemLine}>
                    <div className={styles.colorBlue}>{item.toolTypeName || '--'}</div>
                    <div>{t('发货子仓')}: {item.shipSubWarehouseName || '--'}</div>
                  </div>
                  <div>
                    <span>{t('应收子仓')}: {item.receivableSubWarehouseName || '--'}</span>
                  </div>
                  <div className={styles.detailInfoItemLine}>
                    <span>{t('托数')}: {item.palletNum || '--'}</span>
                    {
                      // 当工具类型不为蓝托盘及红托盘时才显示
                      ![12, 13].includes(item.toolType) && (
                        <span>{t('周转箱数量')}: {item.boxShipNum || '--'}</span>
                      )
                    }
                    <span>{t('托盘数量')}: {item.palletShipNum || '--'}</span>
                  </div>
                </div>
              ))
            }
          </div>
        </View>
        <Footer beforeBack={() => {
          store.changeData({
            detailInfoList: [],
          });
        }}
        />
      </div>
    );
  }
}

Detail.propTypes = {
  info: PropTypes.arrayOf(PropTypes.shape()),
};

export default i18n(Detail);
