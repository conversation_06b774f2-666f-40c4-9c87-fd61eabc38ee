import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { put } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import {
  classFocus, getHeaderTitle, licenseRegZhHeader,
} from 'lib/util';
import { message, modal } from 'common';
import { getConfigByCodeApi } from '../../../server/basic/common';
import { selectDict } from '../../../server/basic/data-dictionary';
import {
  loadScanLicensePlateAPI,
  loadShipAPI,
  loadQueryHandoverDetailAPI,
} from './server';

// 工局类型 属性值
export const changeTypeMap = new Map([
  [t('托数'), 'palletNum'],
  [t('周转箱数量'), 'boxShipNum'],
  [t('托盘数量'), 'palletShipNum'],
]);

/**
 * 获取工具满托数量配置
 * @param {String} toolType 工具类型
 * @param {Array} map 参数管理: 工具满托数量配置 [{key: '1', value: '30'}]
 * @return {String} number || 30 默认30
 */
// eslint-disable-next-line max-len
export const getToolFullPalletNum = (toolType, map) => Number(map.find((x) => Number(x.key) === Number(toolType))?.value || 30);

const defaultState = {
  dataLoading: 0, // 数据加载 0加载中 1加载成功 2加载失败
  headerTitle: '', // 头部标题

  subWarehouseId: '', // 子仓id
  subWarehouseName: '', // 子仓名称
  containerPhysicalPropertieList: [], // 容器物理属性列表, 数据字典：CONTAINER_PHYSICAL_PROPERTIES
  toolFullPalletNumMap: [], // 参数管理: 工具满托数量配置
  maxNumberOfTools: { // 参数管理: 最大工具数量配置
    palletNum: 17, // 托数
    boxShipNum: 2000, // 周转箱发货数量
    palletShipNum: 17, // 托盘发货数量
  },
  // 车牌号 相关
  licencePlate: '', // 车牌号
  licencePlateEnterFlag: false, // 扫描车牌号 回车确认
  licencePlateInfo: { // 车辆信息
    // isTractor: false, // 是否牵引车：0-否1-是
    // licensePlate: '', // 车牌号
    // licensePlateType: '', // 车牌类型
    // receivableSubWarehouseList: [ // 应收子仓(信息展示)
    //   {
    //     key: '', // 子仓id
    //     value: '', // 子仓名称
    //   },
    // ],
    // schedulingTaskCode: '', // 调度任务号(信息展示)
    // toolTypeList: [], // 工具类型
  },

  // 车牌类型 相关
  licensePlateType: '', // 车牌类型
  licensePlateTypeInfo: {
    licensePlateTypePopUpShow: false, // 车牌类型 弹窗显示
    licensePlateTypeList: [ // 车牌类型 列表
      {
        dictCode: '1',
        dictNameZh: t('普通车牌'),
      },
      {
        dictCode: '2',
        dictNameZh: t('前置仓车牌'),
      },
      {
        dictCode: '3',
        dictNameZh: t('牵引车牌'),
      },
    ],
  },

  // 工具类型 相关
  toolType: [ // 新增的工具类型
    // {
    //   toolType: '', // 工具类型
    //   palletNum: 20, // 托数
    //   boxShipNum: 10, // 周转箱发货数量
    //   palletShipNum: 10, // 托盘发货数量
    // },
  ],
  toolTypeInfo: {
    toolTypeListPopUpShow: false, // 弹窗显示
  },

  // modalInput
  modalInputShow: false, // 弹窗显示
  modalInputInfo: { // 需要修改的数据 信息(缓存)
    modalInputIndex: null, // index
    modalInputParam: '', // 类型：palletNum boxShipNum palletShipNum
    modalInputParamName: '', // 类型名称：托数 周转箱数量 托盘数量
    modalInputValue: '', // 值
  },

  // detailInfo
  detailInfoList: [ // 详情信息
    // {
    //   id: 1,
    //   toolType: 12,
    //   toolTypeName: 'null',
    //   shipSubWarehouseName: 'null',
    //   receivableSubWarehouseName: 'null',
    //   palletNum: 5,
    //   boxShipNum: 150,
    //   palletShipNum: 0,
    // },
  ],
};

export default {
  state: defaultState,

  $init: () => defaultState,

  changeData(state, data) {
    Object.assign(state, data);
  },

  * init() {
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    } else {
      // console.log(preSubMenu);

      const { subWarehouseId, allList } = preSubMenu;

      yield this.changeData({
        subWarehouseId,
        subWarehouseName: allList.find((x) => x.subWarehouseId === subWarehouseId)?.subWarehouseName || '',
      });
    }

    yield this.changeData({
      headerTitle: getHeaderTitle() || t('新PDA模式空框装车'),
    });

    markStatus('dataLoading');
    const [selectData, configData1, configData2] = yield Promise.all([
      selectDict({ catCode: ['CONTAINER_PHYSICAL_PROPERTIES'] }),
      getConfigByCodeApi({ param: 'TOOL_FULL_PALLET_QUANTITY' }),
      getConfigByCodeApi({ param: 'MAXIMUM_NUMBER_OF_TOOLS' }),
    ]);
    if (selectData.code === '0' && configData1.code === '0' && configData2.code === '0') {
      // 工具满托数量配置
      // value = 1=1;2=2;3=2;4=2;
      const toolFullPalletNumMap = configData1.info.configValue
        .split(';')
        .map((x) => {
          const [key, value] = x.split('=');
          if (value) {
            return {
              key, // 工具类型
              value, // 满托数量
            };
          } else {
            return null;
          }
        }).filter((x) => x !== null) || [];

      // 最大工具数量配置
      let maxNumberOfTools;

      try {
        maxNumberOfTools = JSON.parse(configData2.info.configValue);
      } catch (error) {
        modal.error({ content: t('最大工具数量配置错误') });
        return;
      }

      yield this.changeData({
        containerPhysicalPropertieList: selectData.info.data[0].dictListRsps || [],
        toolFullPalletNumMap,
        maxNumberOfTools,
      });
    } else {
      modal.error({ content: selectData.msg || configData1.msg || configData2.msg });
    }
  },

  /**
   * @description 修改工具类型
   * @param {string} type 类型：add delete edit
   * @param {number} index 索引
   * @param {string} value 值
   * @param {string} param palletNum boxShipNum palletShipNum
   */
  * handleToolTypeChange(action) {
    const {
      type, index, value, param,
    } = action;
    const { toolType, toolFullPalletNumMap, maxNumberOfTools } = yield '';

    if (type === 'add') {
      if (toolType.map((x) => x.toolType).includes(value)) {
        message.error(t('工具类型不能重复'));
        return;
      }
      yield this.changeData({
        toolType: [...toolType, {
          toolType: value, // 工具类型
          palletNum: 0, // 托数
          boxShipNum: 0, // 周转箱发货数量
          palletShipNum: 0, // 托盘发货数量
        }],
      });
      return;
    }
    if (type === 'delete') {
      yield this.changeData({
        toolType: toolType.filter((x, i) => i !== index),
      });
      return;
    }
    if (type === 'edit') {
      if (param === changeTypeMap.get(t('托数'))) {
        const selectedToolType = toolType[index];
        const boxShipNum = ![12, 13].includes(selectedToolType.toolType) ? // 周转箱数量
          // eslint-disable-next-line max-len
          Number(value) * getToolFullPalletNum(selectedToolType.toolType, toolFullPalletNumMap) : // 托数*满托
          0;

        if (Number(value) > Number(maxNumberOfTools.palletNum)) {
          message.error(t('{}不能超过{}', t('托数'), Number(maxNumberOfTools.palletNum)));
          return;
        }
        if (boxShipNum > Number(maxNumberOfTools.boxShipNum)) {
          message.error(t('{}不能超过{}', t('周转箱数量'), Number(maxNumberOfTools.boxShipNum)));
          return;
        }

        yield this.changeData({
          toolType: toolType.map((x, i) => (i === index ? {
            ...x,
            palletNum: Number(value), // 托数
            palletShipNum: Number(value), // 托盘数量
            boxShipNum,
          } : x)),
        });
      } else {
        if (param === changeTypeMap.get(t('周转箱数量')) &&
          Number(value) > Number(maxNumberOfTools.boxShipNum)) {
          message.error(t('{}不能超过{}', t('周转箱数量'), Number(maxNumberOfTools.boxShipNum)));
          return;
        }
        if (param === changeTypeMap.get(t('托盘数量')) &&
          Number(value) > Number(maxNumberOfTools.palletShipNum)) {
          message.error(t('{}不能超过{}', t('托盘数量'), Number(maxNumberOfTools.palletShipNum)));
          return;
        }
        yield this.changeData({
          toolType: toolType.map((x, i) => (i === index ? {
            ...x,
            [param]: Number(value),
          } : x)),
        });
      }
    }
  },

  /**
   * 扫描车牌号
   * @param {string} licencePlate 车牌号
   */
  * scanLicencePlate(action) {
    const { licencePlate } = action;
    const { subWarehouseId } = yield '';

    if (!licenseRegZhHeader.test(licencePlate)) {
      const status = yield new Promise((r) => {
        modal.confirm({
          modalBlurInput: true,
          title: t('请输入正确的车牌号'),
          onOk: () => r(true),
        });
      });
      if (status) {
        yield this.changeData({
          licencePlate: '',
        });
        classFocus('licencePlate');
      }
      return;
    }
    const { code, info, msg } = yield loadScanLicensePlateAPI({
      licencePlate,
      loadSubWarehouseId: subWarehouseId,
    });
    if (code === '0') {
      yield this.changeData({
        licencePlateEnterFlag: true,
        licencePlateInfo: { // 车牌信息
          receivableSubWarehouseList: Object.entries(info.receivableSubWarehouseList || {})
            .map(([key, value]) => ({ key, value })),
          schedulingTaskCode: info.schedulingTaskCode,
          licensePlateType: info.licensePlateType,
        },
        licensePlateType: info.licensePlateType, // 车牌类型
        toolType: (info.toolTypeList || []).map((x) => ({ // 工具类型
          toolType: x, // 工具类型
          palletNum: 0, // 托数
          boxShipNum: 0, // 周转箱发货数量
          palletShipNum: 0, // 托盘发货数量
        })),
      });
    } else {
      message.error(msg);
      yield this.changeData({
        licencePlate: '',
      });
      classFocus('licencePlate');
    }
  },

  /**
   * 查看交接明细
   */
  * viewDetail() {
    const { licencePlate, licencePlateInfo } = yield '';
    const { schedulingTaskCode, licensePlateType } = licencePlateInfo;

    if (!licencePlate) {
      message.error(t('请输入车牌号后再进行查看'));
      return;
    }
    if (Number(licensePlateType) !== 1) {
      message.error(t('当车牌类型为普通车牌时才能进行查看拼车信息'));
      return;
    }

    const { code, info, msg } = yield loadQueryHandoverDetailAPI({
      licencePlate,
      schedulingTaskCode,
    });
    if (code === '0') {
      yield this.changeData({
        detailInfoList: info.data || [],
      });
      if (!info.data.length) {
        message.error(t('暂无数据无法查看'));
      }
    } else {
      message.error(msg);
    }
  },

  /**
   * 装车
   */
  * loadShip() {
    const {
      subWarehouseId, subWarehouseName, headerTitle, toolFullPalletNumMap,
      containerPhysicalPropertieList,
      licencePlate, licensePlateType, licencePlateInfo, toolType,
    } = yield '';

    markStatus('dataLoading');
    const param = {
      licencePlate,
      licensePlateType,
      receivableSubWarehouseIdList:
        licencePlateInfo.receivableSubWarehouseList
          .map((x) => Number(x.key)) || [],
      schedulingTaskCode: licencePlateInfo.schedulingTaskCode || '',
      shipSubWarehouseId: subWarehouseId,
      toolTypeInfos: toolType || [],
    };
    const { msg, code } = yield loadShipAPI(param);

    if (code === '0') {
      message.success(t('装车成功'));
      yield this.changeData({
        ...defaultState,
        dataLoading: 1,
        subWarehouseId,
        subWarehouseName,
        headerTitle,
        toolFullPalletNumMap,
        containerPhysicalPropertieList,
      });
      classFocus('licencePlate');
    } else {
      message.error(msg);
    }
  },
};
