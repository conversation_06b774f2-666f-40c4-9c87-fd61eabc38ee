
.blockStyle{
    padding: 0px 5px;
}
.itemStyle{
    border: 1px solid #d9d9d9;
    border-radius: 10px;
    font-size: 12px;
    padding: 10px;
    margin: 5px 0px;
    color: #333333;
}
.topItem{
    line-height: 26px;
}
.buLuTopItem{
    display: flex;  
    justify-content: space-between; 
}
.topLink{
    color: #1890ff;
    margin-right: 20px;
    font-size: 14px;
}
.middleItem{
    display: flex;
    flex-wrap: wrap;
    line-height: 26px;
}

.receiptNumStyle{
    display: flex;
    align-items: center;
    margin: 5px 0px;
}
.confirmUnloadStyle{
    height: 36px;
    line-height: 1;
    font-size: 14px;
    word-break: break-all;
    margin-top: 10px;
}

.inputLabel{
    width: 100px;
}

.itemInput{
    border: 1px solid rgba(170, 170, 170, 1);
    box-sizing:border-box;
    height: 28px;
    width: 75px;
    text-align: center;
    /* marginBottom: 1, */
    font-size: 14px;
    background-color: rgba(189, 166, 166, 0.08);
     border-radius: 5px ;
}
.middleLabel{
    margin-right: 18px;
}

.toolTypeItemInput {
    width: 55px;
    margin-left: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    display: flex;
    justify-content: center;
  }
.toolTypeItemInputTs{
    width: 55px;
    margin-left: -1px;
    border: 1px solid #ccc;
    border-radius: 5px;
    display: flex;
    justify-content: center;
}
  .disFlex {
    display: flex;
    align-items: center;
  }
  
  .toolTypeItem {
    border: 1px solid #d9d9d9;
    margin: 10px 10px 0 10px;
    padding: 5px 10px;
    border-radius: 10px;
    font-size: 12px;
  }
  
  .toolTypeItemLine {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
  }
  
  
  .toolTypeItemIcon {
    padding: 0px 10px;
    color: #141737;
    font-size: 14px;
  }
  .marginRight14{
      margin-right: 12px;
  }

  .fontSize14{
  font-size: 14px;
  }
  .toolTypeNameColor{
      color: #197afa;
  }
  .itemMarginTop5{
 margin-top: 5px;
  }
  .itemMarginRight8{
      margin-right: 8px;
  }
  .addToolStyle{
      padding: 10px 14px;
      font-size: 14px;
      color: red;
  }
  .noDataStyles{
    text-align: center;
    font-size: 12px;
  }
