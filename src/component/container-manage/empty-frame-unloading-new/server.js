import { sendPostRequest } from 'lib/public-request';

// 卸车-扫描车牌号
// export const scanLicensePlateAPI = (param) => sendPostRequest({
//   url: 'https://soapi-sdk-web01.dotfashion.cn/mock/2916/wws/front/pda/supplies_handover/unload/scan_license_plate',
//   param,
// }, '');
export const scanLicensePlateAPI = (param) => sendPostRequest({
  url: '/pda/supplies_handover/unload/scan_license_plate',
  param,
}, process.env.WWS_URI);

// 卸货-确认/补录
// export const unloadConfirmAPI = (param) => sendPostRequest({
//   url: 'https://soapi-sdk-web01.dotfashion.cn/mock/2916/wws/front/pda/supplies_handover/unload/confirm',
//   param,
// }, '');
export const unloadConfirmAPI = (param) => sendPostRequest({
  url: '/pda/supplies_handover/unload/confirm',
  param,
}, process.env.WWS_URI);
