import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import Icon from '@shein-components/Icon';
import classnames from 'classnames';
import {
  message,
  PopRadio,
} from 'common';
import ModalInput from './modalInput';
import store, { changeTypeMap } from '../reducers';
// import ''
import styles from '../style.less';

class AddRecord extends Component {
  render() {
    const {
      fullPalletObj,
      maxpalletNum,
      maxBoxReceiptNum,
      maxPalletReceiptNum,
      addToolTypeList,
      toolTypeInfo,
      modalInputRecordShow,
      modalInputInfo,
      currenFullPallet,
      currenToolType,
    } = this.props;

    const {
      toolTypeListPopUpShow,
      toolTypeList,
    } = toolTypeInfo;

    const {
      modalInputIndex,
      modalInputParam,
      modalInputParamName,
      modalInputValue,
    } = modalInputInfo;

    const modalInputHeaderTitle = modalInputIndex !== null ?
      `${t('修改')}${toolTypeList.find((x) => x.dictCode === addToolTypeList[modalInputIndex].toolType)?.dictNameZh || ''} ${modalInputParamName}` :
      '';
    const modalInputPlaceholder = modalInputIndex !== null ? `${t('原数值')}: ${modalInputValue}` : t('请输入');

    return (
      <div>
        {
            // todo 工具类型
            addToolTypeList.map((item, index) => (
              <div className={styles.toolTypeItem} key={item.toolType}>
                <div
                  className={classnames(styles.toolTypeItemLine, styles.fontSize14)}
                >
                  <span className={styles.toolTypeNameColor}>{toolTypeList.find((x) => x.dictCode === item.toolType)?.dictNameZh || ''}</span>
                  <div
                    className={styles.toolTypeItemIcon}
                    onClick={() => {
                      store.handleToolTypeChange({
                        type: 'delete',
                        index,
                      });
                    }}
                  >
                    <Icon name="m-delete2" />
                  </div>
                </div>
                <div className={classnames(styles.toolTypeItemLine, styles.itemMarginTop5)}>
                  <div
                    className={styles.disFlex}
                  >
                    <span className={styles.itemMarginRight8}>{t('托数')}:</span>
                    <div
                      className={styles.toolTypeItemIcon}
                      onClick={() => {
                        if (item.palletNum > 0) {
                          store.handleToolTypeChange({
                            type: 'edit',
                            index,
                            value: item.palletNum - 1,
                            param: changeTypeMap.get(t('托数')),
                            fullPallet: fullPalletObj[item.toolType] || 30,
                          });
                        }
                      }}
                    >
                      <Icon name="m-reduce" />
                    </div>
                    <div
                      className={styles.toolTypeItemInputTs}
                      onClick={() => {
                        store.changeData({
                          modalInputRecordShow: true,
                          modalInputInfo: {
                            modalInputIndex: index,
                            modalInputParam: changeTypeMap.get(t('托数')),
                            modalInputParamName: t('托数'),
                            modalInputValue: item.palletNum,
                          },
                          currenFullPallet: fullPalletObj[item.toolType] || 30,
                          currenToolType: item.toolType,
                        });
                      }}
                    >{item.palletNum}
                    </div>
                    <div
                      className={styles.toolTypeItemIcon}
                      onClick={() => {
                        const newValue = item.palletNum + 1;
                        if (newValue > maxpalletNum) {
                          message.error(t('托数不能超过{}', maxpalletNum));
                          return;
                        }
                        store.handleToolTypeChange({
                          type: 'edit',
                          index,
                          value: newValue,
                          param: changeTypeMap.get(t('托数')),
                          fullPallet: fullPalletObj[item.toolType] || 30,
                        });
                      }}
                    >
                      <Icon name="m-add" />
                    </div>
                  </div>
                  <div className={styles.disFlex}>{t('满托')}{fullPalletObj[item.toolType] || 30}</div>
                </div>
                <div className={classnames(styles.toolTypeItemLine, styles.itemMarginTop5)}>
                  {/* 当工具类型不为蓝托盘及红托盘时才显示 */}
                  {![12, 13].includes(item.toolType) && (
                  <div
                    className={styles.disFlex}
                    onClick={() => {
                      store.changeData({
                        modalInputRecordShow: true,
                        modalInputInfo: {
                          modalInputIndex: index,
                          modalInputParam: changeTypeMap.get(t('周转箱数量')),
                          modalInputParamName: t('周转箱数量'),
                          modalInputValue: item.boxReceiptNum,
                        },
                        currenFullPallet: fullPalletObj[item.toolType] || 30,
                        currenToolType: item.toolType,
                      });
                    }}
                  >
                    <span>{t('周转箱数量')}: </span>
                    <div className={styles.toolTypeItemInput}>{item.boxReceiptNum}</div>
                  </div>
                  )}
                  <div
                    className={styles.disFlex}
                    onClick={() => {
                      store.changeData({
                        modalInputRecordShow: true,
                        modalInputInfo: {
                          modalInputIndex: index,
                          modalInputParam: changeTypeMap.get(t('托盘数量')),
                          modalInputParamName: t('托盘数量'),
                          modalInputValue: item.palletReceiptNum,
                        },
                        currenFullPallet: fullPalletObj[item.toolType] || 30,
                        currenToolType: item.toolType,
                      });
                    }}
                  >
                    <span className={[12, 13].includes(item.toolType) ? styles.marginRight14 : ''}>{t('托盘数量')}: </span>
                    <div className={styles.toolTypeItemInput}>{item.palletReceiptNum}</div>
                  </div>
                </div>
              </div>
            ))
          }
        <div
          className={styles.addToolStyle}
          onClick={() => {
            store.changeData({
              toolTypeInfo: {
                ...toolTypeInfo,
                toolTypeListPopUpShow: true,
              },
            });
          }}
        >
          <span>{t('新增工具类型')}+</span>
        </div>
        <PopRadio
          // disabled={!palletCodeEnterFlag}
          hideInput
          selectList={toolTypeList}
          show={toolTypeListPopUpShow}
          className="licensePlateType"
          valueName="dictCode"
          labelName="dictNameZh"
          onCancel={() => {
            store.changeData({
              toolTypeInfo: {
                ...toolTypeInfo,
                toolTypeListPopUpShow: false,
              },
            });
          }}
          onOk={(value) => {
            // 前端判断工具类型是否为空
            if (!toolTypeList.map((e) => e.dictCode).includes(value)) {
              return;
            }
            store.changeData({
              toolTypeInfo: {
                ...toolTypeInfo,
                toolTypeListPopUpShow: false,
              },
            });
            store.handleToolTypeChange({
              type: 'add',
              value,
            });
          }}
        />
        {
            modalInputRecordShow && (
              <ModalInput
                visible={modalInputRecordShow}
                headerTitle={modalInputHeaderTitle}
                placeholder={modalInputPlaceholder}
                onClose={() => {
                  store.changeData({
                    modalInputRecordShow: false,
                    modalInputInfo: {
                      modalInputIndex: null, // index
                      modalInputParam: '', // 类型：palletNum boxReceiptNum palletReceiptNum
                      modalInputParamName: '', // 类型名称：托数 周转箱数量 托盘数量
                      modalInputValue: '', // 值
                    },
                  });
                }}
                onOk={(value) => {
                  const newValue = Number(value);
                  // const { modalInputParamName, modalInputParam } = modalInputInfo;
                  let maxValue = 0;
                  // palletNum boxReceiptNum palletReceiptNum
                  switch (modalInputParam) {
                    case 'palletNum':
                      maxValue = maxpalletNum;
                      break;
                    case 'boxReceiptNum':
                      maxValue = maxBoxReceiptNum;
                      break;
                    case 'palletReceiptNum':
                      maxValue = maxPalletReceiptNum;
                      break;
                    default:
                      break;
                  }
                  if (newValue > maxValue) {
                    message.error(t('{}不能超过{}', modalInputParamName, maxValue));
                    return;
                  }
                  // 不为蓝托盘及红托盘时 周转箱数量需要大于0
                  if (![12, 13].includes(currenToolType) && modalInputParam === 'boxReceiptNum' && newValue === 0) {
                    message.error(t('周转箱数量需要大于0'));
                    return;
                  }
                  if ([12, 13].includes(currenToolType) && modalInputParam === 'palletReceiptNum' && newValue === 0) {
                    message.error(t('工具类型为蓝托盘及红托盘时,托盘数量需要大于0'));
                    return;
                  }
                  store.handleToolTypeChange({
                    type: 'edit',
                    index: modalInputIndex,
                    value: newValue,
                    param: modalInputParam,
                    fullPallet: currenFullPallet,
                  });
                  store.changeData({
                    modalInputRecordShow: false,
                    modalInputInfo: {
                      modalInputIndex: null, // index
                      modalInputParam: '', // 类型：palletNum boxReceiptNum palletReceiptNum
                      modalInputParamName: '', // 类型名称：托数 周转箱数量 托盘数量
                      modalInputValue: '', // 值
                    },
                  });
                }}
              />
            )
          }
      </div>
    );
  }
}

AddRecord.propTypes = {
  fullPalletObj: PropTypes.shape(),
  addToolTypeList: PropTypes.arrayOf(PropTypes.shape()),
  toolTypeInfo: PropTypes.shape(),

  modalInputRecordShow: PropTypes.bool,
  modalInputInfo: PropTypes.shape(),
  maxBoxReceiptNum: PropTypes.number,
  maxPalletReceiptNum: PropTypes.number,
  maxpalletNum: PropTypes.number,
  currenFullPallet: PropTypes.number,
  currenToolType: PropTypes.number,
};

export default i18n(AddRecord);
