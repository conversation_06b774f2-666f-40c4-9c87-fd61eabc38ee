import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Dialog } from 'react-weui/build/packages';
import { validNum } from 'lib/util';
import {
  FocusInput,
} from 'common';

class ModalInput extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: '',
    };
  }

  render() {
    const {
      visible,
      headerTitle,
      placeholder,
      onOk,
      onClose,
    } = this.props;

    const {
      value,
    } = this.state;
    return (
      <Dialog
        title={headerTitle || ''}
        show={visible}
        buttons={[{
          type: 'default',
          label: t('关闭'),
          onClick: () => {
            onClose();
          },
        }, {
          type: 'primary',
          label: t('确定'),
          onClick: () => {
            onOk(value);
          },
        }]}
      >
        <FocusInput
          value={value}
          placeholder={placeholder || t('请输入')}
          autoFocus
          onChange={(e) => {
            const inputValue = e.target.value;
            if (Number(inputValue) !== 0 && !validNum(inputValue)) {
              return;
            }
            this.setState({
              value: inputValue,
            });
          }}
        />
      </Dialog>
    );
  }
}

ModalInput.propTypes = {
  visible: PropTypes.bool.isRequired,
  headerTitle: PropTypes.string,
  placeholder: PropTypes.string,
  onOk: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default ModalInput;
