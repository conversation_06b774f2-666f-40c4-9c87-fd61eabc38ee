import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { Form, Button } from 'react-weui/build/packages';
import {
  Header, View, FooterBtn, modal, FocusInput, Footer,
  message,
} from 'common';
import Icon from '@shein-components/Icon';
import { validNum } from 'lib/util';
import style from 'common/common.css';
import store, { changeTypeMap } from './reducers';
import styles from './style.less';
import ModalInput from './jsx/modalInput';
import AddRecord from './jsx/add-record';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      initLoading,
      dataLoading,
      headerTitle,
      licencePlateInfo,
      showDetail,
      isAddRecord,
      licensePlate,
      licencePlateEnterFlag,
      subwarehouseName,
      editObj,
      modalInputShow,
      modalInputInfo,
      modalInputHeaderTitle,
      modalInputPlaceholder,
      addToolTypeList,
      toolTypeInfo,
      maxPalletReceiptNum,
      maxBoxReceiptNum,
    } = this.props;

    const {
      toolTypeList,
    } = toolTypeInfo;

    const {
      modalInputParam,
      modalInputParamName,
      id,
    } = modalInputInfo;

    const {
      detailInfos, // 交接单明细列表
      schedulingTaskCode, // 调度任务号
    } = licencePlateInfo;

    return (
      <div>
        <Header style={{ width: '100%' }} title={isAddRecord ? t('补录') : headerTitle}>
          {showDetail && !isAddRecord && (
          <div>
            {/* 补录 */}
            <div onClick={() => {
              store.changeData({ isAddRecord: true, addToolTypeList: [] });
            }}
            >{t('补录')}
            </div>
          </div>
          )}
        </Header>
        <View
          flex={false}
          diff={100}
          initLoading={initLoading === 0}
        >
          <Form className={style.pageStikyChild}>
            <FocusInput
              label={t('卸货子仓')}
              disabled
              value={subwarehouseName}
              lineBreak={false}
            />
            <FocusInput
              label={t('车牌')}
              placeholder={t('请扫描车牌')}
              autoFocus
              value={licensePlate}
              disabled={!dataLoading ||
                 licencePlateEnterFlag || licencePlateInfo.detailInfos.length > 0}
              className="licensePlate"
              lineBreak={false}
              onChange={(e) => {
                store.changeData({
                  licensePlate: e.target.value,
                });
              }}
              onPressEnter={() => {
                if (licensePlate) {
                  store.scanLicencePlate(licensePlate);
                }
              }}
            />
            {
              schedulingTaskCode && (
              <FocusInput
                label={t('调度任务')}
                disabled
                value={schedulingTaskCode}
                placeholder={t('请扫描托盘号')}
                lineBreak={false}
              />
              )
            }
          </Form>
          {/* 补录 */}
          {
             isAddRecord && <AddRecord {...this.props} />
           }

          {showDetail && !isAddRecord && (
            <div className={styles.blockStyle}>
              <div className={styles.detailInfos}>
                {
                    detailInfos.length > 0 ?
                      detailInfos.map((e, index) => (e.id ? (
                        <div className={styles.itemStyle} key={JSON.stringify(index)}>
                          <div className={styles.topItem}>
                            <span className={styles.topLink}>{toolTypeList.find((x) => x.dictCode === Number(e.toolType))?.dictNameZh || ''} </span>
                            <span>{t('发货子仓')}：{e.shipSubWarehouseName}</span>
                          </div>
                          <div className={styles.middleItem}>
                            <span className={styles.middleLabel}>{t('托数')}：{e.palletNum} </span>
                            {/* 当物资交接明细的周转箱发货数量大于0时显示 按周转箱发货数量/周转箱收货数量显示 */}
                            {e.boxShipNum > 0 && <span className={styles.middleLabel}>{t('周转箱总数')}：{e.boxShipNum}/{e.boxReceiptNum} </span>}
                            {/* 按托盘发货数量/托盘收货数量显示 */}
                            <span>{t('托盘总数')}：{e.palletShipNum}/{e.palletReceiptNum} </span>
                          </div>
                          {/* 当物资交接明细的周转箱发货数量大于0时且周转箱发货数量大于周转箱收货数量时显示 */}
                          {e.boxShipNum > 0 && e.boxShipNum > e.boxReceiptNum && (
                          <div className={styles.receiptNumStyle}>
                            <span className={styles.inputLabel}>
                              {t('本次收货周转箱')}
                            </span>
                            <div
                              className={styles.toolTypeItemInput}
                              onClick={() => {
                                const defaultValue = editObj[e.id] && editObj[e.id].boxReceiptNum;
                                const title = toolTypeList.find((x) => x.dictCode === e.toolType)?.dictNameZh || '';
                                store.changeData({
                                  modalInputHeaderTitle: t('修改{}本次收货周转箱', title),
                                  modalInputPlaceholder: defaultValue ? t('原数值{}', defaultValue) : t('请输入'),
                                  modalInputShow: true,
                                  modalInputInfo: {
                                    modalInputIndex: '',
                                    modalInputParam: changeTypeMap.get(t('本次收货周转箱')),
                                    modalInputParamName: t('本次收货周转箱'),
                                    modalInputValue: defaultValue,
                                    id: e.id,
                                  },
                                });
                              }}
                            >{editObj[e.id] && editObj[e.id].boxReceiptNum}
                            </div>
                          </div>
                          )}
                          {/* 当物资交接明细的托盘发货数量大于0时且托盘发货数量大于托盘收货数量时显示 */}
                          {e.palletShipNum > 0 && e.palletShipNum > e.palletReceiptNum && (
                          <div className={styles.receiptNumStyle}>
                            <span className={styles.inputLabel}>
                              {t('本次收货托盘')}
                            </span>
                            <div
                              className={styles.toolTypeItemInput}
                              onClick={() => {
                                // eslint-disable-next-line max-len
                                const defaultValue = editObj[e.id] && editObj[e.id].palletReceiptNum;
                                const title = toolTypeList.find((x) => x.dictCode === e.toolType)?.dictNameZh || '';
                                store.changeData({
                                  modalInputShow: true,
                                  modalInputHeaderTitle: t('修改{}本次收货托盘', title),
                                  modalInputPlaceholder: defaultValue ? t('原数值{}', defaultValue) : t('请输入'),
                                  modalInputInfo: {
                                    modalInputIndex: '',
                                    modalInputParam: changeTypeMap.get(t('本次收货托盘')),
                                    modalInputParamName: t('本次收货托盘'),
                                    modalInputValue: defaultValue,
                                    id: e.id,
                                  },
                                });
                              }}
                            >{editObj[e.id] && editObj[e.id].palletReceiptNum}
                            </div>
                          </div>
                          )}
                          <Button
                            className={styles.confirmUnloadStyle}
                            onClick={() => {
                              modal.confirm({
                                modalBlurInput: true,
                                content: t('请确认本次收货数量是否提交?'),
                                onOk: () => {
                                  store.confirmUnload(e);
                                },
                              });
                            }}
                          >{t('确认卸货')}
                          </Button>
                        </div>
                      ) : (
                        <div className={styles.itemStyle} key={JSON.stringify(index)}>
                          <div className={styles.buLuTopItem}>
                            <div>
                              <span className={styles.topLink}>{toolTypeList.find((x) => x.dictCode === e.toolType)?.dictNameZh || ''} </span>
                              <span>{t('补录')}</span>
                            </div>
                            <div
                              className={styles.toolTypeItemIcon}
                              onClick={() => {
                                store.deleteToolType(index);
                              }}
                            >
                              <Icon name="m-delete2" />
                            </div>
                          </div>
                          <div className={styles.middleItem}>
                            <span className={styles.middleLabel}>{t('托数')}：{e.palletNum} </span>
                            {/* 当物资交接明细的周转箱收货数量大于0时显示 */}
                            {e.boxReceiptNum > 0 && <span className={styles.middleLabel}>{t('周转箱总数')}：{e.boxReceiptNum} </span>}
                            {/* 按托盘收货数量大于0时显示 */}
                            {e.palletReceiptNum > 0 && <span>{t('托盘总数')}：{e.palletReceiptNum} </span>}
                          </div>
                          <Button
                            className={styles.confirmUnloadStyle}
                            onClick={() => {
                              modal.confirm({
                                modalBlurInput: true,
                                content: t('请确认本次收货数量是否提交?'),
                                onOk: () => {
                                  store.confirmUnload(e);
                                },
                              });
                            }}
                          >{t('确认卸货')}
                          </Button>
                        </div>
                      )))
                      :
                      (<div className={styles.noDataStyles}>{t('暂无数据')}</div>)
                  }
              </div>
            </div>
          )}
          {
            modalInputShow && (
              <ModalInput
                visible={modalInputShow}
                headerTitle={modalInputHeaderTitle}
                placeholder={modalInputPlaceholder}
                onClose={() => {
                  store.changeData({
                    modalInputShow: false,
                    modalInputInfo: {
                      modalInputIndex: null, // index
                      modalInputParam: '', // 类型：palletNum boxShipNum palletShipNum
                      modalInputParamName: '', // 类型名称：托数 周转箱数量 托盘数量
                      modalInputValue: '', // 值
                      id: '',
                    },
                  });
                }}
                onOk={(value) => {
                  const newValue = Number(value);
                  let maxValue = 0;
                  // palletNum boxReceiptNum palletReceiptNum
                  switch (modalInputParam) {
                    case 'boxReceiptNum':
                      maxValue = maxBoxReceiptNum;
                      break;
                    case 'palletReceiptNum':
                      maxValue = maxPalletReceiptNum;
                      break;
                    default:
                      break;
                  }
                  if (newValue > maxValue) {
                    message.error(t('{}不能超过{}', modalInputParamName, maxValue));
                    return;
                  }
                  if (validNum(value) || value === '') {
                    store.changeEditData({
                      [id]: {
                        ...editObj[id],
                        [modalInputParam]: Number(value),
                      },
                    });
                  }
                  store.changeData({
                    modalInputShow: false,
                    modalInputInfo: {
                      modalInputIndex: null, // index
                      modalInputParam: '', // 类型：palletNum boxShipNum palletShipNum
                      modalInputParamName: '', // 类型名称：托数 周转箱数量 托盘数量
                      modalInputValue: '', // 值
                      id: '',
                    },
                  });
                }}
              />
            )
          }
        </View>
        <Footer
          beforeBack={(back) => {
            if (showDetail) {
              const confirmTitle = isAddRecord ? t('该车牌号已填写补录的装车数据，请确认') : t('该车牌号已填写卸车数据，请确认？');
              if (isAddRecord && addToolTypeList.length === 0) {
                store.changeData({
                  isAddRecord: false,
                });
                return;
              }
              modal.confirm({
                content: confirmTitle,
                onCancel: () => {
                  if (isAddRecord) {
                    // 返回PDA模式空框卸车初始页面
                    store.changeData({
                      isAddRecord: false,
                      addToolTypeList: [],
                    });
                  } else {
                    back();
                  }
                },
                okText: t('取消'),
                cancelText: t('直接返回'),
              });
            } else {
              back();
            }
          }}
        >
          {isAddRecord && (
            <FooterBtn
              disabled={!dataLoading || addToolTypeList.length === 0}
              onClick={() => {
                if (addToolTypeList.some((e) => e.toolType === null)) {
                  modal.error({ content: t('工具类型不为空') });
                  return;
                }
                //  当工具类型为蓝托盘及红托盘时 不校验
                if (addToolTypeList.some((e) => e.boxReceiptNum === 0
                && ![12, 13].includes(e.toolType))) {
                  modal.error({ content: t('周转箱数量需要大于0的整数') });
                  return;
                }
                // 当工具类型为蓝托盘及红托盘时 需要大于0
                if (addToolTypeList.some((e) => e.palletReceiptNum === 0
                && [12, 13].includes(e.toolType))) {
                  modal.error({ content: t('工具类型为蓝托盘及红托盘时,托盘数量需要大于0的整数') });
                  return;
                }
                modal.confirm({
                  content: t('请确认该车牌下的补录的装车数据是否有误?'),
                  onOk: () => {
                    store.addRecordToEditObj();
                  },
                });
              }}
            >
              {t('确认补录')}
            </FooterBtn>
          )}

        </Footer>
      </div>

    );
  }
}

Container.propTypes = {
  initLoading: PropTypes.number,
  dataLoading: PropTypes.number,
  headerTitle: PropTypes.string,
  receiveNum: PropTypes.string,
  enterFlag: PropTypes.bool,
  licencePlateInfo: PropTypes.shape(),
  showDetail: PropTypes.bool,
  isAddRecord: PropTypes.bool,
  licensePlate: PropTypes.string,
  licencePlateEnterFlag: PropTypes.bool,
  subwarehouseName: PropTypes.string,
  editObj: PropTypes.shape(),
  modalInputShow: PropTypes.bool,
  modalInputInfo: PropTypes.shape(),
  modalInputHeaderTitle: PropTypes.string,
  modalInputPlaceholder: PropTypes.string,
  addToolTypeList: PropTypes.arrayOf(PropTypes.shape()),
  toolTypeInfo: PropTypes.shape(),
  maxBoxReceiptNum: PropTypes.number,
  maxPalletReceiptNum: PropTypes.number,
};

export default i18n(Container);
