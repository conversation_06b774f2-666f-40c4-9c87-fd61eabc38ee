import { t } from '@shein-bbl/react';
import { markStatus } from 'rrc-loader-helper';
import { push } from 'react-router-redux';
import { modal, message } from 'common';
import {
  getHeaderTitle,
} from 'lib/util';
import {
  unloadConfirmAPI,
  scanLicensePlateAPI,
} from './server';
import { selectDict } from '../../../server/basic/data-dictionary';
import { getConfigByCodeApi } from '../../../server/basic/common';

export const changeTypeMap = new Map([
  [t('本次收货周转箱'), 'boxReceiptNum'],
  [t('本次收货托盘'), 'palletReceiptNum'],
  [t('托数'), 'palletNum'],
  [t('周转箱数量'), 'boxReceiptNum'],
  [t('托盘数量'), 'palletReceiptNum'],
]);

const defaultState = {
  initLoading: 1, // 0加载中 1加载成功 2加载失败
  dataLoading: 1, // 0加载中 1加载成功 2加载失败
  licensePlate: '', // 车牌号
  subwarehouseName: '', // 卸货子仓
  licencePlateInfo: {
    licensePlate: '', // 车牌号
    schedulingTaskCode: '', // 调度任务号
    detailInfos: [], // 物资交接明细
  },
  showDetail: false,
  editObj: {
    // boxReceiptNum: '', // 本次收货周转箱
    // palletReceiptNum: '' // 本次收货托盘
  },
  modalInputHeaderTitle: '',
  modalInputPlaceholder: '',
  // modalInput
  modalInputShow: false, // 弹窗显示
  modalInputInfo: { // 需要修改的数据 信息(缓存)
    modalInputIndex: null, // index
    modalInputParam: '', // 类型：palletNum boxShipNum palletShipNum
    modalInputParamName: '', // 类型名称：托数 周转箱数量 托盘数量
    modalInputValue: '', // 值
    id: '',
  },

  fullPalletObj: [], // 列表
  maxpalletNum: 17, // 托数上限
  maxBoxReceiptNum: 2000, // 周转箱上限
  maxPalletReceiptNum: 20, // 托盘上限
  toolTypeInfo: {
    toolTypeListPopUpShow: false, // 弹窗显示
    toolTypeList: [], // 工具类型
  },
  // 工具类型 相关
  addToolTypeList: [ // 新增的工具类型
    // {
    //   toolType: '', // 工具类型
    //   palletNum: 20, // 托数
    //   boxReceiptNum: 10, // 周转箱收货数量
    //   palletReceiptNum: 10, // 托盘收货数量
    // },
  ],
  isAddRecord: false,
  modalInputRecordShow: false,
  currenFullPallet: 0, // 当前满托数
  currenToolType: 0,
};

export default {
  state: defaultState,
  $init: () => defaultState,
  * init(action, ctx, put) {
    yield this.changeData({
      headerTitle: getHeaderTitle() || t('新PDA模式空框卸车'),
    });
    // 判断是否有先在作业子仓页选择子仓，没则跳转过去
    const preSubMenu = yield 'homework-subwarehouse';
    if (!preSubMenu || !preSubMenu.subWarehouseId) {
      yield put(push('/homework-subwarehouse'));
      return;
    }
    const item = preSubMenu.allList.find((e) => e.subWarehouseId === preSubMenu.subWarehouseId);
    markStatus('initLoading');
    yield this.changeData({
      subwarehouseName: item?.subWarehouseName,
    });

    // 获取上限参数配置
    const maxRes = yield getConfigByCodeApi({ param: 'MAXIMUM_NUMBER_OF_TOOLS' });
    if (maxRes.code === '0' && maxRes.info && maxRes.info.configValue) {
      try {
        const configValueObj = JSON.parse(maxRes.info.configValue);
        yield this.changeData({
          maxpalletNum: configValueObj.palletNum,
          maxBoxReceiptNum: configValueObj.boxShipNum,
          maxPalletReceiptNum: configValueObj.palletShipNum,
        });
      } catch (error) {
        message.error(t('获取工具装车上限配置失败'));
      }
    } else {
      message.error(maxRes.msg || t('获取工具装车上限配置失败'));
    }

    // 获取满托数参数配置
    const fullRes = yield getConfigByCodeApi({ param: 'TOOL_FULL_PALLET_QUANTITY' });
    if (fullRes.code === '0' && fullRes.info && fullRes.info.configValue) {
      const fullPalletObj = {};
      try {
        fullRes.info.configValue.split(';')?.forEach(
          (e) => {
            const items = e.split('=');
            // eslint-disable-next-line prefer-destructuring
            fullPalletObj[items[0]] = Number(items[1]);
          },
        );
        yield this.changeData({
          fullPalletObj,
        });
      } catch (error) {
        message.error(t('获取满托数量配置失败'));
      }
    } else {
      message.error(fullRes.msg || t('获取工具满托数量配置失败'));
    }

    // 获取容器物理属性（工具类型下拉框）
    const selectData = yield selectDict({ catCode: ['CONTAINER_PHYSICAL_PROPERTIES'] });
    if (selectData.code === '0') {
      const { toolTypeInfo } = yield '';
      yield this.changeData({
        toolTypeInfo: {
          ...toolTypeInfo,
          toolTypeList: selectData.info.data.find((v) => v.catCode === 'CONTAINER_PHYSICAL_PROPERTIES').dictListRsps,
        },
      });
    } else {
      message.error(selectData.msg);
    }
  },
  changeData(state, data) {
    Object.assign(state, data);
  },
  changeEditData(state, data) {
    Object.assign(state.editObj, data);
  },

  /**
   * 扫描车牌号
   * @param {string} licensePlate 车牌号
   */
  * scanLicencePlate(licensePlate) {
    markStatus('dataLoading');
    const { code, info, msg } = yield scanLicensePlateAPI({ licencePlate: licensePlate });
    if (code === '0') {
      yield this.updateEditObj(info.detailInfos);
      yield this.changeData({
        licencePlateInfo: info,
        showDetail: true,
        addToolTypeList: [],
      });
    } else {
      yield this.changeData({
        licensePlate: '',
      });
      modal.error({ content: msg, className: 'licensePlate' });
    }
  },

  // 更新编辑数据
  * updateEditObj(detailInfos) {
    // 便于输入框回写，显示默认值
    const resetEditObj = {};
    detailInfos.forEach((e) => {
      // 本次可收数量(周转箱) 用于提交校验  周转箱发货数量-周转箱收货数量
      const accepBoxReceiptNum = e.boxShipNum - e.boxReceiptNum;
      // 本次可收数量(托盘) 用于提交校验 托盘发货数量-托盘收货数量
      const accepPalletReceiptNum = e.palletShipNum - e.palletReceiptNum;
      resetEditObj[e.id] = {
        accepBoxReceiptNum,
        accepPalletReceiptNum,
        boxReceiptNum: accepBoxReceiptNum,
        palletReceiptNum: accepPalletReceiptNum,
      };
    });
    yield this.changeData({
      editObj: resetEditObj,
    });
  },

  // 删除补录数据
  * deleteToolType(index) {
    const { licencePlateInfo } = yield '';
    const { detailInfos } = licencePlateInfo;
    yield this.changeData({
      licencePlateInfo: {
        ...licencePlateInfo,
        detailInfos: detailInfos.filter((x, i) => i !== index),
      },
    });
  },
  // 添加补录数据 物资交接明细
  * addRecordToEditObj() {
    const { addToolTypeList, licencePlateInfo } = yield '';
    const { detailInfos } = licencePlateInfo;
    yield this.changeData({
      isAddRecord: false,
      licencePlateInfo: {
        ...licencePlateInfo,
        detailInfos: [...detailInfos, ...addToolTypeList],
      },
      addToolTypeList: [],
    });
  },
  /**
   * 确认卸货/补录
   */
  * confirmUnload(param) {
    const { editObj, licencePlateInfo } = yield '';
    // 补录不需要校验 本次收货数量大于可收数量（发货数量-已收数量） 弹框提示
    if (param.id &&
      (
        editObj[param.id].boxReceiptNum > editObj[param.id].accepBoxReceiptNum ||
      editObj[param.id].palletReceiptNum > editObj[param.id].accepPalletReceiptNum
      )) {
      const numConfirmStatus = yield new Promise((r) => modal.confirm({
        modalBlurInput: true,
        content: t('本次收货数量大于可收数量（发货数量-已收数量），请确认是否继续收货?'),
        onOk: () => r('ok'),
        onCancel: () => r('cancel'),
      }));
      if (numConfirmStatus === 'cancel') {
        return;
      }
    }

    markStatus('dataLoading');
    const { subWarehouseId } = yield 'homework-subwarehouse';
    const { msg, code, info } = yield unloadConfirmAPI({
      ...param,
      ...editObj[param.id],
      licencePlate: licencePlateInfo.licensePlate,
      schedulingTaskCode: licencePlateInfo.schedulingTaskCode,
      isSupplement: param.id ? 0 : 1, // 是否补录（0-否 1-是）
      receiptSubWarehouseId: subWarehouseId,
    });
    if (code === '0') {
      const { detailInfos } = licencePlateInfo;
      const supplementList = detailInfos.filter((e) => !e.id);
      message.success(t('卸车完成'));
      //  卸车完成 若车牌号下无待收货或收货中的交接单数据时，初始化页面
      // is_exist_not_finish 当前车牌号是否存在未完结的交接单据 0:不存在 1:存在
      const { isExistNotFinish } = info;
      if (isExistNotFinish === 1) {
        // 如果点击卸货是补录的数据，需要过滤掉点击处理的那一条
        const concatList = param.id ?
          supplementList : supplementList.filter((e) => e.toolType !== param.toolType);
        yield this.updateEditObj(info.detailInfos);
        yield this.changeData({
          licencePlateInfo: {
            ...licencePlateInfo,
            detailInfos: [...info.detailInfos, ...concatList],
          },
          addToolTypeList: [],
          showDetail: true,
        });
      } else {
        // 初始化页面
        yield this.init();
      }
    } else {
      modal.error({ content: msg });
    }
  },

  /**
   * @description 修改工具类型
   * @param {string} type 类型：add delete edit
   * @param {number} index 索引
   * @param {string} value 值
   * @param {string} param palletNum boxReceiptNum palletReceiptNum
   */
  * handleToolTypeChange(action) {
    const {
      type, index, value, param, fullPallet,
    } = action;
    const { addToolTypeList, licencePlateInfo } = yield '';
    if (type === 'add') {
      const { detailInfos } = licencePlateInfo;
      if ([...detailInfos.filter((e) => !e.id),
        ...addToolTypeList].map((x) => x.toolType).includes(value)) {
        message.error(t('工具类型已添加'));
        return;
      }
      yield this.changeData({
        addToolTypeList: [...addToolTypeList, {
          toolType: value, // 工具类型
          palletNum: 0, // 托数
          boxReceiptNum: 0, // 周转箱收货数量
          palletReceiptNum: 0, // 托盘收货数量
        }],
      });
      return;
    }
    if (type === 'delete') {
      yield this.changeData({
        addToolTypeList: addToolTypeList.filter((x, i) => i !== index),
      });
      return;
    }
    if (type === 'edit') {
      if (param === changeTypeMap.get(t('托数'))) {
        yield this.changeData({
          addToolTypeList: addToolTypeList.map((x, i) => (i === index ? {
            ...x,
            palletNum: value, // 托数
            // 周转箱数量(托数*满托)
            boxReceiptNum: ![12, 13].includes(x.toolType) && value ? value * fullPallet : 0,
            // 托数文本框的值赋值到托盘数量文本框中
            palletReceiptNum: value,
          } : x)),
        });
      } else {
        yield this.changeData({
          addToolTypeList: addToolTypeList.map((x, i) => (i === index ? {
            ...x,
            [param]: value,
          } : x)),
        });
      }
    }
  },
};
