import React from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-weui/build/packages';
import { t } from '@shein-bbl/react';
import {
  classFocus, validNum,
} from 'lib/util';
import store from '../reducers';
import {
  FocusInput, SelectInput,
} from '../../../common';

function Entry(props) {
  const {
    invalidNum,
    containerTypeList,
    containerTypeName,
    // showPickers,
    containerType,
  } = props;
  return (
    <div>
      <Form>
        <SelectInput
          value={containerTypeName}
          selectValue={containerType}
          lineBreak={false}
          label={`${t('容器类型')}:`}
          enterShow
          // disabled
          className="containerType"
          selectList={containerTypeList}
          onSelect={(value) => {
            const item = containerTypeList.find((e) => e.value === value);
            store.changeData({
              containerType: value,
              containerTypeName: item.name,
              infoData: [],
              invalidNum: '',
            });
            classFocus('invalidNum');
          }}
        />
        {/* <Pickers
          value={containerTypeName}
          label={t('容器类型')}
          onClick={() => store.changeData({ showPickers: true })}
          onChange={(select) => {
            store.changeData({
              containerType: select.value,
              containerTypeName: select.label,
              infoData: [],
              invalidNum: '',
              isQueried: false,
              showPickers: false,
            });
            classFocus('invalidNum');
          }}
          show={showPickers}
          pickerData={containerTypeList}
          onCancel={() => store.changeData({ showPickers: false })}
        /> */}
        <FocusInput
          placeholder={t('请输入作废数量')}
          value={invalidNum}
          className="invalidNum"
          allowClear
          lineBreak={false}
          onChange={(e) => {
            const num = e.target.value;
            // 限制输入大于0且小于999的正整数
            if ((num && validNum(num, 999)) || num === '') {
              store.changeData({
                invalidNum: e.target.value,
              });
            } else {
              store.changeData({
                invalidNum: '',
              });
            }
          }}
        >
          <label>{t('作废数量')}:</label>
        </FocusInput>
      </Form>
    </div>
  );
}

Entry.propTypes = {
  invalidNum: PropTypes.string,
  containerTypeName: PropTypes.string,
  containerTypeList: PropTypes.arrayOf(PropTypes.shape()),
  containerType: PropTypes.string,
};

export default Entry;
