import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import store from './reducers';
import {
  Header, Footer, pages, FooterBtn, modal,
} from '../../common';
import { classFocus } from '../../../lib/util';
import Entry from './jsx/entry';

const { View } = pages;
class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      initLoading,
      invalidNum,
      containerType,
      voidBtnLoading,
    } = this.props;
    return (
      <div>
        <Header title={headerTitle || t('PDA作废容器')} />
        <View
          flex={false} // flex布局，默认为true，当需要固定单个输入框是，不启用
          diff={110} // 页面高度：window.innerHeight - diff 中的 diff 值
          initLoading={initLoading} // 是否需要初始加载时的loading，防止用户在初始化数据完成前操作页面
        >
          <Entry {...this.props} />
        </View>
        <Footer>
          <FooterBtn
            disabled={!containerType || !invalidNum || !voidBtnLoading}
            onClick={() => {
              modal.confirm({
                content: t('是否确认作废{}个容器?', invalidNum),
                onOk: () => {
                  store.toVoid();
                },
                onCancel: () => { classFocus('invalidNum'); },
              });
            }}
          >
            {t('作废')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string,
  totalContainerNum: PropTypes.number,
  totalPalletNum: PropTypes.number,
  showDetail: PropTypes.bool,
  isQueried: PropTypes.bool,
  initLoading: PropTypes.bool,
  invalidNum: PropTypes.string,
  containerType: PropTypes.string,
  voidBtnLoading: PropTypes.number,
};

export default i18n(Container);
