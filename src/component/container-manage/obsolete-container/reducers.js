import { t } from '@shein-bbl/react';
import assign from 'object-assign';
import { markStatus } from 'rrc-loader-helper';
import {
  classFocus, getHeaderTitle,
} from 'lib/util';
import {
  toVoidAPI,
} from './server';
import Modal from '../../common/modal';
import { selectDict } from '../../../server/basic/data-dictionary';
import { message } from '../../common';

const defaultState = {
  containerTypeList: [
  ], // 容器类型下拉框
  containerTypeName: '', // 选中的类型名称
  containerType: '', // 选中的类型值
  invalidNum: '', // 作废数量
  voidBtnLoading: 1,
  initLoading: false, // 初始化加载
  // showPickers: false,
};

export default {
  state: defaultState,
  $init: () => defaultState,
  * init() {
    yield this.changeData({ headerTitle: getHeaderTitle(), initLoading: false });
    const [selectData] = yield Promise.all([
      selectDict({ catCode: ['INVALID_CONTAINER_TYPE'] }),
    ]);
    if (selectData.code === '0') {
      const list = selectData.info.data[0].dictListRsps || [];
      // 转成name value格式
      yield this.changeData({
        containerTypeList: list.map((e) => (
          {
            name: e.dictNameZh,
            value: `${e.dictCode}`,
          }
        )),
      });
    }
  },

  changeData(state, data) {
    assign(state, data);
  },
  /**
   * 作废
   */
  * toVoid() {
    markStatus('voidBtnLoading');
    const { containerType, invalidNum } = yield '';
    const res = yield toVoidAPI({
      containerType, // 容器类型
      invalidNum, // 作废数量
    });
    if (res.code === '0') {
      yield this.changeData({
        infoData: res.info || [],
      });
      classFocus('containerType');
      message.success(t('作废成功'));
    } else {
      Modal.error({
        content: res.msg,
        className: 'containerType',
      });
    }
    // 清空作废数量、 容器类型
    yield this.changeData({
      invalidNum: '',
      containerType: '',
      containerTypeName: '',
    });
  },
};
