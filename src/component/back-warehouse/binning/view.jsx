import React, { Component } from 'react';
import { push } from 'react-router-redux';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { connect } from 'react-redux';
import {
  Form,
  CellsTitle,
} from 'react-weui/build/packages';
import Header from '../../common/header';
import Footer from '../../common/footer';
import FooterBtn from '../../common/footer-btn';
import FocusInput from '../../common/focus-input';
import Modal from '../../common/modal';
import { getParentHref, getWarehouseObj } from '../../../lib/util';
import store, { defaultState } from './reducers';
import RowInfo from '../../common/row-info';
import Pickers from '../../common/pickers';
import Table from '../../common/table';
import pages from '../../common/pages';

const { View } = pages;

const trimLeft = (str) => {
  try {
    return str.replace(/^\s+/g, '');
  } catch (e) {
    return '';
  }
};

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      dispatch,
      containerCode,
      containerCodeValid,
      barcode,
      barcodeValid,
      scanNum,
      boxNum,
      shiftOrderCode,
      warehouseIdSelectShow,
      headerTitle,
      goodsList,
      subWarehouseList,
      subWarehouseIdSelectShow,
      subWarehouseId,
    } = this.props;

    const back = () => {
      dispatch(push(getParentHref()));
      store.changeData({
        data: defaultState,
      });
    };

    const warehouseObj = getWarehouseObj();
    // 仓库选择列表
    const warehouseListMap = [
      {
        items: [{
          label: warehouseObj.warehouseName || '',
          value: warehouseObj.warehouseId || 0,
        }],
      },
    ];
    // 子仓选择列表
    const subWarehouseListMap = [
      {
        items: subWarehouseList.map((i) => (
          {
            label: i.subWarehouseName,
            value: i.subWarehouseId,
          }
        )),
      },
    ];

    const warehouseId = warehouseObj.warehouseId || 0;

    const warehouseIdLabel =
      warehouseListMap[0].items.find((v) => v.value === warehouseId) ?
        warehouseListMap[0].items.find((v) => v.value === warehouseId).label
        :
        '';

    const subWarehouseIdLabel =
      subWarehouseListMap[0].items.find((v) => v.value === subWarehouseId) ?
        subWarehouseListMap[0].items.find((v) => v.value === subWarehouseId).label
        :
        '';

    // 减去header和footer高度，实现页面局部滚动
    // const overflowStyle = {
    //   height: window.innerHeight - 56 - 44,
    //   overflow: 'auto',
    // };

    return (
      <div>
        <Header title={headerTitle || t('返仓装箱')} />
        <View diff={110} flex={false}>
          <Form>
            <RowInfo
              label={t('已扫描箱数')}
              type="warn"
              content={boxNum.toString()}
            />
            <RowInfo
              type="info"
              label={t('已扫描件数')}
              content={scanNum.toString()}
            />
          </Form>
          <Form>
            <Pickers
              disabled
              value={warehouseIdLabel}
              label={t('仓库')}
              placeholder={t('请选择')}
              onClick={() => store.changeData({ data: { warehouseIdSelectShow: true } })}
              onChange={(select) => {
                store.changeData({
                  data: {
                    warehouseIdSelectShow: false,
                    warehouseId: select.value,
                  },
                });
              }}
              show={warehouseIdSelectShow}
              pickerData={warehouseListMap}
              onCancel={() => store.changeData({ data: { warehouseIdSelectShow: false } })}
            />
            <Pickers
              value={subWarehouseIdLabel}
              label={t('子仓')}
              placeholder={t('请选择')}
              onClick={() => store.changeData({ data: { subWarehouseIdSelectShow: true } })}
              onChange={(select) => {
                store.changeData({
                  data: {
                    subWarehouseIdSelectShow: false,
                    subWarehouseId: select.value,
                  },
                });
              }}
              show={subWarehouseIdSelectShow}
              pickerData={subWarehouseListMap}
              onCancel={() => store.changeData({ data: { subWarehouseIdSelectShow: false } })}
              disabled={!!shiftOrderCode}
            />
          </Form>
          <Form style={{ marginTop: '10px' }}>
            <FocusInput
              placeholder={t('请选择')}
              autoFocus
              value={containerCode}
              className="container"
              disabled={containerCodeValid}
              onChange={(e) => {
                store.changeData({
                  data: {
                    containerCode: e.target.value.trim(),
                  },
                });
              }}
              onPressEnter={() => {
                store.scanContainer({
                  data: {
                    param: {
                      containerCode,
                      warehouseId,
                      validateStatus: 3,
                    },
                  },
                });
              }}
            >
              <label>{t('周转箱')}</label>
            </FocusInput>
          </Form>
          <Form>
            <FocusInput
              placeholder={t('请扫描')}
              value={barcode}
              className="goods"
              disabled={barcodeValid}
              onChange={(e) => {
                store.changeData({
                  data: {
                    barcode: trimLeft(e.target.value),
                  },
                });
              }}
              onPressEnter={() => {
                store.scanGoods({
                  data: {
                    param: {
                      containerCode,
                      barcode,
                      warehouseId,
                      subWarehouseId,
                    },
                  },
                });
              }}
            >
              <label>{t('商品条码')}</label>
            </FocusInput>
          </Form>
          {shiftOrderCode ? (
            <div style={{ marginTop: '2px' }}>
              <CellsTitle
                style={{ fontSize: 14 }}
              >
                {t('详细信息')}
                <span
                  style={{
                    marginLeft: 25,
                    color: 'red',
                  }}
                >
                  {shiftOrderCode}
                </span>
              </CellsTitle>
            </div>
          ) : ''}
          {!!goodsList.length && (
            <div>
              <Table
                columns={[
                  {
                    title: 'SKC',
                    dataIndex: 'goodsSn',
                    width: 20,
                  },
                  {
                    title: t('尺码'),
                    dataIndex: 'size',
                    width: 10,
                  },
                  {
                    title: t('数量'),
                    dataIndex: 'num',
                    width: 10,
                  },
                ]}
                dataSource={goodsList}
              />
            </div>
          )}
        </View>
        <Footer
          beforeBack={() => {
            if (containerCodeValid) {
              store.scanContainer({
                data: {
                  param: {
                    containerCode,
                    warehouseId,
                    validateStatus: 2,
                  },
                  back,
                },
              });
            } else {
              back();
            }
          }}
        >
          <FooterBtn
            disabled={!containerCodeValid}
            onClick={() => {
              Modal.confirm({
                title: t('是否确认关箱操作?'),
                onOk: () => {
                  store.closeContainer({
                    data: {
                      param: { containerCode, shiftOrderCode },
                    },
                  });
                },
              });
            }}
          >
            {t('关箱')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Container.propTypes = {
  dispatch: PropTypes.func.isRequired,
  headerTitle: PropTypes.string.isRequired,
  goodsList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseIdSelectShow: PropTypes.bool,
  subWarehouseId: PropTypes.number,
  containerCode: PropTypes.string,
  containerCodeValid: PropTypes.bool,
  barcode: PropTypes.string,
  barcodeValid: PropTypes.bool,
  scanNum: PropTypes.number,
  boxNum: PropTypes.number,
  shiftOrderCode: PropTypes.string,
  warehouseIdSelectShow: PropTypes.bool,
};

const mapStateToProps = (state) => state['back-warehouse/binning'];
export default connect(mapStateToProps)(i18n(Container));
