import { sendPostRequest } from '../../../lib/public-request';

/**
 * 返仓上架--获取仓库
 * @param param
 * @returns {*}
 */
export const getWarehouse = (param) => sendPostRequest({
  url: '/inf_user/query_user_warehouse',
  param,
}, process.env.WMS_INTERNAL_FRONT);

/**
 * 返仓装箱--扫描周转箱/返回的时候验证周转箱
 * @param param
 * @returns {*}
 */
export const scanContainer = (param) => sendPostRequest({
  url: '/pda/scan_return_warehouse_encase_container',
  param,
}, process.env.WWS_URI);

/**
 * 返仓装箱--扫描商品条码
 * @param param
 * @returns {*}
 */
export const scanGoods = (param) => sendPostRequest({
  url: '/pda/scan_return_warehouse_encase_sku',
  param,
}, process.env.WWS_URI);

/**
 * 返仓装箱--关箱
 * @param param
 * @returns {*}
 */
export const closeContainer = (param) => sendPostRequest({
  url: '/pda/close_return_warehouse_encase_container',
  param,
}, process.env.WWS_URI);

/**
 * 返仓装箱--完成
 * @param param
 * @returns {*}
 */
export const doCompleteClose = (param) => sendPostRequest({
  url: '/pda/submit_return_warehouse_encase',
  param,
});

/**
 * 根据用户和仓库查询是否有未完成的移位单
 * @param param
 * @returns {*}
 */
export const doGetWarehouseShiftOrderCode = (param) => sendPostRequest({
  url: '/pda/get_return_warehouse_shift_order_code',
  param,
}, process.env.WWS_URI);

/**
 * 返仓上架--获取子仓列表
 * @param param
 * @returns {*}
 */
export const getSubWarehouse = (param) => sendPostRequest({
  url: '/pda/get_subwarehouse_by_weight',
  param,
}, process.env.WWS_URI);
