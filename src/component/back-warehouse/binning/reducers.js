import assign from 'object-assign';
import { select } from 'redux-saga/effects';
import { t } from '@shein-bbl/react';
import Modal from '../../common/modal';
import { classFocus, getHeaderTitle, getWarehouseId } from '../../../lib/util';
import {
  scanContainer as scanContainerServer,
  scanGoods as scanGoodsServer,
  closeContainer as closeContainerServer,
  getWarehouse, doCompleteClose, doGetWarehouseShiftOrderCode, getSubWarehouse,
} from './server';
import error from '../../../source/audio/delete.mp3';

// 报错异常声音
const audio = new Audio(error);
audio.load();
export const defaultState = {
  warehouseId: '',
  containerCode: '',
  containerCodeValid: false,
  barcode: '',
  barcodeValid: true,
  warehouseList: [],
  scanNum: 0,
  boxNum: 0,
  closeLoading: false,
  ready: false,
  shiftOrderCode: '',
  warehouseDisabled: false,
  headerTitle: '',
  goodsList: [],
  subWarehouseList: [],
  subWarehouseIdSelectShow: false,
  subWarehouseId: undefined,
};

export default {
  defaultState,
  $init: () => defaultState,
  changeData(draft, action) {
    assign(draft, action.data);
  },
  reset(draft) {
    assign(draft, {
      containerCode: '',
      containerCodeValid: false,
      barcode: '',
      barcodeValid: true,
      scanNum: 0,
      boxNum: 0,
      closeLoading: false,
      ready: true,
      shiftOrderCode: '',
      warehouseDisabled: false,
    });
  },
  * getSubWarehouseList(action, ctx) {
    const result = yield getSubWarehouse({ warehouseId: getWarehouseId() });
    if (result.code === '0') {
      const list = result.info;
      yield ctx.changeData({ data: { subWarehouseList: list } });
      if (list.length > 0) {
        yield ctx.changeData({ data: { subWarehouseId: list[0].subWarehouseId } });
      }
    }
  },
  /**
   * 初始
   * @param action
   * @returns {IterableIterator<*>}
   */* init(action, ctx) {
    yield ctx.changeData({ data: { headerTitle: getHeaderTitle() } });
    yield ctx.getSubWarehouseList();
    const result = yield getWarehouse();
    if (result.code === '0') {
      yield ctx.changeData({
        data: {
          ready: true,
          warehouseId: result.info.warehouseId,
          warehouseList: result.info.warehouseList,
        },
      });
      // 查询用户在该仓库下是否有未完成的移位单
      const shiftOrderResult = yield doGetWarehouseShiftOrderCode({
        warehouseId: result.info.warehouseId,
      });
      if (shiftOrderResult.code === '0') {
        yield ctx.changeData({
          data: {
            shiftOrderCode: shiftOrderResult.info.shiftOrderCode || '',
          },
        });
      }
    } else {
      Modal.error({
        title: result.msg,
        onOk: () => {
          window.location.href = '#/back-warehouse';
        },
      });
    }
  },
  /**
   * 返仓装箱--扫描周转箱/返回的时候验证周转箱
   * @param action
   * @returns {IterableIterator<*>}
   */* scanContainer(action, ctx) {
    if (action.data.param.validateStatus === 3) {
      yield ctx.changeData({
        data: {
          containerCodeValid: true,
          warehouseDisabled: true,
        },
      });
    }
    const result = yield scanContainerServer({ ...action.data.param, containerCode: (action.data.param.containerCode || '').toUpperCase() });
    if (result.code === '0') {
      if (action.data.param.validateStatus === 2) {
        if (result.info.status === '2') {
          Modal.confirm({
            title: t('该周转箱为占用状态,是否确定返回'),
            onOk: () => {
              action.data.back();
            },
          });
        } else {
          action.data.back();
        }
      } else {
        yield ctx.changeData({
          data: {
            barcodeValid: false,
          },
        });
        classFocus('goods');
      }
    } else {
      // 所有异常提示需要发出声音。
      audio.play();
      yield ctx.changeData({
        data: {
          containerCode: '',
          containerCodeValid: false,
          warehouseDisabled: false,
        },
      });
      Modal.error({
        title: result.msg,
        className: 'container',
      });
    }
  },
  /**
   * 扫描商品条码
   * @param action
   * @returns {IterableIterator<*>}
   */* scanGoods(action, ctx) {
    yield ctx.changeData({
      data: {
        barcodeValid: true,
      },
    });
    const result = yield scanGoodsServer({ ...action.data.param, containerCode: (action.data.param.containerCode || '').toUpperCase() });
    if (result.code === '0') {
      yield ctx.changeData({
        data: {
          shiftOrderCode: result.info.shiftOrderCode || '',
          goodsList: result.info.goodsList || [],
        },
      });
      if (result.info.status === '3') {
        yield ctx.changeData({
          data: {
            barcode: '',
            barcodeValid: true,
            containerCode: '',
            containerCodeValid: false,
            warehouseDisabled: false,
          },
        });
        Modal.error({
          title: t('该周转箱已关箱,不允许操作'),
          className: 'container',
        });
      } else {
        const prop = yield select(state => state['back-warehouse/binning']);
        yield ctx.changeData({
          data: {
            barcode: '',
            barcodeValid: false,
            scanNum: prop.scanNum + 1,
          },
        });
        classFocus('goods');
      }
    } else {
      // 所有异常提示需要发出声音。
      audio.play();
      yield ctx.changeData({
        data: {
          barcode: '',
          barcodeValid: false,
        },
      });
      Modal.error({
        title: result.msg,
        className: 'goods',
      });
    }
  },
  /**
   * 关箱
   * @param action
   * @returns {IterableIterator<*>}
   */* closeContainer(action, ctx) {
    yield ctx.changeData({
      data: {
        closeLoading: true,
      },
    });
    const result = yield closeContainerServer(action.data.param);
    const prop = yield select(state => state['back-warehouse/binning']);
    if (result.code === '0') {
      yield ctx.changeData({
        data: {
          containerCode: '',
          containerCodeValid: false,
          barcode: '',
          barcodeValid: true,
          closeLoading: false,
          warehouseDisabled: false,
          boxNum: prop.boxNum + 1,
          shiftOrderCode: '',
          goodsList: [],
        },
      });
      classFocus('container');
    } else {
      yield ctx.changeData({
        data: {
          closeLoading: false,
          containerCode: '',
          containerCodeValid: false,
          warehouseDisabled: false,
          barcode: '',
          barcodeValid: true,
        },
      });
      Modal.error({
        title: result.msg,
        className: 'container',
      });
    }
  },
  /**
   * 完成
   * @param action
   * @returns {IterableIterator<*>}
   */* completeClose(action, ctx) {
    const data = yield doCompleteClose(action.data.param);
    if (data.code === '0') {
      yield ctx.reset();
      Modal.success({
        title: t('装箱完成'),
        className: 'container',
      });
    } else {
      Modal.error({
        title: data.msg,
        className: 'container',
      });
    }
  },
  /**
   * 根据用户和仓库查询未完成的移位单
   * @param action
   * @returns {IterableIterator<*>}
   */* getWarehouseShiftOrderCode(action, ctx) {
    const data = yield doGetWarehouseShiftOrderCode(action.data.param);
    if (data.code === '0') {
      yield ctx.changeData({
        data: {
          shiftOrderCode: data.info.shiftOrderCode || '',
        },
      });
    } else {
      Modal.error({
        title: data.msg,
      });
    }
  },
};
