/* eslint-disable no-nested-ternary */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { i18n, t } from '@shein-bbl/react';
import { connect } from 'react-redux';
import {
  LoadMore,
} from 'react-weui/build/packages';
import Header from '../../common/header';
import store from './reducers';
import Station from './jsx/station';
import Scanning from './jsx/scanning';
import Detail from './jsx/detail';

class Container extends Component {
  componentDidMount() {
    store.init();
  }

  render() {
    const {
      headerTitle,
      ready,
      showJsx,
    } = this.props;

    return (
      <div style={{ marginBottom: '56px' }}>
        <Header title={headerTitle || t('返仓装箱')} />
        {ready ?
          showJsx === 'station' ?
            (<Station {...this.props} />) :
            showJsx === 'scanning' ?
              (<Scanning {...this.props} />) :
              showJsx === 'detail' ?
                (<Detail {...this.props} />) : ''
          : (<LoadMore loading>{t('加载中...')}</LoadMore>)}
      </div>
    );
  }
}

Container.propTypes = {
  headerTitle: PropTypes.string.isRequired,
  ready: PropTypes.bool,
  showJsx: PropTypes.string,
};

const mapStateToProps = (state) => state['back-warehouse/new-binning'];
export default connect(mapStateToProps)(i18n(Container));
