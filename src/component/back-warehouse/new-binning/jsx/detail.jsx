import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  InfiniteLoader,
  Cell,
  CellBody,
  LoadMore,
} from 'react-weui/build/packages';
import { classFocus } from '../../../../lib/util';
import store from '../reducers';
import Footer from '../../../common/footer';
import Style from '../style.css';
import Modal from '../../../common/modal';

class Detail extends Component {
  componentDidMount() {
  }

  render() {
    const {
      tabState,
      detailItems,
      pageNum,
      count,
      containerCodeValid,
      barcodeValid,
    } = this.props;

    const imgShow = (data) => {
      const img = <img width="100%" src={data} />;
      Modal.img({
        content: img,
      });
    };

    // 减去header和footer高度，实现页面局部滚动
    const overflowStyle = {
      height: window.innerHeight - 56 - 44 - 34 - 10,
      overflow: 'hidden',
    };

    const back = () => {
      store.changeData({
        showJsx: 'scanning',
      });
      if (containerCodeValid && !barcodeValid) {
        classFocus('goods');
      }
    };

    return (
      <div>
        <div className={Style.detailTab}>
          <div
            className={tabState === 1 ? Style.detailTabListActive : Style.detailTabList}
            onClick={() => {
              if (tabState !== 1) {
                store.handleDetailPage({
                  tabState: 1,
                  pageNum: 1,
                });
              }
            }}
          >{t('取消件')}
          </div>
          <div
            className={tabState === 2 ? Style.detailTabListActive : Style.detailTabList}
            onClick={() => {
              if (tabState !== 2) {
                store.handleDetailPage({
                  tabState: 2,
                  pageNum: 1,
                });
              }
            }}
          >{t('多件')}
          </div>
        </div>
        <div style={overflowStyle}>
          {
              detailItems.length > 0 ? (
                <InfiniteLoader
                  height="100%"
                  loaderDefaultIcon={<LoadMore showLine>{t('没有更多了')}</LoadMore>}
                  onLoadMore={(resolve, finish) => {
                    if (detailItems.length >= count) {
                      finish();
                    } else {
                      store.handleDetailPage({
                        tabState,
                        pageNum,
                      });
                      resolve();
                    }
                  }}
                >
                  <div>
                    {
                  detailItems.map((item) => (
                    <Cell key={item.id} access>
                      <CellBody onClick={() => {
                        imgShow(item.imageUrl);
                      }}
                      >
                        <div className={Style.detailItems}>
                          <div className={Style.detailItemsLeft}>
                            <div>
                              <label>SKC: </label>
                              <span>{item.skc}</span>
                            </div>
                            <div>
                              <label>{t('尺码')}: </label>
                              <span>{item.size}</span>
                            </div>
                            <div>
                              <label>{t('周转箱')}: </label>
                              <span>{item.sowingBoxNo}</span>
                            </div>
                          </div>
                          <div className={Style.detailItemsRight}>
                            <div>
                              <label>{t('数量')}: </label>
                              <span>{item.number}</span>
                            </div>
                            <div>
                              <span>{item.storeTypeName}</span>
                            </div>
                            <div>
                              <label>{t('装箱人')}: </label>
                              <span>{item.boxingUser}</span>
                            </div>
                          </div>
                        </div>
                      </CellBody>
                    </Cell>
                  ))
                }
                  </div>
                </InfiniteLoader>
              )
                : <LoadMore showLine>{t('暂无数据')}</LoadMore>
            }
        </div>
        <Footer
          beforeBack={() => {
            back();
          }}
        />
      </div>

    );
  }
}

Detail.propTypes = {
  tabState: PropTypes.number,
  detailItems: PropTypes.arrayOf(PropTypes.shape()),
  pageNum: PropTypes.number,
  count: PropTypes.number,
  containerCodeValid: PropTypes.bool,
  barcodeValid: PropTypes.bool,
};

export default Detail;
