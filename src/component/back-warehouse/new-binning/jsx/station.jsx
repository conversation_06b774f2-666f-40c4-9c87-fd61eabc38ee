/* eslint-disable max-len */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { push } from 'react-router-redux';
import {
  Form,
} from 'react-weui/build/packages';
import FocusInput from '../../../common/focus-input';
import store from '../reducers';
import { getParentHref } from '../../../../lib/util';
import Footer from '../../../common/footer';
import RowInfo from '../../../common/row-info';
import Pickers from '../../../common/pickers';
import Style from '../style.css';
import Modal from '../../../common/modal';

class Station extends Component {
  render() {
    const {
      dispatch,
      dataLoading,
      workLocationCode,
      boxNum,
      multiNum,
      cancelNum,
      // shiftOrderCode,
      subWarehouseList,
      subWarehouseIdSelectShow,
      subWarehouseId,
      configValue,
      userNames,
      nationalLineNameList,
      // nationalLineNameZh,
      needWorkLocation,
      isShowCircle,
      boxDisabled,
    } = this.props;

    // // 子仓选择列表
    const subWarehouseListMap = [
      {
        items: subWarehouseList.map((i) => (
          {
            label: i.subWarehouseName,
            value: i.subWarehouseId,
          }
        )),
      },
    ];

    const subWarehouseIdLabel =
              subWarehouseListMap[0].items.find((v) => v.value === subWarehouseId) ?
                subWarehouseListMap[0].items.find((v) => v.value === subWarehouseId).label
                :
                '';

    // 减去header和footer高度，实现页面局部滚动
    const overflowStyle = {
      height: window.innerHeight - 56 - 44 - 10,
      overflow: 'auto',
    };

    const back = () => {
      dispatch(push(getParentHref()));
    };

    return (
      <div>
        <div style={overflowStyle}>
          <Form style={{ marginBottom: '10px' }}>
            <RowInfo
              label={t('已扫描箱数')}
              type="warn"
              content={boxNum.toString()}
            />
            <RowInfo
              data={[
                {
                  label: t('多件'),
                  content: multiNum.toString(),
                  type: 'info',
                },
                {
                  label: t('取消件'),
                  content: cancelNum.toString(),
                  type: 'info',
                },
              ]}
            />
          </Form>
          {configValue === '1' ? (
            <Form>
              <FocusInput
                placeholder={t('请选择')}
                autoFocus
                className="workLocation"
                disabled={boxDisabled === 0}
                value={workLocationCode}
                onChange={(e) => {
                  store.changeData({
                    workLocationCode: e.target.value.trim(),
                    isShowCircle: false,
                  });
                }}
                onPressEnter={() => {
                  store.scanWorkLocationCode({
                    workLocationCode,
                  });
                }}
              >
                <label>{t('工位')}</label>
              </FocusInput>
            </Form>
          ) : ''}
          <Form>
            <Pickers
              value={subWarehouseIdLabel}
              defaultValue={subWarehouseId}
              label={t('子仓')}
              placeholder={t('请选择子仓')}
              onClick={() => store.changeData({ subWarehouseIdSelectShow: true })}
              onChange={(select) => {
                store.changeData({
                  subWarehouseIdSelectShow: false,
                  subWarehouseId: select.value,
                });
                // localStorage.setItem('binningSubWarehouseId', select.value);
              }}
              show={subWarehouseIdSelectShow}
              pickerData={subWarehouseListMap}
              onCancel={() => store.changeData({ subWarehouseIdSelectShow: false })}
              // disabled={!!shiftOrderCode}
            />
          </Form>
          {(needWorkLocation || configValue !== '1') && isShowCircle && subWarehouseId ? (
            <div>
              <div className={Style.upStation}>
                {userNames?.length ? (
                  <div className={Style.upStationLeft}>
                    <span className={Style.upStationLeftLabel}>{t('工位')}</span>
                    {
                      userNames.map((item) => (
                        <span className={Style.upStationLeftVal} key={item}>{item}</span>
                      ))
                    }
                  </div>
                ) : ''}
                {/* <div className={Style.upStationRight}>
                  {nationalLineNameZh ? <span className={Style.upStationRightVal}>{nationalLineNameZh}</span> : ''}
                </div> */}
              </div>
              <div className={Style.upStation} style={{ paddingTop: 0 }}>
                {nationalLineNameList?.length ? (
                  <div className={Style.upStationLeft}>
                    <span className={Style.upStationLeftLabel}>{t('国家线')}</span>
                    {
                      nationalLineNameList.map((item) => (
                        <span className={Style.upStationVal} key={item}>{item}</span>
                      ))
                    }
                  </div>
                ) : ''}
              </div>
              <div className={Style.upContent}>
                <div
                  className={Style.upContentCircle}
                  onClick={() => {
                    store.handleStartWorking();
                  }}
                >
                  <div className={Style.upContentCircleName}>{t('上机操作')}</div>
                </div>
              </div>
            </div>
          ) : ''}

        </div>
        <Footer
          beforeBack={() => {
            if (needWorkLocation && isShowCircle) {
              Modal.confirm({
                title: t('是否确定下机'),
                onOk: () => {
                  if (dataLoading === 0) {
                    return;
                  }
                  store.handleOffWorking({
                    back,
                  });
                },
              });
            } else {
              back();
            }
          }}
        />
      </div>

    );
  }
}

Station.propTypes = {
  dispatch: PropTypes.func.isRequired,
  dataLoading: PropTypes.number,
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  subWarehouseIdSelectShow: PropTypes.bool,
  subWarehouseId: PropTypes.number,
  workLocationCode: PropTypes.string,
  multiNum: PropTypes.number,
  cancelNum: PropTypes.number,
  boxNum: PropTypes.number,
  // shiftOrderCode: PropTypes.string,
  configValue: PropTypes.string,
  userNames: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  nationalLineNameList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  // nationalLineNameZh: PropTypes.string,
  needWorkLocation: PropTypes.bool,
  isShowCircle: PropTypes.bool,
  boxDisabled: PropTypes.number,
};

export default Station;
