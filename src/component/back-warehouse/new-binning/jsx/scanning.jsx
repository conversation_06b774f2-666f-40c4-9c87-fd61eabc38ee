import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import {
  Form,
  Button,
} from 'react-weui/build/packages';
import FocusInput from '../../../common/focus-input';
import FooterBtn from '../../../common/footer-btn';
import store from '../reducers';
import RowInfo from '../../../common/row-info';
import Footer from '../../../common/footer';
import Modal from '../../../common/modal';
import Style from '../style.css';
import pages from '../../../common/pages';

const { View } = pages;

const trimLeft = (str) => {
  try {
    return str.replace(/^\s+/g, '');
  } catch (e) {
    return '';
  }
};

class Scanning extends Component {
  componentDidMount() {
  }

  render() {
    const {
      containerCode,
      containerCodeValid,
      barcode,
      barcodeValid,
      multiNum,
      cancelNum,
      goodsList,
      subWarehouseId,
      warehouseId,
      nationalLineType,
      containerSerialNum,
      changeColor,
      workLocationCode,
      isScanContainerAgain,
    } = this.props;

    const back = () => {
      store.changeData({
        showJsx: 'station',
      });
    };

    // 减去header和footer高度，实现页面局部滚动
    const overflowStyle = {
      height: window.innerHeight - 56 - 44 - 10,
      overflow: 'auto',
    };

    return (
      <div>
        <View diff={110} flex={false}>
          <Form>
            <RowInfo
              data={[
                {
                  label: t('多件'),
                  content: multiNum.toString(),
                  type: 'info',
                },
                {
                  label: t('取消件'),
                  content: cancelNum.toString(),
                  type: 'info',
                },
              ]}
            />
          </Form>
          <Form
            // eslint-disable-next-line no-nested-ternary
            className={changeColor === 'green' ? Style.batchContentGreen : changeColor === 'red' ? Style.batchContentRed : Style.batchContent}
          >
            <div className={Style.containerlabel}>{t('批次')}:</div>
            <div
              // eslint-disable-next-line no-nested-ternary
              className={changeColor === 'green' ? Style.containerSerialNumGreen : changeColor === 'red' ? Style.containerSerialNumRed : Style.containerSerialNum}
            >{containerSerialNum}
            </div>
          </Form>
          <Form className={Style.containerCodeContent}>
            <FocusInput
              placeholder={t('请选择')}
              autoFocus
              value={containerCode}
              className="container"
              disabled={containerCodeValid}
              onChange={(e) => {
                store.changeData({
                  containerCode: e.target.value.trim(),
                });
              }}
              onPressEnter={() => {
                // 如果有值直接走条形码逻辑,绑定新箱子
                if (isScanContainerAgain === 1) {
                  store.scanGoods({
                    param: {
                      containerCode,
                      barcode,
                      warehouseId,
                      subWarehouseId,
                      workLocationCode,
                      scanState: 1, // isScanContainerAgain为1 就为2次扫描
                    },
                  });
                } else {
                  store.scanContainer({
                    param: {
                      warehouseId,
                      containerCode,
                      validateStatus: 3,
                    },
                  });
                }
              }}
              footer={
                (
                  <Button
                    size="small"
                    className={Style.containerCodeContentBtn}
                    disabled={!containerCodeValid}
                    onClick={() => {
                      Modal.confirm({
                        title: t('是否确认关箱操作?'),
                        onOk: () => {
                          store.closeContainer();
                        },
                      });
                    }}
                  >
                    {t('关箱')}
                  </Button>
                )
              }
            >
              <label>{t('周转箱')}</label>
            </FocusInput>

          </Form>
          <Form>
            <FocusInput
              placeholder={t('请扫描')}
              value={barcode}
              className="goods"
              disabled={barcodeValid}
              onChange={(e) => {
                store.changeData({
                  barcode: trimLeft(e.target.value),
                });
              }}
              onPressEnter={() => {
                store.scanGoods({
                  param: {
                    containerCode,
                    barcode,
                    warehouseId,
                    subWarehouseId,
                    workLocationCode,
                  },
                });
              }}
            >
              <label>{t('商品条码')}</label>
            </FocusInput>
          </Form>
          <Form className={Style.goodsInfo}>{t('商品信息')}</Form>
          <div>
            {!!goodsList.length &&
            goodsList.map((list) => (
              <div className={Style.scannigItems} key={list.skuCode}>
                <div className={Style.scannigItemsLeft}>
                  <div className={Style.scannigItemsLabel}>{list.goodsSn}</div>
                  <div>
                    {list.storeTypeName}
                    <div className={list.isMultiPieces === 1 ? Style.multiPiecesLabel : Style.multiPiecesLabelNone}>{list.isMultiPieces === 1 ? t('多件') : ''}</div>
                  </div>
                </div>
                <div className={Style.scannigItemsRight}>
                  <div className={Style.scannigItemsLabel}>{list.size}</div>
                  <div>{t('已装箱')}:{list.num}</div>
                </div>
              </div>
            ))}
          </div>
        </View>
        <Footer
          beforeBack={() => {
            if (containerCodeValid) {
              store.scanContainer({
                param: {
                  containerCode,
                  validateStatus: 2,
                  warehouseId,
                  subWarehouseId,
                  nationalLineType,
                },
                back,
              });
            } else {
              back();
            }
          }}
        >
          <FooterBtn
            onClick={() => {
              store.handleDetailPage({
                tabState: 1,
                pageNum: 1,
              });
            }}
          >
            {t('明细')}
          </FooterBtn>
        </Footer>
      </div>
    );
  }
}

Scanning.propTypes = {
  goodsList: PropTypes.arrayOf(PropTypes.shape()).isRequired,
  subWarehouseId: PropTypes.number,
  containerCode: PropTypes.string,
  containerCodeValid: PropTypes.bool,
  barcode: PropTypes.string,
  barcodeValid: PropTypes.bool,
  multiNum: PropTypes.number,
  cancelNum: PropTypes.number,
  warehouseId: PropTypes.number,
  nationalLineType: PropTypes.string,
  containerSerialNum: PropTypes.string,
  changeColor: PropTypes.string,
  workLocationCode: PropTypes.string,
  isScanContainerAgain: PropTypes.string,
};

export default Scanning;
