/* upStation
---------------------------------------------------------------- */
.upStation{
    display: flex;
    padding: 10px;
}

.upStationLeft{
    flex: 1;
}

.upStationLeftLabel{
    color: #333e59;
    padding: 0 5px;
    font-size: 12px;
}

.upStationLeftVal{
    margin-right: 5px;
    padding: 0 5px;
    background: #F1F4F8;
    border-radius: 2px;
    font-weight: 400;
    color: #333e59;
    font-size: 12px;
}

.upStationRight{
    width: 80px;
}

.upStationRightVal{
    margin-top: 4px;
    padding: 0 5px;
    border: 1px solid #ff9636;
    color: #ff9636;
    float: right;
    font-size: 12px;
    line-height: 17px;
    height: 17px;
}

.upStationVal{
    margin: 4px 5px 0 0;
    padding: 0 5px;
    border: 1px solid #ff9636;
    color: #ff9636;
    font-size: 12px;
    line-height: 17px;
    height: 17px;
}


/* upContent
---------------------------------------------------------------- */

.upContent{
    position: relative;
}

.upContentCircle{
    position: absolute;
    left: 50%;
    top:20px;
    width: 130px;
    height: 130px;
    background-color: #fff;
    border-radius: 50%;
    border: 10px solid #26CA9D;
    transform: translateX(-50%);
    cursor: pointer;
}

.upContentCircleName{
    position: absolute;
    color:#26ca9d;
    font-size: 20px;
    font-weight: 500;
    top:50%;
    left: 50%;
    transform: translate(-50%,-50%);
    white-space: nowrap;
}

/* batchContent
---------------------------------------------------------------- */
.batchContent{
    background: #EEEEEE;
    height: 80px;
    line-height: 80px;
    padding: 0 65px 0 15px;
    margin: 6px 0 0 0;
    display: flex;
}

.batchContentGreen{
    background:  #E4FFE9;
    height: 80px;
    line-height: 80px;
    padding: 0 65px 0 15px;
    margin: 6px 0 0 0;
    display: flex;
}

.batchContentRed{
    background:  #FFF3F3;
    height: 80px;
    line-height: 80px;
    padding: 0 65px 0 15px;
    margin: 6px 0 0 0;
    display: flex;
}

.containerlabel{
    width: 50px;
}
.containerSerialNum{
    flex: 1;
    color:#e4ffe9;
    text-align: center;
    font-size: 80px;
}

.containerSerialNumGreen{
    flex: 1;
    color:#31c505;
    text-align: center;
    font-size: 80px;
}

.containerSerialNumRed{
    flex: 1;
    color:#ff3636;
    text-align: center;
    font-size: 80px;
}
/* containerCodeContent
---------------------------------------------------------------- */
.containerCodeContent{
    position: relative;
}

.containerCodeContentBtn{
    margin-bottom: 5px;
}

/* goodsInfo
---------------------------------------------------------------- */
.goodsInfo{
    color: #666c7c;
    background: #f6f5f5;
    height: 32px;
    line-height: 32px;
    padding: 0 15px;
}
/* detailTab
---------------------------------------------------------------- */
.detailTab{
    display: flex;
    height: 34px;
    line-height: 34px;
    border-bottom: 1px solid #ccc;
    margin-bottom: 3px;
}

.detailTabList{
    flex: 1;
    text-align: center;
    color:#333e59;
    background-color: #eeeeee;
}
.detailTabListActive{
    color:#0059ce;
    background: #fff;
    flex: 1;
    text-align: center;
}

/* detailItems
---------------------------------------------------------------- */

.detailItems{
    display:flex;
    color:#666c7c;
    font-size:12px;
}

.detailItemsLeft{
    flex:2
}

.detailItemsRight{
    flex:1
}

/* scannigItems
---------------------------------------------------------------- */
.scannigItems{
    display:flex;
    color:#666c7c;
    font-size:12px;
    padding: 6px 13px;
}

.scannigItemsLeft{
    text-align: left;
    flex:2
}
.scannigItemsLabel{
    color:#141737;
    font-weight: 500;
}

.scannigItemsRight{
    text-align: right;
    flex:1
}

.multiPiecesLabel{
    display: inline-block;
    border:1px solid #0059CE;
    color:#0059ce;
    padding: 0 8px;
    border-radius: 2px;
}


.multiPiecesLabelNone{
    display: inline-block;
}