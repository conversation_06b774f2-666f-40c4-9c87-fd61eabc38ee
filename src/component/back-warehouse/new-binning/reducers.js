import { markStatus } from 'rrc-loader-helper';
import { t } from '@shein-bbl/react';
// import { LOCAL_BINNING_SUB_WAREHOUSE_ID } from 'lib/storage';
import Modal from '../../common/modal';
import Message from '../../common/message';
import {
  classFocus, getHeaderTitle, getWarehouseId, apolloFormatObj,
  getUsername,
} from '../../../lib/util';
import {
  // doGetWarehouseShiftOrderCode,
  // getSubWarehouse,
  scanReturnWarehouseWorkLocation,
  getOfflineWorkLocation,
  scanContainerServer,
  scanGoodsServer,
  closeContainerServer,
  getReturnWarehouseEncaseGoodsDetail,
  getBindSubWarehouseAPI,
  getAllSubWarehouseAPI,
} from './server';
import { getApolloConfigAPI } from '../../../server/basic/common';
import errorAudio from '../../../source/audio/delete.mp3';
import successAudio from '../../../source/audio/ok.mp3';

// 报错异常声音
const audioError = new Audio(errorAudio);
audioError.load();
const audioSuccess = new Audio(successAudio);
audioSuccess.load();

const defaultState = {
  dataLoading: 1, // 0 1 2
  warehouseId: '',
  containerCode: '',
  containerCodeValid: false,
  barcode: '',
  barcodeValid: true,
  warehouseList: [],
  multiNum: 0,
  boxNum: 0,
  cancelNum: 0,
  closeLoading: false,
  ready: false,
  // shiftOrderCode: '',
  boxDisabled: 1,
  headerTitle: '',
  goodsList: [],
  subWarehouseList: [],
  subWarehouseIdSelectShow: false,
  subWarehouseId: undefined,
  workLocationCode: '', // 工位
  configValue: '0', // 1：佛山仓下所有子仓必须扫描工位 关闭 0：不扫描工位，选择子仓后直接进入页面
  userNames: [],
  nationalLineNameZh: '',
  nationalLineType: '',
  nationalLineNameList: [],
  nationalLineTypeList: [],
  needWorkLocation: false, // 是否需要工位相关的上下机操作
  isShowCircle: true, // 是否展示上下机操作
  showJsx: 'station', // station 扫描工位页面  , scanning 操作页面 ,  detail明细页面
  tabState: 1, // (1)取消件传5,6 (2)多件传7
  detailItems: [],
  containerSerialNum: '',
  changeColor: '', // 和上一个一样绿色 和上一个不一样红色  green / red
  count: 0,
  isScanContainerAgain: '',
};

export default {
  state: defaultState,
  $init: () => defaultState,
  changeData(state, data) {
    Object.assign(state, data);
  },
  /**
   * 获取子仓列表
   * @param {*} param
   * @param {*} ctx
   */
  * getSubWarehouseList() {
    const result = yield getBindSubWarehouseAPI({
      id: getWarehouseId(),
      userName: getUsername(),
    });
    if (result.code === '0') {
      const list = result.info?.data || [];
      yield this.changeData({ subWarehouseList: list });
      if (list.length === 1) {
        yield this.changeData({ subWarehouseId: list[0]?.subWarehouseId });
      }
      // 没有绑定子仓则请求所有子仓
      if (!list.length) {
        yield this.getAllSubWarehouseList();
      }
      // if (list.length > 0) {
      //   //  记录上次选择
      //   const lastTimeSub = localStorage.getItem(LOCAL_BINNING_SUB_WAREHOUSE_ID);
      //   if (lastTimeSub && list.find((obj) => (+lastTimeSub === obj.subWarehouseId))) {
      //     yield ctx.changeData({ subWarehouseId: +lastTimeSub });
      //   } else {
      //     localStorage.setItem(LOCAL_BINNING_SUB_WAREHOUSE_ID, list[0].subWarehouseId);
      //     yield ctx.changeData({ subWarehouseId: list[0].subWarehouseId });
      //   }
      // }
    }
  },
  /**
   * 初始
   * @param action
   * @returns {IterableIterator<*>}
   */
  * init() {
    yield this.changeData({
      headerTitle: getHeaderTitle(),
      warehouseId: getWarehouseId(),
    });
    const { warehouseId } = this.state;

    yield this.getSubWarehouseList();
    // // 查询用户在该仓库下是否有未完成的移位单
    // const shiftOrderResult = yield doGetWarehouseShiftOrderCode({
    //   warehouseId,
    // });
    // if (shiftOrderResult.code === '0') {
    //   yield this.changeData({
    //     shiftOrderCode: shiftOrderResult.info.shiftOrderCode || '',
    //   });
    // }

    // 判断是否等于佛山仓(佛山仓需要查询配置)
    if (+warehouseId === 1) {
      // 获取是否扫描工位的开关
      const res = yield getApolloConfigAPI({ params: ['IS_BACK_WAREHOUSE_SWITCH'] });
      if (res.code === '0') {
        const apolloConfigFormatRes = apolloFormatObj(res.info);
        yield this.changeData({
          configValue: apolloConfigFormatRes?.IS_BACK_WAREHOUSE_SWITCH || '0',
          ready: true,
        });
      }
    } else {
      yield this.changeData({
        ready: true,
      });
    }
  },
  // /**
  //  * 根据用户和仓库查询未完成的移位单
  //  * @param action
  //  * @returns {IterableIterator<*>}
  //  */
  // * getWarehouseShiftOrderCode(action) {
  //   const data = yield doGetWarehouseShiftOrderCode(action.data.param);
  //   if (data.code === '0') {
  //     yield this.changeData({
  //       shiftOrderCode: data.info.shiftOrderCode || '',
  //     });
  //   } else {
  //     Modal.error({
  //       title: data.msg,
  //     });
  //   }
  // },
  /**
   * 扫描工位
   * @param {*} param
   */
  * scanWorkLocationCode(param) {
    markStatus('boxDisabled');
    const { warehouseId } = this.state;
    const data = yield scanReturnWarehouseWorkLocation({
      warehouseId,
      workLocationCode: param.workLocationCode || '',
    });
    if (data.code === '0') {
      Message.success(t('已定位子仓和国家线，请装箱'));
      yield this.changeData({
        userNames: data.info.userNames,
        nationalLineNameZh: data.info.nationalLineNameZh,
        nationalLineType: data.info.nationalLineType,
        nationalLineNameList: data.info.nationalLineNameList,
        nationalLineTypeList: data.info.nationalLineTypeList,
        subWarehouseId: data.info.subWarehouseId,
        needWorkLocation: true,
        isShowCircle: true,
      });
    } else {
      yield this.changeData({
        workLocationCode: '',
      });
      Modal.error({
        audioErr: true,
        title: data.msg,
        className: 'workLocation',
      });
    }
  },
  /**
  * 下机处理
  */
  * handleOffWorking(param) {
    const { workLocationCode, warehouseId } = this.state;
    markStatus('dataLoading');
    const data = yield getOfflineWorkLocation({
      workLocationCode,
      warehouseId,
    });
    if (data.code === '0') {
      Message.success(t('下机成功'));
      param.back();
    } else {
      Modal.error({
        title: data.msg,
      });
    }
  },
  /**
   * 上机操作
   */
  * handleStartWorking() {
    const { containerCodeValid, barcodeValid } = this.state;
    yield this.changeData({
      showJsx: 'scanning',
      boxDisabled: 0,
    });
    if (containerCodeValid && !barcodeValid) {
      classFocus('goods');
    }
  },

  /**
   * 返仓装箱--扫描周转箱/返回的时候验证周转箱
   * @param action
   * @returns {IterableIterator<*>}
   */
  * scanContainer(data, ctx) {
    if (data.param.validateStatus === 3) {
      yield ctx.changeData({
        containerCodeValid: true,
      });
    }
    const result = yield scanContainerServer({ ...data.param, containerCode: (data.param.containerCode || '').toUpperCase() });
    if (result.code === '0') {
      // 返回上一页的时候会进行判断
      if (data.param.validateStatus === 2) {
        if (result.info.status === '2') {
          Modal.confirm({
            title: t('该周转箱为占用状态,是否确定返回'),
            onOk: () => {
              data.back();
            },
          });
        } else {
          data.back();
        }
      } else {
        yield ctx.changeData({
          barcodeValid: false,
          goodsList: result.info.goodsList || [],
        });
        classFocus('goods');
      }
    } else {
      yield ctx.changeData({
        containerCode: '',
        containerCodeValid: false,
      });
      Modal.error({
        audioErr: true,
        title: result.msg,
        className: 'container',
      });
    }
  },
  /**
   * 扫描商品条码
   * @param action
   * @returns {IterableIterator<*>}
   */
  * scanGoods(data, ctx) {
    yield ctx.changeData({
      barcodeValid: true,
    });
    // 如果条形码扫了当前箱子,则视为关箱
    if (data.param.containerCode === data.param.barcode) {
      const status = yield new Promise((r) => Modal.confirm({
        title: t('是否确认关箱操作?'),
        onOk: () => r(true),
        onCancel: () => r(false),
      }));
      if (status) {
        yield this.closeContainer();
      } else {
        yield ctx.changeData({
          barcode: '',
          barcodeValid: false,
        });
        classFocus('goods');
      }
      return;
    }
    const { nationalLineTypeList } = this.state;
    const result = yield scanGoodsServer({
      ...data.param,
      containerCode: (data.param.containerCode || '').toUpperCase(),
      nationalLineTypeList,
    });
    if (result.code === '0') {
      // 根据接口初始化isScanContainerAgain,用来判断周转箱走哪个接口
      yield this.changeData({
        isScanContainerAgain: result.info.isScanContainerAgain,
      });
      if (result.info.status === '3') {
        yield ctx.changeData({
          barcodeValid: true,
          containerCode: '',
          containerCodeValid: false,
        });
        Modal.error({
          title: t('该周转箱已关箱,不允许操作'),
          className: 'container',
        });
      } else {
        // 1. 先判断是否换箱操作 isChangeContainer 0否1是
        if (result.info.isChangeContainer === 1) {
          // 调用关箱接口
          yield this.closeContainer({
            flag: 'change',
            newChangeContainerCode: result.info.newChangeContainerCode,
            goodsList: result.info.goodsList || [],
            containerSerialNum: result.info.containerSerialNum,
          });
          return;
        }

        // 其他情况赋值
        yield this.changeData({
          // shiftOrderCode: result.info.shiftOrderCode || '',
          goodsList: result.info.goodsList || [],
          containerSerialNum: result.info.containerSerialNum,
          isScanContainerAgain: result.info.isScanContainerAgain,
        });

        // 2.批次颜色判断 和上次一样绿色 , 和上次不一样红色 , 重新扫箱子灰色
        // isScanContainerAgain
        // 0不需要但会返回周转箱号(和上次不一样的箱子) 1需要(需要重新绑定箱子) 2不需要(和上次一样的箱子)
        if (result.info.isScanContainerAgain === 2) {
          yield ctx.changeData({
            changeColor: 'green',
          });
        } else if (result.info.isScanContainerAgain === 0) {
          yield ctx.changeData({
            changeColor: 'red',
            containerCode: result.info.rightContainerCode,
          });
        } else if (result.info.isScanContainerAgain === 1) {
          yield ctx.changeData({
            changeColor: 'red',
            barcodeValid: true,
            containerCode: '',
            containerCodeValid: false,
            multiNum: 0,
            cancelNum: 0,
          });
          // 异常提示需要发出声音。
          audioError.play();
          Message.warning(t('请在对应提示的序号上绑定新的周转箱'));
          classFocus('container');
          return;
        }
        // 正常提示声音
        audioSuccess.play();
        let selfmultiNum = 0;
        let selfcancelNum = 0;
        // 3.判断是否多件
        result.info.goodsList.forEach((ele) => {
          if (ele.isMultiPieces === 1) {
            selfmultiNum += ele.num;
          } else {
            selfcancelNum += ele.num;
          }
        });
        if (result.info.isMultiPieces === 1) {
          Message.success(t('已登记为多件商品'));
        } else {
          Message.success(t('已登记为取消件商品'));
        }
        yield ctx.changeData({
          barcode: '',
          barcodeValid: false,
          multiNum: selfmultiNum,
          cancelNum: selfcancelNum,
        });
        classFocus('goods');
      }
    } else {
      if (data.param.scanState === 1) {
        yield ctx.changeData({
          containerCode: '',
          containerCodeValid: false,
        });
        classFocus('container');
        Modal.error({
          audioErr: true,
          title: result.msg,
          className: 'container',
        });
      } else {
        yield ctx.changeData({
          barcode: '',
          barcodeValid: false,
        });
        classFocus('goods');
        Modal.error({
          audioErr: true,
          title: result.msg,
          className: 'goods',
        });
      }
    }
  },
  /**
   * 关箱
   * @returns {IterableIterator<*>}
   */
  * closeContainer(param) {
    const {
      containerCode, subWarehouseId, workLocationCode,
    } = this.state;
    const {
      flag, newChangeContainerCode, goodsList, containerSerialNum,
    } = param;
    yield this.changeData({
      closeLoading: true,
    });
    const result = yield closeContainerServer({
      containerCode,
      // shiftOrderCode,
      subWarehouseId,
      workLocationCode,
    });
    const { boxNum } = this.state;
    if (result.code === '0') {
      // 换箱时候的关箱操作
      if (flag === 'change') {
        yield this.changeData({
          barcode: '',
          barcodeValid: false,
          multiNum: 0,
          cancelNum: 0,
          containerCode: newChangeContainerCode,
          goodsList: goodsList || [],
          containerSerialNum,
        });
        classFocus('goods');
        Message.success(t('已换箱,请扫描条码'));
      } else {
        yield this.changeData({
          containerCode: '',
          containerCodeValid: false,
          barcode: '',
          barcodeValid: true,
          closeLoading: false,
          boxNum: boxNum + 1,
          // shiftOrderCode: '',
          goodsList: [],
          multiNum: 0,
          cancelNum: 0,
          containerSerialNum: '',
        });
        classFocus('container');
      }
    } else {
      yield this.changeData({
        closeLoading: false,
        containerCode: '',
        containerCodeValid: false,
        barcode: '',
        barcodeValid: true,
      });
      Modal.error({
        title: result.msg,
        className: 'container',
      });
    }
  },
  /**
   * 明细查询
   * @param {*} params
   */
  * handleDetailPage(params) {
    const { tabState, pageNum } = params;
    const { detailItems, count } = this.state;

    yield this.changeData({
      showJsx: 'detail',
      tabState,
      ready: false,
      count: pageNum === 1 ? 0 : count,
    });

    const selfDetailItems = pageNum === 1 ? [] : detailItems;
    const param = {
      expTypeList: tabState === 1 ? ['5', '6'] : ['7'],
      pageSize: 10,
      pageNum,
    };
    const result = yield getReturnWarehouseEncaseGoodsDetail(param);
    if (result.code === '0') {
      yield this.changeData({
        pageNum: pageNum + 1,
        ready: true,
        detailItems: selfDetailItems.concat([...result.info.data]),
        count: result.info.meta.count || 0,
      });
    } else {
      yield this.changeData({
        ready: true,
      });
      Modal.error({
        title: result.msg,
        className: 'container',
      });
    }
  },
  // 获取仓库下的所有子仓
  * getAllSubWarehouseList() {
    const result = yield getAllSubWarehouseAPI({
      warehouseId: getWarehouseId(),
      enabled: 1,
    });
    if (result.code === '0') {
      const resList = result.info?.data || [];
      // 保持数据结构一致
      const list = resList.map((item) => ({
        ...item,
        subWarehouseId: item.id,
        subWarehouseName: item.nameZh,
      }));
      yield this.changeData({ subWarehouseList: list });
      if (list.length === 1) {
        yield this.changeData({ subWarehouseId: list[0]?.subWarehouseId });
      }
    }
  },
};
