import { sendPostRequest } from '../../../lib/public-request';

/**
 * 获取子仓列表
 * @param param
 * @returns {*}
 */
export const getSubWarehouse = (param) => sendPostRequest({
  url: '/pda/get_subwarehouse_by_weight',
  param,
}, process.env.WWS_URI);

/**
 * 根据用户和仓库查询是否有未完成的移位单
 * @param param
 * @returns {*}
 */
export const doGetWarehouseShiftOrderCode = (param) => sendPostRequest({
  url: '/pda/get_return_warehouse_shift_order_code',
  param,
}, process.env.WWS_URI);

/**
 * 返仓装箱扫描工位-上机
 * @param param
 * @returns {*}
 */
export const scanReturnWarehouseWorkLocation = (param) => sendPostRequest({
  url: '/pda/scan_return_warehouse_work_location',
  param,
}, process.env.WWS_URI);

/**
 * 返仓装箱扫描工位-下机
 * @param param
 * @returns {*}
 */
export const getOfflineWorkLocation = (param) => sendPostRequest({
  url: '/pda/get_offline_work_location',
  param,
}, process.env.WWS_URI);

/**
 * 返仓装箱--扫描周转箱/返回的时候验证周转箱
 * @param param
 * @returns {*}
 */
export const scanContainerServer = (param) => sendPostRequest({
  url: '/pda/scan_return_warehouse_encase_container_new',
  param,
}, process.env.WWS_URI);

/**
 * 返仓装箱--扫描商品条码
 * @param param
 * @returns {*}
 */
export const scanGoodsServer = (param) => sendPostRequest({
  url: '/pda/scan_return_warehouse_encase_sku_new',
  param,
}, process.env.WWS_URI);

/**
 * 返仓装箱--关箱
 * @param param
 * @returns {*}
 */
export const closeContainerServer = (param) => sendPostRequest({
  url: '/pda/close_return_warehouse_encase_container_new',
  param,
}, process.env.WWS_URI);

/**
 * 返仓装箱明细查看
 * @param param
 * @returns {*}
 */
export const getReturnWarehouseEncaseGoodsDetail = (param) => sendPostRequest({
  url: '/pda/get_return_warehouse_encase_goods_detail',
  param,
}, process.env.WWS_URI);

/**
 * 获取绑定的子仓
 * @param param
 * @returns {*}
 */
export const getBindSubWarehouseAPI = (param) => sendPostRequest({
  url: '/sub_warehouse_user/get_bind_sub_warehouse',
  param,
}, process.env.WIS_FRONT);

// 所有子仓
export const getAllSubWarehouseAPI = (param) => sendPostRequest({
  url: '/sub_warehouse/select',
  param,
}, process.env.BASE_URI_WMD);
