/* eslint-disable @shein-bbl/bbl/translate-i18n-byT */
const { exec } = require('child_process');
const { W2_PORT } = require('./config/config');

exec(`w2 restart -p ${W2_PORT}`, (errTip) => {
  if (errTip) {
    console.log('更新失败:', errTip);
    return;
  }
  exec('w2 use ./mock/agency.js --force', (err, stdout) => {
    if (err) {
      console.log('更新失败:', err);
      return;
    }
    console.log(`更新成功: ${stdout}`);
  });
});
