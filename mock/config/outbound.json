{"enable": true, "pages": [{"page": "order-picking/picking/get-task", "enable": false, "apiList": ["/wos/front/pda/pda_take_pick_task"], "ignoreList": []}, {"page": "oversea/forward", "enable": false, "apiList": ["/owos/front/overseas_forward_box/receive_scan_box"], "ignoreList": []}, {"page": "put-shelves/batch-two-shelf-bind", "enable": false, "apiList": ["/wms/front/batch_shelf/bind_num", "/wms/front/batch_shelf/scan_seed_container", "/wms/front/batch_shelf/scan_batch_shelf", "/wms/front/batch_shelf/get_scan_record"], "ignoreList": []}, {"page": "oversea/forwarding-collection", "enable": false, "apiList": ["/owos/front/overseas_collection/collection_box_num", "/owos/front/overseas_collection/scan_box", "/owos/front/overseas_collection/scan_pallet"], "ignoreList": []}, {"page": "oversea/oversea-relay-group", "enable": false, "apiList": ["/wos/front/forward_group/scan_pick_container", "/wos/front/forward_group/hand_up_goods", "/wos/front/forward_group/get_current_user_pick_container", "/wos/front/forward_group/hand_up_containers", "/wos/front/forward_group/confirm_pick_container", "/wos/front/forward_group/scan_goods", "/wos/front/forward_group/bind_group_container", "/wos/front/forward_group/force_complete_group_info", "/wos/front/forward_group/force_complete_group_confirm", "/wos/front/forward_group/close_group_container", "/wos/front/forward_group/empty_container_info", "/wos/front/forward_group/release_pick_container_confirm", "/wos/front/forward_group/hang_up_info", "/wos/front/forward_group/hang_up", "/wos/front/forward_group/group_detail"], "ignoreList": []}, {"page": "oversea/sowing-first", "enable": false, "apiList": ["/owos/front/query/overseas/index_data", "/owos/front/query/overseas/hand_up_box_detail", "/owos/front/first_sowing/scan_forward_box", "/owos/front/first_sowing/confirm_forward_box", "/owos/front/first_sowing/hand_up_goods", "/owos/front/first_sowing/hang_up", "/owos/front/first_sowing/scan_goods", "/owos/front/first_sowing/close_batch_container", "/owos/front/first_sowing/release_forward_box_query", "/owos/front/first_sowing/release_forward_box_confirm", "/owos/front/first_sowing/first_sowing_detail_view_list", "/owos/front/first_sowing/bind_batch_container", "/owos/front/first_sowing/force_complete_first_sowing", "/owos/front/first_sowing/confirm_force_complete_first"], "ignoreList": []}, {"page": "oversea/sowing-second", "enable": false, "apiList": ["/owos/front/second_sowing/scan_batch_container_box", "/owos/front/second_sowing/scan_sku", "/owos/front/second_sowing/close_seed_container", "/owos/front/second_sowing/scan_seed_container_code", "/owos/front/second_sowing/hand_up_good", "/owos/front/second_sowing/mutal_empty_batch_container", "/owos/front/second_sowing/matul_second_sowing_finish_query", "/owos/front/second_sowing/matul_second_sowing_finish", "/owos/front/second_sowing/view", "/owos/front/second_sowing/hang_up", "/owos/front/second_sowing/get_current_user_batch_container", "/owos/front/second_sowing/second_sowing_goods_num", "/owos/front/second_sowing/change_second_sowing_user", "/owos/front/second_sowing/hand_up_goods"], "ignoreList": []}, {"page": "order-picking/back-down", "enable": false, "apiList": ["/wms/stat", "/wws/front/pda/return_down/index", "/wws/front/pda/return_down/scan_container", "/wws/front/pda/return_down/scan_goods", "/wws/front/pda/return_down/scan_location", "/wws/front/pda/return_down/short_pick", "/wws/front/pda/return_down/close_container", "/wws/front/pda/return_down/down_info", "/wms/front/pda/return_down/scan_piece", "/wms/front/pda/return_down/scan_goods_piece", "/wws/front/scan_down/history_down_list"], "ignoreList": []}, {"page": "order-picking/batch-down", "enable": false, "apiList": ["/wos/front/pda/standard_pick_goods_num", "/wms/stat", "/wos/front/pda/standard_take_pick_task", "/wms/front/pda/batch_off/scan_location", "/wos/front/pda/standard_close_container", "/wos/front/pda/standard_short_pick", "/wos/front/pda/standard_pick_detail"], "ignoreList": []}, {"page": "order-picking/freeze", "enable": false, "apiList": ["/wws/front/freezing_down/index", "/wws/front/freezing_down/scan_container", "/wws/front/freezing_down/scan_location", "/wws/front/freezing_down/scan_goods", "/wws/front/freezing_down/close_container", "/wws/front/freezing_down/commit", "/wws/front/freezing_down/detail/list"], "ignoreList": []}, {"page": "order-picking/other-outbound", "enable": false, "apiList": ["/wos/front/pda/standard_pick_goods_num", "/wms/stat", "/wos/front/pda/standard_take_pick_task", "/wws/front/replenish_shelves/scan_location", "/wos/front/pda/standard_close_container", "/wos/front/pda/standard_post_pick_data", "/wos/front/pda/standard_short_pick", "/wos/front/pda/standard_pick_detail"], "ignoreList": []}, {"page": "order-picking/picking", "enable": false, "apiList": ["/wos/front/pda/pda_pick_goods_num", "/wos/front/pda/pda_take_pick_task", "/wos/front/pda/pda_short_pick", "/wos/front/pda/pda_post_pick_data", "/wos/front/pda/pda_change_pick_container", "/wos/front/pda/pda_validate_pick_container", "/wms/stat", "/wos/front/pda/pda_pick_detail", "/wos/front/pda/pda_validate_task", "/wos/front/pda/pda_take_skip_location", "/wms/internal/front/profit_daily/modify_target", "/wms/internal/front/profit_daily/rank_info", "/wms/stat/user_rank_and_ratio/query", "/wos/front/pda/query_picking_tip", "/wms/front/pda/task_detail_recommend", "/wmd/frontundefined", "/wos/front/pda/pda_confirm_container_finish"], "ignoreList": []}, {"page": "order-picking/recall-shift-down", "enable": false, "apiList": ["/wos/front/withdraw/get_user_info", "/wos/front/withdraw/scan_box_code", "/wos/front/withdraw/scan_location", "/wos/front/withdraw/scan_barcode", "/wos/front/withdraw/close_box", "/wos/front/withdraw/get_detail_info"], "ignoreList": []}, {"page": "order-picking/replenish-down", "enable": false, "apiList": ["/wws/front/replenish_shelves/query_task", "/wms/stat/rank/query", "/wws/front/replenish_shelves/scan_pick_container", "/wws/front/replenish_shelves/scan_location", "/wws/front/replenish_shelves/close_pick_container", "/wws/front/replenish_shelves/scan_goods", "/wws/front/replenish_shelves/bottom_shelves", "/wms/front/replenish_shelves/scan_pallet", "/wms/front/replenish_shelves/under_pallet", "/wws/front/replenish_shelves/short_pick", "/wws/front/replenish_shelves/under_info", "/wws/front/replenish_shelves/obtain_under_task", "/wws/front/replenish_shelves/short_pick_by_box", "/wws/front/replenish_shelves/scan_pick_container_by_box", "/wws/front/scan_down/history_down_list"], "ignoreList": []}, {"page": "order-picking/return-down", "enable": false, "apiList": ["/wms/stat", "/wms/front/replenish_shelves/query_purchase_return_task_info", "/wms/front/replenish_shelves/scan_location", "/wms/front/replenish_shelves/scan_pallet", "/wws/front/replenish_shelves/short_pick", "/wms/front/replenish_shelves/scan_purchase_return_pick_container", "/wms/front/replenish_shelves/scan_purchase_return_barcode", "/wms/front/replenish_shelves/submit_purchase_return_goods_count", "/wms/front/replenish_shelves/close_purchase_return_pick_container", "/wms/front/replenish_shelves/purchase_return_short_pick", "/wms/front/replenish_shelves/query_record_detail"], "ignoreList": []}, {"page": "order-picking/return-down-new", "enable": false, "apiList": ["/wms/qc/front/replenish_shelves/query_purchase_return_task_info", "/wms/qc/front/replenish_shelves/scan_location", "/wms/qc/front/replenish_shelves/scan_pallet", "/wws/front/replenish_shelves/short_pick", "/wms/qc/front/replenish_shelves/scan_purchase_return_pick_container", "/wms/qc/front/replenish_shelves/scan_purchase_return_barcode", "/wms/qc/front/replenish_shelves/submit_purchase_return_goods_count", "/wms/qc/front/replenish_shelves/close_purchase_return_pick_container", "/wms/qc/front/replenish_shelves/purchase_return_short_pick", "/wms/qc/front/replenish_shelves/query_record_detail"], "ignoreList": []}, {"page": "order-picking/shelf-life-frozen", "enable": false, "apiList": ["/wws/front/validity_freezing_down/query_task", "/wms/stat/rank/query", "/wws/front/validity_freezing_down/sacn_container", "/wws/front/validity_freezing_down/scan_location", "/wws/front/validity_freezing_down/scan_goods", "/wws/front/validity_freezing_down/close_pick_container", "/wws/front/validity_freezing_down/commit", "/wws/front/validity_freezing_down/short_pick", "/wws/front/validity_freezing_down/under_info"], "ignoreList": []}, {"page": "order-picking/shift-down", "enable": false, "apiList": ["/wws/front/shift_down/index", "/wws/front/shift_down/scan_container", "/wws/front/shift_down/scan_location", "/wws/front/shift_down/scan_goods", "/wws/front/shift_down/commit", "/wws/front/shift_down/close_container", "/wws/front/shift_down/detail/list", "/wms/front/shift/down/recommend/location", "/wws/front/scan_down/history_down_list"], "ignoreList": []}, {"page": "sowing/empty-container", "enable": false, "apiList": ["/wos/front/first_sowing/scan_pick_container_empty", "/wos/front/second_sowing/scan_batch_container_box_empty"], "ignoreList": []}, {"page": "sowing/exception", "enable": false, "apiList": ["/wms/front/exception_sowing/exception_sacn_sowing", "/wms/front/exception_sowing/exception_sacn_exception", "/wms/front/exception_sowing/empty_box", "/wms/front/exception_sowing/confirm_empty_box", "/wms/front/exception_sowing/exception_scan_seed_box", "/wos/front/exception_sowing/exception_sowing_detail"], "ignoreList": []}, {"page": "sowing/exp-first", "enable": false, "apiList": ["/wos/front/exp_first_sowing/init_exp_first_fowing", "/wos/front/exp_first_sowing/exp_first_fowing_scan_container", "/wos/front/exp_first_sowing/exp_first_fowing_scan_sku", "/wos/front/exp_first_sowing/exp_first_fowing_close_container", "/wos/front/exp_first_sowing/empty_box", "/wos/front/exp_first_sowing/get_pick_container_sowing_detail", "/wos/front/exp_first_sowing/get_batch_container_sowing_detail", "/wos/front/exp_first_sowing/exp_first_fowing_scan_first_sowing_container", "/wos/front/exp_first_sowing/empty_box_confirm", "/wos/front/exp_first_sowing/go_back"], "ignoreList": []}, {"page": "sowing/exp-second", "enable": false, "apiList": ["/wos/front/exp_sowing/get_current_user_container", "/wos/front/exp_sowing/scan_sowing_box_code", "/wos/front/exp_sowing/scan_sku", "/wos/front/exp_sowing/scan_exception_box", "/wos/front/exp_sowing/mutal_empty_sowing_box", "/wos/front/exp_sowing/query_exp_second_sowing_detail", "/wos/front/exp_sowing/query_exp_container", "/wos/front/exp_sowing/hang_up", "/wos/front/sowing_multiple_goods/get_sowing_multiple_goods"], "ignoreList": []}, {"page": "sowing/first", "enable": false, "apiList": ["/wos/front/first_sowing/scan_pick_container", "/wos/front/first_sowing/confirm_pick_container", "/wos/front/first_sowing/scan_goods", "/wos/front/first_sowing/bind_batch_container", "/wos/front/first_sowing/confirm_force_complete_first", "/wos/front/first_sowing/close_batch_container", "/wos/front/first_sowing/release_pick_container_query", "/wos/front/first_sowing/release_pick_container_confirm", "/wos/front/first_sowing/force_complete_first_sowing", "/wos/front/first_sowing/first_sowing_detail_view_list", "/wos/front/first_sowing/hang_up", "/wos/front/first_sowing/get_current_user_pick_container", "/wos/front/first_sowing/first_sowing_goods_num", "/wos/front/sowing_multiple_goods/get_sowing_multiple_goods", "/wos/front/first_sowing/click_pick_container", "/wos/front/sowing_multiple_goods/scan_barcode", "/wos/front/first_sowing/hand_up_good"], "ignoreList": []}, {"page": "sowing/first-scan", "enable": false, "apiList": ["/wos/front/first_sowing/scan"], "ignoreList": []}, {"page": "sowing/handle-error", "enable": false, "apiList": ["/wos/front/exception/scan_container", "/wos/front/exception/empty_box_query", "/wos/front/exception/manual_completion_query", "/wos/front/exception/manual_completion_confirm", "/wos/front/exception/empty_box_confirm"], "ignoreList": []}, {"page": "sowing/more-goods-scan", "enable": false, "apiList": ["/wos/front/sowing_multiple_goods/scan_container_code", "/wos/front/sowing_multiple_goods/scan_barcode"], "ignoreList": []}, {"page": "sowing/multi-wear-handle-error", "enable": false, "apiList": ["/wos/front/multi_wear/exp/query", "/wos/front/multi_wear/exp/handle"], "ignoreList": []}, {"page": "sowing/pickup-container", "enable": false, "apiList": ["/wos/front/exception_task_container/query"], "ignoreList": []}, {"page": "sowing/second", "enable": false, "apiList": ["/wos/front/second_sowing/scan_batch_container_box", "/wos/front/second_sowing/scan_sku", "/wos/front/second_sowing/close_seed_container", "/wos/front/second_sowing/scan_seed_container_code", "/wos/front/second_sowing/mutal_empty_batch_container", "/wos/front/second_sowing/matul_second_sowing_finish_query", "/wos/front/second_sowing/matul_second_sowing_finish", "/wos/front/second_sowing/view", "/wos/front/second_sowing/hang_up", "/wos/front/second_sowing/get_current_user_batch_container", "/wos/front/second_sowing/second_sowing_goods_num", "/wos/front/second_sowing/change_second_sowing_user", "/wos/front/sowing_multiple_goods/get_sowing_multiple_goods", "/wos/front/second_sowing/scan_work_location_code", "/wos/front/second_sowing/update_on_work_location_info", "/wos/front/second_sowing/yesterday_second_sowing_goods_num", "/wos/front/second_sowing/scan_sowing_car_no", "/wos/front/sowing_multiple_goods/scan_barcode", "/wos/front/second_sowing/hand_up_good"], "ignoreList": []}, {"page": "sowing/second-scan", "enable": false, "apiList": ["/wos/front/second_sowing/scan_list", "/wos/front/second_sowing/scan"], "ignoreList": []}, {"page": "sowing/special-first", "enable": false, "apiList": ["/wos/front/sp_first_sowing/scan_pick_container", "/wos/front/sp_first_sowing/scan_sku", "/wos/front/sp_first_sowing/close_box", "/wos/front/sp_first_sowing/empty_box", "/wos/front/sp_first_sowing/hang_up_box", "/wos/front/sp_first_sowing/scan_batch_container_code", "/wos/front/sp_first_sowing/detail", "/wos/front/sp_pick_container/detail", "/wms/front/sp_first_sowing/empty_box_selector"], "ignoreList": []}, {"page": "sowing/special-second", "enable": false, "apiList": ["/wos/front/sp_second_sowing/scan_source_container", "/wos/front/sp_second_sowing/scan_sku", "/wos/front/sp_second_sowing/close_box", "/wos/front/sp_second_sowing/empty_box", "/wos/front/sp_second_sowing/hang_up_box", "/wos/front/sp_second_sowing/single_product_moving_box", "/wos/front/sp_second_sowing/scan_seed_container_code", "/wos/front/sp_second_sowing/detail", "/wos/front/sp_second_sowing/empty_box_selector"], "ignoreList": []}, {"page": "collection/close-collection-task", "enable": false, "apiList": ["/wos/front/collection/task/cancel/scan_container_code", "/wos/front/collection/task/cancel"], "ignoreList": []}, {"page": "collection/collection-transfer", "enable": false, "apiList": ["/wos/front/sub_collection/scan_transfer_container", "/wos/front/sub_collection/scan_transfer_pallet", "/wos/front/sub_collection/close_transfer_pallet", "/wos/front/sub_collection/confirm_receive", "/wos/front/sub_collection/turn_on_scan_pallet"], "ignoreList": []}, {"page": "collection/collection-transshipment", "enable": false, "apiList": ["/wos/front/collect_transport/scan_pallet", "/wos/front/collect_transport/scan_license", "/wos/front/collect_transport/click_transfer", "/wos/front/vehicle_task/push_lvms_update"], "ignoreList": []}, {"page": "collection/main-collection", "enable": false, "apiList": ["/wos/front/main_collect/scan_pallet", "/wos/front/main_collect/scan_location", "/wos/front/main_collect/main_collect_box_num"], "ignoreList": []}, {"page": "collection/multi-wear-collection", "enable": false, "apiList": ["/wos/front/multi_wear_collection/multi_wear_scan_container"], "ignoreList": []}, {"page": "collection/receive-collection-task", "enable": false, "apiList": ["/wos/front/collection/task/receive/scan_container_code", "/wos/front/collection/task/receive/get_task"], "ignoreList": []}, {"page": "collection/release-collection", "enable": false, "apiList": ["/wos/front/sub_collection/release_collection", "/wos/front/sub_collection/update_release_record"], "ignoreList": []}, {"page": "collection/search-collection", "enable": false, "apiList": ["/wos/front/main_collect/collect_query"], "ignoreList": []}, {"page": "collection/sub-collection", "enable": false, "apiList": ["/wos/front/sub_collection/sub_scan_container", "/wos/front/sub_collection/sub_scan_pallet", "/wos/front/sub_collection/close_pallet", "/wos/front/sub_collection/sub_collection_box_num", "/wos/front/sub_collection/show/task_info"], "ignoreList": []}, {"page": "collection/transfer-and-hand-over", "enable": false, "apiList": ["/wos/front/main_collect/handover_scan_container", "/wos/front/main_collect/handover_scan_location", "/wos/front/main_collect/handover_cancel", "/wms/stat"], "ignoreList": []}, {"page": "collection/transfer-collection", "enable": false, "apiList": ["/wos/front/pda/scanContainerCode", "/wos/front/pda/scanLocationCode", "/wos/front/pda/scanLocationCollectionFinish"], "ignoreList": []}, {"page": "collection/transmission-line-query", "enable": false, "apiList": ["/wos/front/collect_conveyer_base/query"], "ignoreList": []}, {"page": "collection/wellen-query", "enable": false, "apiList": ["/wos/front/main_collect/judge_collect_type/scan_pallet"], "ignoreList": []}, {"page": "compound-package/batch-collaboration", "enable": false, "apiList": ["/wpoc/front/split_batch/scan_box_no", "/wpoc/front/split_batch/scan_location", "/wpoc/front/wellen_collect_split/force_finish_split"], "ignoreList": []}, {"page": "compound-package/collect-under", "enable": false, "apiList": ["/wpoc/front/collect_under/init_scan_transfer_container_code", "/wpoc/front/collect_under/scan_transfer_container_code_step_one", "/wpoc/front/collect_under/scan_transfer_container_code_step_two", "/wpoc/front/collect_under/scan_transfer_container_code_step_three", "/wpoc/front/combine_package_scanning/replace_location", "/wpoc/front/collect_under/get_location"], "ignoreList": []}, {"page": "compound-package/container-scan", "enable": false, "apiList": ["/wpoc/front/collectionbox/scan_box_no", "/wpoc/front/collectionbox/scan_box_number"], "ignoreList": []}, {"page": "compound-package/delivery-scan", "enable": false, "apiList": ["/wpoc/front/pre/send/transfer"], "ignoreList": []}, {"page": "compound-package/exp-on-shelf", "enable": false, "apiList": ["/wpoc/front/combine_exception/scanPackageNo", "/wpoc/front/combine_exception/scanSecondContainer", "/wpoc/front/combine_exception/scanLocation"], "ignoreList": []}, {"page": "compound-package/exp-on-shelf-query", "enable": false, "apiList": ["/wpoc/front/combine_exception/exception_query", "/wos/front/pda/combine_exception/second_detail_query", "/wms/front/inventory/get_batch_container_info_scan"], "ignoreList": []}, {"page": "compound-package/location-query", "enable": false, "apiList": ["/wpoc/front/change_transfer/query"], "ignoreList": []}, {"page": "compound-package/receiving-scan", "enable": false, "apiList": ["/wpoc/front/receive/manager/scan_box_no", "/wpoc/front/receive/manager/confirm_receive", "/wpoc/front/receive/manager/difference/confirm_receive", "/wpoc/front/receive/manager/delivery_by_case/scan_box_no"], "ignoreList": []}, {"page": "compound-package/shipment-scan", "enable": false, "apiList": ["/wpoc/front/truck_loading/scan_car_no", "/wpoc/front/truck_loading/scan_box_no"], "ignoreList": []}, {"page": "compound-package/subcontracting-query", "enable": false, "apiList": ["/wpoc/front/split_up/multi_type_query", "/wms/front/inventory/container/query", "/wms/front/inventory/seed_container/list", "/wms/front/inventory/get_batch_container_info_scan"], "ignoreList": []}, {"page": "compound-package/subpackage-hit-sheleves", "enable": false, "apiList": ["/wpoc/front/split_up/scan_box", "/wpoc/front/split_up/scan_work_location", "/wos/front/second_sowing/change_second_sowing_user", "/wpoc/front/subcontract_scan/list_split_detail", "/wpoc/front/split_up/scan_package", "/wpoc/front/split_up/empty_box", "/wpoc/front/split_up/comfirm_empty", "/wos/front/sowing_multiple_goods/get_sowing_multiple_goods", "/wos/front/second_sowing/hang_up", "/wos/front/second_sowing/manual_empty_batch_container", "/wos/front/second_sowing/view_spilt_upper", "/wos/front/split_up/scan_barcode", "/wos/front/split_up/scan_second_container", "/wos/front/second_sowing/close_seed_container", "/wos/front/sowing_multiple_goods/scan_barcode"], "ignoreList": []}, {"page": "compound-package/subpackage-scan", "enable": false, "apiList": ["/wpoc/front/combine_package_scanning/new_scan_box_no", "/wpoc/front/combine_package_scanning/judge_scan_box", "/wpoc/front/combine_package_scanning/get_un_scan_transferboxes", "/wpoc/front/combine_package_scanning/empty_box", "/wpoc/front/combine_package_scanning/replace_location", "/wms/front/combine_package_scanning/init_scan_box_no", "/wms/front/combine_package_scanning/get_location"], "ignoreList": []}, {"page": "compound-package/subpackage-scan-new", "enable": false, "apiList": ["/wpoc/front/subcontract_scan/subcontract_scan_box_no", "/wpoc/front/subcontract_scan/subcontract_scan_package_no", "/wpoc/front/combine_package_scanning/empty_box", "/wpoc/front/combine_package_scanning/empty_box", "/wpoc/front/subcontract_scan/list_split_detail"], "ignoreList": []}, {"page": "compound-package/wave-box", "enable": true, "apiList": ["/wpoc/front/wellen_collect_split/scan_box_no", "/wpoc/front/wellen_collect_split/scan_location"], "ignoreList": []}, {"page": "special-out/box-out", "enable": false, "apiList": ["/wos/front/overseas_out/pda/scan_box", "/wos/front/overseas_out/pda/force_complete", "/wos/front/overseas_out/pda/scan_outbound_code"], "ignoreList": []}, {"page": "special-out/box-storage", "enable": false, "apiList": ["/wms/front/box_tmp_storage/scan_box_no", "/wms/front/box_tmp_storage/scan_location_no"], "ignoreList": []}, {"page": "special-out/box-storage-cancel", "enable": false, "apiList": ["/wms/front/box_tmp_storage/cancel_scan_box_no", "/wms/front/box_tmp_storage/scan_location_no"], "ignoreList": []}, {"page": "special-out/box-storage-shelf", "enable": false, "apiList": ["/wos/front/pda/box_carry_down/scan_outbound_code", "/wos/front/pda/box_carry_down/scan_location", "/wos/front/pda/box_carry_down/scan_box"], "ignoreList": []}, {"page": "special-out/collection-transfer", "enable": false, "apiList": ["/wos/front/pda/special_collect_transfer/collect_load/scan_container_code", "/wos/front/pda/special_collect_transfer/collect_load/scan_pallet_code", "/wos/front/pda/special_collect_transfer/collect_load/close_pallet", "/wos/front/pda/special_collect_transfer/change_pallet/init"], "ignoreList": []}, {"page": "special-out/collection-transfer-changepallet", "enable": false, "apiList": ["/wos/front/pda/special_collect_transfer/change_pallet/scan_other_pallet_container", "/wos/front/pda/special_collect_transfer/change_pallet/scan_container", "/wos/front/pda/special_collect_transfer/change_pallet/scan_pallet", "/wos/front/pda/special_collect_transfer/change_pallet/confirm_close", "/wos/front/pda/special_collect_transfer/collect_load/close_pallet", "/wos/front/sub_collection/confirm_receive", "/wos/front/pda/special_collect_transfer/change_pallet/init", "/wos/front/pda/special_collect_transfer/change_pallet/use_new_pallet"], "ignoreList": []}, {"page": "special-out/collection-transshipment", "enable": false, "apiList": ["/wos/front/pda/special_collect_transfer/collect_transfer/scan_container_pallet", "/wos/front/pda/special_collect_transfer/collect_transfer/transfer", "/wos/front/vehicle_task/push_lvms_update"], "ignoreList": []}, {"page": "special-out/collection-transshipment-receive", "enable": false, "apiList": ["/wos/front/pda/special_collect_transfer/receive/scan_pallet_code", "/wos/front/pda/special_collect_transfer/receive/scan_container_code", "/wos/front/pda/special_collect_transfer/receive/receive_result"], "ignoreList": []}, {"page": "allot-out-temporary/wait-allot-storage-down", "enable": false, "apiList": ["/wts/front/take_down/init_data", "/wts/front/take_down/scan_location", "/wts/front/take_down/scan_container_code", "/wts/front/take_down/lack_container_code", "/wts/front/take_down/take_task", "/wts/front/take_down/finish_task"], "ignoreList": []}, {"page": "allot-out-temporary/wait-allot-storage-fcl", "enable": false, "apiList": ["/wts/front/box_storage/scan_location", "/wts/front/box_storage/scan_container"], "ignoreList": []}]}