// 系统名称
const SYSTEM = 'mot';

// 代理环境，默认代理测试环境接口
const PROXY_ENV = 'test';

// 是否开启本地代理和本地URL
const OPEN_LOCAL_AGENT = true;
const LOCAL_AGENT_URL = 'http://127.0.0.1:8088/';

// 各环境对应域名
const hostConfigObj = {
  test: 'mot-eu-test01.dotfashion.cn',
  sit: 'mot-eu-sit01.dotfashion.cn',
  dev: 'mot-eu-dev01.dotfashion.cn',
};

// 各后端子系统前缀对应soapi的mock数值【大多数】
const sysMockNumObj = {
  '/wms/front': '584',
  '/wmd/front': '1552',
  '/wms/qc/front': '625',
  '/was/out': '1498',
  '/wos/front': '2217',
  '/wpoc/front': '2699',
  '/wws/front': '2916',
  '/wms/qc': '625',
  '/owos/front': '3455',
  '/wms/stat/front': '547',
  '/wts/front': '1469',
  '/qms/pda': '625',
  '/owis/pda': '3576',
  '/wms/internal': '612',
  '/wms/stat': '547',
  '/qms/front': '5044',
};

// w2启动端口, 默认6688
const W2_PORT = '6688';

// soapi接口HOST值
const MOCK_HOST = 'https://soapi-sdk-web01.dotfashion.cn/mock/';

// 是否将soapi接口info.code值统一替换为0
const REPLACE_RES_CODE = true;

module.exports = {
  SYSTEM,
  PROXY_ENV,
  hostConfigObj,
  sysMockNumObj,
  MOCK_HOST,
  OPEN_LOCAL_AGENT,
  LOCAL_AGENT_URL,
  W2_PORT,
  REPLACE_RES_CODE,
};
