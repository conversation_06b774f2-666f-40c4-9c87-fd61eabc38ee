{"enable": false, "pages": [{"page": "nbound-manage/transfer-scan", "enable": true, "apiList": ["/qms/front/pda/handover_loading/scan_container", "/qms/front/pda/handover_loading/scan_pallet", "/qms/front/pda/handover_loading/close_pallet", "/qms/front/pda/handover_loading/return_submit", "/qms/front/pda/handover_wait_delivery_storage/scan_location", "/qms/front/pda/handover_wait_delivery_storage/scan_pallet"]}, {"page": "return/return-picking-off", "enable": true, "apiList": ["/wms/qc/front/tk_purchase_shelves/query_has_receive_task", "/wms/qc/front/tk_purchase_shelves/short_pick", "/wms/qc/front/tk_purchase_shelves/receive_task", "/wms/qc/front/tk_purchase_shelves/scan_location", "/wms/qc/front/tk_purchase_shelves/scan_box"]}, {"page": "put-shelves/batch-shelves", "enable": false, "apiList": ["/wms/qc/receipts/new_pda/query", "/wms/qc/receipts/new_pda/commit", "/wms/qc/receipts/pda/update/express_no", "/wms/qc/front/sub_warehouse_user/get_bind_sub_warehouse"], "ignoreList": []}, {"page": "put-shelves/put-away", "enable": true, "apiList": ["/wms/qc/front/pda/bill_store_scan_container", "/wms/qc/front/pda/bill_store_scan_location", "/wms/qc/front/pda/bill_store_check_goods_info", "/wms/qc/front/pda/bill_store_scan_barcode", "/wms/qc/front/pda/bill_store_box_detail", "/wms/qc/front/wait_up_check/empty_container", "/wms/qc/front/pda/bill_store_upper_history", "/wms/qc/front/pda/dept_ts_member_assigned/query", "/wws/front/common/recommend/location", "/wms/front/pda/upper_config/get", "/wmd/front/goods/get_goods_imgs_by_skc", "/wms/qc/front/pda/box_info"], "ignoreList": []}, {"page": "put-shelves/put-error", "enable": false, "apiList": ["/wms/front/shelf/exception_shelf_check_package_no", "/wms/front/shelf/exception_shelf_check_container", "/wms/front/shelf/exception_shelf_post", "/wos/front/shelf/scan_container_or_package", "/wos/front/shelf/scan_container_and_package", "/wos/front/shelf/scan_exp_package", "/wos/front/shelf/scan_exp_location"], "ignoreList": []}, {"page": "put-shelves/putaway-task", "enable": false, "apiList": ["/wms/front/gather_task/pda/take_task", "/wms/front/pda/upper_task/scan_container_code", "/wms/front/pda/upper_task/get_containers_by_task_code", "/wms/front/pda/upper_task/force_close", "/wms/front/pda/upper_task/get_task_msg"], "ignoreList": []}, {"page": "put-shelves/putaway-task-new", "enable": false, "apiList": ["/wms/front/gather_task/pda/take_task", "/wms/qc/front/pda/upper_task/scan_container_code_new", "/wms/qc/front/pda/upper_task/shelf_collection", "/wms/qc/front/pda/upper_task/get_containers_by_task_code", "/wms/qc/front/pda/upper_task/force_close", "/wms/qc/front/pda/upper_task/get_task_msg"], "ignoreList": []}, {"page": "put-shelves/quality-shelves", "enable": true, "apiList": ["/wms/front/quality_upper/scan_container", "/wms/front/quality_upper/scan_bar_code", "/wms/front/quality_upper/scan_location", "/wms/front/quality_upper/empty_container", "/wms/front/quality_upper/detail", "/wws/front/common/recommend/location", "/wms/front/pda/upper_config/get", "/wmd/front/goods/get_goods_imgs_by_skc", "/wms/front/quality_upper/scan_container_check_soft", "/wms/qc/front/pda/box_info"], "ignoreList": []}, {"page": "put-shelves/shift-all", "enable": false, "apiList": ["/wms/front/full_pallet_upper/scan_pallet", "/wms/front/full_pallet_upper/scan_location"], "ignoreList": []}, {"page": "put-shelves/shift-up", "enable": true, "apiList": ["/wws/front/shift_upper/query", "/wws/front/shift_upper/scan_container_new", "/wws/front/shift_upper/scan_location", "/wws/front/shift_upper/scan_goods_check_goods_info", "/wws/front/shift_upper/scan_goods", "/wws/front/shift_upper/box_empty", "/wws/front/shift_upper/scan_finish_new", "/wws/front/shift_upper/history", "/wws/front/common/recommend/location", "/wms/front/pda/upper_config/get", "/wmd/front/goods/get_goods_imgs_by_skc", "/wms/qc/front/pda/box_info"], "ignoreList": []}, {"page": "put-shelves/whole-box-shelves", "enable": false, "apiList": ["/wms/front/all_upper/scan_container", "/wms/front/all_upper/scan_location"], "ignoreList": []}, {"page": "scan", "enable": false, "apiList": ["/wms/front/take_delivery/scan_warehouse", "/wms/front/take_delivery/scan_pallet", "/wms/front/take_delivery/scan_container", "/wms/front/take_delivery/scan_container", "/wms/front/put_on_pallet/judgeContainerType", "/wms/front/put_on_pallet/scan_container", "/wms/front/put_on_pallet/scan_pallet", "/wms/front/delivery/delivery_scan", "/wms/front/delivery/scan_pallet", "/wms/front/delivery/click_delivery"], "ignoreList": []}, {"page": "scan/transfer-query", "enable": false, "apiList": ["/wms/qc/front/pda/handover_scan/scan_handover_all"], "ignoreList": []}, {"page": "scan-new", "enable": false, "apiList": ["/wms/front/take_delivery/scan_warehouse", "/wms/front/take_delivery/scan_pallet", "/wms/front/take_delivery/scan_container", "/wms/front/take_delivery/scan_container", "/wms/qc/front/pda/handover_loading/scan_container", "/wms/qc/front/pda/handover_loading/scan_pallet", "/wms/qc/front/pda/handover_loading/return_submit", "/wms/qc/front/pda/handover_loading/close_pallet", "/wms/qc/front/pda/handover_delivery/delivery_scan", "/wms/qc/front/pda/handover_delivery/scan_pallet", "/wms/qc/front/pda/handover_delivery/click_ship", "/wws/front/common/recommend/location"], "ignoreList": []}, {"page": "scan-new/transfer-query", "enable": false, "apiList": ["/wms/qc/front/pda/handover_scan/query_pick_container", "/wms/qc/front/pda/handover_scan/query_pallet_info", "/wms/qc/front/pda/handover_scan/query_storage_location"], "ignoreList": []}, {"page": "scan-new/load", "enable": true, "apiList": ["/wms/qc/front/pda/handover_loading/scan_container"], "ignoreList": []}, {"page": "scan-new/transfer-scan", "enable": true, "apiList": ["/wms/qc/front/pda/handover_receive/scan_warehouse", "/wms/qc/front/pda/handover_receive/scan_pallet", "/wms/qc/front/pda/handover_receive/scan_pallet_query", "/wms/qc/front/pda/handover_receive/scan_container", "/wms/qc/front/pda/handover_receive/confirm", "/wms/qc/front/sub_warehouse_user/get_bind_sub_warehouse"], "ignoreList": ["/wms/qc/front/sub_warehouse_user/get_bind_sub_warehouse"]}, {"page": "scan-new/transfer-storage-scan", "enable": false, "apiList": ["/wms/qc/front/pda/handover_receive_storage/scan_location", "/wms/qc/front/pda/handover_receive_storage/scan_pallet"], "ignoreList": []}, {"page": "scan-new/upper-temp", "enable": false, "apiList": ["/wms/qc/front/pda/handover_wait_shelf_storage/scan_pallet_new", "/wms/qc/front/pda/handover_wait_shelf_storage/scan_location_new", "/wms/qc/front/pda/handover_wait_shelf_storage/scan_release_location", "/wms/qc/front/pda/handover_wait_shelf_storage/confirm_release_location", "/was/out/check_url_permission"], "ignoreList": []}, {"page": "scan-new/upper-transfer", "enable": false, "apiList": ["/wms/qc/front/pda/handover_wait_shelf/scan_location", "/wms/qc/front/pda/handover_wait_shelf/scan_pallet"], "ignoreList": []}, {"page": "standard-receive-goods", "enable": false, "apiList": ["/wms/front/receive_code/query_warehouse", "/wms/front/scan_noticecode", "/wms/front/scan_boxno", "/wms/front/scan_sku", "/wms/front/receive_box/scan_box", "/wms/front/receive_code/scan_notice_code"], "ignoreList": []}, {"page": "standard-receive-goods/return-package-scan", "enable": false, "apiList": ["/wms/front/new-pda/reback_receive/scan", "/wms/front/new-pda/reback_receive/append_scan"], "ignoreList": []}, {"page": "standard-receive-goods/return-package-scan-new", "enable": false, "apiList": ["/wms/front/reback_receive/reject_scan", "/wms/front/reback_receive/reject_append_scan"], "ignoreList": []}, {"page": "inbound-manage/batch", "enable": false, "apiList": ["/wms/front/goods_diff_box/batch_scan_box_no", "/wms/front/goods_diff_box/create_batch_customs_code"], "ignoreList": []}, {"page": "inbound-manage/points-for-goods", "enable": false, "apiList": ["/wms/front/goods_diff_box/query_warehouse", "/wms/front/goods_diff_box/query_box_num", "/wms/front/goods_diff_box/all_close_box", "/wms/front/goods_diff_box/scan_bar_code", "/wms/front/goods_diff_box/scan_box_code", "/wms/front/goods_diff_box/close_box", "/wms/front/goods_diff_box/query_box_detail", "/wms/front/goods_diff_box/delete_box_detail"], "ignoreList": []}, {"page": "refund-scan/abnormal/deal-with-requisition", "enable": false, "apiList": ["/wms/qc/front/pda/exception_apply/query/scan_apply_code"], "ignoreList": []}, {"page": "refund-scan/abnormal/handle-requisition", "enable": false, "apiList": ["/wms/qc/front/sub_warehouse_user/get_bind_sub_warehouse", "/wms/qc/front/pda/exception_apply/deliver/query_service", "/wms/qc/front/pda/exception_apply/deliver/scan_locate_code", "/wms/qc/front/pda/exception_apply/deliver/scan_apply_code"], "ignoreList": []}, {"page": "refund-scan/abnormal/temporary-storage", "enable": false, "apiList": ["/wms/qc/front/pda/exception_apply/storage/scan_locate_code", "/wms/qc/front/pda/exception_apply/storage/scan_apply_code"], "ignoreList": []}, {"page": "refund-scan/goods-receipt", "enable": false, "apiList": ["/wms/qc/pda/query/SubWarehouse/auth", "/wms/qc/pda/query/orderPackage/info", "/wms/qc/pda/push/receipt", "/wms/qc/receipt/tray/close/tray", "/wms/qc/pda/query/tray/detail", "/wms/qc/pda/add/receipt/tray", "/wms/qc/pda/close/user/tray"], "ignoreList": []}, {"page": "refund-scan/inferior-product-query", "enable": false, "apiList": ["/wms/qc/defective/pda/query_defective_data_list"], "ignoreList": []}, {"page": "refund-scan/inferior-receipt", "enable": false, "apiList": ["/wms/qc/defective/pda/package_receipt_query", "/wms/qc/defective/pda/package_receipt_commit"], "ignoreList": []}, {"page": "refund-scan/new-point-binning", "enable": false, "apiList": ["/wms/qc/warehouse/scan_receipt", "/wms/qc/warehouse/scan_purchase_new", "/wms/qc/warehouse/query_all_order_status", "/wms/qc/warehouse/scan_turnover_new", "/wms/qc/warehouse/scan_goods_get_receipt_code", "/wms/qc/outside/dict/select", "/wms/qc/warehouse/submit_scanned_goods_purchase", "/wms/qc/warehouse/submit_scanned_goods", "/wms/qc/warehouse/scan_goods_new", "/wms/qc/warehouse/query_inf_goods", "/wws/front/common/recommend/location", "/wms/qc/warehouse/query_defective_info", "/wms/qc/warehouse/get_in_storage_history", "wms/qc/front/sub_warehouse_user/get_bind_sub_warehouse"], "ignoreList": []}, {"page": "refund-scan/receive-drawing-task", "enable": false, "apiList": ["/wms/qc/goods/task/pick", "/wms/qc/scan/temporary/location", "/wms/qc/scan/order/package", "/wms/qc/scan/work/station", "/wms/qc/empty/goods/cancel", "/wms/qc/frame/task/pick"], "ignoreList": []}, {"page": "refund-scan/scan-putaway", "enable": false, "apiList": ["/wms/qc/defective/pda/package_pushrack_query", "/wms/qc/defective/pda/locator_code_use", "/wms/qc/defective/pda/package_pushrack_commit"], "ignoreList": []}, {"page": "refund-scan/sold-out", "enable": true, "apiList": ["/wms/qc/defective/pda/package_pick_query", "/wms/qc/defective/pda/package_pick_commit", "/wms/qc/defective/package/lower_shelf"], "ignoreList": []}, {"page": "refund-scan/temporary-storage", "enable": false, "apiList": ["/wms/qc/pda/push/temporary/storage/commit", "/wms/qc/pda/push/temporary/storage/query", "/wms/qc/pda/query/tray/detail"], "ignoreList": []}, {"page": "refund-scan/waybill-weigh", "enable": false, "apiList": ["/wms/qc/express/weight/query", "/wms/qc/express/weight/submit"], "ignoreList": []}, {"page": "refund-scan/pda-receipt", "enable": true, "apiList": ["/wms/qc/receipts/new_pda/query"], "ignoreList": []}, {"page": "receive-goods/special-receive-goods", "enable": false, "apiList": ["/wos/front/pda/standard_receive"], "ignoreList": []}, {"page": "return/logistics-order", "enable": false, "apiList": ["/wms/qc/front/return/logistics/create_logistics_order", "/wms/qc/front/return/logistics/scan_box_code"], "ignoreList": []}, {"page": "return/purchase-return-down", "enable": false, "apiList": ["/wms/qc/front/replenish_shelves_new/query_has_receive_task", "/wms/qc/front/replenish_shelves_new/receive_task", "/wms/qc/front/replenish_shelves_new/scan_location", "/wms/qc/front/replenish_shelves_new/sold_out_short", "/wms/qc/front/replenish_shelves_new/scan_and_submit_container", "/wms/qc/front/replenish_shelves_new/scan_purchase_return_pick_container", "/wms/qc/front/replenish_shelves_new/scan_purchase_return_barcode", "/wms/qc/front/replenish_shelves_new/submit_purchase_return_goods_count", "/wms/qc/front/replenish_shelves_new/submit_and_close_box", "/wms/qc/front/replenish_shelves_new/query_detail"], "ignoreList": []}, {"page": "return/return-delivery", "enable": false, "apiList": ["/wms/qc/front/return/deliver/location/scan", "/wms/qc/front/return/deliver/return_notice_code/scan", "/wms/qc/front/return/deliver/box/scan", "/wms/qc/front/return/scan_qr_code", "/wms/qc/front/return/get_exists_cache", "/wms/qc/front/return/get_by_supplier", "/wms/qc/front/return/free_data"], "ignoreList": []}, {"page": "return/return-down", "enable": false, "apiList": ["/qms/pda/return_to_vendor/get_down_task", "/qms/pda/return_to_vendor_down/scan_box", "/qms/pda/return_to_vendor_down/scan_location", "/wms/qc/front/sub_warehouse_user/get_bind_sub_warehouse", "/qms/pda/return_to_vendor/get_own_down_task", "/qms/pda/return_to_vendor_down/get_wait_off_shelf_handover_boxes"], "ignoreList": []}, {"page": "return/return-receive", "enable": false, "apiList": ["/qms/pda/receive_handover_box", "/wms/qc/front/sub_warehouse_user/get_bind_sub_warehouse"], "ignoreList": []}, {"page": "return/return-store", "enable": false, "apiList": ["/wms/qc/front/return/storage/box/scan", "/wms/qc/front/return/storage/temp_location_code/scan"], "ignoreList": []}, {"page": "return/return-up", "enable": false, "apiList": ["/qms/pda/return_to_vendor_upper/scan_box", "/qms/pda/return_to_vendor_upper/scan_location"], "ignoreList": []}, {"page": "defective-scrap/discard-shelves", "enable": false, "apiList": ["/wms/qc/front/replenish_shelves/query_purchase_scrap_task_info", "/wms/qc/front/replenish_shelves/scan_purchase_scrap_pick_container", "/wms/qc/front/replenish_shelves/scan_purchase_scrap_location", "/wms/qc/front/replenish_shelves/close_purchase_scrap_pick_container", "/wms/qc/front/replenish_shelves/scan_purchase_scrap_barcode", "/wms/qc/front/replenish_shelves/purchase_scrap_short_pick", "/wms/qc/front/replenish_shelves/submit_purchase_scrap_goods_count", "/wms/qc/front/replenish_shelves/scrap_query_record_detail"], "ignoreList": []}, {"page": "defective-scrap/scrap-packing", "enable": false, "apiList": ["/wms/front/defection_box/goods_scan_box", "/wms/front/defection_box/goods_close_box", "/wms/qc/scrap/scrap_packing_scan_handover_box", "/wms/qc/scrap/scrap_packing_scan_bar_code", "/wms/qc/scrap/scrap_packing_empty_handover_box"], "ignoreList": []}, {"page": "transit-operating-warehouse/defective-put-shelves", "enable": false, "apiList": ["/owis/pda/return_upper/scan_box", "/owis/pda/return_upper/scan_package_express", "/owis/pda/return_upper/scan_location", "/owis/pda/return_upper/query_task_info"], "ignoreList": []}, {"page": "transit-operating-warehouse/refund-pick", "enable": false, "apiList": ["/owis/pda/return/query_has_receive_task", "/owis/pda/return/receive_task", "/owis/pda/return/scan_pick_box", "/owis/pda/return/scan_location_code", "/owis/pda/return/scan_package_or_express_code", "/owis/pda/return/short_pick", "/owis/pda/return/close_box", "/owis/pda/return/pick_task_detail"], "ignoreList": []}, {"page": "transit-operating-warehouse/refund-warehouse", "enable": false, "apiList": ["/owis/pda/return_storage/exist", "/owis/pda/return_storage/scan_box_code", "/owis/pda/return_storage/scan_express_code", "/owis/pda/return_storage/finish", "/owis/pda/return_storage/scan_detail"], "ignoreList": []}]}