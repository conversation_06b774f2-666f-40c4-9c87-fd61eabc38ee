{"enable": true, "pages": [{"page": "query/container-query", "enable": false, "apiList": ["wms/front/inventory/container/query"], "ignoreList": []}, {"page": "inbound-manage/binning-by-goods", "enable": false, "apiList": ["/wms/front/abroad/goods_scan_box", "/wms/front/abroad/goods_scan_goods", "/wms/front/abroad/goods_close_box", "/wms/front/abroad/goods_delete_box"], "ignoreList": []}, {"page": "inbound-manage/binning-by-package", "enable": false, "apiList": ["/wms/front/abroad/package_scan_box", "/wms/front/abroad/package_scan_package", "/wms/front/abroad/package_close_box", "/wms/front/abroad/package_delete_box", "/wms/front/abroad/query_shift_data"], "ignoreList": []}, {"page": "inbound-manage/blackcode-by-package", "enable": false, "apiList": ["/wms/front/reback_packing/black_container", "/wms/front/reback_check/close_container", "/wms/front/reback_black_container/modify", "/wms/front/reback_check/scan_material_bar_code"], "ignoreList": []}, {"page": "inbound-manage/change-part-box-manage", "enable": false, "apiList": ["/wms/front/exchange_container/scan_big_container", "/wms/front/exchange_container/scan_parts_container", "/wms/front/exchange_container/scan_goods_bar_code"], "ignoreList": []}, {"page": "inbound-manage/change-whole-box-manage", "enable": false, "apiList": ["/wms/front/exchange_container/scan_full_container", "/wms/front/exchange_container/scan_big_container"], "ignoreList": []}, {"page": "inbound-manage/exchange-container", "enable": false, "apiList": ["/wms/front/reback/scan_container_before_change", "/wms/front/reback_integration/scan_barcode", "/wms/front/reback_integration/scan_container_after_change"], "ignoreList": []}, {"page": "inbound-manage/gather-task", "enable": true, "apiList": ["/wms/front/gather_task/pda/query_goods", "/wms/front/gather_task/pda/lack_goods", "/wms/front/gather_task/pda/submit_task", "/wms/front/gather_task/pda/scan_location"], "ignoreList": []}, {"page": "inbound-manage/location-abnormal-handle", "enable": false, "apiList": ["/wms/front/pda/location_exception/query_by_user", "/wms/front/pda/location_exception/query_by_user_and_location", "/wms/front/pda/location_exception/scan_location", "/wms/front/pda/location_exception/query_handling_task_by_user", "/wms/front/pda/location_exception/finish_handling", "/wms/front/exception/edit_exception_task"], "ignoreList": []}, {"page": "inbound-manage/location-info-manage", "enable": false, "apiList": ["/wmd/front/goods_location/query_enable_extend", "/wms/front/pda/goods_location/query", "/wms/front/pda/goods_location/maintain/query", "/wms/front/pda/goods_location/maintain/modify", "/wms/front/pda/goods_location/gather/query", "/wms/front/pda/goods_location/gather/confirm", "/wms/front/pda/goods_location/gather/lack", "/wms/front/gather_task/pda/query_goods", "/was/out/check_url_permission"], "ignoreList": []}, {"page": "sample-manage/distribution", "enable": false, "apiList": ["/wms/front/pda/sample_dress/query_not_match", "/wms/front/pda/sample_dress/match"], "ignoreList": []}, {"page": "sample-manage/entry", "enable": false, "apiList": ["/wms/front/pda/sample_dress/add", "/wms/front/pda/sample_dress/quality_receive/scan_location"], "ignoreList": []}, {"page": "sample-manage/handover", "enable": false, "apiList": ["/wms/front/pda/sample_dress/upper/handover"], "ignoreList": []}, {"page": "sample-manage/putaway", "enable": false, "apiList": ["/wms/front/pda/sample_dress/upper/scan_barcode", "/wms/front/pda/sample_dress/upper/scan_location", "/wms/front/pda/sample_dress/upper/in_storage"], "ignoreList": []}, {"page": "sample-manage/qc-receive", "enable": false, "apiList": ["/wms/front/pda/sample_dress/quality_receive/scan_goods_print", "/wms/front/pda/sample_dress/quality_receive/submit"], "ignoreList": []}, {"page": "sample-manage/sample-reception", "enable": false, "apiList": ["/wms/front/pda/sample_dress/query_borrow", "/wms/front/pda/sample_dress/borrow"], "ignoreList": []}, {"page": "sample-manage/sample-return", "enable": false, "apiList": ["/wms/front/pda/sample_dress/query_return", "/wms/front/pda/sample_dress/return"], "ignoreList": []}, {"page": "take-account", "enable": false, "apiList": ["/wws/front/check_inventory/scan_goods_location", "/wws/front/check_inventory/scan_goods_bar_code", "/wws/front/check_inventory/valid_goods_location", "/wws/front/check_inventory/check_confirm", "/wmd/front/config/query", "/wws/front/check_inventory/two_location", "/wmd/front/goods/get_goods_imgs_by_skc", "/wws/front/check_inventory/go_back", "/wws/front/check_inventory/scan_pick_container"], "ignoreList": []}, {"page": "back-warehouse/binning", "enable": false, "apiList": ["/wms/front/receive_code/query_warehouse", "/wws/front/pda/scan_return_warehouse_encase_container", "/wws/front/pda/scan_return_warehouse_encase_sku", "/wws/front/pda/close_return_warehouse_encase_container", "/wms/front/pda/submit_return_warehouse_encase", "/wws/front/pda/get_return_warehouse_shift_order_code", "/wws/front/pda/get_subwarehouse"], "ignoreList": []}, {"page": "back-warehouse/new-binning", "enable": false, "apiList": ["/wws/front/pda/get_subwarehouse", "/wws/front/pda/get_return_warehouse_shift_order_code", "/wws/front/pda/scan_return_warehouse_work_location", "/wws/front/pda/get_offline_work_location", "/wws/front/pda/scan_return_warehouse_encase_container_new", "/wws/front/pda/scan_return_warehouse_encase_sku_new", "/wws/front/pda/close_return_warehouse_encase_container_new", "/wws/front/pda/get_return_warehouse_encase_goods_detail"], "ignoreList": []}, {"page": "put-shelves/goods-info-collection", "enable": false, "apiList": ["/wms/front/goods_collect/scan_bar_code", "/wms/front/goods_collect/confirm"], "ignoreList": []}, {"page": "common-function", "enable": false, "apiList": ["/wms/internal/front/piece_work/scan"], "ignoreList": []}, {"page": "container-manage/empty-frame-loading", "enable": false, "apiList": ["/wws/front/pda_empty_box_handover_list/get_total_pallet_and_container_num", "/wws/front/pda_empty_box_handover_list/check_pallet_code", "/wws/front/pda_empty_box_handover_list/deliver", "/wws/front/pda_empty_box_handover_list/loading"], "ignoreList": []}, {"page": "container-manage/empty-frame-unloading", "enable": false, "apiList": ["/wws/front/pda_empty_box_handover_list/get_handover_details", "/wws/front/pda_empty_box_handover_list/receiving"], "ignoreList": []}]}