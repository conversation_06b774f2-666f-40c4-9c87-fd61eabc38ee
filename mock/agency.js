const path = require('path');
const {
  SYSTEM,
  PROXY_ENV, hostConfigObj, sysMockNumObj,
  MOCK_HOST, OPEN_LOCAL_AGENT, LOCAL_AGENT_URL,
  REPLACE_RES_CODE,
} = require('./config/config');
const loadDir = require('./load-config-jsons'); // 动态引入config下的json文件

// 各模块代理接口json
const jsonList = Object.values(loadDir('config'));

/**
 * 将json文件配置处理成需代理的接口数组
 * @returns {*[]}
 */
const getMockApiList = () => {
  let mockApiList = [];
  jsonList.forEach((module) => {
    if (module.enable) {
      (module.pages || []).forEach((page) => {
        const { enable, apiList, ignoreList } = page;
        if (enable) {
          // 去掉忽略代理的接口
          const lastApiList = apiList.filter((v) => !(ignoreList || []).includes(v));
          mockApiList = [...mockApiList, ...lastApiList];
        }
      });
    }
  });
  return mockApiList;
};

/**
 * 根据接口前缀加上对应的mock数值
 * @param apiVal
 * @returns {string}
 */
const handleMockNum = (apiVal) => {
  let num = '';
  Object.entries(sysMockNumObj).forEach(([sysPrefix, mockNum]) => {
    if (apiVal.startsWith(sysPrefix)) {
      num = mockNum;
    }
  });
  if (!num) {
    // eslint-disable-next-line @shein-bbl/bbl/translate-i18n-byT
    console.error('获取不到mock地址数值, 需要配置config.js里边的sysMockNumObj');
  }
  return num;
};

/**
 * 处理成w2支持代理的字符串格式
 * @returns {*}
 */
const handleRulesStr = () => {
  const hostConfigVal = hostConfigObj[PROXY_ENV];
  if (!hostConfigVal) {
    // eslint-disable-next-line @shein-bbl/bbl/translate-i18n-byT
    console.error('环境变量出错, 参考config.js里边的hostConfigObj');
  }
  // 是否开启本地代理： mot-eu-test01.dotfashion.cn/ http://127.0.0.1:8088/
  let localAgentStr = '';
  if (OPEN_LOCAL_AGENT) {
    localAgentStr = `\r${hostConfigVal} ${LOCAL_AGENT_URL}`;
  }
  const apiArr = getMockApiList();
  const rulesStr = apiArr.map((v) => {
    // 若接口地址没加/, 则兼容处理
    const apiVal = v.startsWith('/') ? v : `/${v}`;
    // mock地址数值
    const sysMockNum = handleMockNum(apiVal);
    // 最终值例子: 前边是实际接口地址, 后边是mock接口地址, 中间以空格分隔
    //eu-test01.dotfashion.cn/wms/qc/front/return/scan_qr_code https://soapi-sdk-web01.dotfashion.cn/mock/625/wms/qc/front/return/scan_qr_code
    let mockRule = `${hostConfigVal}${apiVal} ${MOCK_HOST}${sysMockNum}${apiVal}`;
    // 若开启则将将soapi接口info.code值统一替换为0
    if (REPLACE_RES_CODE) {
      const resReplaceFilePath = path.join(__dirname, './config/res-replace.json');
      mockRule += ` resReplace://${resReplaceFilePath}`;
    }
    return mockRule;
  }).join('\r'); // 换行实现同时代理多个接口
  return `${rulesStr}${localAgentStr}`;
};

// 抛出w2代理匹配规则
module.exports = (cb) => {
  cb({
    name: `${SYSTEM}_mock`,
    rules: handleRulesStr(),
  });
};
