const fs = require('fs');
const path = require('path');

// eslint-disable-next-line global-require,import/no-dynamic-require
const load = (pathStr) => require(pathStr);

module.exports = function (dir) {
  const jsonObj = {};
  fs.readdirSync(`${__dirname}/${dir}`).forEach((filename) => {
    if (!/\.json$/.test(filename)) {
      return;
    }
    const name = path.basename(filename, '.json');
    const loadRequire = load.bind(null, `./${dir}/${name}`);
    // eslint-disable-next-line no-restricted-properties,no-underscore-dangle
    jsonObj.__defineGetter__(name, loadRequire);
  });
  return jsonObj;
};
