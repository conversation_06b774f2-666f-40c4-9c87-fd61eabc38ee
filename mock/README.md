# w2 + sopai实现mock数据
> MOT系统

## 环境依赖
- 要求安装并配置好whistle代理
- whistle版本: 2.9.28
- 参考链接: https://wiki.dotfashion.cn/pages/viewpage.action?pageId=875510449
- github地址：https://github.com/avwo/whistle/blob/master/CHANGELOG.md
___

## mock结构解析
ps：.md实现换行--两个空格  
├── config
│   ├── config.js // 配置文件
│   ├── res-replace.json // 用于统一体检soapi接口info.code值非0问题
│   ├── common.json // 公共接口配置
│   ├── inbound.json // 入库接口配置
│   ├── outbound.json // 出库接口配置
│   └── warehouse-inside.json // 库内接口配置
├── agency.js // 抛出给w2 use的代理规则
├── index.js // 运行shell命令
├── nodemon.json // nodemon配置文件
└── README.md // mock代理介绍

## config.js配置文件解析


### 维护或修改mock代理接口
- 若有新增或遗漏的后端子系统, 则在util.js的sysMockNumObj配置系统接口前缀和mock数值
- 若要修改具体代理接口, 则修改对应的json文件: 包括增删改查、开启与关闭
___

### 安装nodemon, 用于热更新【版本2.0.19】
- 命令: npm i -g nodemon
___

### 启动w2+mock代理
- 命令: npm run mock
___

### 查看当前代理的接口等
- 网址: http://127.0.0.1:6688/
- 面板: rules中的'系统名_mock'面板
___

### 关闭
- 快捷键: ctrl+c 【关闭】
- 命令: w2 stop
___

### 待优化
1, 热更新: nodemon
2, 根据server.js动态生成json内容: 可实现
3, info.code值, code值统一替换为0: resReplace
4, rules规则不能超过16k: The rules cannot be empty and the size cannot exceed 16k: whistle  2.9.7版本及以上, 支持256k
5, 只想代理页面的部分接口: ignoreList
6, mot旧代理写法(MOCK_SWITCH)代码移除: 待做
7, 冲突: 当前的先配好; 各页面enable默认都是false, 关闭时默认改成false, ignoreList重置为空[自动+手动命令]【非新增不提交】: 待做
8, 封装成npm插件包并在其它系统引入: 待做
