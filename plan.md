# API字段类型迁移执行计划 (Long → String)

## 项目概述
- **目标**: 评估后端接口字段从 long 类型变更为 string 类型对前端代码的影响
- **数据规模**: 147条原始数据（todo.md中共43行有效数据）
- **执行方式**: 分批次、分步骤执行，确保检查质量和可追踪性

## 执行策略
将43条数据分为6个批次，每批次6-8条数据，便于逐步检查和验证。

---

## 阶段一：环境准备和基础验证 (预计1小时)

### 任务1.1: 环境配置分析
**执行标准**:
- [ ] 分析 `config/.legorc.ts` 中的环境变量配置
- [ ] 确认各个后端服务的基础URL映射关系
- [ ] 记录关键环境变量：BASE_URI_WMD, WIS_FRONT, BASE_URI 等

**验收条件**:
- 完成环境变量映射表
- 确认接口前缀与实际代码中的匹配关系

### 任务1.2: 项目结构梳理
**执行标准**:
- [ ] 分析主要目录结构和文件组织方式
- [ ] 确认接口定义文件的位置模式（server.js, reducers.js等）
- [ ] 建立文件路径与功能模块的对应关系

**验收条件**:
- 完成项目结构文档
- 明确接口文件的查找规律

---

## 阶段二：批次化接口验证 (预计6小时)

### 批次1: RFID管理模块 (第1-7行)
**数据范围**: todo.md 第5-11行
**涉及字段**: id, extendId
**主要接口**:
- `/wmd/front/rf_container/get_by_tid`
- `/wmd/front/goods_location/query`
- `/wmd/front/goods_location/query_enable_extend`

**执行步骤**:
1. **接口匹配验证**
   - [ ] 验证接口URL与代码中的实际路径匹配
   - [ ] 确认环境变量替换后的完整接口地址
   - [ ] 检查接口在代码中的定义位置

2. **使用情况分析**
   - [ ] 搜索每个接口在整个代码库中的调用情况
   - [ ] 识别调用这些接口的组件和模块
   - [ ] 分析数据流向和处理逻辑

3. **字段风险评估**
   - [ ] 确认受影响字段（id, extendId）的使用方式
   - [ ] 检查是否存在数值计算、比较操作
   - [ ] 评估类型变更的潜在风险点

**验收条件**:
- 完成批次1的详细分析报告
- 每个接口都有明确的风险评估结论
- 记录所有需要关注的代码位置

### 批次2: 库存容器查询模块 (第12-18行)
**数据范围**: todo.md 第12-18行
**涉及字段**: id
**主要接口**: `/wmd/front/inventory/container/query`

**执行步骤**: [同批次1的执行步骤]

### 批次3: 库存容器查询模块扩展 (第19-25行)
**数据范围**: todo.md 第19-25行
**涉及字段**: id
**主要接口**: `/wmd/front/inventory/container/query` (续)

**执行步骤**: [同批次1的执行步骤]

### 批次4: 容器查询新版本模块 (第26-32行)
**数据范围**: todo.md 第26-32行
**涉及字段**: id
**主要接口**: `/wmd/front/inventory/container/query` (新版本相关)

**执行步骤**: [同批次1的执行步骤]

### 批次5: 查询服务模块 (第33-39行)
**数据范围**: todo.md 第33-39行
**涉及字段**: id
**主要接口**: `/wmd/front/inventory/container/query` (查询服务)

**执行步骤**: [同批次1的执行步骤]

### 批次6: 复合包装和位置管理 (第40-43行)
**数据范围**: todo.md 第40-43行
**涉及字段**: id, extendId
**主要接口**:
- `/wmd/front/rf_container/get_by_tid`
- `/wmd/front/goods_location/query`
- `/wmd/front/goods_location/query_enable_extend`

**执行步骤**: [同批次1的执行步骤]

---

## 阶段三：高风险点修复 (预计4小时)

### 任务3.1: 数值运算操作修复 (P0 - 立即处理)
**基于发现**: 所有批次都存在数值递增、累加、计算操作
**执行标准**:
- [ ] 修复数值递增操作：`result.current.reviewNum = (result.current.reviewNum || 0) + 1`
- [ ] 修复累加计算：`const nextIndex = startIndex + sliceList.length`
- [ ] 确保运算逻辑在字符串类型下正常工作
- [ ] 添加类型转换：使用 `Number()` 或 `parseInt()` 进行安全转换

**验收条件**:
- 所有数值运算都添加了字符串兼容性处理
- 运算结果类型保持一致性

### 任务3.2: 数值比较操作修复 (P0 - 立即处理)
**基于发现**: 存在大量数值比较、边界判断操作
**执行标准**:
- [ ] 推广兼容性比较模式：`if (res.info.maxItemNum === 0 || res.info.maxItemNum === '0')`
- [ ] 修复数值比较：`Number(v.reviewNum) < Number(v.num)`
- [ ] 统一边界判断逻辑
- [ ] 确保比较操作的类型安全

**验收条件**:
- 所有数值比较都支持字符串和数值双重判断
- 边界条件处理正确

### 任务3.3: 默认值处理修复 (P1 - 重点关注)
**基于发现**: 数值字段的默认值设置需要调整
**执行标准**:
- [ ] 调整数值默认值为字符串：`const { containerNum = '0', goodsNum = '0' } = res.info || {}`
- [ ] 推广字符串默认值模式：`totalNum: res.info.totalNum || ''`
- [ ] 确保默认值类型一致性
- [ ] 检查所有解构赋值中的默认值

**验收条件**:
- 所有相关字段的默认值都调整为字符串类型
- 默认值设置保持业务逻辑正确性

### 任务3.4: TypeScript类型定义更新
**执行标准**:
- [ ] 更新接口定义中的字段类型：long → string
- [ ] 检查类型声明文件中的字段类型
- [ ] 评估类型变更对编译的影响
- [ ] 确保类型定义与实际使用一致

**验收条件**:
- 完成TypeScript类型影响评估
- 明确需要修改的类型定义文件

---

## 阶段四：系统性问题修复 (预计3小时)

### 任务4.1: 接口URL记录更正 (P1 - 重点关注)
**基于发现**: todo.md中记录的接口URL与实际代码严重不符
**执行标准**:
- [ ] 系统性更正todo.md中的接口记录
- [ ] 验证实际接口路径：`/inventory/container/query_container_detail` vs 记录的 `/wmd/front/inventory/container/query`
- [ ] 更新所有不匹配的接口URL记录
- [ ] 建立接口URL验证机制

**验收条件**:
- todo.md中的接口URL与实际代码完全匹配
- 建立接口URL维护规范

### 任务4.2: 多前缀系统兼容性检查 (P2 - 持续监控)
**基于发现**: WIS、WMD、WWS、WOS等多套前缀系统增加复杂性
**执行标准**:
- [ ] 验证前缀切换逻辑：`process.env.WIS_FRONT`, `process.env.BASE_URI_WMD`
- [ ] 确保动态接口选择正常工作
- [ ] 检查环境变量配置的完整性
- [ ] 测试不同环境下的前缀系统

**验收条件**:
- 所有前缀系统在字段类型变更后正常工作
- 动态接口选择逻辑保持稳定

### 任务4.3: 累加函数专项检查 (P1 - 重点关注)
**基于发现**: getGoodsSnNum等累加函数需要特别关注
**执行标准**:
- [ ] 检查getGoodsSnNum函数的实现逻辑
- [ ] 验证累加计算在字符串类型下的正确性
- [ ] 确保累加结果的类型一致性
- [ ] 测试累加函数的边界情况

**验收条件**:
- 累加函数在字符串类型下正常工作
- 累加结果保持业务逻辑正确性

### 任务4.4: 测试场景设计
**基于风险分析结果**:
**执行标准**:
- [ ] 数值运算测试：验证所有递增、累加、计算逻辑
- [ ] 数值比较测试：验证所有比较、边界判断逻辑
- [ ] 接口调用测试：验证所有前缀系统的接口调用
- [ ] 业务流程测试：验证端到端业务流程
- [ ] 边界值测试：0 vs '0', null vs '' vs undefined

**验收条件**:
- 完成核心测试场景覆盖
- 每个修改都有对应的测试用例

---

## 阶段五：修复验证和质量保证 (预计2小时)

### 任务5.1: 修复效果验证
**执行标准**:
- [ ] 验证所有P0级别修复的正确性
- [ ] 确认数值运算和比较操作的兼容性
- [ ] 测试修复后的业务流程完整性
- [ ] 检查修复是否引入新的问题

**验收条件**:
- 所有高风险点修复验证通过
- 业务功能保持正常运行

### 任务5.2: 回归测试执行
**基于风险分析的测试重点**:
- [ ] 执行核心测试场景：数值运算、比较、接口调用
- [ ] 验证边界值处理：0 vs '0', 空值处理
- [ ] 测试多前缀系统的稳定性
- [ ] 确认累加函数的正确性

**验收条件**:
- 回归测试全部通过
- 性能指标无明显下降

### 任务5.3: 最终报告更新
**执行标准**:
- [ ] 更新summary-report.md，记录修复结果
- [ ] 生成修复前后的对比分析
- [ ] 提供后续监控建议
- [ ] 输出最终的风险评估

**验收条件**:
- 完成修复验证报告
- 提供持续监控指南

---

## 输出文档结构

### 1. 批次执行报告 (batch-{n}-report.md) ✅ 已完成
每个批次完成后生成，包含：
- 接口验证结果
- 使用情况分析
- 风险点识别
- 初步建议

### 2. 总结报告 (summary-report.md) ✅ 已完成
包含：
- 风险等级分布和主要风险模式
- 系统性问题分析（接口URL不匹配、数值运算等）
- 良好实践发现和修改建议优先级
- 测试建议和风险缓解措施

### 3. 修复执行报告 (fix-execution-report.md) 🔄 待生成
包含：
- P0级别修复的详细记录
- 修复前后的代码对比
- 修复效果验证结果
- 遗留问题和后续建议

### 4. 最终验证报告 (final-validation-report.md) 🔄 待生成
包含：
- 回归测试结果
- 性能影响评估
- 风险缓解效果评估
- 持续监控建议

---

## 质量保证措施

1. **双重验证**: 每个接口都要进行代码搜索和实际文件确认
2. **交叉检查**: 相同接口在不同文件中的使用要进行对比分析
3. **风险分级**: 按照影响程度对所有发现进行分级处理
4. **可追踪性**: 每个结论都要有明确的证据和推理过程

---

## 预期时间安排

- **阶段一**: 1小时 (环境准备) ✅ 已完成
- **阶段二**: 6小时 (分批验证，每批次1小时) ✅ 已完成
- **阶段三**: 4小时 (高风险点修复) 🔄 当前阶段
- **阶段四**: 3小时 (系统性问题修复)
- **阶段五**: 2小时 (修复验证和质量保证)

**总计**: 16小时，建议分4-5个工作日完成

## 当前状态和下一步行动

### 已完成 ✅
- 环境准备和基础验证
- 6个批次的详细分析（batch-1-report.md 到 batch-6-report.md）
- 风险点识别和总结报告（summary-report.md）

### 当前重点 🔄
基于summary-report.md中识别的高风险点，立即开始：

**P0级别（立即处理）**:
1. 数值运算操作修复
2. 数值比较操作修复

**P1级别（重点关注）**:
1. 默认值处理修复
2. 接口URL记录更正
3. 累加函数专项检查

---

## 执行注意事项

### 基于风险分析的关键注意点
1. **数值运算优先级最高**: 所有涉及数值递增、累加的代码必须立即修复
2. **兼容性比较模式**: 推广 `if (value === 0 || value === '0')` 的双重判断模式
3. **字符串默认值**: 统一使用字符串类型的默认值，如 `''` 而不是 `0`
4. **接口URL验证**: 修复前必须验证实际接口路径与记录的一致性
5. **累加函数重点关注**: getGoodsSnNum等累加函数需要特别测试

### 质量保证要求
1. 每个P0级别修复都要有对应的测试用例
2. 修复后立即进行回归测试验证
3. 保持与后端团队的沟通，确认字段变更的具体影响
4. 所有修改都要经过代码review确认
5. 建立修复效果的持续监控机制
