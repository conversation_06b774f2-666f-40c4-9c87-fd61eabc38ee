# API字段类型迁移执行计划 (Long → String)

## 项目概述
- **目标**: 评估后端接口字段从 long 类型变更为 string 类型对前端代码的影响
- **数据规模**: 147条原始数据（todo.md中共43行有效数据）
- **执行方式**: 分批次、分步骤执行，确保检查质量和可追踪性

## 执行策略
将43条数据分为6个批次，每批次6-8条数据，便于逐步检查和验证。

---

## 阶段一：环境准备和基础验证 (预计1小时)

### 任务1.1: 环境配置分析
**执行标准**:
- [ ] 分析 `config/.legorc.ts` 中的环境变量配置
- [ ] 确认各个后端服务的基础URL映射关系
- [ ] 记录关键环境变量：BASE_URI_WMD, WIS_FRONT, BASE_URI 等

**验收条件**:
- 完成环境变量映射表
- 确认接口前缀与实际代码中的匹配关系

### 任务1.2: 项目结构梳理
**执行标准**:
- [ ] 分析主要目录结构和文件组织方式
- [ ] 确认接口定义文件的位置模式（server.js, reducers.js等）
- [ ] 建立文件路径与功能模块的对应关系

**验收条件**:
- 完成项目结构文档
- 明确接口文件的查找规律

---

## 阶段二：批次化接口验证 (预计6小时)

### 批次1: RFID管理模块 (第1-7行)
**数据范围**: todo.md 第5-11行
**涉及字段**: id, extendId
**主要接口**:
- `/wmd/front/rf_container/get_by_tid`
- `/wmd/front/goods_location/query`
- `/wmd/front/goods_location/query_enable_extend`

**执行步骤**:
1. **接口匹配验证**
   - [ ] 验证接口URL与代码中的实际路径匹配
   - [ ] 确认环境变量替换后的完整接口地址
   - [ ] 检查接口在代码中的定义位置

2. **使用情况分析**
   - [ ] 搜索每个接口在整个代码库中的调用情况
   - [ ] 识别调用这些接口的组件和模块
   - [ ] 分析数据流向和处理逻辑

3. **字段风险评估**
   - [ ] 确认受影响字段（id, extendId）的使用方式
   - [ ] 检查是否存在数值计算、比较操作
   - [ ] 评估类型变更的潜在风险点

**验收条件**:
- 完成批次1的详细分析报告
- 每个接口都有明确的风险评估结论
- 记录所有需要关注的代码位置

### 批次2: 库存容器查询模块 (第12-18行)
**数据范围**: todo.md 第12-18行
**涉及字段**: id
**主要接口**: `/wmd/front/inventory/container/query`

**执行步骤**: [同批次1的执行步骤]

### 批次3: 库存容器查询模块扩展 (第19-25行)
**数据范围**: todo.md 第19-25行
**涉及字段**: id
**主要接口**: `/wmd/front/inventory/container/query` (续)

**执行步骤**: [同批次1的执行步骤]

### 批次4: 容器查询新版本模块 (第26-32行)
**数据范围**: todo.md 第26-32行
**涉及字段**: id
**主要接口**: `/wmd/front/inventory/container/query` (新版本相关)

**执行步骤**: [同批次1的执行步骤]

### 批次5: 查询服务模块 (第33-39行)
**数据范围**: todo.md 第33-39行
**涉及字段**: id
**主要接口**: `/wmd/front/inventory/container/query` (查询服务)

**执行步骤**: [同批次1的执行步骤]

### 批次6: 复合包装和位置管理 (第40-43行)
**数据范围**: todo.md 第40-43行
**涉及字段**: id, extendId
**主要接口**:
- `/wmd/front/rf_container/get_by_tid`
- `/wmd/front/goods_location/query`
- `/wmd/front/goods_location/query_enable_extend`

**执行步骤**: [同批次1的执行步骤]

---

## 阶段三：风险点深度分析 (预计3小时)

### 任务3.1: 数值操作风险评估
**执行标准**:
- [ ] 搜索所有涉及id字段的数学运算
- [ ] 检查字段比较操作（>, <, ==, ===等）
- [ ] 分析排序和过滤逻辑中的字段使用

**验收条件**:
- 完成高风险代码点清单
- 每个风险点都有具体的代码位置和影响分析

### 任务3.2: TypeScript类型定义检查
**执行标准**:
- [ ] 查找相关的TypeScript接口定义
- [ ] 检查类型声明文件中的字段类型
- [ ] 评估类型变更对编译的影响

**验收条件**:
- 完成TypeScript类型影响评估
- 明确需要修改的类型定义文件

---

## 阶段四：修改策略制定 (预计2小时)

### 任务4.1: 修改分类
**执行标准**:
- [ ] 区分仅需TypeScript类型调整的情况
- [ ] 识别需要代码逻辑修改的情况
- [ ] 制定具体的修改策略

**验收条件**:
- 完成修改策略文档
- 每个修改点都有明确的处理方案

### 任务4.2: 测试场景设计
**执行标准**:
- [ ] 为每个修改点设计测试场景
- [ ] 确定回归测试的范围
- [ ] 制定验证标准

**验收条件**:
- 完成测试计划文档
- 每个修改都有对应的测试用例

---

## 阶段五：完整性检查和文档输出 (预计1小时)

### 任务5.1: 完整性验证
**执行标准**:
- [ ] 确认todo.md中的每一行都已检查
- [ ] 验证所有接口都有明确的结论
- [ ] 检查是否有遗漏的风险点

**验收条件**:
- 100%覆盖原始数据
- 每个数据项都有明确的状态标记

### 任务5.2: 最终报告生成
**执行标准**:
- [ ] 生成详细的影响分析报告
- [ ] 提供具体的修改建议
- [ ] 输出风险评估和缓解措施

**验收条件**:
- 完成最终评估报告
- 提供可执行的修改指南

---

## 输出文档结构

### 1. 批次执行报告 (batch-{n}-report.md)
每个批次完成后生成，包含：
- 接口验证结果
- 使用情况分析
- 风险点识别
- 初步建议

### 2. 风险评估汇总 (risk-assessment.md)
包含：
- 高风险点清单
- 中风险点清单
- 低风险点清单
- 缓解措施建议

### 3. 修改指南 (modification-guide.md)
包含：
- TypeScript类型调整清单
- 代码逻辑修改清单
- 测试场景描述
- 实施步骤

### 4. 完整性检查清单 (completeness-checklist.md)
包含：
- 原始数据检查状态
- 遗漏项目记录
- 质量保证措施

---

## 质量保证措施

1. **双重验证**: 每个接口都要进行代码搜索和实际文件确认
2. **交叉检查**: 相同接口在不同文件中的使用要进行对比分析
3. **风险分级**: 按照影响程度对所有发现进行分级处理
4. **可追踪性**: 每个结论都要有明确的证据和推理过程

---

## 预期时间安排

- **阶段一**: 1小时 (环境准备)
- **阶段二**: 6小时 (分批验证，每批次1小时)
- **阶段三**: 3小时 (深度分析)
- **阶段四**: 2小时 (策略制定)
- **阶段五**: 1小时 (完整性检查)

**总计**: 13小时，建议分3-4个工作日完成

---

## 执行注意事项

1. 每完成一个批次立即生成报告，避免信息丢失
2. 发现高风险点时立即记录并标记优先级
3. 保持与后端团队的沟通，确认字段变更的具体影响
4. 所有修改建议都要经过代码review确认
