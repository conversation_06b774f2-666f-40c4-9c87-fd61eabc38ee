# 批次5分析报告：查询服务模块

## 数据范围
- **todo.md行数**: 第33-39行
- **涉及字段**: id
- **主要接口**: `/wmd/front/inventory/container/query` (但实际代码中是不同的具体接口)

## 1. 接口匹配验证

### 1.1 接口URL不匹配问题分析
**重要发现**: 与前面批次类似，todo.md中记录的接口URL与实际代码内容存在不匹配

#### todo.md第33行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/query_container_task_detail'`
- **文件位置**: `src/component/query/server.js:49`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wos/front/inventory/container/query_container_task_detail`

#### todo.md第34行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/shift_inf_query'`
- **文件位置**: `src/component/query/server.js:55`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wws/front/inventory/container/shift_inf_query`

#### todo.md第35行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/upper_detail'`
- **文件位置**: `src/component/query/server.js:92`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wmd/front/inventory/container/upper_detail`

#### todo.md第36行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/upper_detail'`
- **文件位置**: `src/component/query/server.js:96`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wis/front/inventory/container/upper_detail`

#### todo.md第37行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/task_no_detail'`
- **文件位置**: `src/component/query/server.js:102`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wmd/front/inventory/container/task_no_detail`

#### todo.md第38行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/task_no_detail'`
- **文件位置**: `src/component/query/server.js:106`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wis/front/inventory/container/task_no_detail`

### 1.2 实际接口分析

#### 拣货任务查询接口 (WOS前缀)
```javascript
// 拣货周转箱，任务号查询接口
export const queryPickTaskDetailApi = (param) => sendPostRequest({
  url: '/inventory/container/query_container_task_detail',
  param,
}, process.env.WOS_URI);
// 完整URL: /wos/front/inventory/container/query_container_task_detail
```

#### 移位单信息查询接口 (WWS前缀)
```javascript
// 补货周转箱，当前移位单信息查询接口
export const shiftInfQueryApi = (param) => sendPostRequest({
  url: '/inventory/container/shift_inf_query',
  param,
}, process.env.WWS_URI);
// 完整URL: /wws/front/inventory/container/shift_inf_query
```

#### 周转箱明细查询接口 (WMD/WIS前缀)
```javascript
// 容器查询 - 周转箱明细查询（从容器出来的页面跳转）
export const upperDetailApi = (param) => sendPostRequest({
  url: '/inventory/container/upper_detail',
  param,
}, process.env.BASE_URI_WMD);  // /wmd/front

export const upperQCDetailApi = (param) => sendPostRequest({
  url: '/inventory/container/upper_detail',
  param,
}, process.env.WIS_FRONT);     // /wis/front
```

#### 任务明细查询接口 (WMD/WIS前缀)
```javascript
// 容器查询 - 周转箱明细查询（从周转箱明细查询出来的页面跳转）
export const taskNoDetailApi = (param) => sendPostRequest({
  url: '/inventory/container/task_no_detail',
  param,
}, process.env.BASE_URI_WMD);  // /wmd/front

export const taskNoQCDetailApi = (param) => sendPostRequest({
  url: '/inventory/container/task_no_detail',
  param,
}, process.env.WIS_FRONT);     // /wis/front
```

## 2. 实际接口使用情况分析

### 2.1 拣货任务查询 (queryPickTaskDetailApi)
**定义位置**: `src/component/query/server.js:48`
**调用位置**: `src/component/query/container-query/reducers.js:390`
**数据流分析**:
```javascript
const res = yield queryPickTaskDetailApi(action.param);
if (res.code === '0') {
  const { list } = res.info;
  const containerNum = list.length;              // 数组长度计算
  const goodsSnNum = getGoodsSnNum(list, 'goodsSnNum');  // 数值累加
  yield ctx.changeData({
    data: {
      list,
      containerNum,     // 容器数量 (可能受影响)
      goodsSnNum,       // 商品数量 (可能受影响)
      listHeaderKey,
    },
  });
}
```

### 2.2 移位单信息查询 (shiftInfQueryApi)
**定义位置**: `src/component/query/server.js:54`
**调用位置**: `src/component/query/container-query/reducers.js:416`
**数据流分析**:
```javascript
const res = yield shiftInfQueryApi(action.param);
if (res.code === '0') {
  const { list = [], containerNum = 0, goodsNum = 0 } = res.info || {};
  yield ctx.changeData({
    data: {
      list,
      containerNum,           // 直接赋值 (可能受影响)
      goodsSnNum: goodsNum,   // 直接赋值 (可能受影响)
      listHeaderKey,
    },
  });
}
```

### 2.3 周转箱明细查询 (upperDetailApi/upperQCDetailApi)
**定义位置**: `src/component/query/server.js:91-98`
**调用位置**: `src/component/query/container-query/reducers.js:547`
**数据流分析**:
```javascript
const res = [1, 2, 6].includes(upperType) ? 
  yield upperQCDetailApi(params) :    // 使用WIS前缀
  yield upperDetailApi(params);       // 使用WMD前缀

if (res.code === '0') {
  yield ctx.changeData({
    data: {
      jumpType,
      showUpperShelfPage: true,
      upperShelfPageData: assign({}, { upperType }, { ...res.info }),
    },
  });
}
```

### 2.4 任务明细查询 (taskNoDetailApi/taskNoQCDetailApi)
**定义位置**: `src/component/query/server.js:101-108`
**调用位置**: `src/component/query/container-query/reducers.js:585`
**数据流分析**:
```javascript
const res = [1, 2, 6].includes(upperType) ? 
  yield taskNoQCDetailApi(params) :   // 使用WIS前缀
  yield taskNoDetailApi(params);      // 使用WMD前缀

if (res.code === '0') {
  yield ctx.changeData({
    data: {
      showUpperListPage: true,
      list: res.info.goodsLists || [],
      upperListPageData: res.info,
      numStatus,
    },
  });
}
```

## 3. 字段使用方式分析

### 3.1 数值计算操作 (高风险)
**位置**: `src/component/query/container-query/reducers.js:393-394`
```javascript
const containerNum = list.length;                        // 数组长度计算
const goodsSnNum = getGoodsSnNum(list, 'goodsSnNum');   // 数值累加函数
```

**位置**: `src/component/query/container-query/reducers.js:679`
```javascript
const checkNum = ++action.checkNum;  // 数值递增
```

**风险分析**: 
- getGoodsSnNum函数可能涉及数值累加操作
- checkNum的递增操作需要确保数值类型正确

### 3.2 数值字段直接赋值 (高风险)
**位置**: `src/component/query/container-query/reducers.js:418`
```javascript
const { list = [], containerNum = 0, goodsNum = 0 } = res.info || {};
yield ctx.changeData({
  data: {
    containerNum,           // 直接赋值数值
    goodsSnNum: goodsNum,   // 直接赋值数值
  },
});
```

**位置**: `src/component/query/container-query/reducers.js:771-772`
```javascript
totalNum: res.info.totalNum || '',   // 字符串默认值
underNum: res.info.underNum || '',   // 字符串默认值
```

**风险分析**: 
- 如果containerNum、goodsNum字段从long变为string，默认值需要调整
- totalNum、underNum使用字符串默认值，相对安全

### 3.3 数值比较和枚举判断 (中风险)
**位置**: `src/component/query/container-query/reducers.js:547`
```javascript
const res = [1, 2, 6].includes(upperType) ? 
  yield upperQCDetailApi(params) : 
  yield upperDetailApi(params);
```

**位置**: `src/component/query/container-query/reducers.js:585`
```javascript
const res = [1, 2, 6].includes(upperType) ? 
  yield taskNoQCDetailApi(params) : 
  yield taskNoDetailApi(params);
```

**风险分析**: 
- upperType用于数值比较，但这是枚举值，通常不会受影响
- 中等风险

### 3.4 字符串操作 (低风险)
**位置**: 多个文件中的模式
```javascript
list: res.info.goodsLists || [],
containerCode: res.info?.containerCode || '',
```

**风险分析**:
- 这是安全的字符串操作，类型变更不影响功能
- 无需修改

## 4. 数据结构分析

### 4.1 拣货任务查询返回数据
```javascript
{
  code: '0',
  info: {
    list: [
      {
        goodsSnNum: number,    // 可能受影响
        // ... 其他字段
      }
    ]
  }
}
```

### 4.2 移位单信息查询返回数据
```javascript
{
  code: '0',
  info: {
    list: [...],
    containerNum: number,    // 可能受影响
    goodsNum: number,        // 可能受影响
  }
}
```

### 4.3 周转箱明细查询返回数据
```javascript
{
  code: '0',
  info: {
    // 具体结构取决于upperType
    goodsLists: [...],
    // ... 其他字段
  }
}
```

### 4.4 退供任务查询返回数据
```javascript
{
  code: '0',
  info: {
    totalNum: string,        // 已使用字符串类型
    underNum: string,        // 已使用字符串类型
    list: [
      {
        number: number,      // 可能受影响
        // ... 其他字段
      }
    ]
  }
}
```

## 5. 风险点评估

### 5.1 高风险点
1. **数值计算操作**:
   ```javascript
   const containerNum = list.length;
   const goodsSnNum = getGoodsSnNum(list, 'goodsSnNum');
   ```
   - getGoodsSnNum函数可能涉及数值累加，需要确认其实现

2. **数值字段直接赋值**:
   ```javascript
   const { containerNum = 0, goodsNum = 0 } = res.info || {};
   ```
   - 如果这些字段从long变为string，默认值需要调整

3. **数值递增操作**:
   ```javascript
   const checkNum = ++action.checkNum;
   ```
   - 需要确保checkNum字段的类型一致性

### 5.2 中风险点
1. **接口URL不匹配**: todo.md中的记录与实际代码完全不符
2. **多前缀系统**: WOS、WWS、WMD、WIS四套前缀系统，增加了复杂性
3. **条件接口选择**: 根据upperType动态选择WIS或WMD前缀接口

### 5.3 低风险点
1. **字符串操作**: 数组赋值、字符串默认值等操作
2. **枚举比较**: upperType的数值比较（枚举值）
3. **已适配字段**: totalNum、underNum已使用字符串类型

## 6. 修改建议

### 6.1 需要验证的点
1. **确认受影响字段**: 明确哪些字段从long变为string
2. **getGoodsSnNum函数**: 检查该函数的具体实现和数值处理逻辑
3. **数值操作兼容性**: 确保计算和赋值在字符串类型下正常工作
4. **接口URL映射**: 更正todo.md中的接口记录

### 6.2 可能需要修改的代码
如果containerNum、goodsNum、goodsSnNum等字段受影响：

```javascript
// 当前代码
const { containerNum = 0, goodsNum = 0 } = res.info || {};

// 可能需要的修改
const { containerNum = '0', goodsNum = '0' } = res.info || {};

// 当前代码
const checkNum = ++action.checkNum;

// 可能需要的修改
const checkNum = parseInt(action.checkNum || '0', 10) + 1;
```

### 6.3 需要检查的函数
```javascript
// 需要检查getGoodsSnNum函数的实现
const goodsSnNum = getGoodsSnNum(list, 'goodsSnNum');
```

## 7. 测试场景建议

### 7.1 数值计算测试
- **场景1**: 验证getGoodsSnNum函数的累加逻辑
- **场景2**: 测试containerNum、goodsNum的赋值和显示
- **场景3**: 验证checkNum的递增功能

### 7.2 接口调用测试
- **场景1**: 验证WOS前缀拣货任务接口的正常调用
- **场景2**: 验证WWS前缀移位单接口的正常调用
- **场景3**: 验证WMD/WIS前缀周转箱接口的条件选择逻辑

### 7.3 业务流程测试
- **场景1**: 拣货任务查询完整流程
- **场景2**: 移位单信息查询完整流程
- **场景3**: 周转箱明细查询完整流程
- **场景4**: 任务明细查询完整流程

### 7.4 数据一致性测试
- **场景1**: 验证不同前缀接口返回数据的一致性
- **场景2**: 测试数值字段在不同业务场景下的表现
- **场景3**: 验证字符串类型字段的兼容性

## 8. 结论

**批次5总体风险评估**: 高风险
- **高风险项**: 3个 (数值计算、直接赋值、递增操作)
- **中风险项**: 3个 (接口URL不匹配、多前缀系统、条件接口选择)
- **低风险项**: 多个 (字符串操作、枚举比较、已适配字段)

**关键问题**:
1. **todo.md记录错误**: 所有接口URL记录与实际代码不符
2. **数值操作风险**: 存在多处数值计算、赋值和递增，需要重点关注
3. **多前缀复杂**: WOS、WWS、WMD、WIS四套前缀系统，增加了复杂性
4. **条件接口选择**: 根据upperType动态选择不同前缀的接口

**建议优先级**:
1. **立即处理**: 验证数值字段的类型变更影响，特别是getGoodsSnNum函数
2. **重点关注**: 更正todo.md中的接口记录
3. **持续监控**: 确保多前缀系统和条件接口选择逻辑正常

**与前面批次的对比**:
- 批次5涉及最多的前缀系统（4套），复杂性最高
- 数值操作风险模式与前面批次类似，但增加了累加函数的风险
- 接口URL不匹配问题依然存在，需要系统性解决
- 部分字段（totalNum、underNum）已经适配为字符串类型，显示了渐进式迁移的模式
