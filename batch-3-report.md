# 批次3分析报告：库存容器查询模块扩展

## 数据范围
- **todo.md行数**: 第19-25行
- **涉及字段**: id
- **主要接口**: `/wmd/front/inventory/container/query` (但实际代码中是不同的具体接口)

## 1. 接口匹配验证

### 1.1 接口URL不匹配问题分析
**重要发现**: 与批次2类似，todo.md中记录的接口URL与实际代码内容存在严重不匹配

#### todo.md第19行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url = '/inventory/container/query_container_detail';`
- **文件位置**: `src/component/query/container-query/reducers.js:218`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wmd/front/inventory/container/query_container_detail`

#### todo.md第20行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url = '/inventory/container/container_shift_query';`
- **文件位置**: `src/component/query/container-query/reducers.js:229`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wmd/front/inventory/container/container_shift_query`

#### todo.md第21行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url = '/inventory/container/query_container_wellen_detail';`
- **文件位置**: `src/component/query/container-query/reducers.js:481`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wmd/front/inventory/container/query_container_wellen_detail`

#### todo.md第22行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/task_no_detail_new_special'`
- **文件位置**: `src/component/query/container-query-new/inbound/enter-warehouse-special/server.js:5`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wis/front/inventory/container/task_no_detail_new_special`

#### todo.md第23行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/diff_box_for_non_genuine'`
- **文件位置**: `src/component/query/container-query-new/inbound/defective-binning/server.js:5`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wis/front/inventory/container/diff_box_for_non_genuine`

#### todo.md第24行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/non_genuine_detail'`
- **文件位置**: `src/component/query/container-query-new/inbound/defective-binning/server.js:11`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wis/front/inventory/container/non_genuine_detail`

#### todo.md第25行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/upper_detail_new'`
- **文件位置**: `src/component/query/container-query-new/inbound/enter-warehouse/server.js:5`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wis/front/inventory/container/upper_detail_new`

### 1.2 实际接口分析

#### 容器查询动态接口 (src/component/query/container-query/reducers.js)
这些接口根据容器类型动态选择：
```javascript
switch (containerType) {
  case 2:
    url = '/inventory/container/query_container_detail';  // 拣货周转箱
    break;
  case 3:
    url = '/inventory/get_batch_container_info';          // 批次周转箱
    break;
  case 4:
  case BZQ_CONTAINER_TYPE:
    url = '/inventory/seed_container/list';               // 播种周转箱
    break;
  case 6:
  case 7:
    url = '/inventory/container/container_shift_query';   // 补货/移位周转箱
    break;
  case 12:
    url = '/inventory/return/shift_detail';               // 退供交接箱
    break;
}
```

#### 新版容器查询接口 (WIS前缀)
这些是新版本的容器查询接口，使用WIS_FRONT前缀：
```javascript
// 特殊入库装箱
'/wis/front/inventory/container/task_no_detail_new_special'

// 非正品分箱
'/wis/front/inventory/container/diff_box_for_non_genuine'
'/wis/front/inventory/container/non_genuine_detail'

// 入库装箱
'/wis/front/inventory/container/upper_detail_new'
```

## 2. 实际接口使用情况分析

### 2.1 容器详情查询 (getContainerDetail)
**定义位置**: `src/component/query/container-query/reducers.js:210-237`
**调用逻辑**: 根据containerType动态选择接口URL
**数据流分析**:
```javascript
* getContainerDetail(action, ctx) {
  const { param } = action;
  const { containerType } = param;
  let url;
  switch (containerType) {
    case 2: url = '/inventory/container/query_container_detail'; break;
    case 3: url = '/inventory/get_batch_container_info'; break;
    // ... 其他类型
  }
  // 使用动态URL调用接口
}
```

### 2.2 特殊入库装箱 (queryDetailAPI)
**定义位置**: `src/component/query/container-query-new/inbound/enter-warehouse-special/server.js:4`
```javascript
export const queryDetailAPI = (param) => sendPostRequest({
  url: '/inventory/container/task_no_detail_new_special',
  param,
}, process.env.WIS_FRONT);  // 使用WIS前缀
```

**调用位置**: `src/component/query/container-query-new/inbound/enter-warehouse-special/enter-warehouse-special.reducers.js:39`
**数据流分析**:
```javascript
const { code, info, msg } = yield queryDetailAPI(params);
if (code === '0') {
  yield this.changeData({
    detailData: info || {},
    list: info?.goodsLists || [],
  });
}
```

### 2.3 非正品分箱 (diffBoxForNonGenuineAPI)
**定义位置**: `src/component/query/container-query-new/inbound/defective-binning/server.js:4`
```javascript
export const diffBoxForNonGenuineAPI = (param) => sendPostRequest({
  url: '/inventory/container/diff_box_for_non_genuine',
  param,
}, process.env.WIS_FRONT);
```

**调用位置**: `src/component/query/container-query-new/inbound/defective-binning/defective-binning.reducers.js:43`
**数据流分析**:
```javascript
const {
  code,
  info: {
    containerCode,    // 容器号
    bizTypeStr,       // 类型
    operationStepStr, // 状态
    sowingNum,        // 已扫描 (可能受影响)
    totalNum,         // 总数 (可能受影响)
    turnoverBoxDetails,
  }, msg,
} = yield diffBoxForNonGenuineAPI(params);
```

### 2.4 入库装箱 (queryDetailAPI)
**定义位置**: `src/component/query/container-query-new/inbound/enter-warehouse/server.js:4`
```javascript
export const queryDetailAPI = (param) => sendPostRequest({
  url: '/inventory/container/upper_detail_new',
  param,
}, process.env.WIS_FRONT);
```

**返回数据结构**:
```javascript
{
  containerCode: 'RKSS012655852',
  taskNo: '22101200006',
  statusName: null,
  totalNum: 20,           // 总数 (可能受影响)
  upperNum: '123',        // 已上架数 (可能受影响)
  waitingUpperNum: '123', // 待上架数 (可能受影响)
  goodsLists: [
    {
      goodsSn: 'sM22032934465154',
      size: '',
      num: 10,            // 数量 (可能受影响)
      skuCode: 'I152zqpt0qif'
    }
  ],
}
```

## 3. 字段使用方式分析

### 3.1 数值运算操作 (高风险)
**位置**: `src/component/query/container-query-new/util.js:66`
```javascript
result.current.reviewNum = (result.current.reviewNum || 0) + 1;  // 数值运算
```

**位置**: `src/component/query/container-query-new/inbound/defective-binning/defective-binning.reducers.js:111`
```javascript
reviewTotalNum: reviewTotalNum + 1,  // 数值运算
```

**风险分析**: 
- 如果reviewNum字段从long变为string，这些运算可能失效
- 需要确保运算逻辑在字符串类型下正常工作

### 3.2 数值比较操作 (高风险)
**位置**: `src/component/query/container-query-new/util.js:14-16`
```javascript
targetItemIndex = list.findIndex((v) => 
  `${v.skuCode}` === skuCode && 
  Number(v.reviewNum) < Number(v.num)  // 数值比较
);
```

**风险分析**: 
- 如果reviewNum或num字段从long变为string，Number()转换仍然有效
- 但需要确保字符串格式正确（纯数字字符串）

### 3.3 字符串拼接作为ID (低风险)
**位置**: 多个文件中的模式
```javascript
const id = `${goodsSn}-${size}`;  // 字符串拼接
list.findIndex((v) => `${v.goodsSn}-${v.size}` === id);  // 字符串比较
```

**风险分析**:
- 这是安全的字符串操作，类型变更不影响功能
- 无需修改

### 3.4 容器类型枚举判断 (低风险)
**位置**: `src/component/query/container-query/reducers.js:216-236`
```javascript
switch (containerType) {
  case 2:
  case 3:
  case 4:
  // ... 数值枚举比较
}
```

**风险分析**: 
- containerType是枚举值，通常不会受到字段类型变更影响
- 低风险

## 4. 数据结构分析

### 4.1 特殊入库装箱返回数据
```javascript
{
  code: '0',
  info: {
    containerCode: string,
    taskNo: string,
    totalNum: number,        // 可能受影响
    upperNum: string,        // 可能受影响
    waitingUpperNum: string, // 可能受影响
    goodsLists: [
      {
        goodsSn: string,
        size: string,
        num: number,         // 可能受影响
        reviewNum: number,   // 可能受影响
        skuCode: string
      }
    ]
  }
}
```

### 4.2 非正品分箱返回数据
```javascript
{
  code: '0',
  info: {
    containerCode: string,
    bizTypeStr: string,
    operationStepStr: string,
    sowingNum: number,       // 已扫描数 (可能受影响)
    totalNum: number,        // 总数 (可能受影响)
    turnoverBoxDetails: [...]
  }
}
```

### 4.3 入库装箱返回数据
```javascript
{
  code: '0',
  info: {
    containerCode: string,
    taskNo: string,
    totalNum: number,        // 可能受影响
    upperNum: string,        // 可能受影响
    waitingUpperNum: string, // 可能受影响
    goodsLists: [
      {
        goodsSn: string,
        size: string,
        num: number,         // 可能受影响
        skuCode: string
      }
    ]
  }
}
```

## 5. 风险点评估

### 5.1 高风险点
1. **数值运算操作**:
   ```javascript
   result.current.reviewNum = (result.current.reviewNum || 0) + 1;
   reviewTotalNum: reviewTotalNum + 1;
   ```
   - 如果reviewNum字段从long变为string，需要确保运算逻辑正确

2. **数值比较操作**:
   ```javascript
   Number(v.reviewNum) < Number(v.num)
   ```
   - 如果reviewNum或num字段类型变更，需要确保Number()转换正常

### 5.2 中风险点
1. **接口URL不匹配**: todo.md中的记录与实际代码完全不符
2. **前缀切换复杂**: WIS和WMD两套前缀系统，增加了复杂性
3. **动态接口选择**: 根据containerType动态选择接口，增加了维护复杂度

### 5.3 低风险点
1. **字符串拼接**: `${goodsSn}-${size}` 等操作
2. **容器类型枚举**: switch case中的数值比较
3. **字符串操作**: 纯字符串处理逻辑

## 6. 修改建议

### 6.1 需要验证的点
1. **确认受影响字段**: 明确哪些字段从long变为string
2. **数值操作兼容性**: 确保运算和比较在字符串类型下正常工作
3. **接口URL映射**: 更正todo.md中的接口记录

### 6.2 可能需要修改的代码
如果reviewNum、num、totalNum、sowingNum等字段受影响：

```javascript
// 当前代码
result.current.reviewNum = (result.current.reviewNum || 0) + 1;

// 可能需要的修改
result.current.reviewNum = String(parseInt(result.current.reviewNum || '0', 10) + 1);

// 当前代码
Number(v.reviewNum) < Number(v.num)

// 可能需要的修改
parseInt(v.reviewNum || '0', 10) < parseInt(v.num || '0', 10)
```

## 7. 测试场景建议

### 7.1 数值运算测试
- **场景1**: 验证reviewNum字段的递增逻辑
- **场景2**: 测试reviewTotalNum的累加功能
- **场景3**: 验证边界值处理 (0, 负数, 大数值)

### 7.2 数值比较测试
- **场景1**: 验证reviewNum < num的比较逻辑
- **场景2**: 测试Number()转换的正确性
- **场景3**: 验证字符串数字的比较结果

### 7.3 接口调用测试
- **场景1**: 验证WIS前缀接口的正常调用
- **场景2**: 验证WMD前缀接口的正常调用
- **场景3**: 测试动态接口选择逻辑

### 7.4 业务流程测试
- **场景1**: 特殊入库装箱完整流程
- **场景2**: 非正品分箱完整流程
- **场景3**: 入库装箱完整流程

## 8. 结论

**批次3总体风险评估**: 高风险
- **高风险项**: 2个 (数值运算和比较)
- **中风险项**: 3个 (接口URL不匹配、前缀切换、动态接口)
- **低风险项**: 多个 (字符串操作、枚举比较)

**关键问题**:
1. **todo.md记录错误**: 所有接口URL记录与实际代码不符
2. **数值操作风险**: 存在多处数值运算和比较，需要重点关注
3. **接口前缀复杂**: WIS和WMD两套前缀系统，增加了复杂性
4. **动态接口选择**: 根据容器类型动态选择接口，维护复杂

**建议优先级**:
1. **立即处理**: 验证数值字段的类型变更影响
2. **重点关注**: 更正todo.md中的接口记录
3. **持续监控**: 确保接口前缀切换和动态选择逻辑正常

**与前两个批次的对比**:
- 批次3的风险模式与批次2类似，主要集中在数值操作上
- 接口URL不匹配问题在所有批次中都存在，需要系统性解决
- 新版容器查询模块使用了更多的WIS前缀接口，增加了复杂性
