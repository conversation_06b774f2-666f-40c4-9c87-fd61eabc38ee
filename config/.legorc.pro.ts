import { defineConfig } from '@shein-lego/apis';

export default defineConfig({
    defineInProcessEnv: {
      NPID: JSON.stringify(7),
      motenv: JSON.stringify('production'),
      CLOUD_APPKEY: JSON.stringify('a38803a08c08f48ad0c3d82e'),
      URL: JSON.stringify('https://bbl.biz.sheincorp.cn/trans/api/all-trans'),
      GMOT_URI: JSON.stringify('https://tmoteu.dotfashion.cn/#/gtms-pda/gtms-menus'),
      MOT_EU_URL: JSON.stringify('https://mot-eu.biz.sheinbackend.com'),
      DEPLOYED_AREA: JSON.stringify('mot-pl'),
      CLOUD_MESSAGE_ADDR: JSON.stringify('cloud-message.sheincorp.cn'),
      MOT_EU_OFFICE_URL: JSON.stringify('https://mot-eu-office.biz.sheinbackend.com'),
    },
})
