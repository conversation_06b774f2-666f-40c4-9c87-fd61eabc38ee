import { defineConfig } from '@shein-lego/apis';
const path = require('path');

export default defineConfig({
  html: {
    favicon: path.resolve(__dirname, '../src/source/img/test-env.png'),
  },
  defineInProcessEnv: {
    motenv: JSON.stringify('test'),
    CLOUD_KEY: JSON.stringify('PL_PDA_GUIDELINE_TEST'),
    CLOUD_APPKEY: JSON.stringify('bf2bc16c4bdc5a06ed028fa6'),
    MOT_EU_URL: JSON.stringify('https://mot-eu-test01.dotfashion.cn'),
    CLOUD_MESSAGE_ADDR: JSON.stringify('cloud-message-test01.sheincorp.cn'),
    JS_CHALLENGE: JSON.stringify('https://pc-test25.shein.com/'),
  },
  experiments: {
    topLevelAwait: true,
    reactFastRefresh: true,
    lazyCompilation: true,
  },
  dev: {
    host: 'localhost',
    port: 8021, // 自定义的本地端口
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    proxy: {
      '/index.php/Home': {
        target: 'http://tms-front-test-test.test.paas-test.sheincorp.cn', // 测试环境
        changeOrigin: true,
        pathRewrite: {
          '^/index.php/Home/.{2}_mot': '/index.php/Home',
        },
        headers: {},
      },
      '/': {
        target: 'http://wgw-eu-cneast-test-test.test.paas-test.sheincorp.cn/', // 测试环境
        changeOrigin: true,
        secure: false,
        headers: {},
      },
    },
  },
});
