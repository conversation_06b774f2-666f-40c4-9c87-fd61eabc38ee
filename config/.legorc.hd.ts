import { defineConfig } from '@shein-lego/apis';
const path = require('path');

export default defineConfig({
  html: {
    favicon: path.resolve(__dirname, '../src/source/img/hd-env.png'),
  },
  defineInProcessEnv:{
    NPID: JSON.stringify(7),
    motenv: JSON.stringify('alpha'),
    CLOUD_APPKEY: JSON.stringify('a38803a08c08f48ad0c3d82e'),
    URL: JSON.stringify('https://bbl.biz.sheincorp.cn/trans/api/all-trans'),
    GMOT_URI: JSON.stringify('https://tmot-gray.dotfashion.cn/#/gtms-pda/gtms-menus'),
    DEPLOYED_AREA: JSON.stringify('mot-pl'),
    MOT_EU_URL: JSON.stringify('https://mot-eu-hd.biz.sheinbackend.com'),
    CLOUD_MESSAGE_ADDR: JSON.stringify('cloud-message.sheincorp.cn'),
    MOT_EU_OFFICE_URL: JSON.stringify('https://mot-eu-office-hd.biz.sheinbackend.com'),
  },
})
