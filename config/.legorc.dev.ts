import { defineConfig } from '@shein-lego/apis';

export default defineConfig({
  defineInProcessEnv: {
    CLOUD_MESSAGE_ADDR: JSON.stringify('cloud-message-dev01.sheincorp.cn'),
    motenv: JSON.stringify('dev'),
    CLOUD_KEY: JSON.stringify('PL_PDA_GUIDELINE_TEST'),
    CLOUD_APPKEY: JSON.stringify('bf2bc16c4bdc5a06ed028fa6'),
    MOT_EU_URL: JSON.stringify('https://mot-eu-dev01.dotfashion.cn'),
  },
  dev: {
    host: 'localhost',
    port: 8021, // 自定义的本地端口
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    proxy: {
      '/index.php/Home': {
        target: 'http://tms-front-dev-dev.dev.paas-dev.sheincorp.cn', // 开发环境
        changeOrigin: true,
        pathRewrite: {
          '^/index.php/Home/.{2}_mot': '/index.php/Home',
        },
        headers: {},
      },
      '/': {
        target: 'http://wgw-pl-cneast-dev-dev.dev.paas-dev.sheincorp.cn/', // 开发环境
        changeOrigin: true,
        secure: false,
        headers: {},
      },
    },
  },
});
