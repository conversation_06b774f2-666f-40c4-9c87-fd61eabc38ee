import { defineConfig } from '@shein-lego/apis';
import { LessCoding } from '@shein-lego/plugin-lcd-compile';
import { ExternalsMap } from '@shein-lego/plugin-externals-map';

const path = require('path');

export default defineConfig<LessCoding & ExternalsMap>({
  // webpack entry 或 entry.index
  entry: path.resolve(__dirname, '../src/bbl.js'),
  alias: {
    lib: path.join(__dirname, '../src/lib'),
    common: path.join(__dirname, '../src/component/common'),
    source: path.join(__dirname, '../src/source'),
    server: path.join(__dirname, '../src/server'),
    component: path.join(__dirname, '../src/component'),
  },
  externals: {
    lodash: 'window._',
    moment: 'window.moment'
  },
  externalsMap: {
    cdnPrefix: 'https://assets2.dotfashion.cn/unpkg/',
    attrs: {
      fetchpriority: 'high',
    },
  },
  // html-webpack-plugin 配置
  html: {
    template: path.resolve(__dirname, '../index.ejs'),
    favicon: path.join(__dirname, '../src/source/img', 'mot-eu-20240606.png'),
    filename: 'index.html',
    minify: {
      collapseWhitespace: true,
      keepClosingSlash: true,
      removeComments: true,
      minifyCSS: true,
      minifyJS: true,
      removeRedundantAttributes: true,
      removeScriptTypeAttributes: true,
      removeStyleLinkTypeAttributes: true,
      useShortDoctype: true,
    },
  },
  assets: {
    regex: /\.(png|jpg|jpeg|gif|eot|svg|ttf|woff|mp3|mp4|otf)$/,
    publicPath: process.env.LEGO_FRONTEND_HTTP2_PUBLIC_PATH,
  },
  // webpack pleugins
  plugins: [
    require.resolve('@alita/lego-plugin'), // alita lego插件
    require.resolve("@shein-lego/plugin-lcd-compile"),// 加载新 LCD 插件
    require.resolve('@shein-lego/plugin-externals-map'), // 替代 @shein/webpack-external-map
    require.resolve('./favicons'),
    require.resolve('@shein-lego/plugin-generate-sourcemap')
  ],
  extraBabelPlugins: ["@babel/plugin-transform-runtime"],
  excludeBabelCompile: [path.resolve(__dirname, '../src/component/common/worker-utils')],
  // webpack.DefinePlugin process.env 环境变量
  defineInProcessEnv: {
    BUILD_ENV: JSON.stringify(process.env.BUILD_ENV),
    MOT_EU_URL:JSON.stringify('https://mot-eu-test01.dotfashion.cn'),
    USE_REACT_CONTEXT: JSON.stringify(true),
    CLOUD_APPKEY: JSON.stringify('a38803a08c08f48ad0c3d82e'),
    motenv: JSON.stringify('test'),
    NPID: JSON.stringify(3),
    APP_VERSION: JSON.stringify(process.env.VERSION || ''),
    URL: JSON.stringify('https://bbl.biz.sheincorp.cn/trans/api/all-trans'),
    BASE_URI: JSON.stringify('/wms/front'),
    BASE_URI_WMD: JSON.stringify('/wmd/front'),
    OUT_URI: JSON.stringify('/was/out'),
    WOS_URI: JSON.stringify('/wos/front'),
    WPOC_URI: JSON.stringify('/wpoc/front'),
    WWS_URI: JSON.stringify('/wws/front'),
    QC_FRONT: JSON.stringify('/wms/qc/front'),
    QMS_FRONT:JSON.stringify('/qms/front'),
    QC_URI: JSON.stringify('/wms/qc'),
    QMS: JSON.stringify('/qms'),
    OWOS_URI: JSON.stringify('/owos/front'),
    STAT_URI: JSON.stringify('/wms/stat/front'),
    GMOT_URI: JSON.stringify('http://gtms-mot-front-cneast-test-01.test.paas-test.sheincorp.cn/#/gtms-pda/gtms-menus'),
    CLOUD_KEY: JSON.stringify('PL_PDA_GUIDELINE'),
    WGS_FRONT: JSON.stringify('/wgs/front'),
    WGS_FRONT_BRIDGE: JSON.stringify('/wgs/front/bridge'),
    WMS_INTERNAL_FRONT: JSON.stringify('/osm/front'),
    WMS_INTERNAL: JSON.stringify('/osm'),
    DEPLOYED_AREA: JSON.stringify('mot-pl'),
    WSS_FRONT: JSON.stringify('/wss/front'),
    CLOUD_MESSAGE_ADDR: JSON.stringify('cloud-message.sheincorp.cn'),
    WTS_FRONT: JSON.stringify('/wts/front'),
    WAS_FRONT: JSON.stringify('/was/front'),
    JS_CHALLENGE: JSON.stringify('https://zpnv-eur.shein.com/'),
    WIS: JSON.stringify('/wis'),
    WIS_FRONT: JSON.stringify('/wis/front'),
    WKB: JSON.stringify('/wkb'),
    WKB_FRONT: JSON.stringify('/wkb/front'),
    OWIS: JSON.stringify('/owis'),
  },
  publicPath: process.env.LEGO_FRONTEND_HTTP2_PUBLIC_PATH,
  // LCD 插件配置 对应原 react-redux-component-loader 相关配置
  lessCoding: {
    externals: ['nav', 'login'],
    reducerName: 'reducers',
    pageDir: "component"
  },
  // 埋点插件配置 uem-sdk2.x 直接在html页面进行引入
  // analysis: {
  //   appId: "bd01515b-81c1-5307-92d4-6573bcaa0b43" // 各应用的埋点 appId
  // },
  // CSS Modules 配置，默认所有样式文件开启 CSS Modules 模式
  cssModules: true,
  alita: {
    container: '#container',
    name: 'motBase', // 基座应用名称
    base: '/motBase.html',
    baseName: 'motBase',
    assetsPrefix: 'https://assets2.dotfashion.cn/unpkg/',
    baseApp: {
      sandbox: false,
    },
  },
});
