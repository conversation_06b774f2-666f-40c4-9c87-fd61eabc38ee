import { defineConfig } from '@shein-lego/apis';

export default defineConfig({
  defineInProcessEnv:{
    APP_VERSION: JSON.stringify('local'),
    NPID: 7,
    URL: JSON.stringify('https://bbl.biz.sheincorp.cn/trans/api/all-trans'),
    CLOUD_APPKEY: JSON.stringify('e8f062a43cbbd30f0a9a42dd'),
    motenv: JSON.stringify('local'),
    CLOUD_KEY: JSON.stringify('PL_PDA_GUIDELINE_TEST'),
  },
  // 支持本地跑https
  publicPath: 'http://localhost:8021/',
  alita: {
    // 子应用入口地址
    entries:{
      'mot-standard': 'http://localhost:8087/mot-standard.html',
      'inbound-standard': 'http://localhost:8086/inbound-standard.html',
    },
  },
})

