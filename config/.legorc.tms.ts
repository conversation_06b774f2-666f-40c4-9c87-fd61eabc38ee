import { defineConfig } from '@shein-lego/apis';

const proxy = {
  dev: {
    '/': {
      target: 'http://***********',
      changeOrigin: false,
      secure: false,
      headers: {
        Host: 'mot-eu-dev01.dotfashion.cn',
      },
    },
  },
  test: {
    '/': {
      target: 'http://************',
      changeOrigin: false,
      secure: false,
      headers: {
        Host: 'mot-eu-test01.dotfashion.cn',
      },
    },
  },
  prod: {
    '/': {
      target: 'http://************',
      changeOrigin: false,
      secure: false,
      headers: {
        Host: 'hms.sheincorp.cn',
      },
    },
  },
};

export default defineConfig({
  dev: {
    host: '127.0.0.1',
    port: 4000,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    proxy: proxy['test'],
    open: true
  },
  // defineInProcessEnv: {
  //   GMOT_URI: JSON.stringify('http://127.0.0.1:4001/#/gtms-pda/gtms-menus'),
  // },
})