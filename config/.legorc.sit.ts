import { defineConfig } from '@shein-lego/apis';

export default defineConfig({
  defineInProcessEnv: {
    motenv: JSON.stringify('sit'),
    CLOUD_KEY: JSON.stringify('PL_PDA_GUIDELINE_TEST'),
    // 无验收环境 - 后续有了需修改
    MOT_EU_URL: JSON.stringify('https://mot-eu-test01.dotfashion.cn'),
  },
  dev: {
    host: 'localhost',
    port: 8088, // 自定义的本地端口
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    proxy: {
      '/': {
        target: 'https://mot-pl-sit01.dotfashion.cn', // 验收环境
        changeOrigin: true,
        secure: false,
        headers: {},
      },
    },
  },
});
