/* eslint-disable @shein-bbl/bbl/translate-i18n-byT */
import { definePlugin } from '@shein-lego/apis';
import FaviconsWebpackPlugin from 'favicons-webpack-plugin';
import WorkboxPlugin from 'workbox-webpack-plugin';

export default definePlugin((api) => {
  let url = 'https://mot-eu.biz.sheinbackend.com';
  let appName = 'MOT-EU';
  switch (process.env.motenv) {
    case 'dev':
      url = 'https://mot-eu-dev01.dotfashion.cn';
      appName = '[dev] MOT-EU';
      break;
    case 'test':
      url = 'https://mot-eu-test01.dotfashion.cn';
      appName = '[test] MOT-EU';
      break;
    case 'alpha':
      url = 'https://mot-eu-hd.biz.sheinbackend.com';
      appName = '[hd] MOT-EU';
      break;
    default:
      url = 'https://mot-eu.biz.sheinbackend.com';
      appName = 'MOT-EU';
      break;
  }

  api.register({ namespace: 'mrp' });
  api.beforeWebpackCompiled((webpackConfig) => {
    const legoFaviconsWebpackPlugin = new FaviconsWebpackPlugin({
      logo: './src/source/img/mot-pl.png',
      cache: true,
      mode: 'webapp',
      favicons: {
        appName,
        appDescription: 'Warehouse Manage System by pda',
        developerName: 'wms-team',
        developerURL: null, // prevent retrieving from the nearest package.json
        background: '#ddd',
        theme_color: '#333',
        orientation: 'portrait-primary',
        start_url: `${url}/#/main-menu`,
        icons: {
          appleIcon: false,
          appleStartup: false,
          firefox: false,
          favicons: false,
          windows: false,
          coast: false,
          yandex: false,
        },
      },
    });

    const legoWorkboxPlugin = new WorkboxPlugin.GenerateSW({
      clientsClaim: true,
      skipWaiting: true,
      swDest: 'serviceworker.js',
      runtimeCaching: [
        {
          // 原本 /.*\.(js|css|html)$/ , 会将埋点误判存进缓存 https://monitor-web.dotfashion.cn/go/pv?xxxindex.html
          // 正则过滤https://monitor-web.dotfashion.cn/go/pv开头域名 只匹配剩下的js|css|html
          urlPattern: /^(https:\/\/assets2?\.dotfashion\.cn)((\/unpkg.*\.js)|(.*\.(css|svg|png)))$/,
          handler: async ({ request }) => {
            // 自定义缓存策略的逻辑
            const { url: cacheUrl } = request;
            // 获取对应的缓存信息
            const cache = await caches.open('mot-eu-cache');
            const cachedResponse = await cache.match(request);
            if (cachedResponse) {
              return cachedResponse;
            }
            try {
              const networkResponse = await fetch(cacheUrl, {
                ...request,
                mode: 'cors',
              });

              // 响应成功为200 则进行缓存处理
              if (networkResponse.status === 200) {
                cache.put(request, networkResponse.clone());
                return networkResponse;
              }

              // 无缓存或者响应成功 则直接将响应结果进行返回
              return null;
            } catch {
              // 失败则返回缓存匹配信息
              return null;
            }
          },
        },
      ],
    });
    webpackConfig.plugins.push(legoFaviconsWebpackPlugin);
    webpackConfig.plugins.push(legoWorkboxPlugin);
  });
});
