#!/usr/bin/env sh
# Load nvm
if [[ -n "$NVM_DIR" ]] && [[ -s "$NVM_DIR/nvm.sh" ]]; then
  echo "存在nvm"
else
  echo "不存在nvm"
  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
fi

if ! command -v jq >/dev/null 2>&1; then
  echo "jq not found, please execute this command: brew install jq"
else
  echo "jq命令存在"
fi

. "$(dirname -- "$0")/_/husky.sh"
# get current branch name
BRANCH_NAME=$(git branch --show-current)
# 不区分大小写
branch_lowercase=$(echo "$BRANCH_NAME" | tr '[:upper:]' '[:lower:]')

# 根据git log -1080条记录，正则匹配到有Merge branch '\(test\|sit|ft_.*\)' into '的分支
# ${git log -1000 --pretty=format:"%h %s" | grep -E ""}
LOGS=$(git log -1000 --pretty=format:"%h %s");

# 读取配置项
configPath=$(dirname "$0")
json=$(cat "$configPath/.huskyrules.json")
illegalMerge=$(echo $json | jq '.rules.illegalMerge')

if $illegalMerge ; then
  if [[ $branch_lowercase =~ ^.*-(test|dev|sit)$ ]]; then
    echo "合并冲突的分支~"
    exit 0;
  elif echo "$LOGS" | grep -qiE "merge(.*)branch(.*)'(ft_.*|test|sit)' into "; then
    echo "w(ﾟДﾟ)w 当前分支merge了开发、测试或者验收分支，请检查 $1>$2"
    exit 1;
  else
    echo '你很棒棒哦~ 这是一个规范的分支'
    exit 0;
  fi
else
  exit 0
fi
