#!/usr/bin/env sh

# Load nvm
if [[ -n "$NVM_DIR" ]] && [[ -s "$NVM_DIR/nvm.sh" ]]; then
  echo "存在nvm"
else
  echo "不存在nvm"
  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
fi

if ! command -v jq >/dev/null 2>&1; then
  echo "jq not found, please execute this command: brew install jq"
else
  echo "jq命令存在"
fi

# 读取配置项
configPath=$(dirname "$0")
json=$(cat "$configPath/.huskyrules.json")
eslintVerify=$(echo $json | jq '.rules.eslintVerify')
duplicatedApi=$(echo $json | jq '.rules.duplicatedApi')
storageVerify=$(echo $json | jq '.rules.storageVerify')
# Load husky library
. "$(dirname "$0")/_/husky.sh"

## --- 检查「修改的文件」是否遵循eslint规范 ---
#  npx lint-staged

# --- 进行SessionStorage/LocalStorage使用校验 ---

CHECK_STORAGE_PATH= "$configPath/scripts/general-inspection.js"

node "$CHECK_STORAGE_PATH"


# --- 进行接口声明重复性校验 ---

# Define the script to run
SCRIPT_PATH="$configPath/scripts/regular-inspection/check-duplicated.js"
BRANCH_NAME=$(git branch --show-current)
# 不区分大小写
branch_lowercase=$(echo "$BRANCH_NAME" | tr '[:upper:]' '[:lower:]')


# Run the script using absolute path to node command
echo $duplicatedApi

if $duplicatedApi ; then
  node  "$SCRIPT_PATH"
  # Check the script result
  if [ $? -ne 0 ]; then
    echo "Error: 提交的接口有重复声明，请检查完再提交。 "
    exit 1
  fi
else
  echo "已关闭重复性接口校验"
fi

if $eslintVerify ; then
# --- 检查「修改的文件」是否遵循eslint规范 ---
#  npx lint-staged
  if [[ $branch_lowercase =~ ^.*-(test|dev|sit)$ ]]; then
    echo "合并冲突的分支~ 不校验eslint"
  exit 0;
  elif [[ $branch_lowercase =~ ^(pre-master-.*|hd-master)$ ]]; then
    echo "灰度和pre-master分支~ 不校验eslint"
  exit 0
  else
    npx --no-install lint-staged
  fi
else
  echo "已关闭eslint校验"
fi


exit 0
