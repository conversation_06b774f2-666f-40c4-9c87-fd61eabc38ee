# 批次4分析报告：容器查询新版本模块

## 数据范围
- **todo.md行数**: 第26-32行
- **涉及字段**: id
- **主要接口**: `/wmd/front/inventory/container/query` (但实际代码中是不同的具体接口)

## 1. 接口匹配验证

### 1.1 接口URL不匹配问题分析
**重要发现**: 与前面批次类似，todo.md中记录的接口URL与实际代码内容存在不匹配

#### todo.md第26行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `getInterfacePermissionsApi({ url: \`${process.env.WIS_FRONT}/inventory/container/number_auth\` })`
- **文件位置**: `src/component/query/container-query-new/reducers.js:33`
- **验证结果**: ❌ 完全不匹配
- **实际接口**: `/wis/front/inventory/container/number_auth` (权限验证接口)

#### todo.md第27行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/query'`
- **文件位置**: `src/component/query/container-query-new/server.js:5`
- **验证结果**: ✅ 部分匹配
- **完整URL**: `/wmd/front/inventory/container/query`

#### todo.md第28行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/container_shift_query_details'`
- **文件位置**: `src/component/query/container-query-new/in-warehouse/crossover-inbound-enter-warehouse/server.js:5`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wws/front/inventory/container/container_shift_query_details`

#### todo.md第29行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/shift_inf_query'`
- **文件位置**: `src/component/query/container-query-new/in-warehouse/crossover-inbound-enter-warehouse/server.js:11`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wws/front/inventory/container/shift_inf_query`

#### todo.md第30行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/container_shift_query_details'`
- **文件位置**: `src/component/query/container-query-new/in-warehouse/replenishment/server.js:5`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wws/front/inventory/container/container_shift_query_details`

#### todo.md第31行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/shift_inf_query'`
- **文件位置**: `src/component/query/container-query-new/in-warehouse/replenishment/server.js:11`
- **验证结果**: ❌ 不匹配
- **完整URL**: `/wws/front/inventory/container/shift_inf_query`

#### todo.md第32行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/query'`
- **文件位置**: `src/component/query/server.js:37`
- **验证结果**: ✅ 匹配
- **完整URL**: `/wmd/front/inventory/container/query`

#### todo.md第39行
- **记录的接口**: `/wmd/front/inventory/container/query`
- **实际代码内容**: `url: '/inventory/container/query'`
- **文件位置**: `src/component/compound-package/subcontracting-query/server.js:20`
- **验证结果**: ✅ 匹配
- **完整URL**: `/wmd/front/inventory/container/query`

### 1.2 实际接口分析

#### 权限验证接口 (WIS前缀)
```javascript
// 检查是否有权限显示件数
getInterfacePermissionsApi({ 
  url: `${process.env.WIS_FRONT}/inventory/container/number_auth` 
})
// 完整URL: /wis/front/inventory/container/number_auth
```

#### 容器查询接口 (WMD前缀)
```javascript
// 新版容器查询主接口
export const scanContainerCodeAPI = (param) => sendPostRequest({
  url: '/inventory/container/query',
  param,
}, process.env.BASE_URI_WMD);
// 完整URL: /wmd/front/inventory/container/query
```

#### 移位单查询接口 (WWS前缀)
```javascript
// 获取移位单详情
export const queryDetailAPI = (param) => sendPostRequest({
  url: '/inventory/container/container_shift_query_details',
  param,
}, process.env.WWS_URI);

// 获取移位单信息
export const shiftInfQueryAPI = (param) => sendPostRequest({
  url: '/inventory/container/shift_inf_query',
  param,
}, process.env.WWS_URI);
```

#### 分包查询接口 (WMD前缀)
```javascript
// 分包查询中的容器查询
export const scanContainerCodeAPI = (param) => sendPostRequest({
  url: '/inventory/container/query',
  param,
}, process.env.BASE_URI_WMD);
```

## 2. 实际接口使用情况分析

### 2.1 权限验证接口使用
**定义位置**: `src/component/query/container-query-new/reducers.js:33`
**调用逻辑**: 初始化时检查用户是否有权限显示件数
**数据流分析**:
```javascript
const res = yield getInterfacePermissionsApi({ 
  url: `${process.env.WIS_FRONT}/inventory/container/number_auth` 
});
yield this.changeData({ showNumberFlag: res.code === '0' });
```

### 2.2 新版容器查询接口
**定义位置**: `src/component/query/container-query-new/server.js:4`
**调用位置**: `src/component/query/container-query-new/reducers.js:57`
**数据流分析**:
```javascript
const { code, info, msg } = yield scanContainerCodeAPI(params);
if (code === '0') {
  yield this.changeData({
    containerData: info || {},
    inboundPageUsedData: info?.upperContainerQueryVos?.[0] || {},
  });
}
```

### 2.3 移位单详情查询
**定义位置**: `src/component/query/container-query-new/in-warehouse/crossover-inbound-enter-warehouse/server.js:4`
**返回数据结构**:
```javascript
{
  code: '0',
  info: {
    containerCode: string,
    totalNum: number,        // 总数 (可能受影响)
    upperNum: number,        // 已上架数 (可能受影响)
    waitUpperNum: number,    // 待上架数 (可能受影响)
    shiftOrderDetailRspList: [
      {
        location: number,    // 位置 (可能受影响)
        goodsSn: string,
        size: string,
        skuCode: string,
        number: number,      // 数量 (可能受影响)
      }
    ]
  }
}
```

### 2.4 移位单信息查询
**定义位置**: `src/component/query/container-query-new/in-warehouse/crossover-inbound-enter-warehouse/server.js:10`
**返回数据结构**:
```javascript
{
  code: '0',
  info: {
    list: [...],
    containerNum: number,    // 容器数量 (可能受影响)
    goodsNum: number,        // 商品数量 (可能受影响)
  }
}
```

## 3. 字段使用方式分析

### 3.1 数值运算操作 (高风险)
**位置**: `src/component/query/container-query-new/util.js:66`
```javascript
result.current.reviewNum = (result.current.reviewNum || 0) + 1;  // 数值运算
```

**位置**: `src/component/query/container-query-new/in-warehouse/crossover-inbound-enter-warehouse/crossover-inbound-enter-warehouse.reducers.js:148`
```javascript
const { list = [], containerNum = 0, goodsNum = 0 } = res.info || {};
yield this.changeData({
  containerNum,           // 直接赋值数值
  goodsSnNum: goodsNum,   // 直接赋值数值
});
```

**风险分析**: 
- 如果containerNum、goodsNum字段从long变为string，需要确保赋值逻辑正确
- reviewNum的运算逻辑需要适配字符串类型

### 3.2 数值比较操作 (高风险)
**位置**: `src/component/query/container-query-new/util.js:14-16`
```javascript
targetItemIndex = list.findIndex((v) => 
  `${v.skuCode}` === skuCode && 
  Number(v.reviewNum) < Number(v.num)  // 数值比较
);
```

**风险分析**: 
- 如果reviewNum或num字段从long变为string，Number()转换仍然有效
- 但需要确保字符串格式正确（纯数字字符串）

### 3.3 权限验证逻辑 (低风险)
**位置**: `src/component/query/container-query-new/reducers.js:35`
```javascript
yield this.changeData({ showNumberFlag: res.code === '0' });
```

**风险分析**: 
- 这是基于接口返回的code字段进行判断，通常不受影响
- 低风险

### 3.4 字符串拼接作为ID (低风险)
**位置**: 多个文件中的模式
```javascript
const id = `${goodsSn}-${size}`;  // 字符串拼接
list.findIndex((v) => `${v.goodsSn}-${v.size}` === id);  // 字符串比较
```

**风险分析**:
- 这是安全的字符串操作，类型变更不影响功能
- 无需修改

## 4. 数据结构分析

### 4.1 容器查询返回数据
```javascript
{
  code: '0',
  info: {
    businessType: number,
    containerCode: string,
    upperContainerQueryVos: [
      {
        // 入库业务页面使用的数据
        totalNum: number,        // 可能受影响
        upperNum: string,        // 可能受影响
        waitingUpperNum: string, // 可能受影响
        number: number,          // 可能受影响
      }
    ]
  }
}
```

### 4.2 移位单详情返回数据
```javascript
{
  code: '0',
  info: {
    containerCode: string,
    totalNum: number,        // 可能受影响
    upperNum: number,        // 可能受影响
    waitUpperNum: number,    // 可能受影响
    shiftOrderDetailRspList: [
      {
        location: number,    // 可能受影响
        goodsSn: string,
        size: string,
        skuCode: string,
        number: number,      // 可能受影响
      }
    ]
  }
}
```

### 4.3 移位单信息返回数据
```javascript
{
  code: '0',
  info: {
    list: [...],
    containerNum: number,    // 可能受影响
    goodsNum: number,        // 可能受影响
  }
}
```

## 5. 风险点评估

### 5.1 高风险点
1. **数值运算操作**:
   ```javascript
   result.current.reviewNum = (result.current.reviewNum || 0) + 1;
   ```
   - 如果reviewNum字段从long变为string，需要确保运算逻辑正确

2. **数值比较操作**:
   ```javascript
   Number(v.reviewNum) < Number(v.num)
   ```
   - 如果reviewNum或num字段类型变更，需要确保Number()转换正常

3. **数值字段直接赋值**:
   ```javascript
   const { containerNum = 0, goodsNum = 0 } = res.info || {};
   ```
   - 如果这些字段从long变为string，默认值和赋值逻辑需要调整

### 5.2 中风险点
1. **接口URL不匹配**: todo.md中的记录与实际代码大部分不符
2. **多前缀系统**: WIS、WMD、WWS三套前缀系统，增加了复杂性
3. **权限验证依赖**: 依赖WIS接口的权限验证结果

### 5.3 低风险点
1. **字符串拼接**: `${goodsSn}-${size}` 等操作
2. **权限验证逻辑**: 基于code字段的判断
3. **字符串操作**: 纯字符串处理逻辑

## 6. 修改建议

### 6.1 需要验证的点
1. **确认受影响字段**: 明确哪些字段从long变为string
2. **数值操作兼容性**: 确保运算和比较在字符串类型下正常工作
3. **接口URL映射**: 更正todo.md中的接口记录
4. **默认值处理**: 检查数值字段的默认值设置

### 6.2 可能需要修改的代码
如果containerNum、goodsNum、totalNum、upperNum等字段受影响：

```javascript
// 当前代码
const { containerNum = 0, goodsNum = 0 } = res.info || {};

// 可能需要的修改
const { containerNum = '0', goodsNum = '0' } = res.info || {};

// 当前代码
result.current.reviewNum = (result.current.reviewNum || 0) + 1;

// 可能需要的修改
result.current.reviewNum = String(parseInt(result.current.reviewNum || '0', 10) + 1);
```

## 7. 测试场景建议

### 7.1 数值运算测试
- **场景1**: 验证reviewNum字段的递增逻辑
- **场景2**: 测试containerNum、goodsNum的赋值和显示
- **场景3**: 验证边界值处理 (0, 负数, 大数值)

### 7.2 数值比较测试
- **场景1**: 验证reviewNum < num的比较逻辑
- **场景2**: 测试Number()转换的正确性
- **场景3**: 验证字符串数字的比较结果

### 7.3 接口调用测试
- **场景1**: 验证WIS前缀权限接口的正常调用
- **场景2**: 验证WMD前缀容器查询接口的正常调用
- **场景3**: 验证WWS前缀移位单接口的正常调用

### 7.4 业务流程测试
- **场景1**: 新版容器查询完整流程
- **场景2**: 移位单查询完整流程
- **场景3**: 分包查询完整流程
- **场景4**: 权限验证和件数显示功能

## 8. 结论

**批次4总体风险评估**: 高风险
- **高风险项**: 3个 (数值运算、比较、直接赋值)
- **中风险项**: 3个 (接口URL不匹配、多前缀系统、权限验证)
- **低风险项**: 多个 (字符串操作、权限逻辑)

**关键问题**:
1. **todo.md记录错误**: 大部分接口URL记录与实际代码不符
2. **数值操作风险**: 存在多处数值运算、比较和赋值，需要重点关注
3. **多前缀复杂**: WIS、WMD、WWS三套前缀系统，增加了复杂性
4. **权限验证依赖**: 新版容器查询依赖WIS接口的权限验证

**建议优先级**:
1. **立即处理**: 验证数值字段的类型变更影响，特别是默认值处理
2. **重点关注**: 更正todo.md中的接口记录
3. **持续监控**: 确保多前缀系统和权限验证逻辑正常

**与前面批次的对比**:
- 批次4引入了权限验证的复杂性，增加了WIS前缀的依赖
- 数值操作风险模式与前面批次类似，但增加了默认值处理的风险
- 接口URL不匹配问题依然存在，需要系统性解决
- 新版容器查询模块的架构更加复杂，涉及多个前缀系统
