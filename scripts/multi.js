const fs = require('fs/promises');

async function generate(type, url) {
  // 生成{type}/index.html
  let htmlContent = await fs.readFile('./dist/index.html', 'utf8');
  htmlContent = htmlContent.replace(
    'assets/manifest.json',
    `assets/manifest-${type}.json`,
  );
  // 创建对应的文件夹
  await fs.mkdir(`./dist/${type}`);
  await fs.writeFile(`./dist/${type}/index.html`, htmlContent);

  // manifest-${type}.json
  let manifestContent = await fs.readFile(
    './dist/assets/manifest.json',
    'utf8',
  );
  manifestContent = manifestContent.replace(
    '"start_url": "https://mot-eu.biz.sheinbackend.com/#/main-menu"',
    `"start_url": "https://${url}/#/main-menu"`,
  );
  await fs.writeFile(`./dist/assets/manifest-${type}.json`, manifestContent);
}

// us de 微软云
generate('us', 'motus.dotfashion.cn');
generate('de', 'motde.dotfashion.cn');
generate('biz', 'mot.biz.sheinbackend.com');
